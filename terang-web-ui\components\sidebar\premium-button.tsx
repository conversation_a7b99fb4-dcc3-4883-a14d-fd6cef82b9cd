import React from 'react';
import { But<PERSON> } from "@heroui/react";
import { SparklesIcon } from 'hugeicons-react';
import { useRouter } from 'next/navigation';

const PremiumButton = () => {
    const router = useRouter();
  return (
    <div className="relative group">
      <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-violet-400/0 via-violet-400/10 to-violet-400/0 animate-pulse pointer-events-none" />
      
      <Button
        onPress={() => router.push('/subscription')}
        radius="lg"
        className="w-full px-6 py-3 font-medium relative overflow-hidden z-10
          bg-gradient-to-r from-violet-500 to-fuchsia-500 
          hover:from-violet-600 hover:to-fuchsia-600 
          text-white
          shadow-lg hover:shadow-xl 
          transform hover:scale-[1.00] 
          transition-all duration-300
          before:absolute
          before:content-['']
          before:w-12
          before:h-full
          before:top-0
          before:-left-6
          before:skew-x-[20deg]
          before:animate-shine
          before:bg-gradient-to-r
          before:from-transparent
          before:via-white/30
          before:to-transparent
          before:pointer-events-none"
      >
        <div className="relative z-10 flex items-center justify-center gap-1">
          <span className="text-md pl-1 font-semibold">Upgrade to Premium</span>
          <SparklesIcon 
            size={24} 
            className="text-white animate-pulse"
          />
        </div>
      </Button>
    </div>
  );
};

// Add this to your tailwind.config.js:
// theme: {
//   extend: {
//     keyframes: {
//       shine: {
//         '0%': { left: '-100%' },
//         '100%': { left: '200%' },
//       },
//     },
//     animation: {
//       shine: 'shine 2s infinite linear',
//     },
//   },
// },

export default PremiumButton;