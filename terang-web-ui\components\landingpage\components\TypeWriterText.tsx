'use client';

import React, { useEffect, useRef, useState, memo } from "react";
import styled from "styled-components";
import Button from "./Button";
import { useLanguage } from "@/app/language-wrapper";

// Theme interface for styled-components
interface ThemeProps {
  theme: {
    text: string;
    textRgba: string;
    fontxl: string;
    fontxxl: string;
    fontmd: string;
    fontsm: string;
  }
}

// Optimized styled components with common theme variables
const MainTitle = styled.h1<ThemeProps>`
  font-size: 4em;
  text-transform: capitalize;
  font-weight: bold;
  width: 90%;
  color: ${(props) => props.theme.text};
  align-self: flex-start;
  line-height: 1;
  font-family: "Sora", sans-serif;

  @media (max-width: 70em) {
    font-size: ${(props) => props.theme.fontxl};
  }
  @media (max-width: 48em) {
    align-self: center;
    text-align: center;
  }
  @media (max-width: 40em) {
    width: 90%;
  }
`;

const Title = styled.h2<ThemeProps>`
  font-size: ${(props) => props.theme.fontxxl};
  text-transform: capitalize;
  width: 100%;
  color: ${(props) => props.theme.text};
  align-self: flex-start;
  font-weight: bold;

  .text-1 {
    color: #4a90e2; /* Soft Blue */
  }
  .text-2 {
    color: #f39c12; /* Soft Orange */
  }
  .text-3 {
    color: #e74c3c; /* Soft Red */
  }

  @media (max-width: 70em) {
    font-size: ${(props) => props.theme.fontxl};
  }
  @media (max-width: 48em) {
    align-self: center;
    text-align: center;
  }
  @media (max-width: 40em) {
    width: 90%;
  }
`;

const SubTitle = styled.h3<ThemeProps>`
  font-size: 20px;
  text-transform: capitalize;
  color: ${(props) => `rgba(${props.theme.textRgba}, 0.6)`};
  font-weight: 600;
  margin-bottom: 1rem;
  width: 80%;
  align-self: flex-start;
  line-height: 1.2;
  font-family: "Sora", sans-serif;

  @media (max-width: 48em) {
    align-self: center;
    text-align: center;
    font-size: ${(props) => props.theme.fontmd};
  }
`;

const ButtonContainer = styled.div`
  width: 100%;
  align-self: flex-start;

  @media (max-width: 48em) {
    align-self: center;
    text-align: center;

    button {
      margin: 0 auto;
    }
  }
`;

// Optimized cursor with reduced animation complexity
const CursorSpan = styled.span`
  display: inline-block;
  width: 6px;
  height: 1.05em;
  background-color: #202020;
  margin-left: 5px;
  animation: blink 1s step-end infinite;
  vertical-align: text-bottom;
  transform: translateZ(0); /* Hardware acceleration */
  
  @keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
  }
`;

// Define TypeWriter text item interface
interface TypewriterTextItem {
  key: string;
  class: string;
}

// TypeWriterText component with language support
const TypeWriterText: React.FC = memo(() => {
  const [displayText, setDisplayText] = useState<string>("");
  const [currentClass, setCurrentClass] = useState<string>("");
  const { language, t } = useLanguage();
  
  // Use refs for values that shouldn't trigger re-renders
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const textIndexRef = useRef<number>(0);
  const charIndexRef = useRef<number>(0);
  const isDeletingRef = useRef<boolean>(false);
  const pauseRef = useRef<boolean>(false);
  const typewriterTextsRef = useRef<TypewriterTextItem[]>([
    { key: "lpdp_exam", class: "text-2" },
    { key: "utbk_exam", class: "text-3" },
    { key: "cpns_exam", class: "text-1" },
  ]);

  // Reset typewriter when language changes
  useEffect(() => {
    // Reset all typewriter state when language changes
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    textIndexRef.current = 0;
    charIndexRef.current = 0;
    isDeletingRef.current = false;
    pauseRef.current = false;
    setDisplayText("");
    
    // Start typewriter again with new language
    const typingDelay = 150;
    timerRef.current = setTimeout(typeWriter, typingDelay);
    
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [language]);

  const typeWriter = () => {
    const currentTextObj = typewriterTextsRef.current[textIndexRef.current];
    const fullText = t(currentTextObj.key);
    
    // Clear any existing timer
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    
    // Handle pausing
    if (pauseRef.current) {
      pauseRef.current = false;
      timerRef.current = setTimeout(typeWriter, 2000); // Pause delay
      return;
    }
    
    if (isDeletingRef.current) {
      // Deleting text
      charIndexRef.current--;
      setDisplayText(fullText.substring(0, charIndexRef.current));
      
      // If all characters deleted, move to next word
      if (charIndexRef.current === 0) {
        isDeletingRef.current = false;
        textIndexRef.current = (textIndexRef.current + 1) % typewriterTextsRef.current.length;
        timerRef.current = setTimeout(typeWriter, 500); // Pause between words
      } else {
        timerRef.current = setTimeout(typeWriter, 75); // Deleting delay
      }
    } else {
      // Typing text
      charIndexRef.current++;
      setDisplayText(fullText.substring(0, charIndexRef.current));
      
      // Set the appropriate class
      setCurrentClass(currentTextObj.class);
      
      // If word is complete, prepare to delete
      if (charIndexRef.current === fullText.length) {
        isDeletingRef.current = true;
        pauseRef.current = true;
        timerRef.current = setTimeout(typeWriter, 150); // Typing delay
      } else {
        timerRef.current = setTimeout(typeWriter, 150); // Typing delay
      }
    }
  };

  return (
    <>
      <MainTitle>{t('cool_way')}</MainTitle>
      <Title>
        <span className={currentClass}>{displayText}</span>
        <CursorSpan />
      </Title>
      <SubTitle>
        {t('ask_explanation')}
      </SubTitle>
      <ButtonContainer>
        <Button 
          link="/available-exams" 
          text={t('start_now')} 
        />
      </ButtonContainer>
    </>
  );
});

// Add display name for better debugging
TypeWriterText.displayName = 'TypeWriterText';

export default TypeWriterText;