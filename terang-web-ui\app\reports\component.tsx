"use client";

import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Tab, Card, CardBody, CardHeader } from "@heroui/react";
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
} from 'chart.js';
import { Radar, Bar } from 'react-chartjs-2';
import Dot<PERSON>ottieAnimation from "@/components/shared/dotlottie-animation";
import { title } from "@/components/primitives";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/breadcrumbs";
import TimeManagementVisualization from './time-management';

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement
);

interface RadarDataPoint {
  x: string;
  y: number;
}

interface BarDataPoint {
  category: string;
  value: number;
  average: number;
}

const LaporanPersonal: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<string>("sesi");
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [isBlurred, setIsBlurred] = useState<boolean>(false); // New state for blur toggle
  const containerRef = useRef<HTMLDivElement>(null);

  const toggleBlur = () => {
    setIsBlurred(!isBlurred);
  };

  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.offsetWidth);
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  const handleTabChange = (key: React.Key): void => {
    setSelectedTab(key.toString());
  };

  // Radar chart data
  const radarData = {
    labels: ['Presentasi', 'Pembelajaran', 'Perencanaan', 'Pemahaman', 'Interaksi'],
    datasets: [
      {
        label: 'Nilai',
        data: [98, 93, 87, 98, 86],
        backgroundColor: 'rgba(99, 102, 241, 0.2)',
        borderColor: 'rgb(99, 102, 241)',
        borderWidth: 2,
        pointBackgroundColor: '#FFFFFF',
        pointBorderColor: 'rgb(99, 102, 241)',
        pointHoverBackgroundColor: '#FFFFFF',
        pointHoverBorderColor: 'rgb(99, 102, 241)',
        pointRadius: containerWidth < 768 ? 3 : 4,
        pointHoverRadius: containerWidth < 768 ? 5 : 6,
      }
    ]
  };

  const radarOptions = {
    scales: {
      r: {
        min: 0,
        max: 100,
        ticks: {
          stepSize: 20,
          font: {
            size: containerWidth < 768 ? 11 : 12,
            family: 'Inter, sans-serif'
          }
        },
        grid: {
          color: '#E2E8F0'
        },
        angleLines: {
          color: '#CBD5E1'
        },
        pointLabels: {
          font: {
            size: containerWidth < 768 ? 11 : 12,
            family: 'Inter, sans-serif'
          }
        }
      }
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.raw}%`;
          }
        }
      }
    },
    maintainAspectRatio: false
  };

  // Bar chart data
  const barData = {
    labels: ['TWK', 'TIU', 'TKP'],
    datasets: [
      {
        label: 'Kamu',
        data: [75, 85, 90],
        backgroundColor: 'rgb(99, 102, 241)',
        borderRadius: 4,
      },
      {
        label: 'Rata-rata',
        data: [65, 75, 80],
        backgroundColor: 'rgb(148, 163, 184)',
        borderRadius: 4,
      }
    ]
  };

  const barOptions = {
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        ticks: {
          callback: function(value: any) {
            return value + '%';
          },
          font: {
            size: containerWidth < 768 ? 12 : 14,
            family: 'Inter, sans-serif'
          }
        },
        grid: {
          color: '#E5E7EB'
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: containerWidth < 768 ? 12 : 14,
            family: 'Inter, sans-serif'
          }
        }
      }
    },
    plugins: {
      legend: {
        position: containerWidth < 768 ? 'bottom' : 'top' as const,
        align: 'end' as const,
        labels: {
          usePointStyle: true,
          pointStyle: 'circle',
          font: {
            family: 'Inter, sans-serif'
          }
        }
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.raw}%`;
          }
        }
      }
    },
    maintainAspectRatio: false
  };

  const ChartComponent: React.FC<{ type: 'radar' | 'bar', data: any, options: any }> = ({ type, data, options }) => {
    const ChartType = type === 'radar' ? Radar : Bar;
    return (
      <div className="w-full h-[350px]">
        <ChartType data={data} options={options} />
      </div>
    );
  };

  return (
    <div className="my-10 px-6 lg:px-6 max-w-[95rem] mx-auto w-full flex flex-col gap-4" ref={containerRef}>
      <ul className="flex">
        <Breadcrumbs>
          <BreadcrumbItem href="/dashboard">Dashboard</BreadcrumbItem>
          <BreadcrumbItem>Skill Insights</BreadcrumbItem>
        </Breadcrumbs>
      </ul>
      <h1 className="text-2xl font-bold mb-6">Laporan Personalisasi Intelijen</h1>
      {/* Overlay for Coming Soon */}
      {isBlurred && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
          <div className="text-center">
            <DotLottieAnimation
              src="https://cdn.terang.ai/dotlotties/coming-soon.lottie"
              autoplay
              loop
              width={"100%"}
              height={"100%"}
            />
            <h1 className={`${title()} text-center mt-4`}>Soon!</h1>
          </div>
        </div>
      )}
      {/* <Tabs 
        selectedKey={selectedTab} 
        onSelectionChange={handleTabChange}
        className="mb-6"
        aria-label="Tab Navigasi"
      > */}
        {/* <Tab key="kemampuan" title="Kemampuan">
          <Card className={`mt-4 ${isBlurred ? "blur-sm" : ""}`}>
            <CardHeader className="font-bold text-lg">Analisis Performa</CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                <div className="lg:col-span-8 bg-white rounded-lg shadow-sm p-4">
                  <ChartComponent
                    type="radar"
                    data={radarData}
                    options={radarOptions}
                  />
                </div>
                <div className="lg:col-span-4 p-4">
                  <h3 className="font-bold mb-4">Ringkasan Laporan</h3>
                  <p className="text-gray-600 mb-4">
                    Berdasarkan analisis performa Kamu, Kamu menunjukkan kemampuan yang kuat dalam 
                    presentasi (98%) dan pembelajaran (93%). Area yang perlu ditingkatkan termasuk 
                    interaksi (86%) dan perencanaan (87%).
                  </p>
                  <div className="mt-4">
                    <h4 className="font-bold mb-2">Latihan yang Direkomendasikan:</h4>
                    <div className="flex flex-wrap gap-2 mb-2">
                      <span className="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm">
                        Latihan 1
                      </span>
                      <span className="px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full text-sm">
                        Latihan 2
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </Tab>     */}
        {/* <Tab key="sesi" title="Insight Sesi">
          <Card className={`mt-4 ${isBlurred ? "blur-sm" : ""}`}>
            <CardHeader className="font-bold text-lg">Perkembangan Ujian</CardHeader>
            <CardBody>
              <div className="text-center text-gray-500 py-8">
                Catatan percobaan sebelumnya akan ditampilkan di sini
              </div>
            </CardBody>
          </Card>
        </Tab>     */}
        {/* <Tab key="sesi" title="Insight Sesi"> */}
          <Card className={`mt-4 ${isBlurred ? "blur-sm" : ""}`}>
            <CardHeader className="font-bold text-lg">Performa dan Kemampuan</CardHeader>
            <CardBody>
              <TimeManagementVisualization />
            </CardBody>
          </Card>
        {/* </Tab> */}
      {/* </Tabs> */}
    </div>
  );
};

export default LaporanPersonal;