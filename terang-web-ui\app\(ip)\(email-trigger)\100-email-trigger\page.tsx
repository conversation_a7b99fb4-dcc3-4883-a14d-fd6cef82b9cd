"use client";

import React from 'react';
import { 
  sendTeacherInvitation, 
  sendStudentInvitation,
  sendStudyReminder,
  sendTeacherMessageNotification,
  sendSubscriptionActivation,
  sendStudentMessageNotification,
  sendAnnouncement
} from './email';

export default function EmailTestButtons() {
  const handleTeacherInvite = async () => {
    const formData = new FormData();
    formData.append('teacherName', 'Mrs. <PERSON>');
    formData.append('institutionName', 'SMA Negeri 70 Jakarta');
    formData.append('email', '<EMAIL>');
    
    try {
      const result = await sendTeacherInvitation(formData);
      console.log('Teacher invite sent:', result);
    } catch (error) {
      console.error('Error sending teacher invite:', error);
    }
  };

  const handleStudentInvite = async () => {
    const formData = new FormData();
    formData.append('studentName', 'Alfian <PERSON>');
    formData.append('className', 'Biology Class 2024');
    formData.append('teacherN<PERSON>', 'Mrs. <PERSON>');
    formData.append('email', '<EMAIL>');
    
    try {
      const result = await sendStudentInvitation(formData);
      console.log('Student invite sent:', result);
    } catch (error) {
      console.error('Error sending student invite:', error);
    }
  };

  const handleStudyReminder = async () => {
    const formData = new FormData();
    formData.append('studentName', 'Alfian Firmansyah');
    formData.append('subjectName', 'Biology');
    formData.append('email', '<EMAIL>');
    formData.append('nextTopic', 'Cell Division and Mitosis');
    formData.append('timeRemaining', '3 days until exam');
    
    try {
      const result = await sendStudyReminder(formData);
      console.log('Study reminder sent:', result);
    } catch (error) {
      console.error('Error sending study reminder:', error);
    }
  };

  const handleTeacherMessage = async () => {
    const formData = new FormData();
    formData.append('studentName', 'Alfian Firmansyah');
    formData.append('teacherName', 'Mrs. Nana');
    formData.append('messagePreview', 'Alfian, tolong kerjakan flashcard dan latihan biologi, beserta selesaikan materi sampai hari jumat 27 Desember 2024 ya');
    formData.append('className', 'Biology Class 2024');
    formData.append('email', '<EMAIL>');
    
    try {
      const result = await sendTeacherMessageNotification(formData);
      console.log('Teacher message sent:', result);
    } catch (error) {
      console.error('Error sending teacher message:', error);
    }
  };

  const handleStudentMessage = async () => {
    const formData = new FormData();
    formData.append('studentName', 'Alfian Firmansyah');
    formData.append('teacherName', 'Mrs. Nana');
    formData.append('messagePreview', 'Baik miss');
    formData.append('className', 'Biology Class 2024');
    formData.append('email', '<EMAIL>');
    
    try {
      const result = await sendStudentMessageNotification(formData);
      console.log('Student message sent:', result);
    } catch (error) {
      console.error('Error sending student message:', error);
    }
  };

  const handleSubscriptionActivation = async () => {
    const formData = new FormData();
    formData.append('managerName', 'Alfian Firmansyah');
    formData.append('institutionName', 'SMA Negeri 70 Jakarta');
    formData.append('email', '<EMAIL>');
    formData.append('planName', 'Enterprise');
    formData.append('expiryDate', '2024-12-27');
    
    try {
      const result = await sendSubscriptionActivation(formData);
      console.log('Subscription activation sent:', result);
    } catch (error) {
      console.error('Error sending subscription activation:', error);
    }
  };

  const handleSendAnnouncement = async () => {
    const formData = new FormData();
    formData.append('teacherName', 'Mrs. Nana');
    formData.append('className', 'Biologi Kelas 11 Mrs. Nana');
    formData.append('email', '<EMAIL>');
    formData.append('expiryDate', '2024-12-27');
    
    try {
      const result = await sendAnnouncement(formData);
      console.log('Subscription activation sent:', result);
    } catch (error) {
      console.error('Error sending subscription activation:', error);
    }
  };

  return (
    <div className="p-6 space-y-4">
      <h2 className="text-2xl font-bold mb-4">Email Test Buttons</h2>
      
      <div className="grid gap-4">
        <button
          onClick={handleTeacherInvite}
          className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded"
        >
          Send Teacher Invitation
        </button>

        <button
          onClick={handleStudentInvite}
          className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded"
        >
          Send Student Invitation
        </button>

        <button
          onClick={handleStudyReminder}
          className="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-2 px-4 rounded"
        >
          Send Study Reminder
        </button>

        <button
          onClick={handleTeacherMessage}
          className="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded"
        >
          Send Teacher Message
        </button>

        <button
          onClick={handleStudentMessage}
          className="bg-indigo-500 hover:bg-indigo-600 text-white font-semibold py-2 px-4 rounded"
        >
          Send Student Message
        </button>

        <button
          onClick={handleSubscriptionActivation}
          className="bg-pink-500 hover:bg-pink-600 text-white font-semibold py-2 px-4 rounded"
        >
          Send Subscription Activation
        </button>

        <button
          onClick={handleSendAnnouncement}
          className="bg-gray-500 hover:bg-pink-600 text-white font-semibold py-2 px-4 rounded"
        >
          Send Announcement
        </button>
      </div>
    </div>
  );
}