import { NextApiRequest, NextApiResponse } from 'next';
import { createClient } from 'redis';

// Initialize Redis client
const client = createClient({
  url: process.env.REDIS_URL
});

client.on('error', (err) => console.log('Redis Client Error', err));

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  try {
    await client.connect();

    const { orderId } = req.query;

    if (!orderId || typeof orderId !== 'string') {
      return res.status(400).json({ message: 'Missing or invalid orderId' });
    }

    // Retrieve the token from Redis
    const token = await client.get(`payment:${orderId}`);

    await client.quit();

    if (!token) {
      return res.status(404).json({ message: 'Payment token not found' });
    }

    res.status(200).json({ token });
  } catch (error) {
    console.error('Error retrieving payment token:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}