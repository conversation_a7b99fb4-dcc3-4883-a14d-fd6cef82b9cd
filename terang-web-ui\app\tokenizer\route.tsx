// app/tokenizer/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { createByModelName } from '@microsoft/tiktokenizer';

// Main function for counting tokens
export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();
    const { text } = body;

    // Validate input
    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: "Missing or invalid 'text' in request body" },
        { status: 400 }
      );
    }

    // Initialize the tokenizer
    const tokenizer = await createByModelName(process.env.AZURE_OPEN_AI_DEPLOYMENT as string);

    // Encode the text into tokens (without special tokens)
    const encodedTokens = tokenizer.encode(text);
    const tokenCount = encodedTokens.length;

    console.log("Encoded tokens:", encodedTokens);
    console.log("Token count:", tokenCount);

    return NextResponse.json({
      tokenCount,
      text,
      encodedTokens
    });
  } catch (error) {
    console.error("Error in tokenization:", error);
    
    // Check if the error is from JSON parsing
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Tokenization failed", details: (error as Error).message },
      { status: 500 }
    );
  }
}