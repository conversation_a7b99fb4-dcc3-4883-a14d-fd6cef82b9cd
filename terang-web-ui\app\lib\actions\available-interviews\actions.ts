"use server";

import { auth } from "@/auth";

interface FetchOptions {
  method?: string;
  body?: any;
}

interface UserSession {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
}

async function fetchFromBackend<T>(
  url: string,
  options: FetchOptions = {},
): Promise<T> {
  const requestOptions: RequestInit = {
    method: options.method || "GET",
    headers: {
      "Content-Type": "application/json",
      "X-Api-Key": process.env.BACKEND_API_KEY as string,
    },
    body: options.body ? JSON.stringify(options.body) : undefined,
  };

  const response = await fetch(url, requestOptions);

  if (!response.ok) {
    const errorText = await response.text();

    throw new Error(
      `Network response was not ok: ${response.status} - ${response.statusText}. Details: ${errorText}`,
    );
  }

  return response.json();
}

async function getSessionUserDetails(): Promise<UserSession | null> {
  const session = await auth();

  if (session && session.user) {
    return {
      id: session.user.id,
      email: session.user.email,
      firstName: session.user.firstname || "First Name", // Default if not available
      lastName: session.user.lastname || "Last Name", // Default if not available
    };
  }

  return null;
}

export async function getAvailableInterviews(type?: string): Promise<any | boolean> {
  const userSession = await getSessionUserDetails();
  if (!userSession) return false;

  const interviewType = type ?? "INTERVIEW";
  console.log(`Fetching ${interviewType} data for user ${userSession.email}`);

  try {
    const url = `${process.env.BACKEND_BASE_URL}/v2/available-interviews?pageSize=2000&page=1&interviewType=${interviewType}`;
    const data = await fetchFromBackend<any>(url);
    return data.data;
  } catch (error: any) {
    console.error(`Error fetching interviews: ${error}`);
    if (error.status === 400 || (error.message && error.message.includes("400"))) {
      console.log(`RESPONSE CODE: 400, User is not registered`);
      return false;
    }
    throw error;
  }
}


// Function to get details for a specific interview by ID
export async function getInterviewById(interviewId: string): Promise<any | null> {
  if (!interviewId) return null;

  try {
    const url = `${process.env.BACKEND_BASE_URL}/v2/available-interviews/${interviewId}`;
    const data = await fetchFromBackend<any>(url);
    return data.data;
  } catch (error) {
    console.error(`Error fetching interview by ID: ${error}`);
    return null;
  }
}