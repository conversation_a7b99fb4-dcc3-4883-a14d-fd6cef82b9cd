"use server";

import { createClient, RedisClientType, RedisDefaultModules, RedisFunctions, RedisScripts } from "redis";
import { fetchExamQuestionsBySessionId, fetchExamQuestionsBySessionIdBySubject } from "../my-exams/actions";
import { ExamSession } from "../types";
import { getUserId } from "@/app/lib/actions/account/actions";
import { auth } from "@/auth";

// Define the Redis client type
type RedisClient = RedisClientType<RedisDefaultModules & RedisFunctions & RedisScripts>;

let redisWriteClient: RedisClient | null = null;
let redisReadClient: RedisClient | null = null;

export async function getRedisWriteClient(): Promise<RedisClient> {
  if (!redisWriteClient) {
    try {
      redisWriteClient = createClient({
        url: process.env.REDIS_URL_WRITE,
      }) as RedisClient;

      await redisWriteClient.connect();
      
      redisWriteClient.on('error', (error: Error) => {
        console.error('Redis Write Client Error:', error);
      });
    } catch (error) {
      console.error('Failed to create Redis write client:', error);
      throw new Error('Redis write client initialization failed');
    }
  }

  if (!redisWriteClient.isOpen) {
    try {
      await redisWriteClient.connect();
    } catch (error) {
      console.error('Failed to reconnect Redis write client:', error);
      throw new Error('Redis write client reconnection failed');
    }
  }

  return redisWriteClient;
}

export async function getRedisReadClient(): Promise<RedisClient> {
  if (!redisReadClient) {
    try {
      redisReadClient = createClient({
        url: process.env.REDIS_URL_READ,
      }) as RedisClient;

      await redisReadClient.connect();
      
      redisReadClient.on('error', (error: Error) => {
        console.error('Redis Read Client Error:', error);
      });
    } catch (error) {
      console.error('Failed to create Redis read client:', error);
      throw new Error('Redis read client initialization failed');
    }
  }

  if (!redisReadClient.isOpen) {
    try {
      await redisReadClient.connect();
    } catch (error) {
      console.error('Failed to reconnect Redis read client:', error);
      throw new Error('Redis read client reconnection failed');
    }
  }

  return redisReadClient;
}

export async function storeToRedis(key: string, value: unknown): Promise<any | null> {
  try {
    const redis = await getRedisWriteClient();
    
    await redis.set(key, JSON.stringify(value), {
      EX: 31536000, // Set expiration to 1 year
    });
  } catch (error) {
    console.error("Failed to store data in Redis:", error);
    throw new Error('Failed to store data in Redis');
  }
}

export async function retrieveFromRedis<T>(key: string): Promise<any | null> {
  try {
    const redis = await getRedisReadClient();
    const data = await redis.get(key);
    
    return data ? JSON.parse(data) as T : null;
  } catch (error) {
    console.error("Failed to retrieve data from Redis:", error);
    throw new Error('Failed to retrieve data from Redis');
  }
}

// Remove all other Gamification interface definitions and keep only this one
export interface Gamification {
  id: string;
  examSessionId: string;
  currentLives: number;
  hintsRemaining: { count: string };
  status: string;
  streakCount: number;
  highestStreak: number;
  elapsedTime: string;
  totalPauseTime: string;
  question_times?: { [key: string]: number };
  endTime?: string;  // Make endTime optional with ?
  startTime?: string; // Add optional startTime to match your data structure
  createdAt?: string;
  modifiedAt?: string;
}

interface QueuedUpdate {
  data: Partial<Gamification>;
  timestamp: string;
}

export async function updateExamSessionDone(
  sessionId: string, examType: string
): Promise<string> {
  const userId = await getUserId();

  if (!userId) throw new Error("User ID is undefined or null.");

  const storedSelectedOptions = await retrieveFromRedis(
    `${sessionId}_selectedOptions`,
  );
  const storedFlaggedQuestions = await retrieveFromRedis(
    `${sessionId}_flaggedQuestions`,
  );

  const currentTime = new Date().toISOString();

  // First update gamification status to COMPLETED
  try {
    const gamificationUpdate: Partial<Gamification> = {
      status: "COMPLETED",
      endTime: currentTime,
      elapsedTime: currentTime
    };

    await updateGamificationWithRedis(sessionId, gamificationUpdate);
    await updateGamification(sessionId, gamificationUpdate);
    await syncGamificationData(sessionId);
  } catch (error) {
    console.error("Error updating gamification status:", error);
    // Continue with exam completion even if gamification update fails
  }

  const newSession: any = {
    status: "COMPLETED",
    end_time: currentTime,
    answers: storedSelectedOptions,
    flagged_questions: storedFlaggedQuestions,
  };

  const response = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/exam-sessions/${sessionId}`,
    {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
      body: JSON.stringify(newSession),
    },
  );

  if (!response.ok) {
    const errorText = await response.text();
    console.error("Failed to update exam session. Response text:", errorText);
    throw new Error("Failed to update exam session.");
  }

  const data = await response.json();

  try {
    // Pass examType instead of subject
    await insertExamScores(sessionId, examType);
  } catch (error) {
    console.error("Error inserting exam score:", error);
  }

  // Clean up Redis keys after successful completion
  try {
    const redis = await getRedisWriteClient();
    await redis.del([
      `${sessionId}_selectedOptions`,
      `${sessionId}_flaggedQuestions`,
      `${sessionId}_gamification`,
      `${sessionId}_gamification_queue`,
      `${sessionId}_failed_gamification_updates`
    ]);
  } catch (error) {
    console.error("Error cleaning up Redis keys:", error);
  }

  return data.data.session_id;
}

// Function to identify exam type via API call to your Go backend
export async function identifyExamTypeFromAPI(subjectName: string): Promise<string | null> {
  try {
    // Call your backend API to get the exam configuration
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/exam-config/subject-to-exam?subject=${encodeURIComponent(subjectName)}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!response.ok) {
      console.error(`Error from API: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    return data.examType || null;
  } catch (error) {
    console.error("Error identifying exam type from API:", error);
    return null;
  }
}


// Types for exam configuration
interface SubjectInfo {
  id: string;
  name: string;
  key: string;
}

interface ExamType {
  id: string;
  name: string;
  subjects: SubjectInfo[];
}

export async function insertExamScores(sessionId: string, examType: string): Promise<string> {
  const userId = await getUserId();

  if (!userId) throw new Error("User ID is undefined or null.");
  
  let examConfig: ExamType;
  
  try {
    // First, try to get the exam configuration from the API
    const configResponse = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/exam-config/${encodeURIComponent(examType.toUpperCase())}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );
    
    if (!configResponse.ok) {
      throw new Error(`Failed to get exam configuration: ${configResponse.statusText}`);
    }
    
    const configData = await configResponse.json();
    examConfig = configData.config;
    
    if (!examConfig) {
      throw new Error("Exam configuration is null or undefined");
    }
    
    console.log("Exam configuration:", examConfig);
  } catch (error) {
    console.error("Error fetching exam configuration:", error);
    throw new Error("Failed to fetch exam configuration. Cannot calculate scores.");
  }

  // Continue with the rest of your function using the resolved examConfig
  const storedSelectedOptions = await retrieveFromRedis(`${sessionId}_selectedOptions`);
  
  if (!storedSelectedOptions) {
    throw new Error("No selected options found for this session.");
  }
  
  const parsedSelectedOptions = JSON.parse(storedSelectedOptions);
  
  const questionData: any = await fetchExamQuestionsBySessionId(sessionId);
  const parsedData: any[] = JSON.parse(questionData.data[0].data);

  const totalQuestions = parsedData.length;
  let correctAnswers = 0;
  
  // Initialize counters for each subject
  const subjectTotals: Record<string, number> = {};
  const subjectCorrect: Record<string, number> = {};
  
  // Initialize all subject counters to zero
  examConfig.subjects.forEach((subject: any) => {
    subjectTotals[subject.key] = 0;
    subjectCorrect[subject.key] = 0;
  });

  // Map to translate subject names to keys
  const subjectNameToKey: Record<string, string> = {};
  examConfig.subjects.forEach((subject: any) => {
    subjectNameToKey[subject.name.toLowerCase()] = subject.key;
    // Also map the ID to the key for robustness
    subjectNameToKey[subject.id.toLowerCase()] = subject.key;
  });

  // Process each question
  parsedData.forEach((question) => {
    const questionId = question.id;
    const selectedOptionId = parsedSelectedOptions[questionId];
    
    const correctOption = question.options.values.find(
      (option: any) => option.is_correct
    );
    
    // Find the subject metadata
    const subjectMetadata = question.metadata.find(
      (meta: { name: string; value: string }) => meta.name === "subject"
    );
    
    if (!subjectMetadata) {
      console.warn(`No subject metadata found for question ${questionId}`);
      return; // Skip if no subject metadata
    }
    
    const subjectName: string = subjectMetadata.value;
    const subjectKey = subjectNameToKey[subjectName.toLowerCase()];
    
    if (!subjectKey) {
      console.warn(`Unknown subject: ${subjectName} for exam type: ${examConfig.id}`);
      return; // Skip if subject not found in configuration
    }
    
    // Increment total for this subject
    subjectTotals[subjectKey]++;
    
    // Check if answer is correct
    if (
      selectedOptionId &&
      correctOption &&
      selectedOptionId === correctOption.id
    ) {
      correctAnswers++;
      subjectCorrect[subjectKey]++;
    }
  });

  // Calculate overall score
  const score = (correctAnswers / totalQuestions) * 100;
  
  // Calculate subject scores
  const metadataScores: Record<string, any> = {
    exam_type: examConfig.id,
  };
  
  // Add subject-specific scores
  examConfig.subjects.forEach((subject: any) => {
    const total = subjectTotals[subject.key];
    const correct = subjectCorrect[subject.key];
    const subjectScore = total > 0 ? (correct / total) * 100 : 0;
    
    metadataScores[`${subject.key}`] = correct;
    metadataScores[`${subject.key}_total`] = total;
    metadataScores[`${subject.key}_score`] = subjectScore;
  });

  console.log("Metadata scores:", metadataScores);
  console.log("Overall score:", score);

  const scoreInsert = {
    session_id: sessionId,
    total_questions: totalQuestions,
    correct_answers: correctAnswers,
    score: score,
    metadata_scores: JSON.stringify(metadataScores)
  };

  console.log("Inserting score:", scoreInsert);

  const scoreResponse = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/exam-scores`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
      body: JSON.stringify(scoreInsert),
    }
  );

  if (!scoreResponse.ok) {
    const errorText = await scoreResponse.text();
    console.error("Failed to insert exam score. Response text:", errorText);
    throw new Error("Failed to insert exam score.");
  }

  const scoreData = await scoreResponse.json();
  return scoreData.data.session_id;
}

export async function fetchExamSessionBySessionId(
  sessionId: string,
): Promise<ExamSession> {
  const response = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/exam-sessions/${sessionId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
    },
  );

  if (!response.ok) {
    const errorText = await response.text();

    console.error("Failed to fetch exam session. Response text:", errorText);
    throw new Error("Failed to fetch exam session.");
  }

  const result = await response.json();

  return result.data;
}


export async function checkFeedbackRecords(): Promise<number> {
  try {
    const session = await auth();

    if (!session || !session.user?.email) {
      throw new Error("User is not authenticated or email is missing");
    }

    const email = session.user.email;

    if (!process.env.BACKEND_BASE_URL) {
      throw new Error("BACKEND_BASE_URL is not defined");
    }

    if (!process.env.BACKEND_API_KEY) {
      throw new Error("BACKEND_API_KEY is not defined");
    }

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/surveys/check-email/feedback?email=${encodeURIComponent(email)}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      },
    );

    if (!response.ok) {
      throw new Error(
        `Failed to check email records: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();

    return data.count;
  } catch (error) {
    console.error("Error checking email records:", error);
    throw error;
  }
}

// Make it properly async with Promise<boolean> return type
export async function isGameOver(gamification: Gamification): Promise<boolean> {
  try {
    // You can add any async operations here if needed
    return gamification.currentLives <= 0;
  } catch (error) {
    console.error("Error checking game over status:", error);
    throw error;
  }
}

// Function to fetch gamification
export async function fetchGamification(sessionId: string): Promise<Gamification> {
  try {
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/gamification/session/${sessionId}`,
      {
        method: "GET",
        headers: {
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Failed to fetch gamification. Response text:", errorText);

      if (response.status === 404) {
        throw new Error("Gamification not found.");
      } else {
        throw new Error(`Unexpected error: ${response.statusText}`);
      }
    }

    const data = await response.json();
    return data; // Assuming the data is in the correct format
  } catch (error) {
    console.error("Error fetching gamification:", error);
    throw error;
  }
}

async function queueGamificationUpdate(
  sessionId: string,
  gamificationData: Partial<Gamification>
): Promise<void> {
  const key = `${sessionId}_gamification_queue`;
  const queue = await retrieveFromRedis(key) || [];
  queue.push({
    data: gamificationData,
    timestamp: new Date().toISOString()
  });
  await storeToRedis(key, queue);
}

async function storeFailedUpdate(
  sessionId: string,
  updateData: Partial<Gamification>
): Promise<void> {
  const key = `${sessionId}_failed_gamification_updates`;
  const failedUpdates = await retrieveFromRedis(key) || [];
  failedUpdates.push({
    data: updateData,
    timestamp: new Date().toISOString()
  });
  await storeToRedis(key, failedUpdates);
}

// API operations
export async function updateGamification(
  sessionId: string,
  updateData: Partial<Gamification>
): Promise<void> {
  try {
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/gamification/session/${sessionId}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
        body: JSON.stringify(updateData),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to update gamification: ${errorText}`);
    }
  } catch (error) {
    console.error("Error updating gamification:", error);
    throw error;
  }
}

export async function syncGamificationData(sessionId: string): Promise<void> {
  try {
    // Process queued updates
    const queueKey = `${sessionId}_gamification_queue`;
    const queue = await retrieveFromRedis(queueKey);
    if (queue && queue.length > 0) {
      for (const item of queue) {
        await updateGamification(sessionId, item.data);
      }
      await storeToRedis(queueKey, []);
    }

    // Process failed updates
    const failedKey = `${sessionId}_failed_gamification_updates`;
    const failedUpdates = await retrieveFromRedis(failedKey);
    if (failedUpdates && failedUpdates.length > 0) {
      for (const item of failedUpdates) {
        await updateGamification(sessionId, item.data);
      }
      await storeToRedis(failedKey, []);
    }
  } catch (error) {
    console.error("Error syncing gamification data:", error);
    throw error;
  }
}

// Function to pause gamification
export async function pauseGamification(sessionId: string): Promise<void> {
  try {
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/gamification/session/${sessionId}/pause`,
      {
        method: "POST",
        headers: {
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Failed to pause gamification. Response text:", errorText);

      throw new Error(`Failed to pause gamification: ${response.statusText}`);
    }

    const data = await response.json();
    console.log("Gamification paused successfully:", data);
  } catch (error) {
    console.error("Error pausing gamification:", error);
    throw error;
  }
}

// Function to resume gamification
export async function resumeGamification(sessionId: string): Promise<void> {
  try {
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/gamification/session/${sessionId}/resume`,
      {
        method: "POST",
        headers: {
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Failed to resume gamification. Response text:", errorText);

      throw new Error(`Failed to resume gamification: ${response.statusText}`);
    }

    const data = await response.json();
    console.log("Gamification resumed successfully:", data);
  } catch (error) {
    console.error("Error resuming gamification:", error);
    throw error;
  }
}

// Function to use a hint
export async function useHint(sessionId: string): Promise<void> {
  try {
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/gamification/session/${sessionId}/use-hint`,
      {
        method: "POST",
        headers: {
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Failed to use hint. Response text:", errorText);

      if (response.status === 400) {
        throw new Error("No hints remaining or invalid request.");
      } else {
        throw new Error(`Failed to use hint: ${response.statusText}`);
      }
    }

    const data = await response.json();
    console.log("Hint used successfully:", data);
  } catch (error) {
    console.error("Error using hint:", error);
    throw error;
  }
}

// Update handleAnswerSubmission to match the interface
export async function handleAnswerSubmission(
  sessionId: string,
  isCorrect: boolean,
  currentGamification: Gamification
): Promise<Gamification> {
  try {
    if (!isCorrect) {
      return await handleIncorrectAnswer(sessionId, currentGamification);
    }

    const newStreakCount = currentGamification.streakCount + 1;
    const newHighestStreak = Math.max(currentGamification.highestStreak, newStreakCount);
    
    const updateData: Partial<Gamification> = {
      streakCount: newStreakCount,
      highestStreak: newHighestStreak,
    };

    await updateGamificationWithRedis(sessionId, updateData);
    await updateGamification(sessionId, updateData);

    return {
      ...currentGamification,
      ...updateData,
    };
  } catch (error) {
    console.error("Error handling answer submission:", error);
    throw error;
  }
}

// In actions.ts, modify updateGamificationWithRedis
export async function updateGamificationWithRedis(
  sessionId: string,
  updateData: Partial<Gamification>
): Promise<void> {
  try {
    // Get existing data
    const existingData = await retrieveFromRedis(`${sessionId}_gamification`);
    const currentGamification = existingData ? JSON.parse(existingData) : {
      currentLives: 3, // Default initial state
      streakCount: 0,
      highestStreak: 0
    };

    // Deep merge with special handling for nested objects
    const updatedGamification = {
      ...currentGamification,
      ...updateData,
      question_times: {
        ...(currentGamification.question_times || {}),
        ...(updateData.question_times || {})
      }
    };

    // Store to Redis with a longer expiration time
    await storeToRedis(
      `${sessionId}_gamification`,
      JSON.stringify(updatedGamification)
    );

    // Queue background update
    await queueGamificationUpdate(sessionId, updatedGamification);
  } catch (error) {
    console.error("Error updating gamification in Redis:", error);
    await storeFailedUpdate(sessionId, updateData);
  }
}

// Add a new function to fetch gamification state from Redis
export async function getGamificationFromRedis(sessionId: string): Promise<Gamification | null> {
  try {
    const data = await retrieveFromRedis(`${sessionId}_gamification`);
    if (!data) return null;
    
    return JSON.parse(data);
  } catch (error) {
    console.error("Error fetching gamification from Redis:", error);
    return null;
  }
}

// Modify handleIncorrectAnswer to ensure Redis is updated
export async function handleIncorrectAnswer(
  sessionId: string,
  currentGamification: Gamification
): Promise<Gamification> {
  try {
    const newLives = Math.max(0, currentGamification.currentLives - 1);
    const updateData: Partial<Gamification> = {
      currentLives: newLives,
      streakCount: 0,
    };

    // Update Redis first
    await updateGamificationWithRedis(sessionId, updateData);
    
    // Then update backend
    await updateGamification(sessionId, updateData);

    return {
      ...currentGamification,
      ...updateData,
    };
  } catch (error) {
    console.error("Error handling incorrect answer:", error);
    throw error;
  }
}

// Modify fetchGamificationState to check Redis first
export async function fetchGamificationState(sessionId: string): Promise<Gamification> {
  try {
    // Try to get from Redis first
    const redisData = await getGamificationFromRedis(sessionId);
    if (redisData) {
      return redisData;
    }

    // If not in Redis, fetch from backend
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/gamification/session/${sessionId}`,
      {
        method: "GET",
        headers: {
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch gamification: ${response.statusText}`);
    }

    const data = await response.json();
    
    // Store in Redis for next time
    await updateGamificationWithRedis(sessionId, data);
    
    return data;
  } catch (error) {
    console.error("Error fetching gamification:", error);
    throw error;
  }
}