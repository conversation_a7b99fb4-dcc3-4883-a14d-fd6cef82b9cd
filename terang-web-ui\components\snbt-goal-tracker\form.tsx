"use client";

import React, { useState, useEffect } from "react";
import {
  Input,
  Button,
  RadioGroup,
  Radio,
  Textarea,
  Checkbox,
  Card,
  CardBody
} from "@heroui/react";

import { SnbtGoalTrackerFormData, FormErrors } from "./actions";

interface SnbtGoalTrackerFormProps {
  initialData?: Partial<SnbtGoalTrackerFormData>;
  onFormDataChange: (data: Partial<SnbtGoalTrackerFormData>) => void;
  onComplete: () => void;
  clearError: (field: string) => void;
  formErrors: FormErrors;
  isSubmitting: boolean;
  userName?: string;
  userEmail?: string;
}

export function SnbtGoalTrackerForm({
  initialData = {},
  onFormDataChange,
  onComplete,
  clearError,
  formErrors,
  isSubmitting,
  userName = "",
  userEmail = ""
}: SnbtGoalTrackerFormProps) {
  const [formData, setFormData] = useState<Partial<SnbtGoalTrackerFormData>>({
    name: userName || "",
    email: userEmail || "",
    penalaranUmum: 0,
    pengetahuanKuantitatif: 0,
    pengetahuanPemahamanUmum: 0,
    pemahamanBacaanMenulis: 0,
    literasiBahasaIndonesia: 0,
    literasiBahasaInggris: 0,
    penalaranMatematika: 0,
    passedSnbt: false,
    feltHelped: false,
    helpfulnessRating: 5,
    mostHelpfulAspect: "",
    improvementSuggestions: "",
    contactConsent: false,
    phoneNumber: "",
    ...initialData,
  });

  // Update form data when initialData changes
  useEffect(() => {
    if (userName && !formData.name) {
      setFormData(prev => ({ ...prev, name: userName }));
    }
    if (userEmail && !formData.email) {
      setFormData(prev => ({ ...prev, email: userEmail }));
    }
  }, [userName, userEmail, formData.name, formData.email]);

  // Notify parent component of form data changes
  useEffect(() => {
    onFormDataChange(formData);
  }, [formData, onFormDataChange]);

  const handleInputChange = (field: keyof SnbtGoalTrackerFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    clearError(field);
  };





  return (
    <div className="w-full max-w-4xl mx-auto p-4 sm:p-6 space-y-6">

      {/* Personal Information */}
      <Card className="bg-white shadow-sm">
        <CardBody className="p-4 sm:p-6">
          <div className="flex items-center mb-4">
            <div className="w-1 h-6 bg-primary rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold">Informasi Pribadi</h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="Nama Lengkap"
              placeholder="Masukkan nama lengkap Anda"
              value={formData.name || ""}
              onChange={(e) => handleInputChange('name', e.target.value)}
              isInvalid={!!formErrors.name}
              errorMessage={formErrors.name}
              isRequired
              classNames={{
                inputWrapper: "bg-white"
              }}
            />
            <Input
              label="Email"
              placeholder="Masukkan email Anda"
              type="email"
              value={formData.email || ""}
              onChange={(e) => handleInputChange('email', e.target.value)}
              isInvalid={!!formErrors.email}
              errorMessage={formErrors.email}
              isRequired
              classNames={{
                inputWrapper: "bg-white"
              }}
            />
          </div>
        </CardBody>
      </Card>

      {/* SNBT Scores */}
      <Card className="bg-white shadow-sm">
        <CardBody className="p-4 sm:p-6">
          <div className="flex items-center mb-4">
            <div className="w-1 h-6 bg-primary rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold">Skor SNBT 2025 Anda</h3>
          </div>
          <p className="text-sm text-gray-600 mb-6">
            Masukkan skor yang Anda peroleh untuk setiap komponen SNBT 2025
          </p>

          {/* Tes Skolastik Section */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <div className="w-1 h-5 bg-blue-500 rounded-full mr-2"></div>
              <h4 className="text-md font-semibold text-blue-700">Tes Skolastik</h4>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Penalaran Umum"
                  placeholder="0"
                  type="number"
                  min="0"
                  value={formData.penalaranUmum?.toString() || "0"}
                  onChange={(e) => handleInputChange('penalaranUmum', parseInt(e.target.value) || 0)}
                  isInvalid={!!formErrors.penalaranUmum}
                  errorMessage={formErrors.penalaranUmum}
                  isRequired
                  classNames={{
                    inputWrapper: "bg-white"
                  }}
                />
                <Input
                  label="Pengetahuan Kuantitatif"
                  placeholder="0"
                  type="number"
                  min="0"
                  value={formData.pengetahuanKuantitatif?.toString() || "0"}
                  onChange={(e) => handleInputChange('pengetahuanKuantitatif', parseInt(e.target.value) || 0)}
                  isInvalid={!!formErrors.pengetahuanKuantitatif}
                  errorMessage={formErrors.pengetahuanKuantitatif}
                  isRequired
                  classNames={{
                    inputWrapper: "bg-white"
                  }}
                />
                <Input
                  label="Pengetahuan dan Pemahaman Umum"
                  placeholder="0"
                  type="number"
                  min="0"
                  value={formData.pengetahuanPemahamanUmum?.toString() || "0"}
                  onChange={(e) => handleInputChange('pengetahuanPemahamanUmum', parseInt(e.target.value) || 0)}
                  isInvalid={!!formErrors.pengetahuanPemahamanUmum}
                  errorMessage={formErrors.pengetahuanPemahamanUmum}
                  isRequired
                  classNames={{
                    inputWrapper: "bg-white"
                  }}
                />
                <Input
                  label="Pemahaman Bacaan dan Menulis"
                  placeholder="0"
                  type="number"
                  min="0"
                  value={formData.pemahamanBacaanMenulis?.toString() || "0"}
                  onChange={(e) => handleInputChange('pemahamanBacaanMenulis', parseInt(e.target.value) || 0)}
                  isInvalid={!!formErrors.pemahamanBacaanMenulis}
                  errorMessage={formErrors.pemahamanBacaanMenulis}
                  isRequired
                  classNames={{
                    inputWrapper: "bg-white"
                  }}
                />
              </div>
            </div>
          </div>

          {/* Penalaran Matematika Section */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <div className="w-1 h-5 bg-green-500 rounded-full mr-2"></div>
              <h4 className="text-md font-semibold text-green-700">Penalaran Matematika</h4>
            </div>
            <div className="bg-green-50 p-4 rounded-lg border border-green-200">
              <div className="grid grid-cols-1 gap-4">
                <Input
                  label="Penalaran Matematika"
                  placeholder="0"
                  type="number"
                  min="0"
                  value={formData.penalaranMatematika?.toString() || "0"}
                  onChange={(e) => handleInputChange('penalaranMatematika', parseInt(e.target.value) || 0)}
                  isInvalid={!!formErrors.penalaranMatematika}
                  errorMessage={formErrors.penalaranMatematika}
                  isRequired
                  classNames={{
                    inputWrapper: "bg-white"
                  }}
                />
              </div>
            </div>
          </div>

          {/* Literasi Section */}
          <div className="mb-6">
            <div className="flex items-center mb-4">
              <div className="w-1 h-5 bg-purple-500 rounded-full mr-2"></div>
              <h4 className="text-md font-semibold text-purple-700">Literasi</h4>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Literasi Bahasa Indonesia"
                  placeholder="0"
                  type="number"
                  min="0"
                  value={formData.literasiBahasaIndonesia?.toString() || "0"}
                  onChange={(e) => handleInputChange('literasiBahasaIndonesia', parseInt(e.target.value) || 0)}
                  isInvalid={!!formErrors.literasiBahasaIndonesia}
                  errorMessage={formErrors.literasiBahasaIndonesia}
                  isRequired
                  classNames={{
                    inputWrapper: "bg-white"
                  }}
                />
                <Input
                  label="Literasi Bahasa Inggris"
                  placeholder="0"
                  type="number"
                  min="0"
                  value={formData.literasiBahasaInggris?.toString() || "0"}
                  onChange={(e) => handleInputChange('literasiBahasaInggris', parseInt(e.target.value) || 0)}
                  isInvalid={!!formErrors.literasiBahasaInggris}
                  errorMessage={formErrors.literasiBahasaInggris}
                  isRequired
                  classNames={{
                    inputWrapper: "bg-white"
                  }}
                />
              </div>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* SNBT Result */}
      <Card className="bg-white shadow-sm">
        <CardBody className="p-4 sm:p-6">
          <div className="flex items-center mb-4">
            <div className="w-1 h-6 bg-primary rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold">Hasil SNBT</h3>
          </div>
          <RadioGroup
            label="Apakah Anda lulus SNBT 2025?"
            value={formData.passedSnbt ? "true" : "false"}
            onValueChange={(value) => handleInputChange('passedSnbt', value === "true")}
            className="mb-4"
          >
            <Radio value="true">Ya, saya lulus SNBT 2025</Radio>
            <Radio value="false">Tidak, saya tidak lulus SNBT 2025</Radio>
          </RadioGroup>
        </CardBody>
      </Card>

      {/* Terang AI Feedback */}
      <Card className="bg-white shadow-sm">
        <CardBody className="p-4 sm:p-6">
          <div className="flex items-center mb-4">
            <div className="w-1 h-6 bg-primary rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold">Feedback tentang Terang AI</h3>
          </div>
          
          <RadioGroup
            label="Apakah Terang AI membantu Anda dalam persiapan SNBT?"
            value={formData.feltHelped ? "true" : "false"}
            onValueChange={(value) => handleInputChange('feltHelped', value === "true")}
            className="mb-6"
          >
            <Radio value="true">Ya, sangat membantu</Radio>
            <Radio value="false">Tidak terlalu membantu</Radio>
          </RadioGroup>

          <div className="mb-6">
            <label htmlFor="helpfulnessRating" className="block text-sm font-medium mb-2">
              Seberapa Terbantu Terang AI untuk membantu persiapan proses ujian? (1-10)
            </label>
            <div className="flex flex-col sm:flex-row items-center gap-2">
              <div className="flex items-center gap-2 w-full">
                <span className="text-sm font-medium">1</span>
                <Input
                  id="helpfulnessRating"
                  type="range"
                  min="1"
                  max="10"
                  value={formData.helpfulnessRating !== undefined ? formData.helpfulnessRating.toString() : '5'}
                  onChange={(e) => {
                    const value = e.target.value === '' ? 5 : parseInt(e.target.value);
                    handleInputChange("helpfulnessRating", value);
                  }}
                  classNames={{
                    inputWrapper: "bg-white"
                  }}
                />
                <span className="text-sm font-medium">10</span>
              </div>
              <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary text-white font-bold">
                {formData.helpfulnessRating || 5}
              </div>
            </div>
            {formErrors.helpfulnessRating && (
              <p className="text-danger text-sm mt-1">{formErrors.helpfulnessRating}</p>
            )}
          </div>

          <Textarea
            label="Aspek mana yang paling membantu?"
            placeholder="Ceritakan aspek Terang AI yang paling membantu dalam persiapan SNBT Anda..."
            value={formData.mostHelpfulAspect || ""}
            onChange={(e) => handleInputChange('mostHelpfulAspect', e.target.value)}
            classNames={{
              inputWrapper: "bg-white"
            }}
            minRows={3}
          />

          <Textarea
            label="Saran untuk perbaikan"
            placeholder="Apa yang bisa kami tingkatkan untuk membantu persiapan SNBT yang lebih baik?"
            value={formData.improvementSuggestions || ""}
            onChange={(e) => handleInputChange('improvementSuggestions', e.target.value)}
            classNames={{
              inputWrapper: "bg-white"
            }}
            minRows={3}
          />
        </CardBody>
      </Card>

      {/* Contact Consent */}
      <Card className="bg-white shadow-sm">
        <CardBody className="p-4 sm:p-6">
          <div className="flex items-center mb-4">
            <div className="w-1 h-6 bg-primary rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold">Izin Kontak</h3>
          </div>
          
          <Checkbox
            isSelected={formData.contactConsent || false}
            onValueChange={(checked) => handleInputChange('contactConsent', checked)}
            className="mb-4"
          >
            Saya setuju untuk dihubungi oleh tim Terang AI untuk follow-up atau penelitian lebih lanjut
          </Checkbox>

          {formData.contactConsent && (
            <Input
              label="Nomor Telepon"
              placeholder="Masukkan nomor telepon Anda"
              value={formData.phoneNumber || ""}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
              isInvalid={!!formErrors.phoneNumber}
              errorMessage={formErrors.phoneNumber}
              classNames={{
                inputWrapper: "bg-white"
              }}
            />
          )}
        </CardBody>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-center pt-6">
        <Button
          color="primary"
          size="lg"
          onPress={onComplete}
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
          className="w-full sm:w-auto px-8 py-3 text-base font-medium"
        >
          {isSubmitting ? "Mengirim..." : "Kirim Survei"}
        </Button>
      </div>
    </div>
  );
}
