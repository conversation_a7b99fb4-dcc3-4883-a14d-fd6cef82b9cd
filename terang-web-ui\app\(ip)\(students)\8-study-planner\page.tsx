'use client';

import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON>eader,
  CardBody,
  Input,
  Button,
  Select,
  SelectItem,
  Chip,
  Textarea,
  Switch,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  useDisclosure,
  Tooltip,
} from "@heroui/react";
import {
  Calendar,
  Clock,
  Bell,
  BookOpen,
  PlusCircle,
  Trash2,
  CheckCircle2,
  Calendar as CalendarIcon,
  Mail,
  Smartphone,
  BellRing,
  CalendarDays,
  Settings2,
  AlertCircle
} from 'lucide-react';

// Types
interface StudyTask {
  id: number;
  title: string;
  description: string;
  date: string;
  time: string;
  duration: string;
  reminder: string;
  subject: string;
  completed: boolean;
  notifications: {
    email: { enabled: boolean; address?: string };
    calendar: { enabled: boolean; type?: 'google' | 'outlook' | 'apple' };
    whatsapp: { enabled: boolean; number?: string };
    push: { enabled: boolean };
  };
}

interface NewTask extends Omit<StudyTask, 'id' | 'completed'> {}

const StudyPlannerPage = () => {
  const [tasks, setTasks] = useState<StudyTask[]>([]);
  const [newTask, setNewTask] = useState<NewTask>({
    title: '',
    description: '',
    date: '',
    time: '',
    duration: '30',
    reminder: '15',
    subject: '',
    notifications: {
      email: { enabled: true },
      calendar: { enabled: false },
      whatsapp: { enabled: false },
      push: { enabled: true }
    }
  });
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedTask, setSelectedTask] = useState<StudyTask | null>(null);

  const subjects = [
    { value: 'math', label: 'Mathematics' },
    { value: 'physics', label: 'Physics' },
    { value: 'chemistry', label: 'Chemistry' },
    { value: 'biology', label: 'Biology' },
    { value: 'english', label: 'English' }
  ];

  const durations = [
    { value: '15', label: '15 minutes' },
    { value: '30', label: '30 minutes' },
    { value: '45', label: '45 minutes' },
    { value: '60', label: '1 hour' },
    { value: '90', label: '1.5 hours' },
    { value: '120', label: '2 hours' }
  ];

  const reminders = [
    { value: '5', label: '5 minutes before' },
    { value: '10', label: '10 minutes before' },
    { value: '15', label: '15 minutes before' },
    { value: '30', label: '30 minutes before' },
    { value: '60', label: '1 hour before' }
  ];

  const calendarTypes = [
    { value: 'google', label: 'Google Calendar' },
    { value: 'outlook', label: 'Microsoft Outlook' },
    { value: 'apple', label: 'Apple Calendar' }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewTask(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddTask = () => {
    if (!newTask.title || !newTask.date || !newTask.time) {
      // You could add error handling/validation here
      return;
    }
    
    const task: StudyTask = {
      ...newTask,
      id: Date.now(),
      completed: false
    };
    
    setTasks(prev => [...prev, task]);
    
    // Reset form
    setNewTask({
      title: '',
      description: '',
      date: '',
      time: '',
      duration: '30',
      reminder: '15',
      subject: '',
      notifications: {
        email: { enabled: true },
        calendar: { enabled: false },
        whatsapp: { enabled: false },
        push: { enabled: true }
      }
    });
  };

  const handleDeleteTask = (taskId: number) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  };

  const handleToggleComplete = (taskId: number) => {
    setTasks(prev => 
      prev.map(task => 
        task.id === taskId ? { ...task, completed: !task.completed } : task
      )
    );
  };

  const handleNotificationChange = (
    type: 'email' | 'calendar' | 'whatsapp' | 'push',
    enabled: boolean
  ) => {
    if (selectedTask) {
      const updatedTask = {
        ...selectedTask,
        notifications: {
          ...selectedTask.notifications,
          [type]: { 
            ...selectedTask.notifications[type], 
            enabled 
          }
        }
      };
      setSelectedTask(updatedTask);
      setTasks(prev =>
        prev.map(task =>
          task.id === selectedTask.id ? updatedTask : task
        )
      );
    }
  };

  const handleNotificationConfigUpdate = (
    type: 'email' | 'calendar' | 'whatsapp',
    value: string
  ) => {
    if (selectedTask) {
      const configKey = type === 'email' ? 'address' : 
                       type === 'calendar' ? 'type' : 
                       'number';
      
      const updatedTask = {
        ...selectedTask,
        notifications: {
          ...selectedTask.notifications,
          [type]: {
            ...selectedTask.notifications[type],
            [configKey]: value
          }
        }
      };
      setSelectedTask(updatedTask);
      setTasks(prev =>
        prev.map(task =>
          task.id === selectedTask.id ? updatedTask : task
        )
      );
    }
  };

  const handleSaveNotifications = () => {
    if (selectedTask) {
      setTasks(prev =>
        prev.map(task =>
          task.id === selectedTask.id ? selectedTask : task
        )
      );
    }
    onClose();
  };

  const handleNotificationSettings = (task: StudyTask) => {
    setSelectedTask(task);
    onOpen();
  };

  // Notification Settings Modal
  const NotificationModal = () => {
    if (!selectedTask) return null;

    return (
      <Modal 
        size="2xl" 
        isOpen={isOpen} 
        onClose={onClose}
        isDismissable={false}
        hideCloseButton
      >
        <ModalContent>
          <ModalHeader>
            <h3 className="text-lg font-semibold">Notification Settings</h3>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              {/* Email */}
              <Card>
                <CardBody>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Mail className="w-5 h-5" />
                      <div>
                        <p className="font-semibold">Email Notifications</p>
                        <p className="text-sm text-gray-500">Get reminders via email</p>
                      </div>
                    </div>
                    <Switch
                      defaultSelected={selectedTask.notifications.email.enabled}
                      onValueChange={(checked) => handleNotificationChange('email', checked)}
                    />
                  </div>
                  {selectedTask.notifications.email.enabled && (
                    <Input
                      type="email"
                      label="Email Address"
                      placeholder="Enter your email"
                      value={selectedTask.notifications.email.address || ''}
                      onChange={(e) => handleNotificationConfigUpdate('email', e.target.value)}
                      className="mt-4"
                    />
                  )}
                </CardBody>
              </Card>

              {/* Calendar */}
              <Card>
                <CardBody>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <CalendarDays className="w-5 h-5" />
                      <div>
                        <p className="font-semibold">Calendar Integration</p>
                        <p className="text-sm text-gray-500">Add to your calendar</p>
                      </div>
                    </div>
                    <Switch
                      defaultSelected={selectedTask.notifications.calendar.enabled}
                      onValueChange={(checked) => handleNotificationChange('calendar', checked)}
                    />
                  </div>
                  {selectedTask.notifications.calendar.enabled && (
                    <Select
                      label="Calendar Type"
                      placeholder="Select calendar"
                      className="mt-4"
                      defaultSelectedKeys={[selectedTask.notifications.calendar.type || 'google']}
                      onChange={(e) => handleNotificationConfigUpdate('calendar', e.target.value)}
                    >
                      {calendarTypes.map((type) => (
                        <SelectItem key={type.value} textValue={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </Select>
                  )}
                </CardBody>
              </Card>

              {/* WhatsApp */}
              <Card>
                <CardBody>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <Smartphone className="w-5 h-5" />
                      <div>
                        <p className="font-semibold">WhatsApp Notifications</p>
                        <p className="text-sm text-gray-500">Get reminders via WhatsApp</p>
                      </div>
                    </div>
                    <Switch
                      defaultSelected={selectedTask.notifications.whatsapp.enabled}
                      onValueChange={(checked) => handleNotificationChange('whatsapp', checked)}
                    />
                  </div>
                  {selectedTask.notifications.whatsapp.enabled && (
                    <Input
                      type="tel"
                      label="Phone Number"
                      placeholder="Enter your WhatsApp number"
                      value={selectedTask.notifications.whatsapp.number || ''}
                      onChange={(e) => handleNotificationConfigUpdate('whatsapp', e.target.value)}
                      className="mt-4"
                    />
                  )}
                </CardBody>
              </Card>

              {/* Push Notifications */}
              <Card>
                <CardBody>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <BellRing className="w-5 h-5" />
                      <div>
                        <p className="font-semibold">Push Notifications</p>
                        <p className="text-sm text-gray-500">Browser notifications</p>
                      </div>
                    </div>
                    <Switch
                      defaultSelected={selectedTask.notifications.push.enabled}
                      onValueChange={(checked) => handleNotificationChange('push', checked)}
                    />
                  </div>
                </CardBody>
              </Card>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="flat" onPress={onClose}>
              Cancel
            </Button>
            <Button color="primary" onPress={handleSaveNotifications}>
              Save Changes
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    );
  };

  return (
    <div className="min-h-screen p-6 bg-gray-50">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold mb-6 flex items-center gap-2">
          <Calendar className="w-6 h-6" />
          Study Planner & Reminders
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Add New Study Plan */}
          <Card className="md:col-span-1">
            <CardHeader>
              <h3 className="text-lg font-semibold">Create New Study Plan</h3>
            </CardHeader>
            <CardBody className="gap-4">
              <Input
                name="title"
                label="Title"
                placeholder="Enter study session title"
                value={newTask.title}
                onChange={handleInputChange}
              />
              
              <Textarea
                name="description"
                label="Description"
                placeholder="Add study session details"
                value={newTask.description}
                onChange={(e) => setNewTask(prev => ({
                  ...prev,
                  description: e.target.value
                }))}
              />

              <Select
                name="subject"
                label="Subject"
                placeholder="Select subject"
                selectedKeys={newTask.subject ? [newTask.subject] : []}
                onChange={handleInputChange}
              >
                {subjects.map((subject) => (
                  <SelectItem key={subject.value} textValue={subject.value}>
                    {subject.label}
                  </SelectItem>
                ))}
              </Select>

              <div className="grid grid-cols-2 gap-4">
                <Input
                  name="date"
                  type="date"
                  label="Date"
                  placeholder="Select date"
                  value={newTask.date}
                  onChange={handleInputChange}
                />
                <Input
                  name="time"
                  type="time"
                  label="Time"
                  placeholder="Select time"
                  value={newTask.time}
                  onChange={handleInputChange}
                />
              </div>

              <Select
                name="duration"
                label="Duration"
                placeholder="Select duration"
                selectedKeys={[newTask.duration]}
                onChange={handleInputChange}
              >
                {durations.map((duration) => (
                  <SelectItem key={duration.value} textValue={duration.value}>
                    {duration.label}
                  </SelectItem>
                ))}
              </Select>

              <Select
                name="reminder"
                label="Reminder"
                placeholder="Set reminder"
                selectedKeys={[newTask.reminder]}
                onChange={handleInputChange}
              >
                {reminders.map((reminder) => (
                  <SelectItem key={reminder.value} textValue={reminder.value}>
                    {reminder.label}
                  </SelectItem>
                ))}
              </Select>

              <Button
                color="primary"
                variant="solid"
                onPress={handleAddTask}
                startContent={<PlusCircle className="w-5 h-5" />}
                className="w-full"
                size="lg"
              >
                Create Study Plan
              </Button>
            </CardBody>
          </Card>

          {/* Study Plans List */}
          <Card className="md:col-span-2">
            <CardHeader>
              <h3 className="text-lg font-semibold">Your Study Plans</h3>
            </CardHeader>
            <CardBody>
              {tasks.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                  No study plans yet. Create one to get started!
                </div>
              ) : (
                <div className="space-y-4">
                  {tasks.map((task) => (
                    <Card key={task.id} className={task.completed ? "bg-gray-50" : ""}>
                      <CardBody className="p-4">
                        <div className="flex items-start justify-between gap-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className={`font-semibold ${task.completed ? "line-through text-gray-500" : ""}`}>
                                {task.title}
                              </h3>
                              <Chip size="sm" color="primary">
                                {subjects.find(s => s.value === task.subject)?.label}
                              </Chip>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{task.description}</p>
                            <div className="flex gap-4 text-sm text-gray-500">
                              <span className="flex items-center gap-1">
                                <Calendar className="w-4 h-4" />
                                {task.date}
                              </span>
                              <span className="flex items-center gap-1">
                                <Clock className="w-4 h-4" />
                                {task.time}
                              </span>
                              <span className="flex items-center gap-1">
                                <Bell className="w-4 h-4" />
                                {reminders.find(r => r.value === task.reminder)?.label}
                              </span>
                            </div>
                            <div className="flex gap-2 mt-2">
                              {task.notifications.email.enabled && (
                                <Tooltip content="Email notifications enabled">
                                  <Chip size="sm" variant="flat" className="cursor-help">
                                    <Mail className="w-3 h-3 text-primary" />
                                  </Chip>
                                </Tooltip>
                              )}
                              {task.notifications.calendar.enabled && (
                                <Tooltip content="Calendar integration enabled">
                                  <Chip size="sm" variant="flat" className="cursor-help">
                                    <CalendarDays className="w-3 h-3 text-primary" />
                                  </Chip>
                                </Tooltip>
                              )}
                              {task.notifications.whatsapp.enabled && (
                                <Tooltip content="WhatsApp notifications enabled">
                                  <Chip size="sm" variant="flat" className="cursor-help">
                                    <Smartphone className="w-3 h-3 text-primary" />
                                  </Chip>
                                </Tooltip>
                              )}
                              {task.notifications.push.enabled && (
                                <Tooltip content="Push notifications enabled">
                                  <Chip size="sm" variant="flat" className="cursor-help">
                                    <BellRing className="w-3 h-3 text-primary" />
                                  </Chip>
                                </Tooltip>
                              )}
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              isIconOnly
                              color="primary"
                              variant="light"
                              onPress={() => handleNotificationSettings(task)}
                            >
                              <Settings2 className="w-5 h-5" />
                            </Button>
                            <Button
                              isIconOnly
                              color={task.completed ? "success" : "primary"}
                              variant="light"
                              onPress={() => handleToggleComplete(task.id)}
                            >
                              <CheckCircle2 className="w-5 h-5" />
                            </Button>
                            <Button
                              isIconOnly
                              color="danger"
                              variant="light"
                              onPress={() => handleDeleteTask(task.id)}
                            >
                              <Trash2 className="w-5 h-5" />
                            </Button>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              )}
            </CardBody>
          </Card>
        </div>
      </div>
      <NotificationModal />
    </div>
  );
};

export default StudyPlannerPage;