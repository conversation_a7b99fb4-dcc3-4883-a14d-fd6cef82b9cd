import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { 
  orderExams, 
  storePaymentInfo, 
  storePaymentToken, 
  getPaymentToken, 
  updatePaymentStatus, 
  finalRedeemDiscount, 
  removePaymentInfo, 
  getPaymentInfo,
  validateAndTrackReferralCode 
} from '@/app/lib/actions/available-exams/actions';
import DotLottieAnimation from '../shared/dotlottie-animation';
import { fetchPurchasedBundles } from '@/store/slices/purchasedBundlesSlice';
import {
  Button,
  Input,
} from "@heroui/react";

// BundlingPurchaseButton props
interface BundlingPurchaseButtonProps {
  examIds: string[] | number[];
  discountPercentage?: number;
  onPurchase?: (ids: string[] | number[]) => Promise<void> | void;
  onClose?: () => void;
  showNotification?: (message: string, type: "success" | "info" | "error") => void;
  bundleId?: string;
  bundleName?: string;
  categoryName?: string;
  bundlePrice?: number;
  bundleDetails?: {
    bundle: any;
    examCount: number;
  } | null;
  bundleDetailsLoading?: boolean;
  referralCode?: string;
  setRefreshData?: React.Dispatch<React.SetStateAction<boolean>>;
  isFreeAccess?: boolean; // Add this line
}

interface PaymentInfo {
  orderId: string;
  examId: string;
  bundleId?: string;
  invoiceId: string;
  paymentId: string;
  refCode?: string;
}

interface PaymentResult {
  transaction_status: string;
  payment_type: string;
  issuer: string;
  acquirer: string;
  transaction_id: string;
  transaction_time: string;
}

interface DiscountInfo {
  originalPrice: number;
  discountPercentage: number;
  discountAmount: number;
  finalPrice: number;
}

// Add this type definition for the purchased bundle item structure
interface PurchasedBundleItem {
  bundle?: {
    id?: string;
    name?: string;
  };
  id?: string;
  purchaseDate?: string;
  orderId?: string;
  orderStatus?: string;
  paymentStatus?: string;
  examsTotal?: number;
  examsCompleted?: number;
  examsCompletedRate?: number;
}

// Declare the global Snap interface
declare global {
  interface Window {
    snap?: {
      pay: (token: string, options: any) => void;
    };
  }
}

const EnhancedBundlingPurchaseButton: React.FC<BundlingPurchaseButtonProps> = ({ 
  examIds = [], 
  discountPercentage = 15, 
  onPurchase,
  onClose,
  showNotification,
  bundleId,
  bundleName,
  categoryName,
  bundlePrice,
  bundleDetails,
  bundleDetailsLoading = false,
  referralCode: initialReferralCode,
  setRefreshData
}) => {
  // Refs to prevent unnecessary renders
  console.log(bundleDetails)
  const initialCheckDone = useRef(false);
  const scriptLoadAttempted = useRef(false);
  
  // Core state variables
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isVisible, setIsVisible] = useState<boolean>(true);
  const [scriptLoaded, setScriptLoaded] = useState<boolean>(false);
  const [pendingPayment, setPendingPayment] = useState<PaymentInfo | null>(null);
  const [isPulsing, setIsPulsing] = useState<boolean>(false);
  const [isAlreadyPurchased, setIsAlreadyPurchased] = useState<boolean>(false);
  const [shouldRender, setShouldRender] = useState<boolean>(false);
  const [isCheckingPurchase, setIsCheckingPurchase] = useState<boolean>(true);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  
  // Derived state for better performance
  const [price, setPrice] = useState<number | null>(bundlePrice || null);
  
  // Referral code functionality
  const [referralCode, setReferralCode] = useState<string>(initialReferralCode || '');
  const [isValidating, setIsValidating] = useState<boolean>(false);
  const [referralError, setReferralError] = useState<string>('');
  const [discountInfo, setDiscountInfo] = useState<DiscountInfo | null>(null);
  const animationTimerRef = useRef<NodeJS.Timeout | null>(null);

  const examCount = useMemo(() => {
    return bundleDetails?.examCount || examIds.length;
  }, [bundleDetails, examIds.length]);
  
  // Redux connections
  const dispatch = useAppDispatch();
  const { loading, error, items: bundles } = useAppSelector((state) => state.bundles);
  const purchasedBundlesState = useAppSelector((state) => state.purchasedBundles) as {
    loading: boolean;
    error: string | null;
    items: PurchasedBundleItem[];
  };

  console.log(bundles)
  
  // Format price to Indonesian Rupiah - memoized for better performance
  const formatPrice = useCallback((amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }, []);

  // Handle close functionality
  const handleClose = useCallback(() => {
    setIsAnimating(false);
    
    setTimeout(() => {
      setIsVisible(false);
      if (onClose) {
        onClose();
      }
    }, 300);
  }, [onClose]);


  // Replace the purchase status check effect with this version
  // This uses a debounce mechanism to prevent multiple consecutive API calls
  const checkingRef = useRef(false);

  // Check if bundle is already purchased (from Redux state)
  // This effect runs once after initial load
  useEffect(() => {
    const checkIfPurchased = async () => {
      // Prevent multiple simultaneous checks
      if (checkingRef.current) return;
      checkingRef.current = true;
      
      setIsCheckingPurchase(true);
      
      // Make sure bundleId exists and is not undefined or null
      if (!bundleId) {
        setIsCheckingPurchase(false);
        checkingRef.current = false;
        return;
      }
      
      try {
        // First, check if we have any purchased bundles in the Redux state
        if (bundleId && purchasedBundlesState.items && Array.isArray(purchasedBundlesState.items)) {
          // Check Redux state first (this is fast and doesn't require API call)
          const isPurchased = purchasedBundlesState.items.some(item => {
            // Safely check if item exists and is an object
            if (!item || typeof item !== 'object') return false;
            
            // Handle direct matching with item.id
            if (item.id && item.id === bundleId) return true;
            
            // Handle nested bundle object matching
            if (item.bundle && typeof item.bundle === 'object' && item.bundle.id && item.bundle.id === bundleId) {
              return true;
            }
            
            return false;
          });
          
          if (isPurchased) {
            console.log(`Bundle ${bundleId} is already purchased (Redux check)`);
            setIsAlreadyPurchased(true);
            setIsCheckingPurchase(false);
            checkingRef.current = false;
            return;
          }
        }
        
        // Only make API call if necessary (prevents network spam)
        const response = await fetch('/api/exam-bundles-purchased');
        
        if (!response.ok) {
          console.error(`Failed to fetch purchased bundles: ${response.statusText}`);
          checkingRef.current = false;
          setIsCheckingPurchase(false);
          return;
        }
        
        const data = await response.json();
        
        if (data && data.bundles && Array.isArray(data.bundles.purchasedBundles)) {
          const isPurchasedFromApi = data.bundles.purchasedBundles.some(
            (item: any) => {
              return item && 
                    item.bundle && 
                    typeof item.bundle === 'object' && 
                    item.bundle.id && 
                    item.bundle.id === bundleId;
            }
          );
          
          if (isPurchasedFromApi) {
            console.log(`Bundle ${bundleId} is already purchased (API check)`);
            setIsAlreadyPurchased(true);
          } else {
            // Reset isAlreadyPurchased to false if not found
            setIsAlreadyPurchased(false);
          }
        }
      } catch (error) {
        console.error("Error checking purchased status:", error);
      } finally {
        // Always make sure to clear flags in finally block
        setIsCheckingPurchase(false);
        checkingRef.current = false;
      }
    };
    
    // Only trigger API fetch when bundleId changes, not when category changes
    if (bundleId) {
      // Use a slight delay to prevent rapid consecutive API calls
      const timer = setTimeout(() => {
        checkIfPurchased();
      }, 300);
      
      return () => clearTimeout(timer);
    }
  }, [dispatch, bundleId, purchasedBundlesState.items]);

  useEffect(() => {
    // Only fetch purchased bundles once on initial mount
    dispatch(fetchPurchasedBundles());
  }, [dispatch]);

  useEffect(() => {
    // When category changes, handle animation with proper sequencing
    const handleCategoryChange = () => {
      // Clear any existing animation timers
      if (animationTimerRef.current) {
        clearTimeout(animationTimerRef.current);
      }
      
      // Gracefully hide with a subtle delay
      setIsAnimating(false);
      
      // Use a slightly longer delay before showing again for smoother transition
      animationTimerRef.current = setTimeout(() => {
        // Only animate if the component should be rendered
        if (shouldRender) {
          setIsAnimating(true);
        }
      }, 400); // Longer delay for smoother transition
    };
  
    // Initial animation on mount
    if (!isAnimating && shouldRender) {
      animationTimerRef.current = setTimeout(() => {
        setIsAnimating(true);
      }, 100);
    }
    
    // Handle animation on category change
    if (categoryName) {
      handleCategoryChange();
    }
    
    // Clean up the timers
    return () => {
      if (animationTimerRef.current) {
        clearTimeout(animationTimerRef.current);
      }
    };
  }, [categoryName, shouldRender]);

  // Check if the bundle has free access via daerah 3T
  const isFreeAccess = bundleDetails?.bundle?.isFreeAccess || false;

  // Set price based on isFreeAccess
  useEffect(() => {
    console.log('Updating price based on:', { 
      bundleDetails: bundleDetails?.bundle?.price,
      bundlePrice,
      categoryName,
      isFreeAccess: bundleDetails?.bundle?.isFreeAccess
    });
    
    if (isFreeAccess) {
      console.log('Setting price to 0 due to free daerah 3T access');
      setPrice(0);
    } else if (bundleDetails && bundleDetails.bundle && bundleDetails.bundle.price) {
      console.log('Setting price from bundleDetails:', bundleDetails.bundle.price);
      setPrice(bundleDetails.bundle.price);
    } else if (bundlePrice) {
      console.log('Setting price from bundlePrice prop:', bundlePrice);
      setPrice(bundlePrice);
    } else if (bundleId && bundles && bundles.length > 0) {
      // Find bundle matching current ID
      const foundBundle = bundles.find(bundle => bundle.id === bundleId);
      
      if (foundBundle) {
        // Check if the found bundle has free access
        if (foundBundle.isFreeAccess) {
          console.log('Setting price to 0 from found bundle with free access');
          setPrice(0);
        } else {
          console.log('Setting price from found bundle:', foundBundle.price);
          setPrice(foundBundle.price);
        }
      } else {
        console.log('No matching bundle found for:', bundleId);
      }
    }
  }, [bundleDetails, bundlePrice, bundleId, bundles, isFreeAccess]);  

  // Check for pending bundle payments when component mounts - runs once
  useEffect(() => {
    const checkPendingPayment = async () => {
      try {
        const storedPaymentInfo = await getPaymentInfo('bundle');
        
        // Verify this payment info is for the current bundle
        if (storedPaymentInfo && storedPaymentInfo.bundleId === bundleId) {
          setPendingPayment(storedPaymentInfo);
          setIsPulsing(true);
        }
      } catch (error) {
        console.error("Error fetching payment info:", error);
      }
    };

    if (bundleId) {
      checkPendingPayment();
    }
  }, [bundleId]);

  // Determine if component should be rendered based on component state
  useEffect(() => {
    if (isAlreadyPurchased) {
      console.log('Bundle already purchased, not rendering');
      setShouldRender(false);
      return;
    }
  
    // Check if the bundle is in the current category
    let categoryMatches: boolean = true; // Default to true if no category specified
    
    if (categoryName && bundleId && bundles && bundles.length > 0) {
      // First try exact match
      const exactMatchingBundle = bundles.find(bundle => 
        bundle.id === bundleId && (
          !categoryName || // If no category name provided, any bundle matches
          categoryName === bundle.categoryName ||
          (bundle.category && categoryName === bundle.category.name)
        )
      );
      
      if (exactMatchingBundle) {
        console.log('Found exact matching bundle for category:', categoryName);
        categoryMatches = true;
      } else {
        // If we have a specific category requirement but no matching bundle was found
        if (categoryName) {
          console.log('No matching bundle found for category:', categoryName);
          categoryMatches = false;
        }
      }
    }
    
    // Check for valid bundle data
    const hasBundleData: boolean = Boolean(
      (bundleId && (bundlePrice || (bundleDetails && bundleDetails.bundle && bundleDetails.bundle.price))) || 
      (examIds && examIds.length > 0)
    );
    
    console.log('Render decision factors:', {
      isCheckingPurchase,
      hasBundleData,
      categoryMatches,
      bundleId,
      categoryName,
      price
    });
    
    // Only render if not checking purchase AND has valid bundle data AND category matches
    if (!isCheckingPurchase && hasBundleData && categoryMatches) {
      console.log('SHOULD render bundle button');
      setShouldRender(true);
    } else {
      console.log('Should NOT render bundle button');
      setShouldRender(false);
    }
  }, [isCheckingPurchase, isAlreadyPurchased, bundleId, bundlePrice, bundleDetails, examIds, categoryName, bundles, price]);

  // Load Midtrans script once on mount
  useEffect(() => {
    if (scriptLoadAttempted.current) return;
    scriptLoadAttempted.current = true;
    
    // Only load script if not already loaded
    if (!document.getElementById("midtrans-script")) {
      const loadMidtransScript = () => {
        const script = document.createElement("script");
        const isProduction = process.env.NODE_ENV === "production";
        script.src = isProduction
          ? "https://app.midtrans.com/snap/snap.js"
          : "https://app.sandbox.midtrans.com/snap/snap.js";
        script.id = "midtrans-script";
        script.async = true;
  
        const clientKey = isProduction
          ? process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY_PRODUCTION
          : process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY_SANDBOX;
  
        script.setAttribute("data-client-key", clientKey || "");
        
        script.onload = () => {
          setScriptLoaded(true);
        };
        
        document.body.appendChild(script);
      };
  
      loadMidtransScript();
    } else {
      setScriptLoaded(true);
    }
  }, []);

  useEffect(() => {
    // Check if the URL has a successful payment parameter
    const urlParams = new URLSearchParams(window.location.search);
    const paymentSuccess = urlParams.get('payment_success');
    const paymentBundleId = urlParams.get('bundle_id');
    
    if (paymentSuccess === 'true' && paymentBundleId === bundleId) {
      // If there's a successful payment for this bundle in URL params,
      // immediately mark as purchased and hide the component
      setIsAlreadyPurchased(true);
      setShouldRender(false);
      
      // Clean URL by removing these parameters
      if (window.history && window.history.replaceState) {
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }
    }
  }, [bundleId]);

  // Revalidate page data after successful payment
  const revalidatePage = () => {
    // Set as purchased immediately to hide component
    setIsAlreadyPurchased(true);
    setShouldRender(false);
    
    if (setRefreshData) {
      setRefreshData((prev) => !prev);
    } else {
      // Add success parameter to URL when reloading
      const url = new URL(window.location.href);
      url.searchParams.set('payment_success', 'true');
      url.searchParams.set('bundle_id', bundleId || '');
      window.location.href = url.toString();
    }
    
    if (onPurchase) {
      const result = onPurchase(examIds);
      if (result && typeof result.catch === 'function') {
        result.catch((error: Error) => {
          console.error("Error in onPurchase callback:", error);
        });
      }
    }
  };

  // Early return if component should not be rendered
  if (!shouldRender || !isVisible) {
    return null;
  }

  const getDiscountPercentage = (discount: any, defaultValue: number = 0): number => {
    if (discount === null || discount === undefined) {
      return defaultValue;
    }
    
    if (typeof discount === 'number') {
      return discount;
    }
    
    if (discount.Valid && typeof discount.Int32 === 'number') {
      return discount.Int32;
    }
    
    return defaultValue;
  };

  // Function to check payment status
  async function checkPaymentStatus(orderId: string): Promise<PaymentResult> {
    const response = await fetch(`/api/check-payment-status?orderId=${orderId}`, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });
  
    if (!response.ok) {
      throw new Error(`Failed to fetch payment status: ${response.statusText}`);
    }
  
    const data = await response.json();
    return data as PaymentResult;
  }

  // Handle Midtrans payment
  const handlePayment = async (orderId: string, invoiceId: string, paymentId: string) => {
    if (typeof window.snap === 'undefined') {
      console.error("Snap.js is not loaded");
      showNotification?.("Payment system is not ready. Please try again later.", "error");
      setIsLoading(false);
      return;
    }
  
    try {
      const token = await getPaymentToken(orderId);
  
      if (!token) {
        throw new Error('Payment token not found');
      }
  
      // Set loading to false before opening the payment window
      setIsLoading(false);
  
      window.snap.pay(token, {
        onSuccess: async (result: any) => {
          console.log("Payment successful", result);
          showNotification?.("Payment successful!", "success");
          
          try {
            const paymentStatus = await checkPaymentStatus(orderId);
            
            if (paymentStatus.transaction_status === "settlement") {
              const paymentTypeString = `${paymentStatus.payment_type}_${paymentStatus.issuer}_${paymentStatus.acquirer}`;
              await updatePaymentStatus(
                orderId, 
                invoiceId, 
                paymentId, 
                "PAID", 
                "COMPLETED", 
                "PAID",
                paymentTypeString,
                paymentStatus.transaction_id,
                paymentStatus.transaction_time
              );
              
              if (referralCode) {
                const redeemResult = await finalRedeemDiscount(paymentId, referralCode);
                if (redeemResult.success) {
                  showNotification?.("Referral code applied successfully!", "success");
                } else {
                  showNotification?.(redeemResult.error || "Failed to apply referral code", "error");
                }
              }
  
              showNotification?.("Order status updated successfully!", "success");
              
              // Set completed state immediately
              setIsAlreadyPurchased(true);
              setShouldRender(false);
            } else {
              showNotification?.("Payment status inconsistent. Please contact support.", "error");
            }
          } catch (error) {
            console.error("Error updating order status:", error);
            showNotification?.("Error updating order status. Please contact support.", "error");
          }
  
          // Clean up payment info after successful payment
          await removePaymentInfo('bundle');
          setPendingPayment(null);
          
          // Make sure we always revalidate after successful payment
          setTimeout(() => {
            revalidatePage();
            handleClose();
          }, 1000);
        },
        onPending: (result: any) => {
          console.log("Payment pending", result);
          showNotification?.(
            "Payment is pending. You can close this window and continue payment later.",
            "info"
          );
        },
        onError: (result: any) => {
          console.error("Payment error", result);
          showNotification?.("Payment failed. Please try again.", "error");
        },
        onClose: () => {
          console.log("Payment popup closed");
          showNotification?.(
            "Payment window closed. You can continue payment later.",
            "info"
          );
        },
      });
    } catch (error) {
      console.error("Error initiating payment:", error);
      showNotification?.("Error initiating payment. Please try again.", "error");
      setIsLoading(false);
    }
  };

  // Function to continue pending payment
  const continuePendingPayment = async () => {
    if (!pendingPayment) {
      showNotification?.("No pending payment found", "error");
      return;
    }

    setIsLoading(true);
    
    try {
      await handlePayment(
        pendingPayment.orderId,
        pendingPayment.invoiceId,
        pendingPayment.paymentId
      );
    } catch (error) {
      console.error("Error continuing payment:", error);
      showNotification?.("Failed to continue payment. Please try again.", "error");
      setIsLoading(false);
    }
  };

  // Function to validate referral code
  const handleValidateReferral = async () => {
    if (!referralCode) return;
    
    setIsValidating(true);
    setReferralError("");
    
    try {
      const validation = await validateAndTrackReferralCode(referralCode);
      
      if (validation.isValid && validation.refereeRewardAmount && price) {
        const discountPercentage = validation.refereeRewardAmount;
        const discountAmount = Math.floor((price * discountPercentage) / 100);
        
        const bundleDiscountAmount = Math.round(price * (effectiveDiscountPercentage / 100));
        const finalPrice = Math.max(0, price - bundleDiscountAmount - discountAmount);
  
        setDiscountInfo({
          originalPrice: price,
          discountPercentage,
          discountAmount,
          finalPrice
        });
        
        showNotification?.(
          `Diskon referral ${discountPercentage}% berhasil diterapkan!`,
          "success"
        );
      } else {
        setDiscountInfo(null);
        setReferralError(validation.errorMessage || "Kode referral tidak valid");
        showNotification?.(
          validation.errorMessage || "Kode referral tidak valid",
          "error"
        );
      }
    } catch (error) {
      console.error("Error validating referral code:", error);
      setDiscountInfo(null);
      setReferralError("Gagal memvalidasi kode referral. Silakan coba lagi");
      showNotification?.(
        "Gagal memvalidasi kode referral. Silakan coba lagi",
        "error"
      );
    } finally {
      setIsValidating(false);
    }
  };

  // Validate that bundle ID exists
  const validateBundle = (): boolean => {
    if (!bundleId) {
      showNotification?.("ID Paket tidak valid", "error");
      return false;
    }

    if (!examIds || examIds.length === 0) {
      showNotification?.("Paket tidak berisi ujian", "error");
      return false;
    }

    return true;
  };

  // Handle new purchase
  const startNewPurchase = async () => {
    if (!validateBundle()) {
      return;
    }

    setIsLoading(true);
    
    try {
      const placeholderExam = {
        id: examIds[0].toString()
      } as any;

      // Check if the bundle is free due to daerah 3T access
      // If so, use a simplified flow without payment tokens
      if (isFreeAccess) {
        console.log("Processing free daerah 3T bundle purchase");
        
        const result = await orderExams(placeholderExam, referralCode, true, bundleId);
        
        if (result.ok) {
          showNotification?.("Paket berhasil didapatkan! (Program Daerah 3T)", "success");
          setIsAlreadyPurchased(true);
          setShouldRender(false);
          revalidatePage();
          setIsLoading(false);
          return;
        } else {
          throw new Error("Gagal memproses pembelian gratis");
        }
      }

      // Regular purchase flow for paid bundles
      const result = await orderExams(placeholderExam, referralCode, true, bundleId);
      
      if (result.ok) {
        if (result.finalAmount === 0) {
          // Handle free orders (either from discount or other reason)
          showNotification?.("Paket berhasil dibeli!", "success");
          setIsAlreadyPurchased(true);
          setShouldRender(false);
          revalidatePage();
          setIsLoading(false);
          return;
        }
        
        // Continue with normal paid flow only for non-zero amounts
        if (result.orderId) {
          const paymentInfo = {
            orderId: result.orderId,
            examId: placeholderExam.id,
            bundleId: bundleId,
            invoiceId: result.invoiceId,
            paymentId: result.paymentId,
            refCode: referralCode
          };
          
          await storePaymentInfo(paymentInfo);
          setPendingPayment(paymentInfo);

          if (result.token) {
            await storePaymentToken(result.orderId, result.token);
          }
        }
        
        await handlePayment(result.orderId, result.invoiceId, result.paymentId);
      } else {
        throw new Error("Gagal memproses pembelian");
      }
    } catch (error: any) {
      console.error("Purchase failed:", error);
      const friendlyError = getUserFriendlyError(error);
      showNotification?.(friendlyError, "error");
      setIsLoading(false);
    }
  };

  // Helper function to get user-friendly error message
  const getUserFriendlyError = (error: any): string => {
    if (error?.message?.includes("Failed to validate referral code")) {
      return "Kode referral tidak valid. Silakan periksa kembali.";
    }
    
    if (error?.message?.includes("Failed to fetch bundle details")) {
      return "Gagal mengambil detail paket. Silakan coba lagi.";
    }
    
    if (error?.message?.includes("500")) {
      return "Terjadi kesalahan server. Silakan coba beberapa saat lagi.";
    }

    if (error?.message?.includes("404")) {
      return "Paket tidak ditemukan. Silakan refresh halaman dan coba lagi.";
    }

    return error?.message || "Gagal memproses pembelian. Silakan coba lagi.";
  };

  // Main handler for the button click
  const handlePurchase = async (): Promise<void> => {
    if (pendingPayment) {
      continuePendingPayment();
    } else {
      startNewPurchase();
    }
  };

  // Get effective discount percentage
  const effectiveDiscountPercentage = bundleDetails?.bundle?.discountPercentage ? 
    getDiscountPercentage(bundleDetails.bundle.discountPercentage) : 
    discountPercentage;

  // Calculate discounted price
  let calculatedDiscountedPrice = price || 0;
  let bundleDiscountAmount = 0;
  
  // Apply bundle discount if available
  if (price && effectiveDiscountPercentage) {
    bundleDiscountAmount = Math.round(price * (effectiveDiscountPercentage / 100));
    calculatedDiscountedPrice = price - bundleDiscountAmount;
  }
  
  // Apply referral discount if available
  if (discountInfo && price) {
    calculatedDiscountedPrice = price - bundleDiscountAmount - discountInfo.discountAmount;
  }

  return (
    <>
      <div 
        className={`p-3 rounded-xl bg-gradient-to-r from-violet-50 to-indigo-50 shadow-sm border border-indigo-100 relative ${
          isAnimating 
            ? 'animate-fadeInSlideUp' 
            : 'opacity-0 transform translate-y-8'
        }`}
        style={{
          transition: 'all 800ms cubic-bezier(0.22, 1, 0.36, 1)'
        }}
      >
        {/* Close Button */}
        <button 
          onClick={handleClose}
          className="absolute -top-2 -right-2 w-6 h-6 rounded-full bg-gray-300 hover:bg-gray-400 flex items-center justify-center transition-colors shadow-sm border border-white"
          aria-label="Tutup"
        >
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="#333" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <div className="text-base w-24 h-24 -mt-6">
              <DotLottieAnimation src='/dotlotties/bundle-items.lottie'
                  autoplay
                  loop
                  width="100%"
                  height="100%"
              ></DotLottieAnimation>
            </div>
            <h3 className="font-semibold text-gray-800 text-xxl">
              {bundleName || "Paket Bundling"}
              {bundleDetailsLoading && <span className="ml-2 text-xs text-gray-500">(Loading...)</span>}
            </h3>
            <span className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white text-xs px-2 py-0.5 rounded-full">
              Hemat {effectiveDiscountPercentage}%
            </span>
          </div>
          
          <p className="text-xs text-gray-600">
            {bundleId 
              ? `Dapatkan akses ke paket ujian dengan ${examCount} soal dan diskon ${effectiveDiscountPercentage}%!`
              : `Beli ${examIds.length} ujian sekaligus dan hemat ${effectiveDiscountPercentage}% dari total harga!`
            }
          </p>
          
          {/* Bundle Details Section */}
          {bundleDetails && (
            <div className="bg-white p-2 rounded-lg text-xs">
              <div className="font-medium text-indigo-700">Detail Paket:</div>
              <div className="flex justify-between text-gray-700">
                <span>Jumlah Ujian:</span>
                <span className="font-medium">{bundleDetails.examCount}</span>
              </div>
              {bundleDetails.bundle.validFrom && (
                <div className="flex justify-between text-gray-700">
                  <span>Berlaku Dari:</span>
                  <span className="font-medium">
                    {new Date(bundleDetails.bundle.validFrom).toLocaleDateString('id-ID')}
                  </span>
                </div>
              )}
              {bundleDetails.bundle.validUntil && (
                <div className="flex justify-between text-gray-700">
                  <span>Berlaku Hingga:</span>
                  <span className="font-medium">
                    {new Date(bundleDetails.bundle.validUntil).toLocaleDateString('id-ID')}
                  </span>
                </div>
              )}
            </div>
          )}
          
          {/* Referral Code Section */}
          <div className="mt-3">
            <div className="flex flex-col gap-2">
              <label htmlFor="referralCode" className="text-sm font-medium">Kode Referral (Opsional)</label>
              <div className="flex gap-2">
              <Input
                id="referalCode"
                value={referralCode}
                onChange={(e) => {
                  setReferralError("");
                  setReferralCode(e.target.value.toUpperCase());
                  setDiscountInfo(null);
                }}
                placeholder="Masukkan kode referral"
                classNames={{
                  // Target the input wrapper
                  inputWrapper: "!bg-white", // Add !important override
                }}
                style={{ 
                  // Force white background and remove opacity
                  backgroundColor: 'white',
                  // Override Heroui's CSS variables
                  // These are example values - adjust based on the library's actual variables
                  // You might need to inspect the rendered element to find exact variable names
                  // Cast to any to bypass TypeScript checking
                  ...({
                    "--input-bg": "#ffffff",
                    "--input-bg-opacity": "1",
                    "--heroui-default-100": "255 255 255", // RGB values for white
                    "--heroui-default-100-opacity": "1",
                  } as any)
                }}
                isInvalid={!!referralError}
              />
                <Button
                  color="primary"
                  size="md"
                  onPress={handleValidateReferral}
                  isDisabled={!referralCode || isValidating}
                >
                  {isValidating ? "Validasi..." : "Terapkan"}
                </Button>
              </div>
              {referralError && (
                <p className="text-sm text-red-500">
                  {referralError}
                </p>
              )}
            </div>

            {/* Discount Info */}
            {discountInfo && (
              <div className="border-t pt-3 mt-3 space-y-2">
                <div className="flex justify-between text-sm text-gray-500">
                  <span>Harga Normal:</span>
                  <span className="line-through">
                    {formatPrice(discountInfo.originalPrice)}
                  </span>
                </div>
                <div className="flex justify-between text-sm text-green-600">
                  <span>Diskon Bundling ({effectiveDiscountPercentage}%):</span>
                  <span>
                    -{formatPrice(Math.round(discountInfo.originalPrice * (effectiveDiscountPercentage / 100)))}
                  </span>
                </div>
                <div className="flex justify-between text-sm text-green-600">
                  <span>Diskon Referral ({discountInfo.discountPercentage}%):</span>
                  <span>
                    -{formatPrice(discountInfo.discountAmount)}
                  </span>
                </div>
              </div>
            )}
          </div>
          
          {/* Price Display */}
          {price !== null && (
            <div className="flex items-center justify-center gap-2 py-2 mt-3">
              {isFreeAccess ? (
                <span className="text-green-600 font-bold text-lg">
                  Gratis (Daerah 3T)
                </span>
              ) : (
                <>
                  <span className="text-gray-500 line-through text-sm">
                    {formatPrice(price)}
                  </span>
                  {calculatedDiscountedPrice !== null && (
                    <span className="text-indigo-700 font-bold text-lg">
                      {formatPrice(calculatedDiscountedPrice)}
                    </span>
                  )}
                </>
              )}
            </div>
          )}
          
          {/* Purchase Button */}
          <button
            onClick={() => {
              if (!isLoading && scriptLoaded) {
                handlePurchase();
              }
            }}
            onMouseEnter={() => {
              setIsHovered(true);
              if (pendingPayment) setIsPulsing(false);
            }}
            onMouseLeave={() => {
              setIsHovered(false);
              if (pendingPayment) setIsPulsing(true);
            }}
            disabled={isLoading || !scriptLoaded}
            className={`relative mt-2 w-full py-2 px-3 rounded-full font-medium text-white transition-all duration-300 overflow-hidden ${
              isLoading || !scriptLoaded
                ? "bg-gray-400 cursor-not-allowed" 
                : isFreeAccess
                  ? "bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                  : pendingPayment 
                    ? `bg-gradient-to-r from-amber-500 to-yellow-600 hover:from-amber-600 hover:to-yellow-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 ${isPulsing ? 'animate-pulse' : ''}`
                    : "bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
            }`}
          >
            {/* Button content */}
            <div className="relative flex items-center justify-center gap-2">
              {isLoading ? (
                <>
                  <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Memproses...</span>
                </>
              ) : !scriptLoaded ? (
                <span>Loading Payment System...</span>
              ) : pendingPayment ? (
                <>
                  <span className="font-bold mr-1">→</span>
                  <span>Lanjutkan Bayar</span>
                </>
              ) : isFreeAccess ? (
                <span>Dapatkan Paket Gratis</span>
              ) : (
                <span>Beli Paket Sekarang</span>
              )}
            </div>
          </button>

          <div className="text-xs text-center text-gray-500 mt-0.5">
            {pendingPayment 
              ? "Kamu memiliki transaksi yang belum selesai untuk paket ini" 
              : isFreeAccess
                ? "Gratis untuk pengguna daerah 3T • Akses instan • Tanpa ada biaya"
                : discountInfo && price
                  ? `Diskon referral ${discountInfo.discountPercentage}% berhasil diterapkan!`
                  : "Pembayaran aman • Akses instan • Tanpa ada biaya lain"}
          </div>
        </div>
      </div>
    </>
  );
};

export default EnhancedBundlingPurchaseButton;