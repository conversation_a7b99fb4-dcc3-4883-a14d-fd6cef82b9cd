import React, { useEffect, useState, ReactNode } from "react";
import styled from "styled-components";

import { Minus } from "./Icons/Minus";
import { Plus } from "./Icons/Plus";

interface ThemeProps {
  carouselColor: string;
  fontsm: string;
  fontmd: string;
  fontxl: string;
  fontxxl: string;
  bodyRgba: string;
}

const Container = styled.div<{ theme: ThemeProps }>`
  cursor: pointer;
  padding: 1rem 0.5rem;
  display: flex;
  flex-direction: column;
  border-bottom: 1px solid ${(props) => props.theme.carouselColor};
  margin: 3rem 0;

  @media (max-width: 48em) {
    margin: 2rem 0;
  }
`;

const Title = styled.div<{ theme: ThemeProps }>`
  font-size: ${(props) => props.theme.fontsm};
  display: flex;
  font-family: "Sora", sans-serif;
  font-weight: 600;
  line-height: 1;
  justify-content: space-between;
  align-items: center;
`;

const Reveal = styled.div<{ $clicked: boolean; theme: ThemeProps }>`
  display: ${(props) => (props.$clicked ? "inline-block" : "none")};
  margin-top: 1rem;
  color: ${(props) => `rgba(${props.theme.bodyRgba}, 0.6)`};
  font-size: ${(props) => props.theme.fontmd};
  font-weight: 450;
  line-height: 1.1rem;
`;

const Name = styled.div`
  font-weight: 600;
  display: flex;
  align-items: center;
`;

const Indicator = styled.span<{ theme: ThemeProps }>`
  font-size: ${(props) => props.theme.fontxxl};

  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    width: 1rem;
    height: auto;
    fill: ${(props) => props.theme.carouselColor};
  }

  @media (max-width: 48em) {
    font-size: ${(props) => props.theme.fontxl};
  }
`;

interface AccordionProps {
  title: string;
  children: ReactNode;
  ScrollTrigger: {
    refresh: () => void;
  };
}

const Accordion: React.FC<AccordionProps> = ({
  title,
  children,
  ScrollTrigger,
}) => {
  const [collapse, setCollapse] = useState<boolean>(false);

  useEffect(() => {
    ScrollTrigger.refresh();
  }, [collapse, ScrollTrigger]);

  return (
    <Container>
      <Title onClick={() => setCollapse(!collapse)}>
        <Name>
          <span>{title}</span>
        </Name>
        {collapse ? (
          <Indicator>
            <Minus />
          </Indicator>
        ) : (
          <Indicator>
            <Plus />
          </Indicator>
        )}
      </Title>
      <Reveal $clicked={collapse}>{children}</Reveal>
    </Container>
  );
};

export default Accordion;
