"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody
} from "@heroui/react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { SnbtGoalTrackerForm } from "@/components/snbt-goal-tracker/form";
import {
  SnbtGoalTrackerFormData,
  FormErrors,
  validateSnbtGoalTrackerForm,
  submitSnbtGoalTrackerForm
} from "@/components/snbt-goal-tracker/actions";
import { getUserData } from "./actions";

export default function SnbtSurveyPage() {
  // State variables
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);
  const [formData, setFormData] = useState<Partial<SnbtGoalTrackerFormData>>({});
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [userData, setUserData] = useState<{ name: string; email: string } | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Notification helper
  const showNotification = (message: string, type: "success" | "error" | "info") => {
    toast[type](message, {
      position: "top-center",
      autoClose: type === "success" ? 3000 : 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      className: "text-center",
      style: {
        minWidth: '300px',
        borderRadius: '10px',
        fontWeight: type === "success" ? 'bold' : 'normal'
      }
    });
  };

  // Load user data on component mount
  useEffect(() => {
    const loadUserData = async () => {
      setIsLoading(true);
      try {
        // Check if email is provided in URL params
        const urlParams = new URLSearchParams(window.location.search);
        const emailParam = urlParams.get('email');
        
        const data = await getUserData(emailParam || undefined);
        setUserData(data);
        
        if (data) {
          console.log("[SNBT Survey] User data loaded:", data);
        } else {
          console.log("[SNBT Survey] No user data available");
        }
      } catch (error) {
        console.error("[SNBT Survey] Error loading user data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
  }, []);

  // Clear error for a specific field
  const handleClearError = (field: keyof SnbtGoalTrackerFormData) => {
    setFormErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  };

  // Validate the form
  const validateForm = async (data: SnbtGoalTrackerFormData): Promise<FormErrors> => {
    return await validateSnbtGoalTrackerForm(data);
  };

  // Function handlers
  const handleFormDataChange = (data: Partial<SnbtGoalTrackerFormData>) => {
    setFormData(data);
  };

  // Handle form decline (user didn't participate in SNBT)
  const handleFormDecline = async () => {
    try {
      setIsSubmitting(true);

      // Create minimal form data with just name and email
      const minimalFormData: SnbtGoalTrackerFormData = {
        name: formData.name || userData?.name || "User",
        email: formData.email || userData?.email || "",
        penalaranUmum: 0,
        pengetahuanKuantitatif: 0,
        pengetahuanPemahamanUmum: 0,
        pemahamanBacaanMenulis: 0,
        literasiBahasaIndonesia: 0,
        literasiBahasaInggris: 0,
        penalaranMatematika: 0,
        passedSnbt: false,
        feltHelped: false,
        helpfulnessRating: 1,
        mostHelpfulAspect: "Tidak mengikuti SNBT atau latihan yang berkaitan",
        improvementSuggestions: "-",
        contactConsent: false,
        phoneNumber: "-",
      };

      console.log("[SNBT Survey] Submitting minimal data (user declined):", minimalFormData);
      const result = await submitSnbtGoalTrackerForm(minimalFormData);

      if (result.success) {
        // Show success screen
        setShowSuccessScreen(true);

        // Log successful submission
        console.log("[SNBT Survey] Form declined and submitted successfully");

        // Show success notification
        showNotification("Terima kasih atas respon Anda", "success");

        // Redirect after a delay
        setTimeout(() => {
          window.location.href = '/';
        }, 3000);
      } else {
        // Log submission failure
        console.error("[SNBT Survey] Form decline submission failed:", result.message);

        // Show error notification
        showNotification(result.message || "Gagal menyimpan data", "error");
      }
    } catch (error) {
      // Log submission error
      console.error("[SNBT Survey] Error submitting declined form:", error);

      // Show error notification
      const errorMessage = error instanceof Error
        ? error.message
        : "Terjadi kesalahan tidak diketahui";
      showNotification(errorMessage, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFormSubmit = async () => {
    if (!formData || Object.keys(formData).length === 0) {
      showNotification("Silakan isi formulir terlebih dahulu", "error");
      setFormErrors({ form: "Please fill out the form" });
      return;
    }

    // Ensure all numeric fields are actually numbers
    const processedFormData = {
      ...formData,
      penalaranUmum: formData.penalaranUmum !== undefined ? Number(formData.penalaranUmum) : undefined,
      pengetahuanKuantitatif: formData.pengetahuanKuantitatif !== undefined ? Number(formData.pengetahuanKuantitatif) : undefined,
      pengetahuanPemahamanUmum: formData.pengetahuanPemahamanUmum !== undefined ? Number(formData.pengetahuanPemahamanUmum) : undefined,
      pemahamanBacaanMenulis: formData.pemahamanBacaanMenulis !== undefined ? Number(formData.pemahamanBacaanMenulis) : undefined,
      literasiBahasaIndonesia: formData.literasiBahasaIndonesia !== undefined ? Number(formData.literasiBahasaIndonesia) : undefined,
      literasiBahasaInggris: formData.literasiBahasaInggris !== undefined ? Number(formData.literasiBahasaInggris) : undefined,
      penalaranMatematika: formData.penalaranMatematika !== undefined ? Number(formData.penalaranMatematika) : undefined,
      helpfulnessRating: formData.helpfulnessRating !== undefined ? Number(formData.helpfulnessRating) : undefined,
    };

    console.log("[SNBT Survey] Form data before validation:", processedFormData);

    // Check if all required fields are present
    const requiredFields: (keyof SnbtGoalTrackerFormData)[] = [
      'name', 'email', 'penalaranUmum', 'pengetahuanKuantitatif', 'pengetahuanPemahamanUmum',
      'pemahamanBacaanMenulis', 'literasiBahasaIndonesia', 'literasiBahasaInggris',
      'penalaranMatematika', 'helpfulnessRating'
    ];

    const missingFields = requiredFields.filter(field => {
      const value = processedFormData[field];
      return value === undefined || value === null || value === '';
    });

    if (missingFields.length > 0) {
      console.log("[SNBT Survey] Missing fields:", missingFields);
      showNotification(`Silakan lengkapi semua field yang diperlukan`, "error");
      const errors: FormErrors = {};
      missingFields.forEach(field => {
        errors[field] = `Field ${field} harus diisi`;
      });
      setFormErrors(errors);
      return;
    }

    // Cast to complete form data since we've verified required fields
    const completeFormData = processedFormData as SnbtGoalTrackerFormData;

    // Validate the form
    const errors = await validateForm(completeFormData);
    if (Object.keys(errors).length > 0) {
      console.log("[SNBT Survey] Validation errors:", errors);
      const errorMessages = Object.values(errors).join(", ");
      showNotification(`Kesalahan validasi: ${errorMessages}`, "error");
      setFormErrors(errors);
      return;
    }

    try {
      setIsSubmitting(true);

      console.log("[SNBT Survey] Submitting form data:", completeFormData);
      const result = await submitSnbtGoalTrackerForm(completeFormData);

      if (result.success) {
        // Show success screen
        setShowSuccessScreen(true);

        // Log successful submission
        console.log("[SNBT Survey] Form submitted successfully");

        // Show success notification
        showNotification("Data berhasil disimpan!", "success");

        // Redirect after a delay
        setTimeout(() => {
          window.location.href = '/';
        }, 5000);
      } else {
        // Log submission failure
        console.error("[SNBT Survey] Form submission failed:", result.message);

        // Show error notification
        showNotification(result.message || "Gagal menyimpan data", "error");
      }
    } catch (error) {
      // Log submission error
      console.error("[SNBT Survey] Error submitting form:", error);

      // Show error notification
      const errorMessage = error instanceof Error
        ? error.message
        : "Terjadi kesalahan tidak diketahui";
      showNotification(errorMessage, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50">
        <Spinner color="primary" size="lg" />
        <p className="mt-4 text-gray-600">Memuat...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50">
      <Card className="w-full max-w-3xl">
        <CardHeader className="flex items-center gap-2 sm:gap-3 bg-white p-3 sm:p-4">
          <img
            src="https://cdn.terang.ai/images/exams/SNBT_2025.png"
            alt="SNBT 2025 Logo"
            className="h-8 sm:h-10 w-auto"
          />
          <div>
            <h2 className="text-lg sm:text-xl font-medium">Survei Hasil SNBT 2025</h2>
            <p className="text-xs sm:text-sm text-gray-500 mt-1">Bantu kami meningkatkan layanan untuk persiapan SNBT</p>
          </div>
        </CardHeader>
        <CardBody className="p-0">
          {showSuccessScreen ? (
            <div className="flex flex-col items-center justify-center py-8 sm:py-10 px-3 sm:px-4 text-center">
              <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-green-100 flex items-center justify-center mb-4 sm:mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 sm:h-10 sm:w-10 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-xl sm:text-2xl font-bold mb-2">Terima Kasih!</h3>
              <p className="text-base sm:text-lg mb-4 sm:mb-6">Jawaban Anda telah berhasil disimpan.</p>
              <p className="text-sm sm:text-base text-gray-600 mb-6 sm:mb-8">
                Kami sangat menghargai waktu dan masukan Anda. Informasi ini akan membantu kami meningkatkan layanan untuk persiapan SNBT.
              </p>
              <div className="flex items-center justify-center gap-3 sm:gap-4">
                <img
                  src="https://cdn.terang.ai/images/exams/SNBT_2025.png"
                  alt="SNBT 2025 Logo"
                  className="h-10 sm:h-12 w-auto"
                />
                <span className="text-lg sm:text-xl font-medium">×</span>
                <img
                  src="https://cdn.terang.ai/images/logo/logo-terang-ai.svg"
                  alt="Terang AI Logo"
                  className="h-7 sm:h-8 w-auto"
                />
              </div>
              <p className="text-xs sm:text-sm text-gray-500 mt-6 sm:mt-8">
                Halaman akan dialihkan dalam beberapa detik...
              </p>
            </div>
          ) : (
            <div className="flex flex-col bg-gray-50 rounded-lg">
              <SnbtGoalTrackerForm
                clearError={(field: string) => handleClearError(field as keyof SnbtGoalTrackerFormData)}
                formErrors={formErrors}
                initialData={formData}
                onFormDataChange={handleFormDataChange}
                onComplete={handleFormSubmit}
                isSubmitting={isSubmitting}
                userName={userData?.name || ""}
                userEmail={userData?.email || ""}
              />
            </div>
          )}
        </CardBody>
      </Card>
      <ToastContainer />
    </div>
  );
}
