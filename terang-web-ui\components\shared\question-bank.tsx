"use client";

import React, { useRef, useEffect, useState } from "react";
import { <PERSON>, CardHeader, CardBody } from "@heroui/react";

import { ExitExam } from "./buttons/exit-exam";

interface QuestionBankProps {
  totalQuestions: number;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  flaggedQuestions: { [key: string]: boolean };
  selectedOptions: { [key: string]: string };
  isTimeUp: boolean;
  onExitExam: () => void;
  isLoading: boolean;
  questionIds: string[];
}

export const QuestionBank: React.FC<QuestionBankProps> = ({
  totalQuestions,
  currentPage,
  setCurrentPage,
  flaggedQuestions,
  selectedOptions,
  isTimeUp,
  onExitExam,
  isLoading,
  questionIds,
}) => {
  const [showScrollbar, setShowScrollbar] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkOverflow = () => {
      if (scrollContainerRef.current) {
        const isOverflowing =
          scrollContainerRef.current.scrollHeight >
          scrollContainerRef.current.clientHeight;

        setShowScrollbar(isOverflowing);
      }
    };

    checkOverflow();
    window.addEventListener("resize", checkOverflow);

    return () => window.removeEventListener("resize", checkOverflow);
  }, [questionIds]);

  // Calculate insights
  const answeredCount = Object.keys(selectedOptions).length;
  const unansweredCount = totalQuestions - answeredCount;
  const flaggedCount = Object.keys(flaggedQuestions).length;

  return (
    <Card
      className="w-full py-4"
      style={{ fontFamily: "'Nunito', sans-serif" }}
    >
      <CardHeader className="flex justify-center items-center pb-0 pt-2 px-4">
        <h4 className="font-bold text-large text-center">Nomor Soal</h4>
      </CardHeader>
      <CardBody className="overflow-visible py-2 flex flex-col items-center">
        <div
          ref={scrollContainerRef}
          className={`h-[400px] overflow-y-auto ${showScrollbar ? "pr-2" : ""} mb-4`}
          style={{
            scrollbarWidth: showScrollbar ? "thin" : "none",
            scrollbarColor: showScrollbar
              ? "#9CA3AF #E5E7EB"
              : "transparent transparent",
          }}
        >
          <div className="grid grid-cols-5 lg:grid-cols-4 gap-2 min-h-[calc(400px+1px)]">
            {questionIds.map((questionId, index) => {
              const isFlagged = flaggedQuestions[questionId];
              const hasSelectedOption = !!selectedOptions[questionId];
              const isCurrent = currentPage === index;

              return (
                <button
                  key={questionId}
                  aria-label={`Question ${index + 1}`}
                  aria-pressed={isCurrent}
                  className={`
                    relative w-[48px] h-[48px] lg:w-[48px] lg:h-[48px] rounded-lg cursor-pointer
                    ${isCurrent ? "bg-blue-500 text-white" : "bg-gray-200 text-black"}
                    ${hasSelectedOption ? "flex flex-col justify-start" : "flex justify-center"}
                    items-center
                    focus:outline-none
                    transition-colors duration-200
                    hover:bg-gray-300
                  `}
                  disabled={isTimeUp}
                  onClick={() => setCurrentPage(index)}
                >
                  <span className="text-base font-semibold">{index + 1}</span>

                  {isFlagged && (
                    <span className="absolute top-0.5 right-0.5 w-3 h-3 bg-yellow-500 rounded-full" />
                  )}

                  {hasSelectedOption && (
                    <div className="absolute bottom-0 left-0 w-full h-1/2 bg-green-500 rounded-b-lg" />
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Insights Section */}
        <div className="grid px-6 grid-cols-2 gap-4 w-full mt-4 text-sm">
          <div className="flex items-center">
            <span className="w-3 h-3 bg-green-500 rounded-full mr-1" /> Terisi:{" "}
            {answeredCount}
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-gray-200 rounded-full mr-1" /> Belum
            terisi: {unansweredCount}
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-yellow-500 rounded-full mr-1" /> Ragu:{" "}
            {flaggedCount}
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-blue-500 rounded-full mr-1" /> Lagi
            dikerjakan
          </div>
        </div>

        <div className="flex justify-center w-full mt-4">
          <ExitExam
            isLoading={isLoading}
            isTimeUp={isTimeUp}
            onExitExam={onExitExam}
          />
        </div>
      </CardBody>
    </Card>
  );
};
