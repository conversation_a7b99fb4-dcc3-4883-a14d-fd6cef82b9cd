// Type definitions
export interface PassingGradeEntry {
    nama: string;
    score: number;
    question_count: number;
  }
  
  export interface ProgramThreshold {
    program: string;
    min_score: number;
  }
  
  export interface CategoryGrading {
    category: string;
    passing_grade: PassingGradeEntry[];
    program_thresholds: ProgramThreshold[];
  }
  
  export interface QuestionCount {
    CategoryID: string;
    Category: string;
    Subject: string;
    QuestionCount: number;
  }
  
  export interface ExamGradingResponse {
    grading_info: CategoryGrading[];
    question_counts: QuestionCount[];
  }