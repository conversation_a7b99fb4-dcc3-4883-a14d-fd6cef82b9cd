// This file contains type definitions for your data.
// It describes the shape of the data, and what data type each property should accept.
// For simplicity of teaching, we're manually defining these types.
// However, these types are generated automatically if you're using an ORM such as Prisma.
export type PostUser = {
  username: string;
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  picture: string;
};

export interface UserGoogle {
  id?: string;
  email: string;
  name: string;
  firstname?: string; // Add this line
  image?: string;
  emailVerified?: boolean;
  lastname?: string;
  // Add any other properties that are part of the User type
}
