import React, { useState, useEffect, useCallback } from "react";
import { Chip } from "@heroui/react";

interface Interest {
  value: string;
  label: string;
  icon: string;
  category: string;
}

// Enhanced interests list with icons and categories
const interestsList: Interest[] = [
  // Career Development
  { value: "cpns", label: "Tes CPNS", icon: "🏛️", category: "Karir" },
  { value: "lpdp", label: "Beasiswa LPDP", icon: "🎓", category: "Karir" },
  { value: "ptn", label: "Masuk PTN", icon: "🏫", category: "Kari<PERSON>" },
  { value: "kerja", label: "<PERSON><PERSON><PERSON>", icon: "💼", category: "Karir" },
  { value: "skill", label: "Peningkatan Skill", icon: "🔧", category: "Karir" },
  { value: "leadership", label: "Kepemimpinan", icon: "👑", category: "Karir" },
  { value: "internasional", label: "Pengalaman Internasional", icon: "🌍", category: "<PERSON><PERSON><PERSON>" },
  { value: "public_speaking", label: "Public Speaking", icon: "🎤", category: "<PERSON>ri<PERSON>" },
  
  // Business & Economy
  { value: "entrepreneur", label: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", icon: "🚀", category: "Bisnis" },
  { value: "keuangan", label: "Manajemen Keuangan", icon: "💰", category: "Bisnis" },
  { value: "investasi", label: "Investasi", icon: "📈", category: "Bisnis" },
  { value: "digital", label: "Pemasaran Digital", icon: "📱", category: "Bisnis" },
  { value: "agribisnis", label: "Agribisnis", icon: "🌾", category: "Bisnis" },
  { value: "ecommerce", label: "E-Commerce", icon: "🛒", category: "Bisnis" },
  
  // Technology & Innovation
  { value: "teknologi", label: "Teknologi", icon: "💻", category: "Teknologi" },
  { value: "data", label: "Analisis Data", icon: "📊", category: "Teknologi" },
  { value: "ai", label: "Kecerdasan Buatan", icon: "🤖", category: "Teknologi" },
  { value: "programming", label: "Pemrograman", icon: "👨‍💻", category: "Teknologi" },
  { value: "cybersecurity", label: "Keamanan Siber", icon: "🔒", category: "Teknologi" },
  { value: "blockchain", label: "Blockchain", icon: "⛓️", category: "Teknologi" },
  { value: "iot", label: "Internet of Things", icon: "📡", category: "Teknologi" },
  
  // Arts & Humanities
  { value: "design", label: "Desain", icon: "🎨", category: "Seni & Budaya" },
  { value: "seni", label: "Seni", icon: "🖼️", category: "Seni & Budaya" },
  { value: "musik", label: "Musik", icon: "🎵", category: "Seni & Budaya" },
  { value: "writing", label: "Penulisan", icon: "✍️", category: "Seni & Budaya" },
  { value: "fotografi", label: "Fotografi", icon: "📷", category: "Seni & Budaya" },
  { value: "bahasa", label: "Bahasa Asing", icon: "🗣️", category: "Seni & Budaya" },
  
  // Health & Well-being
  { value: "olahraga", label: "Olahraga", icon: "⚽", category: "Kesehatan" },
  { value: "kesehatan", label: "Kesehatan", icon: "🏥", category: "Kesehatan" },
  { value: "nutrition", label: "Nutrisi", icon: "🥗", category: "Kesehatan" },
  { value: "yoga", label: "Yoga & Meditasi", icon: "🧘", category: "Kesehatan" },
  { value: "mental_health", label: "Kesehatan Mental", icon: "🧠", category: "Kesehatan" },
  
  // Society & Environment
  { value: "lingkungan", label: "Lingkungan", icon: "🌿", category: "Sosial & Lingkungan" },
  { value: "sosial", label: "Kegiatan Sosial", icon: "🤝", category: "Sosial & Lingkungan" },
  { value: "volunteer", label: "Volunteer", icon: "❤️", category: "Sosial & Lingkungan" },
  { value: "hukum", label: "Hukum", icon: "⚖️", category: "Sosial & Lingkungan" },
  { value: "politik", label: "Politik", icon: "🗳️", category: "Sosial & Lingkungan" },
  { value: "pendidikan", label: "Pendidikan", icon: "📚", category: "Sosial & Lingkungan" },
  { value: "penelitian", label: "Penelitian", icon: "🔬", category: "Sosial & Lingkungan" },
];

// Group interests by category
const groupedInterests = interestsList.reduce((acc, interest) => {
  if (!acc[interest.category]) {
    acc[interest.category] = [];
  }
  acc[interest.category].push(interest);
  return acc;
}, {} as Record<string, Interest[]>);

// Get category order for consistent display
const categoryOrder = [
  "Karir", 
  "Bisnis", 
  "Teknologi", 
  "Seni & Budaya", 
  "Kesehatan", 
  "Sosial & Lingkungan"
];

interface UserInterestsProps {
  onInterestsChange: (interests: string[]) => void;
  required?: boolean;
  initialInterests?: string[];
}

export const UserInterests: React.FC<UserInterestsProps> = ({
  onInterestsChange,
  required = false,
  initialInterests = [],
}) => {
  const [selectedInterests, setSelectedInterests] = useState<string[]>(initialInterests);
  const [error, setError] = useState<string | null>(null);
  const [expandedCategories, setExpandedCategories] = useState<string[]>(["Karir"]);
  
  // Create a stable callback reference that won't change on every render
  const stableOnInterestsChange = useCallback((interests: string[]) => {
    onInterestsChange(interests);
  }, [onInterestsChange]);
  
  // Effect for validation - doesn't trigger the parent callback
  useEffect(() => {
    if (required && selectedInterests.length === 0) {
      setError("Mohon pilih minimal satu minat.");
    } else {
      setError(null);
    }
  }, [selectedInterests, required]);
  
  // Separate effect for parent notification that only runs when necessary
  useEffect(() => {
    // Initial render: don't call the parent's function if we're using initialInterests
    if (JSON.stringify(selectedInterests) !== JSON.stringify(initialInterests)) {
      stableOnInterestsChange(selectedInterests);
    }
  }, [selectedInterests, initialInterests, stableOnInterestsChange]);

  const handleInterestChange = (interest: string) => {
    setSelectedInterests((prevInterests) => {
      return prevInterests.includes(interest)
        ? prevInterests.filter((i) => i !== interest)
        : [...prevInterests, interest];
    });
  };

  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => 
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  return (
    <div>
      <p className="mb-2 font-semibold">
        Minat kamu (pilih semua yang sesuai):
        {required && <span className="text-red-500 ml-1">*</span>}
      </p>
      
      {/* Selected interests at the top */}
      {selectedInterests.length > 0 && (
        <div className="mb-4 p-3 bg-primary-50 rounded-lg border border-primary-200">
          <p className="text-sm font-medium text-gray-700 mb-2">Minat yang dipilih:</p>
          <div className="flex flex-wrap gap-2">
            {selectedInterests.map(interest => {
              const interestData = interestsList.find(i => i.value === interest);
              return (
                <Chip 
                  key={interest}
                  color="primary"
                  variant="flat"
                  onClose={() => handleInterestChange(interest)}
                >
                  <span className="mr-1">{interestData?.icon}</span>
                  {interestData?.label}
                </Chip>
              );
            })}
          </div>
        </div>
      )}
      
      {/* Interests grouped by category */}
      <div className="space-y-6">
        {categoryOrder.map(category => (
          <div key={category} className="border rounded-lg overflow-hidden">
            <button 
              className="w-full bg-gray-50 p-3 flex items-center justify-between cursor-pointer"
              onClick={() => toggleCategory(category)}
              type="button"
            >
              <h4 className="font-medium">{category}</h4>
              <span className="text-lg">
                {expandedCategories.includes(category) ? "▼" : "►"}
              </span>
            </button>
            
            {expandedCategories.includes(category) && (
              <div className="p-3 grid grid-cols-2 md:grid-cols-3 gap-2">
                {groupedInterests[category]?.map((interest) => (
                  <button 
                    key={interest.value}
                    type="button"
                    className={`
                      p-2 rounded-lg border cursor-pointer transition-all flex items-center
                      ${selectedInterests.includes(interest.value) 
                        ? "border-primary-500 bg-primary-50" 
                        : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"}
                    `}
                    onClick={() => handleInterestChange(interest.value)}
                  >
                    <span className="text-xl mr-2">{interest.icon}</span>
                    <span className="text-sm">{interest.label}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
      
      {error && (
        <p className="text-red-500 mt-2 text-sm">{error}</p>
      )}
      
      <div className="mt-4 text-xs text-gray-500">
        <p>Pilih minat yang sesuai dengan passion kamu untuk mendapatkan rekomendasi belajar yang lebih relevan.</p>
      </div>
    </div>
  );
};