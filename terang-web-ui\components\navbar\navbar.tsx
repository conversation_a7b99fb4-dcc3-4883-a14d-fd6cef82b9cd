import { Navbar, NavbarContent } from "@heroui/react";
import React, {useState,useEffect} from "react";

import { BurguerButton } from "./burguer-button";
import { UserDropdown } from "./user-dropdown";

import Link from "next/link";
import { checkAccountIsVerified } from "@/app/lib/auth/checkAccount";
import { useRouter } from "next/navigation";
import UnverifiedNavbar from "./unverified-navbar"
import { AiChat02Icon } from "hugeicons-react";

interface Props {
  children: React.ReactNode;
}

export const NavbarWrapper = ({ children }: Props) => {
  const [unverifiedAccount,setUnverifiedAccount] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const checkAccount = async () => {
      try {
        const ver_res =  await checkAccountIsVerified();
        if (!ver_res)
          setUnverifiedAccount(true)
        setIsLoaded(true);
      } catch (error) {
        if (typeof error === 'object' && error !== null && 'redirect' in error && 'url' in error) {
          router.push((error as { url: string }).url);
        } else {
          console.log(error)
          router.push('/error-page'); // Redirect to an error page if check fails
        }
      }
    };

    checkAccount();
  }, [router]);


  const Comment01Icon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      color={"#000000"}
      fill={"none"}
      height={24}
      viewBox="0 0 24 24"
      width={24}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.85 1.75H14.15C18.2041 1.75 20.2311 1.75 21.4906 3.01407C22.75 4.27813 22.75 6.31261 22.75 10.3816V10.9211C22.75 14.99 22.75 17.0245 21.4906 18.2886C20.2311 19.5526 18.2041 19.5526 14.15 19.5526C13.5475 19.5661 13.0676 19.6121 12.5962 19.7199C11.5124 19.9703 10.4961 20.4783 9.49881 20.9768L9.4985 20.977C9.31046 21.071 9.12311 21.1646 8.93611 21.2562C7.25633 22.0782 6.41645 22.4893 5.88937 22.1045C5.38644 21.7285 5.36431 21.0194 5.5044 20.2351C5.56226 19.9112 5.5912 19.7492 5.52074 19.6474C5.45029 19.5457 5.28808 19.5158 4.96365 19.4558C3.90736 19.2607 3.09038 18.8716 2.50944 18.2886C1.25 17.0245 1.25 14.99 1.25 10.9211V10.3816C1.25 6.31261 1.25 4.27813 2.50944 3.01407C3.76888 1.75 5.79592 1.75 9.85 1.75Z"
        fill="currentColor"
        opacity="0.4"
      />
      <path
        clipRule="evenodd"
        d="M16 14.25C16.4142 14.25 16.75 13.9142 16.75 13.5C16.75 13.0858 16.4142 12.75 16 12.75H8C7.58579 12.75 7.25 13.0858 7.25 13.5C7.25 13.9142 7.58579 14.25 8 14.25H16ZM12 9.25C12.4142 9.25 12.75 8.91421 12.75 8.5C12.75 8.08579 12.4142 7.75 12 7.75H8C7.58579 7.75 7.25 8.08579 7.25 8.5C7.25 8.91421 7.58579 9.25 8 9.25H12Z"
        fill="currentColor"
        fillRule="evenodd"
      />
    </svg>
  );

  return (
    <div className="relative flex flex-col flex-1 overflow-y-auto overflow-x-hidden">
      <Navbar
        isBordered
        className="w-full"
        classNames={{
          wrapper: "w-full max-w-full",
        }}
      >
        <NavbarContent>
          <BurguerButton />
        </NavbarContent>
        <NavbarContent className="w-full" />
        {/* <NavbarContent className="w-full max-md:hidden">
          <Input
            isClearable
            className="w-full"
            classNames={{
              input: "w-full",
              mainWrapper: "w-full",
            }}
            placeholder="Search..."
            startContent={<SearchIcon />}
          />
        </NavbarContent> */}
        <NavbarContent
          className="w-fit data-[justify=end]:flex-grow-0"
          justify="end"
        >
          <div className="flex items-center gap-2 max-md:hidden">
            <Link href={"/feedback"} className="flex items-center gap-2 max-md:hidden">
              <Comment01Icon />
              <span>Feedback?</span>
            </Link>
          </div>

          {/* <NotificationsDropdown /> */}

          <div className="flex items-center gap-2 max-md:hidden">
            <Link href={`/chat-ai/navbar/${Date.now()}`} className="flex items-center gap-2 max-md:hidden">
              <AiChat02Icon />
            </Link>
          </div>

          {/* <Link
            href="https://github.com/Siumauricio/nextui-dashboard-template"
            target={"_blank"}
          >
            <GithubIcon />
          </Link> */}
          <NavbarContent>
            <UserDropdown />
          </NavbarContent>
        </NavbarContent>
      </Navbar>
      { unverifiedAccount && <UnverifiedNavbar></UnverifiedNavbar>}
      {children}
    </div>
  );
};
