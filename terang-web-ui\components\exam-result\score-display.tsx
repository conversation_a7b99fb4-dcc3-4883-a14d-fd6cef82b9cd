import React, { useState, useEffect } from "react";
import {
  Progress,
  Modal,
  ModalContent,
  <PERSON>dal<PERSON><PERSON>,
  ModalFooter,
  But<PERSON>
} from "@heroui/react";
import { 
  fetchExamSubjectDetails
} from "./actions";

// Enhanced types
interface SubjectInfo {
  id: string;
  name: string;
  key: string;
}

interface PassingGrade {
  nama: string;
  score: number;
}

interface ProgramThreshold {
  program: string;
  min_score: number;
}

interface ExamConfig {
  passing_grade?: PassingGrade[] | {
    passing_grade: PassingGrade[];
    program_thresholds?: ProgramThreshold[];
  };
  program_thresholds?: ProgramThreshold[];
}

interface ScoreDisplayProps {
  score: number | null;
  totalQuestions: number;
  examScores: {
    id?: string;
    session_id?: string;
    total_questions?: number;
    correct_answers?: number;
    score?: number;
    accuracy?: number;
    metadata_scores?: string;
    created_at?: string;
    modified_at?: string;
  };
  sessionId: string;
  subject?: string;
  sessionData?: any;
}

const ScoreDisplay: React.FC<ScoreDisplayProps> = ({
  score,
  totalQuestions,
  examScores,
  sessionId,
  subject,
  sessionData,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [passingGrades, setPassingGrades] = useState<Record<string, number>>({});
  const [programThresholds, setProgramThresholds] = useState<ProgramThreshold[]>([]);
  const [metadataScores, setMetadataScores] = useState<Record<string, number>>({});
  const [subjectInfos, setSubjectInfos] = useState<SubjectInfo[]>([]);
  const [examSubjects, setExamSubjects] = useState<SubjectInfo[]>([]);
  const [totalScore, setTotalScore] = useState<number>(0);
  const [examType, setExamType] = useState<string>("");

  useEffect(() => {
    const fetchExamData = async () => {
      try {
        if (sessionId) {
          // Fetch exam configuration and subject details from server action
          const examDetails: { 
            examType: string; 
            subjects: SubjectInfo[]; 
            passingGrade: any; 
            questions: any;
          } = await fetchExamSubjectDetails(sessionId);
          
          // Store the exam type
          setExamType(examDetails.examType?.toLowerCase() || "");
          
          // Store the subjects from the exam config
          setExamSubjects(examDetails.subjects || []);
          
          // Convert passing grades to a lookup object
          if (examDetails.passingGrade) {
            // Check if passingGrade is already an array or if it's an object with nested structure
            let gradesArray: PassingGrade[] = [];
            let thresholds: ProgramThreshold[] = [];
            
            // Handle both possible structures
            if (Array.isArray(examDetails.passingGrade)) {
              // Direct array of passing grades
              gradesArray = examDetails.passingGrade;
            } else if (typeof examDetails.passingGrade === 'object') {
              // Object with nested structure
              if (examDetails.passingGrade.passing_grade && Array.isArray(examDetails.passingGrade.passing_grade)) {
                gradesArray = examDetails.passingGrade.passing_grade;
              }
              
              // Extract program thresholds if they exist
              if (examDetails.passingGrade.program_thresholds && 
                  Array.isArray(examDetails.passingGrade.program_thresholds)) {
                thresholds = examDetails.passingGrade.program_thresholds;
              }
            }
            
            // Process passing grades
            const gradesMap = gradesArray.reduce(
              (acc: Record<string, number>, grade: PassingGrade) => {
                acc[grade.nama] = grade.score;
                return acc;
              }, 
              {}
            );
            setPassingGrades(gradesMap);
            
            // Set program thresholds if they exist
            if (thresholds.length > 0) {
              setProgramThresholds(thresholds);
            }
          }
          
          console.log(examScores)
          // Process metadata scores if available
          if (examScores?.metadata_scores) {
            try {
              const parsedScores = JSON.parse(examScores.metadata_scores);
              setMetadataScores(parsedScores);
              
              // Store the exam type from metadata if available
              if (parsedScores.exam_type) {
                setExamType(parsedScores.exam_type.toLowerCase());
              }
              
              // Extract subject keys from metadata_scores
              const subjects: SubjectInfo[] = [];
              const scoreKeys = Object.keys(parsedScores).filter(key => 
                !key.includes('_total') && 
                !key.includes('_score') && 
                typeof parsedScores[key] === 'number'
              );
              
              // Calculate total score across all subjects
              let calculatedTotalScore = 0;
              let utbkSubjectCount = 0;
              let utbkTotalPoints = 0;
              const isUtbk = parsedScores.exam_type?.toLowerCase() === 'utbk';
              
              scoreKeys.forEach(key => {
                // For each subject key, check if there's an associated _total field
                if (parsedScores[`${key}_total`] !== undefined) {
                  // Calculate subject points based on exam type
                  const subjectScore = parsedScores[key] || 0;
                  
                  // Different calculation based on exam type
                  if (isUtbk) {
                    const points = calculateUtbkPoints(subjectScore, parsedScores[`${key}_total`]);
                    utbkTotalPoints += points;
                    utbkSubjectCount++;
                  } else {
                    // FIXED: Skip kepribadian for non-UTBK exams in total score calculation
                    if (key.toLowerCase() === 'kepribadian') {
                      // Still add the subject to the display list, but don't add to total score
                    } else {
                      const points = calculatePoints(subjectScore);
                      calculatedTotalScore += points;
                    }
                  }
                  
                  // Try to find the subject info from exam subjects
                  const matchingSubject = examDetails.subjects?.find(s => 
                    s.key?.toLowerCase() === key.toLowerCase() || 
                    s.id?.toLowerCase() === key.toLowerCase()
                  );

                  if (matchingSubject) {
                    // Use the official name from exam configuration
                    subjects.push({
                      id: matchingSubject.id,
                      key,
                      name: matchingSubject.name
                    });
                  } else {
                    // Fallback to uppercase key if no match found
                    subjects.push({
                      id: key,
                      key,
                      name: key.toUpperCase()
                    });
                  }
                }
              });
              console.log(utbkTotalPoints)
              console.log(utbkSubjectCount)
              // For UTBK: Calculate the average of all subjects
              if (isUtbk && utbkSubjectCount > 0) {
                calculatedTotalScore = Math.ceil(utbkTotalPoints / utbkSubjectCount); // Changed from Math.round to Math.ceil
              } else {
                calculatedTotalScore = calculatedTotalScore;
              }
              
              setTotalScore(calculatedTotalScore);
              setSubjectInfos(subjects);
            } catch (error) {
              console.error("Error parsing metadata scores:", error);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching exam data:", error);
      }
    };

    fetchExamData();
  }, [sessionId, examScores?.metadata_scores]);

  const getEmoji = (percentage: number) => {
    if (percentage >= 90) return "🎉";
    if (percentage >= 70) return "😊";
    if (percentage >= 50) return "🙂";
    return "😐";
  };

  const getColor = (percentage: number) => {
    if (percentage >= 90) return "success";
    if (percentage >= 70) return "primary";
    if (percentage >= 50) return "warning";
    return "danger";
  };

  const getTextColorClass = (percentage: number) => {
    if (percentage >= 90) return "text-green-500";
    if (percentage >= 70) return "text-blue-500";
    if (percentage >= 50) return "text-yellow-500";
    return "text-red-500";
  };

  const formatScore = (score: number | undefined, fixed = 2) => {
    return score !== undefined ? Number(score).toFixed(fixed) : "N/A";
  };

  const calculatePercentage = (score: number | undefined, total: number | undefined) => {
    if (score === undefined || total === undefined || total === 0) {
      return 0;
    }
    return (score / total) * 100;
  };

  // Standard point calculation (for CPNS/LPDP)
  const calculatePoints = (score: number, multiplier = 5) => {
    return score * multiplier;
  };
  
  // UTBK specific calculation: (correct/total) * 1000
  const calculateUtbkPoints = (score: number, total: number) => {
    if (!total) return 0;
    return Math.ceil((score / total) * 1000); // Changed from Math.round to Math.ceil
  };  

  // Find the program eligibility based on total score
  const getProgramEligibility = () => {
    if (!programThresholds || programThresholds.length === 0) {
      return null;
    }
    
    // Sort thresholds from highest to lowest
    const sortedThresholds = [...programThresholds].sort((a, b) => 
      b.min_score - a.min_score
    );
    
    // Find eligible programs
    const eligiblePrograms = sortedThresholds.filter(
      program => totalScore >= program.min_score
    );
    
    return {
      sortedThresholds,
      eligiblePrograms
    };
  };

  const scorePercentage = score !== null ? (score / totalQuestions) * 100 : 0;
  const scoreTextColorClass = getTextColorClass(scorePercentage);
  const programEligibility = getProgramEligibility();

  // Complete improved score section rendering with clearer scoring explanation
  const renderScoreSection = (subjectInfo: SubjectInfo) => {
    const { key, name } = subjectInfo;
    
    // Get the score and total for this subject
    const subjectScore = metadataScores[key] || 0;
    const subjectTotal = metadataScores[`${key}_total`] || 0;
    
    // Skip rendering if no data is available
    if (subjectScore === 0 && subjectTotal === 0) {
      return null;
    }
    
    // Calculate point score based on exam type
    const isUtbk = examType === 'utbk';
    const points = isUtbk
      ? calculateUtbkPoints(subjectScore, subjectTotal)
      : calculatePoints(subjectScore);
    
    // Find passing grade using smarter matching
    let passingGrade;
    
    // Only look for passing grades if program thresholds don't exist
    if (!programThresholds || programThresholds.length === 0) {
      // Generate normalized versions for better matching
      const normalizedKey = key.toLowerCase().replace(/[_\s-]/g, '');
      
      // Try these matching strategies in order:
      
      // 1. Direct match by name
      if (passingGrades[name]) {
        passingGrade = passingGrades[name];
      } 
      // 2. Match against exam subjects from configuration
      else if (examSubjects && examSubjects.length > 0) {
        // Find matching subject in exam configuration
        const matchingSubject = examSubjects.find(s => 
          s.key?.toLowerCase() === key.toLowerCase() || 
          s.id?.toLowerCase() === key.toLowerCase()
        );
        
        if (matchingSubject && passingGrades[matchingSubject.name]) {
          passingGrade = passingGrades[matchingSubject.name];
        }
      }
      
      // 3. Try more advanced fuzzy matching if still not found
      if (!passingGrade) {
        // Generate all possible ways to match subject keys with grade names
        const matchStrategies = [
          // Strategy 1: Check if the subject key is an acronym of the grade name
          (gradeName: string, subjectKey: string) => {
            const words = gradeName.split(/\s+/);
            // Get first letter of each word and join them
            const acronym = words.map(word => word[0].toLowerCase()).join('');
            return acronym === subjectKey.toLowerCase() ? 100 : 0;
          },
          
          // Strategy 2: Check if each letter in the subject key appears in order in the grade name
          (gradeName: string, subjectKey: string) => {
            const normalizedGradeName = gradeName.toLowerCase();
            const keyLetters = subjectKey.toLowerCase().split('');
            
            let currentPos = 0;
            let foundCount = 0;
            
            for (const letter of keyLetters) {
              const pos = normalizedGradeName.indexOf(letter, currentPos);
              if (pos >= currentPos) {
                foundCount++;
                currentPos = pos + 1;
              }
            }
            
            return (foundCount / keyLetters.length) * 100;
          },
          
          // Strategy 3: Check if the initial letters of any consecutive words match the subject key
          (gradeName: string, subjectKey: string) => {
            const words = gradeName.split(/\s+/);
            if (words.length < subjectKey.length) return 0;
            
            // Check all possible consecutive word combinations
            for (let i = 0; i <= words.length - subjectKey.length; i++) {
              const subset = words.slice(i, i + subjectKey.length);
              const initials = subset.map(word => word[0].toLowerCase()).join('');
              if (initials === subjectKey.toLowerCase()) {
                return 100;
              }
            }
            
            return 0;
          },
          
          // Strategy 4: Check if the first N letters of the grade name match the subject key
          (gradeName: string, subjectKey: string) => {
            const normalizedGradeName = gradeName.toLowerCase().replace(/\s+/g, '');
            return normalizedGradeName.startsWith(subjectKey.toLowerCase()) ? 90 : 0;
          },
          
          // Strategy 5: Check if any word in the grade name starts with the subject key
          (gradeName: string, subjectKey: string) => {
            const words = gradeName.split(/\s+/);
            for (const word of words) {
              if (word.toLowerCase().startsWith(subjectKey.toLowerCase())) {
                return 85;
              }
            }
            return 0;
          }
        ];
        
        // Find the best matching grade name across all strategies
        let bestMatch = '';
        let bestScore = 0;
        
        Object.keys(passingGrades).forEach(gradeName => {
          // Try each strategy and get the best score
          let highestStrategyScore = 0;
          
          for (const strategy of matchStrategies) {
            const score = strategy(gradeName, key);
            highestStrategyScore = Math.max(highestStrategyScore, score);
          }
          
          // If this grade name has a better score, update the best match
          if (highestStrategyScore > bestScore) {
            bestScore = highestStrategyScore;
            bestMatch = gradeName;
          }
        });
        
        // Only use if the match is reasonably confident
        if (bestScore > 50 && bestMatch) {
          passingGrade = passingGrades[bestMatch];
        }
      }
    }
    
    const achievedPercentage = calculatePercentage(subjectScore, subjectTotal);
    const passStatus = passingGrade ? points >= passingGrade : false;
    const scoreDifference = passingGrade ? points - passingGrade : 0;
    
    return (
      <div key={key} className="mb-6">
        <p className="font-semibold mb-2">{name.replace(/_/g, ' ')}</p>
        <Progress 
          size="md"
          radius="sm"
          classNames={{
            base: "max-w-full",
            track: "drop-shadow-md border",
            indicator: "bg-gradient-to-r",
          }}
          color={getColor(achievedPercentage)}
          value={achievedPercentage}
          className="mb-2"
        />
        
        {/* Improved score display section */}
        <div className="flex flex-col gap-1">
          {/* First row: Raw score and percentage */}
          <div className="flex justify-between text-sm text-gray-600">
            <div>
              <span className="font-medium">Jawaban benar:</span> {subjectScore} / {subjectTotal} 
              <span className="text-xs ml-1">({formatScore(achievedPercentage)}%)</span>
            </div>
            
            {/* Points calculation shown clearly - different for UTBK vs others */}
            <div className="font-medium">
              Skor: <span className="text-blue-600">{points}</span> 
              <span className="text-xs text-gray-500 ml-1">
                {isUtbk 
                  ? `(${formatScore(achievedPercentage, 1)}% × 1000)` 
                  : `(${subjectScore} × 5)`
                }
              </span>
            </div>
          </div>
          
          {/* Second row: Only show passing grade information if program thresholds don't exist */}
          {(!programThresholds || programThresholds.length === 0) && passingGrade && (
            <div className={`flex items-center ${passStatus ? "text-green-500" : "text-red-500"} font-medium`}>
              {passStatus ? (
                <div className="flex items-center">
                  <span className="mr-1 text-lg">✓</span>
                  <span>Lulus</span>
                  <span className="mx-1 text-gray-400">|</span>
                  <span className="text-gray-600 text-sm">
                    Skor kamu {points} poin, 
                    <span className="font-medium text-green-500 ml-1">
                      {scoreDifference} di atas passing grade ({passingGrade})
                    </span>
                  </span>
                </div>
              ) : (
                <div className="flex items-center">
                  <span className="mr-1 text-lg">✗</span>
                  <span>Belum Lulus</span>
                  <span className="mx-1 text-gray-400">|</span>
                  <span className="text-gray-600 text-sm">
                    Perlu tambahan 
                    <span className="font-medium text-red-500 mx-1">
                      {Math.abs(scoreDifference)} poin
                    </span>
                    untuk mencapai passing grade ({passingGrade})
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render program eligibility section
  const renderProgramEligibility = () => {
    if (!programEligibility || programEligibility.sortedThresholds.length === 0) {
      return null;
    }

    const { sortedThresholds, eligiblePrograms } = programEligibility;
    
    return (
      <div className="mt-8 border-t pt-6">
        <h3 className="text-xl font-bold mb-4 text-center">Kelayakan Program</h3>
        
        <div className="mb-6 bg-blue-50 border border-blue-100 rounded-lg p-4 text-center">
          <p className="font-medium text-blue-800 mb-1">Total Skor Kamu</p>
          <p className="text-4xl font-bold text-blue-600">{totalScore}</p>
          <p className="text-sm text-blue-600">poin</p>
        </div>
        
        {eligiblePrograms.length > 0 ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <div className="bg-green-100 rounded-full p-2 mr-3 mt-1">
                <span className="text-green-600 text-xl">✓</span>
              </div>
              <div>
                <p className="font-semibold text-green-800 mb-2">
                  Selamat! Kamu memenuhi syarat untuk:
                </p>
                <div className="space-y-2">
                  {eligiblePrograms.map(program => (
                    <div 
                      key={program.program}
                      className="bg-white border border-green-200 rounded-md p-3 flex justify-between items-center"
                    >
                      <div className="font-medium text-green-800">{program.program}</div>
                      <div className="flex items-center">
                        <span className="text-sm text-green-700 mr-2">Min: {program.min_score}</span>
                        <span className="text-green-500 bg-green-100 rounded-full w-6 h-6 flex items-center justify-center">✓</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <div className="bg-red-100 rounded-full p-2 mr-3 mt-1">
                <span className="text-red-600 text-xl">✗</span>
              </div>
              <div>
                <p className="font-semibold text-red-800 mb-2">
                  Kamu belum memenuhi syarat untuk program manapun
                </p>
                <p className="text-red-700 text-sm">
                  Kamu perlu minimal {sortedThresholds[sortedThresholds.length-1].min_score} poin untuk memenuhi syarat program {sortedThresholds[sortedThresholds.length-1].program}
                </p>
              </div>
            </div>
          </div>
        )}
        
        <div className="rounded-lg border border-gray-200 overflow-hidden">
          <div className="bg-gray-50 p-3 border-b border-gray-200">
            <p className="font-medium text-gray-700">Persyaratan Skor Program</p>
          </div>
          <div className="divide-y divide-gray-200">
            {sortedThresholds.map(threshold => (
              <div 
                key={threshold.program}
                className="flex items-center justify-between p-4"
              >
                <div className="flex items-center">
                  {totalScore >= threshold.min_score ? (
                    <span className="inline-flex items-center justify-center w-6 h-6 bg-green-100 text-green-600 rounded-full mr-3 flex-shrink-0">✓</span>
                  ) : (
                    <span className="inline-flex items-center justify-center w-6 h-6 bg-gray-100 text-gray-400 rounded-full mr-3 flex-shrink-0">✗</span>
                  )}
                  <span className={totalScore >= threshold.min_score ? "font-medium" : ""}>{threshold.program}</span>
                </div>
                <div className="flex items-center">
                  <div className={`text-sm ${totalScore >= threshold.min_score ? "text-green-600 font-medium" : "text-gray-600"}`}>
                    {threshold.min_score} poin
                  </div>
                  
                  {totalScore < threshold.min_score && (
                    <div className="ml-3 text-xs text-red-500">
                      Kurang {threshold.min_score - totalScore}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  };

  // Render an explanation of the scoring system based on exam type
  const renderScoringExplanation = () => {
    if (!examType) return null;
    
    const isUtbk = examType === 'utbk';
    
    return (
      <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
        <p className="text-sm text-gray-600">
          {isUtbk ? (
            <>
              <span className="font-medium">Penilaian UTBK:</span> Setiap mata uji dihitung dengan rumus (Jawaban Benar/Total Soal) × 1000, dibulatkan ke atas. Skor akhir adalah rata-rata dari semua mata uji, juga dibulatkan ke atas.
            </>
          ) : (
            <>
              <span className="font-medium">Penilaian {examType.toUpperCase()}:</span> Skor adalah jumlah jawaban benar dikalikan dengan 5.
              {/* ADDED: Note about kepribadian being excluded from total score */}
              <span className="block mt-1 text-xs italic">Catatan: Mata uji &apos;Kepribadian&apos; tidak dihitung dalam total skor.</span>
            </>
          )}
        </p>
      </div>
    );
  };

  return (
    <div className="bg-white shadow-lg rounded-lg p-6 mb-8">
      <h2 className="text-2xl font-bold mb-4 text-center">Skor Kamu</h2>
      
      {/* Display exam type indicator if available */}
      {examType && (
        <div className="mb-3 text-center">
          <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            {examType.toUpperCase()}
          </span>
        </div>
      )}
      
      {/* Improved score and emoji display */}
      <div className="flex flex-col items-center justify-center mb-6">
        {/* Large emoji with custom styling */}
        <div 
          style={{ 
            fontSize: "4rem", 
            lineHeight: 1.2,
            marginBottom: "0.5rem"
          }}
        >
          {getEmoji(scorePercentage)}
        </div>
        
        {/* Score display with increased prominence */}
        <div className="text-center mt-2">
          <span className={`text-5xl font-bold ${scoreTextColorClass}`}>
            {score !== null ? formatScore(score, 0) : "?"}
          </span>
          <span className="text-3xl text-gray-600">/{totalQuestions}</span>
        </div>
      </div>
      
      {score !== null && (
        <p className="text-center mb-6 text-gray-600">
          Kamu dapat {formatScore(scorePercentage)}% jawaban benar!
        </p>
      )}

      {/* Display scoring explanation */}
      {renderScoringExplanation()}

      <div className="space-y-4 mt-6">
        {subjectInfos.map(subject => renderScoreSection(subject))}
      </div>

      {/* Render program eligibility section if available */}
      {renderProgramEligibility()}

      <Modal 
        isOpen={isOpen} 
        onClose={() => setIsOpen(false)}
      >
        <ModalContent>
          <ModalBody>
            <div className="p-4">
              <h3 className="text-xl font-bold mb-4">Detailed Score Breakdown</h3>
              <div className="space-y-4">
                <p>Total Questions: {totalQuestions}</p>
                <p>Correct Answers: {score}</p>
                <p>Accuracy: {formatScore(scorePercentage)}%</p>
                <p>Exam Type: {examType.toUpperCase()}</p>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="primary" onPress={() => setIsOpen(false)}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default ScoreDisplay;