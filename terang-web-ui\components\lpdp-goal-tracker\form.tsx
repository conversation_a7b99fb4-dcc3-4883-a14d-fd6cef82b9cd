"use client";

import React, { useState, useEffect } from "react";
import {
  Form,
  Input,
  Button,
  Textarea,
  Checkbox,
} from "@heroui/react";
import { LpdpGoalTrackerFormData, FormErrors } from "./actions";

interface LpdpGoalTrackerFormProps {
  initialData?: Partial<LpdpGoalTrackerFormData>;
  formErrors?: FormErrors;
  clearError?: (field: string) => void;
  onFormDataChange?: (data: Partial<LpdpGoalTrackerFormData>) => void;
  onComplete?: () => Promise<void>;
  onDecline?: () => Promise<void>;
  isSubmitting?: boolean;
  userName?: string;
  userEmail?: string;
}

export function LpdpGoalTrackerForm({
  initialData,
  formErrors = {},
  clearError,
  onFormDataChange,
  onComplete,
  onDecline,
  isSubmitting = false,
  userName,
  userEmail,
}: LpdpGoalTrackerFormProps) {
  const [formData, setFormData] = useState<Partial<LpdpGoalTrackerFormData>>({
    name: userName || "",
    email: userEmail || "",
    helpfulnessRating: 5,
    passedLpdpTbs: false,
    feltHelped: false,
    mostHelpfulAspect: "",
    improvementSuggestions: "",
    contactConsent: false,
    phoneNumber: "",
    ...(initialData || {}),
  });

  // Update form data when initialData changes
  useEffect(() => {
    if (initialData) {
      setFormData((prev) => ({
        ...prev,
        ...initialData,
      }));
    }
  }, [initialData]);

  // Update form data when userName or userEmail changes
  useEffect(() => {
    if (userName) {
      setFormData((prev) => ({
        ...prev,
        name: userName,
      }));
    }
    if (userEmail) {
      setFormData((prev) => ({
        ...prev,
        email: userEmail,
      }));
    }
  }, [userName, userEmail]);

  const handleInputChange = (field: keyof LpdpGoalTrackerFormData, value: any) => {
    // Clear error for this field if it exists
    if (clearError && formErrors[field]) {
      clearError(field);
    }

    // Update form data
    setFormData((prev) => {
      const newData = { ...prev, [field]: value };

      // Notify parent component of the change
      if (onFormDataChange) {
        onFormDataChange(newData);
      }

      return newData;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (onComplete) {
      await onComplete();
    }
  };

  const handleDecline = async () => {
    if (onDecline) {
      await onDecline();
    }
  };

  return (
    <Form className="space-y-6 py-4 px-2 sm:px-4" onSubmit={handleSubmit}>
      {/* Centered decline button with improved mobile styling */}
      <div className="flex justify-center items-center mb-6 w-full">
        <Button
          type="button"
          color="danger"
          variant="solid"
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
          size="lg"
          className="px-4 sm:px-6 py-4 sm:py-5 text-sm sm:text-base font-medium whitespace-normal text-center"
          style={{ width: "280px", maxWidth: "100%" }}
          onPress={handleDecline}
        >
          Saya Tidak Mengikuti LPDP
        </Button>
      </div>
      <div className="space-y-5">
        <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm border">
          <div className="flex items-center mb-4">
            <div className="w-1 h-6 bg-primary rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold">Informasi Pribadi</h3>
          </div>

          <div className="space-y-4">
            <Input
              label="Nama"
              placeholder="Masukkan nama lengkap"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              isInvalid={!!formErrors.name}
              errorMessage={formErrors.name}
              isRequired
              classNames={{
                base: "max-w-full",
                inputWrapper: "bg-white"
              }}
            />

            <Input
              label="Email"
              placeholder="Masukkan email"
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              isInvalid={!!formErrors.email}
              errorMessage={formErrors.email}
              isRequired
              classNames={{
                base: "max-w-full",
                inputWrapper: "bg-white"
              }}
            />
          </div>
        </div>

        <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm border">
          <div className="flex items-center mb-4">
            <div className="w-1 h-6 bg-primary rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold">Hasil TBS LPDP Batch 1</h3>
          </div>

          <div className="space-y-5">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              <Input
                type="number"
                label="Penalaran Verbal"
                placeholder="Masukkan nilai"
                value={formData.verbalReasoning !== undefined ? formData.verbalReasoning.toString() : ''}
                onChange={(e) => {
                  const value = e.target.value === '' ? undefined : parseInt(e.target.value);
                  handleInputChange("verbalReasoning", value);
                }}
                isInvalid={!!formErrors.verbalReasoning}
                errorMessage={formErrors.verbalReasoning}
                isRequired
                classNames={{
                  base: "max-w-full",
                  inputWrapper: "bg-white"
                }}
              />

              <Input
                type="number"
                label="Penalaran Kuantitatif"
                placeholder="Masukkan nilai"
                value={formData.quantitativeReasoning !== undefined ? formData.quantitativeReasoning.toString() : ''}
                onChange={(e) => {
                  const value = e.target.value === '' ? undefined : parseInt(e.target.value);
                  handleInputChange("quantitativeReasoning", value);
                }}
                isInvalid={!!formErrors.quantitativeReasoning}
                errorMessage={formErrors.quantitativeReasoning}
                isRequired
                classNames={{
                  base: "max-w-full",
                  inputWrapper: "bg-white"
                }}
              />

              <Input
                type="number"
                label="Pemecahan Masalah"
                placeholder="Masukkan nilai"
                value={formData.problemSolving !== undefined ? formData.problemSolving.toString() : ''}
                onChange={(e) => {
                  const value = e.target.value === '' ? undefined : parseInt(e.target.value);
                  handleInputChange("problemSolving", value);
                }}
                isInvalid={!!formErrors.problemSolving}
                errorMessage={formErrors.problemSolving}
                isRequired
                classNames={{
                  base: "max-w-full",
                  inputWrapper: "bg-white"
                }}
              />
            </div>

            <div className="space-y-3">
              <fieldset>
                <legend className="block text-sm font-medium">
                  Apakah Lulus TBS LPDP Batch 1? <span className="text-danger">*</span>
                </legend>
                <div className="flex flex-wrap gap-3 mt-2">
                  <button
                    type="button"
                    className={`flex items-center gap-2 p-3 border rounded-lg cursor-pointer transition-all ${formData.passedLpdpTbs ? 'border-primary bg-primary/10' : 'border-gray-200 hover:border-gray-300'}`}
                    onClick={() => handleInputChange("passedLpdpTbs", true)}
                    aria-pressed={formData.passedLpdpTbs}
                  >
                    <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${formData.passedLpdpTbs ? 'border-primary bg-primary' : 'border-gray-400'}`}>
                      {formData.passedLpdpTbs && <span className="text-white text-xs">✓</span>}
                    </div>
                    <span>Ya</span>
                  </button>
                  <button
                    type="button"
                    className={`flex items-center gap-2 p-3 border rounded-lg cursor-pointer transition-all ${!formData.passedLpdpTbs ? 'border-primary bg-primary/10' : 'border-gray-200 hover:border-gray-300'}`}
                    onClick={() => handleInputChange("passedLpdpTbs", false)}
                    aria-pressed={!formData.passedLpdpTbs}
                  >
                    <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${!formData.passedLpdpTbs ? 'border-primary bg-primary' : 'border-gray-400'}`}>
                      {!formData.passedLpdpTbs && <span className="text-white text-xs">✓</span>}
                    </div>
                    <span>Tidak</span>
                  </button>
                </div>
              </fieldset>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm border">
          <div className="flex items-center mb-4">
            <div className="w-1 h-6 bg-primary rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold">Evaluasi Terang AI</h3>
          </div>

          <div className="space-y-5">
            <div className="space-y-3">
              <fieldset>
                <legend className="block text-sm font-medium">
                  Apakah Merasa Terbantu dalam persiapan TBS LPDP Batch 1? <span className="text-danger">*</span>
                </legend>
                <div className="flex flex-wrap gap-3 mt-2">
                  <button
                    type="button"
                    className={`flex items-center gap-2 p-3 border rounded-lg cursor-pointer transition-all ${formData.feltHelped ? 'border-primary bg-primary/10' : 'border-gray-200 hover:border-gray-300'}`}
                    onClick={() => handleInputChange("feltHelped", true)}
                    aria-pressed={formData.feltHelped}
                  >
                    <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${formData.feltHelped ? 'border-primary bg-primary' : 'border-gray-400'}`}>
                      {formData.feltHelped && <span className="text-white text-xs">✓</span>}
                    </div>
                    <span>Ya</span>
                  </button>
                  <button
                    type="button"
                    className={`flex items-center gap-2 p-3 border rounded-lg cursor-pointer transition-all ${!formData.feltHelped ? 'border-primary bg-primary/10' : 'border-gray-200 hover:border-gray-300'}`}
                    onClick={() => handleInputChange("feltHelped", false)}
                    aria-pressed={!formData.feltHelped}
                  >
                    <div className={`w-5 h-5 rounded-full border flex items-center justify-center ${!formData.feltHelped ? 'border-primary bg-primary' : 'border-gray-400'}`}>
                      {!formData.feltHelped && <span className="text-white text-xs">✓</span>}
                    </div>
                    <span>Tidak</span>
                  </button>
                </div>
              </fieldset>
            </div>

            <div className="space-y-2">
              <div>
                <label htmlFor="helpfulnessRating" className="block text-sm font-medium mb-1">
                  Seberapa Terbantu Terang AI untuk membantu persiapan proses ujian? (1-10)
                </label>
                <div className="flex flex-col sm:flex-row items-center gap-2">
                  <div className="flex items-center gap-2 w-full">
                    <span className="text-sm font-medium">1</span>
                    <Input
                      id="helpfulnessRating"
                      type="range"
                      min="1"
                      max="10"
                      value={formData.helpfulnessRating !== undefined ? formData.helpfulnessRating.toString() : '5'}
                      onChange={(e) => {
                        const value = e.target.value === '' ? 5 : parseInt(e.target.value);
                        handleInputChange("helpfulnessRating", value);
                      }}
                      className="flex-1"
                    />
                    <span className="text-sm font-medium">10</span>
                  </div>
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary text-white font-bold">
                    {formData.helpfulnessRating || 5}
                  </div>
                </div>
                {formErrors.helpfulnessRating && (
                  <p className="text-danger text-sm mt-1">{formErrors.helpfulnessRating}</p>
                )}
              </div>
            </div>

            <Textarea
              label="Dari sisi mana yang merasa paling terbantu?"
              placeholder="Ceritakan pengalaman Anda"
              value={formData.mostHelpfulAspect}
              onChange={(e) => handleInputChange("mostHelpfulAspect", e.target.value)}
              classNames={{
                base: "max-w-full",
                inputWrapper: "bg-white",
                input: "text-sm sm:text-base"
              }}
              minRows={3}
            />

            <Textarea
              label="Apakah ada feature yang perlu ditingkatkan agar bisa membantu proses persiapan ujian lebih maksimal?"
              placeholder="Berikan saran Anda"
              value={formData.improvementSuggestions}
              onChange={(e) => handleInputChange("improvementSuggestions", e.target.value)}
              classNames={{
                base: "max-w-full",
                inputWrapper: "bg-white",
                input: "text-sm sm:text-base"
              }}
              minRows={3}
            />
          </div>
        </div>

        <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm border">
          <div className="flex items-center mb-4">
            <div className="w-1 h-6 bg-primary rounded-full mr-3"></div>
            <h3 className="text-lg font-semibold">Kontak Lanjutan</h3>
          </div>

          <div className="space-y-5">
            <div className="p-3 border border-primary/20 bg-primary/5 rounded-lg">
              <Checkbox
                isSelected={formData.contactConsent}
                onValueChange={(value) => handleInputChange("contactConsent", value)}
                classNames={{
                  base: "data-[selected=true]:border-primary",
                  label: "text-sm md:text-base"
                }}
                size="lg"
              >
                Jika dikontak Terang AI terkait lanjutan pertanyaan lainnya, apakah bersedia?
              </Checkbox>
            </div>

            {formData.contactConsent && (
              <Input
                label="Nomor HP/WhatsApp"
                placeholder="Masukkan nomor HP/WhatsApp"
                value={formData.phoneNumber}
                onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                isInvalid={!!formErrors.phoneNumber}
                errorMessage={formErrors.phoneNumber}
                isRequired={formData.contactConsent}
                classNames={{
                  base: "max-w-full",
                  inputWrapper: "bg-white"
                }}
              />
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-center items-center w-full">
        <Button
          type="submit"
          color="primary"
          isLoading={isSubmitting}
          isDisabled={isSubmitting}
          size="lg"
          className="px-6 sm:px-8 py-4 sm:py-6 text-sm sm:text-base font-medium whitespace-normal text-center"
          style={{ width: "280px", maxWidth: "100%" }}
        >
          {isSubmitting ? "Mengirim..." : "Kirim Formulir"}
        </Button>
      </div>
    </Form>
  );
}
