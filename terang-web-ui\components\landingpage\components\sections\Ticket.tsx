'use client';

import React, { lazy, Suspense, useState, useRef } from "react";
import styled, { ThemeProvider } from "styled-components";
import { Swiper, SwiperSlide } from "swiper/react";
import type { Swiper as SwiperType } from "swiper";
import "swiper/css";
import "swiper/css/effect-cards";
import "swiper/css/pagination";
import "swiper/css/navigation";
import Image from "next/image";
import { Pagination, Navigation, Autoplay, EffectCards } from "swiper/modules";
import { useLanguage } from "@/app/language-wrapper";

import Button from "../Button";
import { dark } from "../../styles/Themes";
import Loading from "../Loading";

import img3 from "@/public/landingpage-assets/exam-tickets/SSCASN-NEW.svg";
import img2 from "@/public/landingpage-assets/exam-tickets/utbk-snbt.svg";
import img from "@/public/landingpage-assets/exam-tickets/lpdp.svg";

const Section = styled.section`
  min-height: 100vh;
  width: 100%;
  background-color: ${(props) => props.theme.text};
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  @media (max-width: 64em) {
    padding-top: 3rem;
    padding-bottom: 2rem;
  }
`;

const Container = styled.div`
  width: 75%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  @media (max-width: 70em) {
    width: 85%;
  }
  @media (max-width: 64em) {
    width: 100%;
    flex-direction: column;
    & > *:last-child {
      width: 80%;
    }
  }
  @media (max-width: 40em) {
    & > *:last-child {
      width: 90%;
    }
  }
`;

const Box = styled.div`
  width: 50%;
  height: 100%;
  min-height: 60vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  @media (max-width: 40em) {
    min-height: 50vh;
  }
`;

const TitleContainer = styled.div`
  position: relative;
  width: 100%;
  margin: 0 auto;
  @media (max-width: 64em) {
    width: 100%;
    text-align: center;
  }
`;

const Title = styled.h2`
  font-size: ${(props) => props.theme.fontxxl};
  text-transform: capitalize;
  color: ${(props) => props.theme.body};
  font-family: "Sora", sans-serif;
  font-weight: 600;
  line-height: 1;
  position: relative;
  @media (max-width: 64em) {
    padding-top: 3rem;
  }
  @media (max-width: 40em) {
    font-size: ${(props) => props.theme.fontxxl};
  }
  @media (max-width: 30em) {
    font-size: ${(props) => props.theme.fontxl};
  }
`;

const KartuUjianWrapper = styled.span`
  position: relative;
  display: inline-block;
  z-index: 2;
`;

const KartuUjianText = styled.span`
  font-weight: 900;
  -webkit-text-stroke: 2px ${(props) => props.theme.body};
  letter-spacing: 1.5px;
  position: relative;
  z-index: 3;
`;

const TitleSVG = styled.svg`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scaleX(0.64) scaleY(0.77) rotate(2deg);
  width: 200%;
  height: 210%;
  z-index: 1;
  pointer-events: none;
`;

const SubText = styled.p`
  font-size: ${(props) => props.theme.fontmd};
  color: ${(props) => props.theme.body};
  align-self: flex-start;
  width: 80%;
  margin: 1rem auto;
  font-weight: 400;
  @media (max-width: 64em) {
    width: 100%;
    text-align: center;
    font-size: ${(props) => props.theme.fontmd};
  }
  @media (max-width: 40em) {
    font-size: ${(props) => props.theme.fontmd};
  }
  @media (max-width: 30em) {
    font-size: ${(props) => props.theme.fontsm};
  }
`;

const SubTextLight = styled.p`
  font-size: ${(props) => props.theme.fontmd};
  color: ${(props) => `rgba(${props.theme.bodyRgba},0.6)`};
  align-self: flex-start;
  width: 80%;
  margin: 1rem auto;
  font-weight: 400;
  @media (max-width: 64em) {
    width: 100%;
    text-align: center;
    font-size: ${(props) => props.theme.fontsm};
  }
  @media (max-width: 40em) {
    font-size: ${(props) => props.theme.fontsm};
  }
  @media (max-width: 30em) {
    font-size: ${(props) => props.theme.fontxs};
  }
`;

const ButtonContainer = styled.div`
  width: 80%;
  margin: 1rem auto;
  display: flex;
  justify-content: flex-start;
  @media (max-width: 64em) {
    width: 100%;
    justify-content: center;
    button {
      margin: 0 auto;
    }
  }
`;

const ExamInfo = styled.div`
  margin-top: 1rem;
  width: 80%;
  @media (max-width: 64em) {
    width: 100%;
    text-align: center;
  }
`;

const ExamInfoItem = styled.div`
  color: ${(props) => props.theme.body};
  margin-bottom: 0.5rem;
`;

const ScoreInfo = styled.div`
  display: flex;
  justify-content: space-between;
  margin-top: 0.25rem;
`;

const ExamTable = styled.div`
  margin-bottom: 1rem;
  color: ${(props) => props.theme.body};
  width: 100%;
`;

const ExamTableHeader = styled.div`
  font-weight: bold;
  margin-bottom: 1rem;
  text-align: left;
  
  @media (max-width: 64em) {
    text-align: center;
  }
`;

const SubjectRow = styled.div`
  margin-bottom: 0.75rem;
`;

const ExamTableCell = styled.div`
  font-size: ${(props) => props.theme.fontmd};
  text-align: left;
  
  @media (max-width: 64em) {
    text-align: center;
  }
  
  @media (max-width: 48em) {
    font-size: ${(props) => props.theme.fontsm};
  }
  
  @media (max-width: 30em) {
    font-size: ${(props) => props.theme.fontxs};
  }
`;

const ExamTableScoreCell = styled.div`
  font-size: ${(props) => props.theme.fontsm};
  text-align: left;
  color: ${(props) => props.theme.bodyRgba && `rgba(${props.theme.bodyRgba}, 0.8)`};
  margin-top: 0.2rem;
  display: flex;
  align-items: center;
  
  @media (max-width: 64em) {
    text-align: center;
    justify-content: center;
  }
  
  @media (max-width: 48em) {
    font-size: ${(props) => props.theme.fontxs};
  }
  
  @media (max-width: 30em) {
    font-size: calc(${(props) => props.theme.fontxs} - 2px);
  }
`;

const ScoreArrow = styled.span`
  display: inline-block;
  margin-right: 0.5rem;
  font-size: ${(props) => props.theme.fontsm};
  
  @media (max-width: 48em) {
    font-size: ${(props) => props.theme.fontxs};
  }
`;

const CarouselContainer = styled.div`
  width: 25vw;
  height: 70vh;

  @media (max-width: 70em) {
    height: 65vh;
  }

  @media (max-width: 64em) {
    height: 65vh;
    width: 30vw;
  }
  @media (max-width: 48em) {
    height: 65vh;
    width: 40vw;
  }
  @media (max-width: 30em) {
    height: 65vh;
    width: 60vw;
  }

  .swiper {
    width: 100%;
    height: 100%;
  }

  .swiper-slide {
    background-color: ${(props) => props.theme.carouselColor};
    border-radius: 20px;
    border: 5px solid #333;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;

    img {
      display: block;
      width: 100%;
      height: auto;
      object-fit: cover;
      margin-top: -100px;
    }

    .exam-title {
      font-size: 1.5rem;
      font-weight: bold;
      margin-top: 10px;
      color: ${(props) => props.theme.text};

      @media (max-width: 64em) {
        font-size: 1.3rem;
      }
      @media (max-width: 48em) {
        font-size: 1.1rem;
      }
      @media (max-width: 30em) {
        font-size: 1rem;
      }
    }

    .exam-tips {
      font-size: 0.85rem;
      color: ${(props) => props.theme.text};
      margin-top: 10px;
      text-align: left;

      @media (max-width: 64em) {
        font-size: 0.8rem;
      }
      @media (max-width: 48em) {
        font-size: 0.75rem;
      }
      @media (max-width: 30em) {
        font-size: 0.7rem;
      }
    }
  }

  .swiper-button-next,
  .swiper-button-prev {
    color: ${(props) => props.theme.text};
    width: 4rem;
    top: 90%;

    &:after {
      display: none;
    }

    @media (max-width: 64em) {
      width: 3rem;
    }
    @media (max-width: 30em) {
      width: 2rem;
    }
  }

  .swiper-button-next {
    right: 10%;
    background-image: url(/landingpage-assets/Arrow.svg);
    background-position: center;
    background-size: cover;
  }

  .swiper-button-prev {
    left: 10%;
    transform: rotate(180deg);
    background-image: url(/landingpage-assets/Arrow.svg);
    background-position: center;
    background-size: cover;
  }

  .swiper-pagination {
    bottom: 5%;
    left: 0;
    right: 0;
    text-align: center;
    color: ${(props) => props.theme.text};
  }
`;

const CategoryTabs = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  gap: 10px;
`;

interface CategoryTabProps {
  $active: boolean;
}

const CategoryTab = styled.button<CategoryTabProps>`
  padding: 8px 16px;
  border-radius: 20px;
  border: 2px solid #333;
  background-color: ${(props) => (props.$active ? "#333" : "transparent")};
  color: ${(props) => (props.$active ? "#fff" : "#333")};
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${(props) => (!props.$active ? "#eee" : "#333")};
  }

  @media (max-width: 48em) {
    padding: 6px 12px;
    font-size: 0.9rem;
  }
  @media (max-width: 30em) {
    padding: 5px 10px;
    font-size: 0.8rem;
  }
`;

type CategoryType = "LPDP" | "UTBK" | "CPNS";

interface SlideContent {
  titleKey: string;
  timeKey: string;
  tipsKey: string;
  image: any;
}

interface CategoryContent {
  [key: string]: SlideContent[];
}

interface ExamSubject {
  nameKey: string;
  questions: number;
  passScore?: number;
  maxScore?: number;
}

interface ExamData {
  titleKey: string;
  subjects: ExamSubject[];
  totalQuestions: number;
  totalTime: string;
  totalMaxScore?: number;
}

const Ticket: React.FC = () => {
  const { t, language } = useLanguage();
  const [activeCategory, setActiveCategory] = useState<CategoryType>("LPDP");
  const swiperRef = useRef<{ swiper: SwiperType } | null>(null);
  
  // Use translation keys instead of hardcoded strings
  const categories: CategoryContent = {
    LPDP: [
      {
        titleKey: "lpdp_verbal_title",
        timeKey: "lpdp_verbal_time",
        tipsKey: "lpdp_verbal_tips",
        image: img
      },
      {
        titleKey: "lpdp_quant_title",
        timeKey: "lpdp_quant_time",
        tipsKey: "lpdp_quant_tips",
        image: img
      },
      {
        titleKey: "lpdp_problem_title",
        timeKey: "lpdp_problem_time",
        tipsKey: "lpdp_problem_tips",
        image: img
      },
      {
        titleKey: "lpdp_personality_title",
        timeKey: "lpdp_personality_time",
        tipsKey: "lpdp_personality_tips",
        image: img
      }
    ],
    UTBK: [
      {
        titleKey: "utbk_literacy_title",
        timeKey: "utbk_literacy_time",
        tipsKey: "utbk_literacy_tips",
        image: img2
      },
      {
        titleKey: "utbk_math_title",
        timeKey: "utbk_math_time",
        tipsKey: "utbk_math_tips",
        image: img2
      },
      {
        titleKey: "utbk_scholastic_title",
        timeKey: "utbk_scholastic_time",
        tipsKey: "utbk_scholastic_tips",
        image: img2
      }
    ],
    CPNS: [
      {
        titleKey: "cpns_tkp_title",
        timeKey: "cpns_tkp_time",
        tipsKey: "cpns_tkp_tips",
        image: img3
      },
      {
        titleKey: "cpns_twk_title",
        timeKey: "cpns_twk_time",
        tipsKey: "cpns_twk_tips",
        image: img3
      },
      {
        titleKey: "cpns_tiu_title",
        timeKey: "cpns_tiu_time",
        tipsKey: "cpns_tiu_tips",
        image: img3
      }
    ]
  };
  
  const examData: Record<CategoryType, ExamData> = {
    LPDP: {
      titleKey: "lpdp_card_title",
      subjects: [
        { nameKey: "verbal_reasoning", questions: 25, passScore: 56, maxScore: 125 },
        { nameKey: "quantitative_reasoning", questions: 25, passScore: 56, maxScore: 125 },
        { nameKey: "problem_solving", questions: 20, passScore: 45, maxScore: 100 },
        { nameKey: "personality", questions: 30, passScore: 110, maxScore: 150 }
      ],
      totalQuestions: 100,
      totalTime: "135 menit",
      totalMaxScore: 500
    },
    UTBK: {
      titleKey: "utbk_card_title",
      subjects: [
        { nameKey: "literacy_test", questions: 50, maxScore: 1000, passScore: 700 },
        { nameKey: "math_reasoning", questions: 20, maxScore: 1000, passScore: 700 },
        { nameKey: "scholastic_test", questions: 90, maxScore: 1000, passScore: 700 }
      ],
      totalQuestions: 130,
      totalTime: "180 menit",
      totalMaxScore: 1000,
    },
    CPNS: {
      titleKey: "cpns_card_title",
      subjects: [
        { nameKey: "twk", questions: 30, passScore: 65, maxScore: 150 },
        { nameKey: "tiu", questions: 35, passScore: 80, maxScore: 175 },
        { nameKey: "tkp", questions: 45, passScore: 166, maxScore: 225 }
      ],
      totalQuestions: 110,
      totalTime: "100 menit",
      totalMaxScore: 550
    }
  };

  // Handle category change
  const handleCategoryChange = (category: CategoryType): void => {
    setActiveCategory(category);
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideTo(0);
    }
  };

  // Handle slide change to next category when reaching the end
  const handleSlideChange = (swiper: SwiperType): void => {
    const { activeIndex, slides } = swiper;
    
    // If we're at the last slide of the current category
    if (activeIndex === slides.length - 1) {
      // Get the categories in order
      const categoryOrder: CategoryType[] = ["LPDP", "UTBK", "CPNS"];
      const currentIndex = categoryOrder.indexOf(activeCategory);
      const nextIndex = (currentIndex + 1) % categoryOrder.length;
      
      // Set a timeout to change category after the last slide is viewed
      setTimeout(() => {
        setActiveCategory(categoryOrder[nextIndex]);
        if (swiperRef.current && swiperRef.current.swiper) {
          swiperRef.current.swiper.slideTo(0);
        }
      }, 3000); // Change after 3 seconds
    }
  };

  // Get current exam data based on active category
  const currentExamData = examData[activeCategory];

  return (
    <Section id="ticket">
      <Container>
        <Box>
          <CategoryTabs>
            <CategoryTab 
              $active={activeCategory === "LPDP"} 
              onClick={() => handleCategoryChange("LPDP")}
            >
              LPDP
            </CategoryTab>
            <CategoryTab 
              $active={activeCategory === "UTBK"} 
              onClick={() => handleCategoryChange("UTBK")}
            >
              UTBK
            </CategoryTab>
            <CategoryTab 
              $active={activeCategory === "CPNS"} 
              onClick={() => handleCategoryChange("CPNS")}
            >
              CPNS
            </CategoryTab>
          </CategoryTabs>
          
          <CarouselContainer>
            <Swiper
              ref={swiperRef}
              autoplay={{
                delay: 3000,
                disableOnInteraction: false,
              }}
              className="mySwiper"
              effect={"cards"}
              grabCursor={true}
              modules={[EffectCards, Pagination, Navigation, Autoplay]}
              navigation={true}
              pagination={{
                type: "fraction",
              }}
              scrollbar={{
                draggable: true,
              }}
              onSlideChange={handleSlideChange}
              key={activeCategory} // Force re-render when category changes
            >
              {categories[activeCategory].map((slide, index) => (
                <SwiperSlide key={`${activeCategory}-${index}`}>
                  <Image alt={t(slide.titleKey)} height={200} src={slide.image} width={200} />
                  <div className="exam-title">{t(slide.titleKey)}</div>
                  <div className="exam-tips">
                    <strong>{t(slide.timeKey)}</strong>
                    <br />
                    <div dangerouslySetInnerHTML={{ __html: t(slide.tipsKey) }} />
                  </div>
                </SwiperSlide>
              ))}
            </Swiper>
          </CarouselContainer>
        </Box>
        <Box>
          <TitleContainer>
            <Title>
              {t('choose')}{" "}
              <KartuUjianWrapper>
                <TitleSVG
                  viewBox="0 0 504 106"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M1106,1813.61193 C1391.48757,1788.3249 1542.09692,1785.22818 1557.82804,1804.32178 C1581.42472,1832.96217 1297.6495,1822.13368 1191.16891,1835.26224 C1084.68832,1848.39079 1016.09991,1866.56524 1566,1841.45052"
                    fill="none"
                    stroke="#4a90e2"
                    strokeLinecap="round"
                    strokeWidth="43"
                    transform="translate(-1084 -1770)"
                  />
                </TitleSVG>
                <KartuUjianText>{t('exam_card')}</KartuUjianText>
              </KartuUjianWrapper>
            </Title>
          </TitleContainer>
          <ExamInfo>
            <ExamTable>
              <ExamTableHeader>{t(currentExamData.titleKey)}</ExamTableHeader>
              {currentExamData.subjects.map((subject, index) => (
                <SubjectRow key={index}>
                  <ExamTableCell>
                    {t(subject.nameKey)} ({subject.questions} {t('questions')})
                  </ExamTableCell>
                  {subject.passScore && subject.maxScore && (
                    <ExamTableScoreCell>
                      <ScoreArrow>→</ScoreArrow>
                      {t('pass')}: {subject.passScore} / {t('max')}: {subject.maxScore}
                    </ExamTableScoreCell>
                  )}
                </SubjectRow>
              ))}
            </ExamTable>
            <ExamInfoItem>
              <strong>{t('total_questions')}:</strong> {currentExamData.totalQuestions}
            </ExamInfoItem>
            <ExamInfoItem>
              <strong>{t('total_time')}:</strong> {currentExamData.totalTime}
            </ExamInfoItem>
            {currentExamData.totalMaxScore && (
              <ExamInfoItem>
                <strong>{t('total_max_score')}:</strong> {currentExamData.totalMaxScore}
              </ExamInfoItem>
            )}
          </ExamInfo>
          <SubTextLight>
            {t('collection')}
          </SubTextLight>
          <ButtonContainer>
            <ThemeProvider theme={dark}>
              <Button
                link="/available-exams"
                newTab={false}
                text={t('start_free')}
              />
            </ThemeProvider>
          </ButtonContainer>
        </Box>
      </Container>
    </Section>
  );
};

export default Ticket;