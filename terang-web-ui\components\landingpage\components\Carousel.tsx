import React, { useState, useRef } from "react";
import styled from "styled-components";
import { Swiper, SwiperSlide } from "swiper/react";
import type { Swiper as SwiperType } from "swiper";
import "swiper/css";
import "swiper/css/effect-cards";
import "swiper/css/pagination";
import "swiper/css/navigation";
import Image from "next/image";
import { Pagination, Navigation, Autoplay, EffectCards } from "swiper/modules";

import img3 from "@/public/landingpage-assets/exam-tickets/SSCASN-NEW.svg";
import img2 from "@/public/landingpage-assets/exam-tickets/utbk-snbt.svg";
import img from "@/public/landingpage-assets/exam-tickets/lpdp.svg";

const Container = styled.div`
  width: 25vw;
  height: 70vh;

  @media (max-width: 70em) {
    height: 65vh;
  }

  @media (max-width: 64em) {
    height: 65vh;
    width: 30vw;
  }
  @media (max-width: 48em) {
    height: 65vh;
    width: 40vw;
  }
  @media (max-width: 30em) {
    height: 65vh;
    width: 60vw;
  }

  .swiper {
    width: 100%;
    height: 100%;
  }

  .swiper-slide {
    background-color: ${(props) => props.theme.carouselColor};
    border-radius: 20px;
    border: 5px solid #333;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;

    img {
      display: block;
      width: 100%;
      height: auto;
      object-fit: cover;
      margin-top: -100px;
    }

    .exam-title {
      font-size: 1.5rem;
      font-weight: bold;
      margin-top: 10px;
      color: ${(props) => props.theme.text};

      @media (max-width: 64em) {
        font-size: 1.3rem;
      }
      @media (max-width: 48em) {
        font-size: 1.1rem;
      }
      @media (max-width: 30em) {
        font-size: 1rem;
      }
    }

    .exam-tips {
      font-size: 0.85rem;
      color: ${(props) => props.theme.text};
      margin-top: 10px;
      text-align: left;

      @media (max-width: 64em) {
        font-size: 0.8rem;
      }
      @media (max-width: 48em) {
        font-size: 0.75rem;
      }
      @media (max-width: 30em) {
        font-size: 0.7rem;
      }
    }
  }

  .swiper-button-next,
  .swiper-button-prev {
    color: ${(props) => props.theme.text};
    width: 4rem;
    top: 90%;

    &:after {
      display: none;
    }

    @media (max-width: 64em) {
      width: 3rem;
    }
    @media (max-width: 30em) {
      width: 2rem;
    }
  }

  .swiper-button-next {
    right: 10%;
    background-image: url(/landingpage-assets/Arrow.svg);
    background-position: center;
    background-size: cover;
  }

  .swiper-button-prev {
    left: 10%;
    transform: rotate(180deg);
    background-image: url(/landingpage-assets/Arrow.svg);
    background-position: center;
    background-size: cover;
  }

  .swiper-pagination {
    bottom: 5%;
    left: 0;
    right: 0;
    text-align: center;
    color: ${(props) => props.theme.text};
  }
`;

const CategoryTabs = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  gap: 10px;
`;

interface CategoryTabProps {
  $active: boolean; // Notice the $ prefix
}

const CategoryTab = styled.button<CategoryTabProps>`
  padding: 8px 16px;
  border-radius: 20px;
  border: 2px solid #333;
  background-color: ${(props) => (props.$active ? "#333" : "transparent")};
  color: ${(props) => (props.$active ? "#fff" : "#333")};
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: ${(props) => (!props.$active ? "#eee" : "#333")};
  }

  @media (max-width: 48em) {
    padding: 6px 12px;
    font-size: 0.9rem;
  }
  @media (max-width: 30em) {
    padding: 5px 10px;
    font-size: 0.8rem;
  }
`;

type CategoryType = "LPDP" | "UTBK" | "CPNS";

interface SlideContent {
  title: string;
  tips: string;
  image: any; // Using any for simplicity, ideally use a more specific type
}

interface CategoryContent {
  [key: string]: SlideContent[];
}

const Carousel: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState<CategoryType>("LPDP");
  const swiperRef = useRef<{ swiper: SwiperType } | null>(null);

  // Define the categories and their slides
  const categories: CategoryContent = {
    LPDP: [
      {
        title: "1. Penalaran Verbal",
        tips: `<strong>Waktu: 20-25 menit</strong>
        <br />
        • Fokus pada pemahaman konteks dan inti teks.  
        <br />
        • Terapin teknik membaca kritis untuk mengidentifikasi ide utama.  
        <br />
        • Latihan soal sinonim, antonim, & analogi di fitur latihan kita.  
        <br />
        • Perkuat kosakata melalui bacaan beragam.  
        <br />
        • Evaluasi jawaban dengan merefleksikan logika teks.`,
        image: img // Replace with actual image
      },
      {
        title: "2. Penalaran Kuantitatif",
        tips: `<strong>Waktu: 25-30 menit</strong>
        <br />
        • Kuasain konsep dasar matematika dan statistik.  
        <br />
        • Pecah soal kompleks menjadi bagian sederhana.  
        <br />
        • Terapin metode eliminasi untuk menyaring jawaban.  
        <br />
        • Latih aplikasi konsep melalui soal rutin.  
        <br />
        • Kelola waktu dengan menyelesaikan soal mudah terlebih dahulu.`,
        image: img // Replace with actual image
      },
      {
        title: "3. Pemecahan Masalah",
        tips: `<strong>Waktu: 20-25 menit</strong>
        <br />
        • Identifikasi masalah secara jelas sebelum mulai.  
        <br />
        • Susun langkah solusi secara sistematis.  
        <br />
        • Latih pola pikir kritis dengan simulasi soal.  
        <br />
        • Feedback dari mentor untuk perbaikan juga perlu.  
        <br />
        • Uji strategi secara berkala dan evaluasi hasil latihan.`,
        image: img // Replace with actual image
      },
      {
        title: "4. Kepribadian",
        tips: `<strong>Waktu: 15-20 menit</strong>
        <br />
        • Tampilin keaslian dan konsistensi dalam jawaban.  
        <br />
        • Gunakan narasi personal yang jujur.  
        <br />
        • Jelaskan nilai yang relevan dengan LPDP.  
        <br />
        • Hindari jawaban ekstrem; berikan penjelasan seimbang.  
        <br />
        • Sesuaikan respons dengan visi dan misi LPDP.`,
        image: img // Replace with actual image
      }
    ],
    UTBK: [
      {
        title: "1. Tes Literasi",
        tips: `<strong>Waktu: 45 menit</strong>
        <br />
        • Pahami struktur teks dengan teknik skimming & scanning.  
        <br />
        • Tandai ide pokok dan buat ringkasan singkat.  
        <br />
        • Tingkatkan kosakata melalui bacaan beragam.  
        <br />
        • Fokus pada konteks dan tujuan setiap teks.  
        <br />
        • Evaluasi hasil latihan untuk menemukan area perbaikan.`,
        image: img2 // Replace with actual image
      },
      {
        title: "2. Tes Penalaran Matematika",
        tips: `<strong>Waktu: 30 menit</strong>
        <br />
        • Kuasai konsep dasar matematika SMA.  
        <br />
        • Pecah soal menjadi bagian yang lebih sederhana.  
        <br />
        • Gunakan eliminasi untuk menyederhanakan pilihan.  
        <br />
        • Latih soal aplikasi melalui simulasi ujian.  
        <br />
        • Kelola waktu dengan strategi pengerjaan efektif.`,
        image: img2 // Replace with actual image
      },
      {
        title: "3. Tes Potensi Skolastik",
        tips: `<strong>Waktu: 105 menit</strong>
        <br />
        • Mulai dengan soal mudah untuk mengumpulkan poin.  
        <br />
        • Gunakan eliminasi pada soal yang menantang.  
        <br />
        • Latih penalaran verbal, kuantitatif, & analitis bergantian.  
        <br />
        • Atur waktu secara cermat agar semua soal terjawab.  
        <br />
        • Evaluasi latihan untuk mengasah strategi dan kecepatan.`,
        image: img2 // Replace with actual image
      }
    ],
    CPNS: [
      {
        title: "1. TKP (Tes Karakteristik Pribadi)",
        tips: `<strong>Waktu: 15-25 menit</strong>
        <br />
        • Mulai dengan ini untuk efektivitas maksimal.
        <br />
        • Target: Minimal 34 soal dengan skor tertinggi (5 poin/soal).
        <br />
        • Jawab semua 45 soal, jangan ada yang terlewat.
        <br />
        • Ingat: Poin TKP lebih tinggi dari TWK/TIU.
        <br />
        • Kerjakan dengan cepat dan yakin, pilih jawaban yang menunjukkan integritas dan orientasi pelayanan.`,
        image: img3 // Replace with actual image
      },
      {
        title: "2. TWK (Tes Wawasan Kebangsaan)",
        tips: `<strong>Waktu: 30-35 menit</strong>
        <br />
        • Target: Minimal 13 soal terjawab benar.
        <br />
        • Hindari soal panjang yang rumit.
        <br />
        • Skip soal sulit, prioritaskan yang mudah.
        <br />
        • Fokus pada Pancasila, UUD 1945, & sejarah Indonesia.
        <br />
        • Gunakan mnemonik untuk fakta penting dan update isu nasional terkini.`,
        image: img3 // Replace with actual image
      },
      {
        title: "3. TIU (Tes Intelegensi Umum)",
        tips: `<strong>Waktu: 40 menit</strong>
        <br />
        • Target: Minimal 16 soal terjawab benar.
        <br />
        • Hindari soal hitung-hitungan rumit.
        <br />
        • Prioritaskan soal yang bisa dijawab cepat.
        <br />
        • Gunakan eliminasi untuk soal sulit.
        <br />
        • Latih logika, matematika dasar, & analogi verbal; jaga kecepatan.`,
        image: img3 // Replace with actual image
      }
    ]
  };    

  // Handle category change
  const handleCategoryChange = (category: CategoryType): void => {
    setActiveCategory(category);
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideTo(0);
    }
  };

  // Handle slide change to next category when reaching the end
  const handleSlideChange = (swiper: SwiperType): void => {
    const { activeIndex, slides } = swiper;
    
    // If we're at the last slide of the current category
    if (activeIndex === slides.length - 1) {
      // Get the categories in order
      const categoryOrder: CategoryType[] = ["LPDP", "UTBK", "CPNS"];
      const currentIndex = categoryOrder.indexOf(activeCategory);
      const nextIndex = (currentIndex + 1) % categoryOrder.length;
      
      // Set a timeout to change category after the last slide is viewed
      setTimeout(() => {
        setActiveCategory(categoryOrder[nextIndex]);
        if (swiperRef.current && swiperRef.current.swiper) {
          swiperRef.current.swiper.slideTo(0);
        }
      }, 3000); // Change after 3 seconds
    }
  };

  return (
    <>
      <CategoryTabs>
        <CategoryTab 
          $active={activeCategory === "LPDP"} 
          onClick={() => handleCategoryChange("LPDP")}
        >
          LPDP
        </CategoryTab>
        <CategoryTab 
          $active={activeCategory === "UTBK"} 
          onClick={() => handleCategoryChange("UTBK")}
        >
          UTBK
        </CategoryTab>
        <CategoryTab 
          $active={activeCategory === "CPNS"} 
          onClick={() => handleCategoryChange("CPNS")}
        >
          CPNS
        </CategoryTab>
      </CategoryTabs>
      
      <Container>
        <Swiper
          ref={swiperRef}
          autoplay={{
            delay: 3000,
            disableOnInteraction: false,
          }}
          className="mySwiper"
          effect={"cards"}
          grabCursor={true}
          modules={[EffectCards, Pagination, Navigation, Autoplay]}
          navigation={true}
          pagination={{
            type: "fraction",
          }}
          scrollbar={{
            draggable: true,
          }}
          onSlideChange={handleSlideChange}
          key={activeCategory} // Force re-render when category changes
        >
          {categories[activeCategory].map((slide, index) => (
            <SwiperSlide key={`${activeCategory}-${index}`}>
              <Image alt={slide.title} height={200} src={slide.image} width={200} />
              <div className="exam-title">{slide.title}</div>
              <div className="exam-tips" dangerouslySetInnerHTML={{ __html: slide.tips }} />
            </SwiperSlide>
          ))}
        </Swiper>
      </Container>
    </>
  );
};

export default Carousel;