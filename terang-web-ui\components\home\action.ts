"use server";

import { createClient } from "redis";

// Types
interface SubjectInfo {
  id: string;
  name: string;
  key: string;
}

interface ExamType {
  id: string;
  name: string;
  subjects: SubjectInfo[];
}

interface ExamConfig {
  examTypes: Record<string, ExamType>;
}

/**
 * Fetches exam configuration from backend with Redis caching
 * @param examType The exam type identifier (e.g., "CPNS", "LPDP")
 * @returns The exam configuration for the specified exam type
 */
export async function fetchExamConfig(examType: string): Promise<ExamType | null> {
  if (!examType) {
    console.error("No exam type provided");
    return null;
  }
  
  const cacheKey = `exam_config_${examType.toLowerCase()}`;
  
  // Try to get from Redis cache first
  const cachedConfig = await retrieveFromRedis(cacheKey);
  if (cachedConfig) {
    console.log(`Using cached exam config for ${examType}`);
    return cachedConfig;
  }
  
  try {
    // Fetch exam configuration from backend
    const configUrl = `${process.env.BACKEND_BASE_URL}/v0/exam-config/${examType}`;
    const response = await fetch(configUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
      next: { revalidate: 3600 }, // Cache for 1 hour on the Edge
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Failed to fetch exam config for ${examType}. Response:`, errorText);
      return null;
    }

    const result = await response.json();
    const examConfig = result.config || result.examInfo;
    
    if (!examConfig) {
      console.error(`No exam config found for ${examType}`);
      return null;
    }
    
    // Store in Redis for faster future access
    await storeToRedis(cacheKey, examConfig);
    
    return examConfig;
  } catch (error) {
    console.error(`Error fetching exam config for ${examType}:`, error);
    return null;
  }
}

/**
 * Fetches all available exam configurations
 * @returns The complete exam configuration object
 */
export async function fetchAllExamConfigs(): Promise<ExamConfig | null> {
  const cacheKey = "all_exam_configs";
  
  // Try to get from Redis cache first
  const cachedConfig = await retrieveFromRedis(cacheKey);
  if (cachedConfig) {
    console.log("Using cached all exam configs");
    return cachedConfig;
  }
  
  try {
    // Fetch all exam configurations
    const configUrl = `${process.env.BACKEND_BASE_URL}/v0/exam-config`;
    const response = await fetch(configUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
      next: { revalidate: 3600 }, // Cache for 1 hour on the Edge
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Failed to fetch all exam configs. Response:", errorText);
      return null;
    }

    const result = await response.json();
    const examConfig = result.config;
    
    if (!examConfig) {
      console.error("No exam configs found");
      return null;
    }
    
    // Store in Redis for faster future access
    await storeToRedis(cacheKey, examConfig);
    
    return examConfig;
  } catch (error) {
    console.error("Error fetching all exam configs:", error);
    return null;
  }
}

// Redis utility functions (reused from your code)
const createWriteClient = () => createClient({
  url: process.env.REDIS_URL_WRITE as string,
});

const createReadClient = () => createClient({
  url: process.env.REDIS_URL_READ as string,
});

export async function storeToRedis(key: string, value: any) {
  const redis = createWriteClient();

  try {
    await redis.connect();
    await redis.set(key, JSON.stringify(value), {
      EX: 21600, // Set expiration to 6 hours (21600 seconds)
    });
  } catch (error) {
    console.error("Failed to store data in Redis:", error);
  } finally {
    await redis.disconnect();
  }
}

export async function retrieveFromRedis(key: string): Promise<any | null> {
  const redis = createReadClient();

  try {
    await redis.connect();
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error("Failed to retrieve data from Redis:", error);
    return null;
  } finally {
    await redis.disconnect();
  }
}