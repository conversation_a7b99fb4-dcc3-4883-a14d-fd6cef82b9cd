import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ody, <PERSON><PERSON>, <PERSON> } from "@heroui/react";

interface PropsIconSilang extends React.SVGProps<SVGSVGElement> {}

const IconSilang: React.FC<PropsIconSilang> = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <circle cx="12" cy="12" r="10" />
    <path d="m15 9-6 6" />
    <path d="m9 9 6 6" />
  </svg>
);

const BannerSelamatDatang: React.FC = () => {
  const [keliatan, setKeliatan] = useState<boolean>(true);

  if (!keliatan) return null;

  return (
    <Card className="max-w-2xl mx-auto my-5 bg-cyan-600 text-white">
      <CardBody className="relative p-6">
        <Button
          isIconOnly
          className="absolute top-2 right-2 text-white bg-transparent hover:bg-cyan-700"
          onClick={() => setKeliatan(false)}
        >
          <IconSilang className="h-5 w-5" />
        </Button>
        <div className="text-center">
          <h3 className="text-xl font-semibold mb-4">
            Makasih ya udah join Terang AI, kamu keren banget!
          </h3>
          <p className="mb-6">
            Heyyo! Terra di sini, siap bantu kamu di Terang AI. Makasih udah mau gabung ke squad kita!
          </p>
          <Button
            as={Link}
            href="https://whatsapp.com/channel/0029VaqSaX6DTkK2qxC3eR3T"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-white text-cyan-600 hover:bg-gray-100"
          >
            Gas, join channel WhatsApp kita yuk!
          </Button>
        </div>
      </CardBody>
    </Card>
  );
};

export default BannerSelamatDatang;