"use client";

import { Inter } from "next/font/google";
import { ChatUI } from "../components/chat-ui";
import rehypeKatex from "rehype-katex";
import 'katex/dist/katex.min.css'


interface ChatAIProps {
  systemContext: string;
  selectedOptions: string;
}

const inter = Inter({ subsets: ["latin"] });

const ChatAI = ({ systemContext, selectedOptions }: ChatAIProps) => {
  // Generate random ID for chat session
  const randomId = Math.random().toString(36).substring(2, 10);
  const fromPage = "result";

  return (
    <div className={`h-full w-full ${inter.className}`}>
      <ChatUI 
        id={randomId} 
        fromPage={fromPage}
        systemContext={systemContext}
        selectedOptions={selectedOptions}
      />
    </div>
  );
};

export default ChatAI;