import { NextResponse } from "next/server";
import { matchUserToken } from "@/app/lib/actions/auth/actions";
export const dynamic = "force-dynamic";

export async function GET(request: Request) {
  try {
    const url = new URL(request.url);
    const token = url.searchParams.get("token");

    if (!token) {
      return NextResponse.json(
        { success: false, error: "Token not found" },
        { status: 400 },
      );
    }

    const res = await matchUserToken(token);

    if (!res) {
      return NextResponse.json(
        { success: false, error: "Invalid Token or Session Data" },
        { status: 400 },
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error verifying email:", error);

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 },
    );
  }
}