# Use a single stage since we're using pre-built assets
FROM node:20-alpine

WORKDIR /app

ENV NODE_ENV production
ENV PORT 3000

# Add a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy pre-built assets and necessary files
COPY build ./build
COPY public ./public
COPY node_modules ./node_modules
COPY package.json ./
COPY next.config.js ./
COPY .env ./
COPY tsconfig.json ./

# Set ownership to the non-root user
RUN chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

EXPOSE 3000

CMD ["npm", "start"]