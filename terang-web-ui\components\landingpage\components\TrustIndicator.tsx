import React from 'react';
import { useLanguage } from '@/app/language-wrapper'; // Adjust import path according to your project structure

const TerangInlineTrust = () => {
  const { t } = useLanguage();

  // Array of avatar URLs - you can choose from different services
  const avatarUrls = [
    // Option 1: <PERSON><PERSON><PERSON><PERSON> (AI-generated avatars) - Most recommended
    "https://api.dicebear.com/7.x/avataaars/svg?seed=John&backgroundColor=3b82f6",
    "https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah&backgroundColor=10b981", 
    "https://api.dicebear.com/7.x/avataaars/svg?seed=Mike&backgroundColor=8b5cf6",
    "https://api.dicebear.com/7.x/avataaars/svg?seed=Anna&backgroundColor=f97316",
    
    // Option 2: Pravatar (Random photos)
    // "https://i.pravatar.cc/24?img=1",
    // "https://i.pravatar.cc/24?img=2",
    // "https://i.pravatar.cc/24?img=3", 
    // "https://i.pravatar.cc/24?img=4",
    
    // Option 3: UI Avatars (Text-based)
    // "https://ui-avatars.com/api/?name=John&size=24&background=3b82f6&color=fff",
    // "https://ui-avatars.com/api/?name=Sarah&size=24&background=10b981&color=fff",
    // "https://ui-avatars.com/api/?name=Mike&size=24&background=8b5cf6&color=fff", 
    // "https://ui-avatars.com/api/?name=Anna&size=24&background=f97316&color=fff",
  ];

  return (
    <div className="inline-flex items-center bg-white rounded-full px-4 py-2 shadow-md border border-gray-100">
      <div className="flex -space-x-1 mr-3">
        {avatarUrls.map((url, index) => (
          <img
            key={index}
            src={url}
            alt={`User avatar ${index + 1}`}
            className="w-6 h-6 rounded-full border-2 border-white object-cover"
            loading="lazy"
          />
        ))}
      </div>
      
      <span 
        className="text-gray-700 text-sm"
        dangerouslySetInnerHTML={{ __html: t('trust_indicator_text') }}
      ></span>
    </div>
  );
};

export default TerangInlineTrust;