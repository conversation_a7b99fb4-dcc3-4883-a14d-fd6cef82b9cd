export interface AvailableExamsType {
  id: string;
  name: string;
  subname: string;
  description: string;
  baseline_price: number;
  visibility: string;
  media_url: string;
  category_name: string | null; // Updated to match the new field, nullable in case there's no category
  is_purchased: boolean; // New field to indicate purchase status
  purchase_date: string | null; // New field for the date of purchase, nullable
  user_id: string;
  created_at: string;
  modified_at: string;
  duration: string;
  type?: string;
  subject?: string;
}

export interface Metadata {
  level: number;
  name: string;
  value: string | null;
}

export interface Content {
  content: string;
  type: string;
}

export interface Option {
  id: string;
  data: Array<{ contents: Content[] }>;
  is_correct: boolean;
}

export interface Options {
  shuffle: boolean;
  values: Option[];
}

export interface QuestionData {
  id: string;
  metadata: Metadata[];
  instruction: any[]; // You may want to define a more specific type if needed
  question: Array<{ contents: Content[] }>;
  options: Options;
  hints: any[]; // You may want to define a more specific type if needed
  explanation: Array<{ contents: Content[] }>;
  insight: any[]; // You may want to define a more specific type if needed
}

export interface ExamSession {
  id: string;
  session_id: string;
  user_id: string;
  exam_id: string;
  type: string;
  status: string;
  start_time: string;
  end_time: string;
  answers: string;
  flagged_questions: string;
  created_at: string;
  modified_at: string;
  exam_duration: string;
  elapsed_duration: string;
  remaining_duration: string;
  subject: string;
}

export interface TrialSession {
  id: string;
  session_id: string;
  user_id: string;
  exam_id: string;
  type: string;
  status: string;
  start_time: string;
  end_time: string;
  answers: string;
  flagged_questions: string;
  remaining_duration?: string;
  created_at: string;
  modified_at: string;
  subject: string;
}

export interface EducationLevel {
  id: number;
  name: string;
}

export interface ProgramStudy {
  tk_pend: string;
  kode_pend: number;
  nama_pend: string;
}

export interface BundleCategory {
  id: string;
  name: string;
}

export interface Bundle {
  id: string;
  name: string;
  description: string;
  price: number;
  discountPercentage: {
    Int32: number;
    Valid: boolean;
  };
  thumbnailUrl: {
    String: string;
    Valid: boolean;
  };
  bannerUrl: {
    String: string;
    Valid: boolean;
  };
  visibility: string;
  validFrom: {
    Time: string;
    Valid: boolean;
  };
  validUntil: {
    Time: string;
    Valid: boolean;
  };
  metadata: string;
  createdAt: string;
  modifiedAt: string;
  userId: string;
  category: BundleCategory;
}

export interface BundleResponse {
  bundles: Bundle[];
  pagination: {
    total_data: number;
    total_pages: number;
    current_page: number;
    page_size: number;
  };
}

// For a more user-friendly version to use in components
export interface BundleUI {
  id: string;
  name: string;
  description: string;
  price: number;
  discountPercentage: number | null;
  thumbnailUrl: string | null;
  bannerUrl: string | null;
  visibility: string;
  validFrom: string | null;
  validUntil: string | null;
  createdAt: string;
  modifiedAt: string;
  categoryId: string;
  categoryName: string;
}

// This will hold the connection between bundles and the exams they contain
export interface BundleExam {
  bundleId: string;
  examId: string;
}

// Interview Types

export interface InterviewSection {
  id: string;
  name: string;
  duration: string;
}

export interface AvailableInterview {
  id: string;
  name: string;
  subname: string;
  description: string;
  baseline_price: number;
  visibility: "PUBLIC" | "PRIVATE";
  duration: string;
  type: "INTERVIEW";
  subject: string | null;
  created_at: string;
  modified_at: string;
  user_id: string;
  media_url: string;
  category_name: string;
  category_id: string;
  is_purchased: boolean;
  purchase_date: string;
  is_free_access: string;
  interview_type: "full" | "partial";
  sections: InterviewSection[];
}

export interface AvailableInterviewsResponse {
  data: AvailableInterview[];
  msg: string;
  status: string;
}

export interface InterviewQuestion {
  id: string;
  question: string;
  expectedAnswerTopics?: string[];
  followUpQuestions?: string[];
}

export interface InterviewFeedback {
  sectionId: string;
  strengths: string[];
  improvements: string[];
  overallRating: number; // 1-5
  commentsByInterviewer: string;
}

export interface InterviewSubmission {
  interviewId: string;
  userId: string;
  startTime: string;
  endTime: string;
  totalDuration: number; // in seconds
  completedSections: {
    sectionId: string;
    questions: {
      questionId: string;
      userAnswer: string;
      duration: number; // in seconds
    }[];
  }[];
  feedback?: InterviewFeedback;
}

// Combined type for easier imports
export type AvailableInterviewsType = AvailableInterviewsResponse;