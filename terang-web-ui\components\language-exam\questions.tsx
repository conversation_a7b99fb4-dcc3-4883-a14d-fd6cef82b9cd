import React, { FC, Fragment } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spinner } from "@heroui/react";
import DOMPurify from 'dompurify';
import ReactMarkdown from "react-markdown";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import remarkBreaks from "remark-breaks";
import remarkGfm from 'remark-gfm'; 
import 'katex/dist/katex.min.css';
import { Components } from "react-markdown";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { tomorrow } from "react-syntax-highlighter/dist/esm/styles/prism";
import rehypeRaw from 'rehype-raw'


import { AdaptiveImage } from "../shared/adaptive-image";
import { NextPrev } from "../shared/buttons/next-prev";
import { FlagButton } from "../shared/buttons/flag";
import { QuestionData, Option, Content } from "../types";
import { 
  processHtmlAndLaTeX,
  containsLaTeX,
  processLaTeX 
} from "../shared/latex-handler"; 

// Define explicit types if they're not already defined in your types file
interface ContentType {
  type: string;
  content: string;
}

interface ItemType {
  contents: ContentType[];
}

// Define proper types for the code component props
interface CodeProps {
  node?: any;
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
}

interface TableContainerProps {
  children: React.ReactNode;
}

interface QuestionProps {
  questionData: QuestionData;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  flaggedQuestions: { [key: string]: boolean };
  setFlaggedQuestions: (questionId: string) => void;
  selectedOptions: { [key: string]: string };
  setSelectedOptions: (questionId: string, optionId: string) => void;
  sessionId: string;
  totalQuestions: number;
  questionLoading: boolean;
}

export const Questions: FC<QuestionProps> = ({
  questionData,
  currentPage,
  setCurrentPage,
  flaggedQuestions,
  setFlaggedQuestions,
  selectedOptions,
  setSelectedOptions,
  sessionId,
  totalQuestions,
  questionLoading,
}) => {
  const handleNext = (): void => {
    if (currentPage < totalQuestions - 1) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePrev = (): void => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleOptionClick = (optionId: string): void => {
    setSelectedOptions(questionData.id, optionId);
  };

  const toggleFlag = (): void => {
    setFlaggedQuestions(questionData.id);
  };

  const optionLabels: string[] = ["A", "B", "C", "D", "E", "F", "G"];

  const TableContainer: React.FC<TableContainerProps> = ({ children }) => (
    <div className="max-w-full overflow-hidden my-4">
      <div className="overflow-x-auto rounded-md border border-gray-200 bg-white shadow-sm">
        {children}
      </div>
    </div>
  );
  const markdownComponents: Components = {
    // Code blocks with syntax highlighting
    code: ({ node, inline, className, children, ...props }: CodeProps) => {
      const match = /language-(\w+)/.exec(className || '');
      return !inline && match ? (
        <div className="max-w-full overflow-hidden my-4">
          <div className="overflow-x-auto">
            <SyntaxHighlighter
              style={tomorrow}
              language={match[1]}
              PreTag="div"
              className="rounded-md"
              customStyle={{
                margin: 0,
                width: 'fit-content',
                minWidth: '100%'
              }}
              {...props}
            >
              {String(children).replace(/\n$/, '')}
            </SyntaxHighlighter>
          </div>
        </div>
      ) : (
        <code className={`${className} bg-gray-100 rounded px-1 break-words`} {...props}>
          {children}
        </code>
      );
    },
    // Headings
    h1: ({ children }) => (
      <h1 className="text-2xl font-bold mb-4 mt-6">{children}</h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-xl font-bold mb-3 mt-5">{children}</h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-lg font-bold mb-2 mt-4">{children}</h3>
    ),
    h4: ({ children }) => (
      <h4 className="text-base font-bold mb-2 mt-4">{children}</h4>
    ),
    h5: ({ children }) => (
      <h5 className="text-sm font-bold mb-2 mt-4">{children}</h5>
    ),
    h6: ({ children }) => (
      <h6 className="text-xs font-bold mb-2 mt-4">{children}</h6>
    ),
    
    // Updated list components with proper LaTeX handling
    ul: ({ children }) => (
      <ul className="mb-4 ml-4 space-y-2 list-disc last:mb-0 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
        {children}
      </ul>
    ),
    ol: ({ children }) => (
      <ol className="mb-4 ml-4 space-y-2 list-decimal last:mb-0 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
        {children}
      </ol>
    ),
    li: ({ children }) => (
      <li className="ml-4 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
        {children}
      </li>
    ),
  
    p: (props: any) => {
      const children = props.children;
      
      // Helper function to extract text and handle special cases
      const extractText = (node: any): string => {
        if (typeof node === 'string') return node;
        if (Array.isArray(node)) return node.map(extractText).join(' ');
        if (node?.props?.children) return extractText(node.props.children);
        return '';
      };
    
      // Get combined text from all children
      const text = extractText(children);
      
      // Use a more robust pattern with custom delimiters
      // Format: @video{title}(url)
      const videoPattern = /@video\{([^}]*)\}\(([^)]*)\)/;
      const match = text.match(videoPattern);
    
      if (match) {
        const [_, title, url] = match;
        return (
          <div className="my-4">
            {/* <VideoPlayer 
              src={url}
              title={title}
              className="w-full rounded-lg"
            /> */}
          </div>
        );
      }
    
      // Regular paragraph rendering
      return (
        <p className="whitespace-pre-wrap break-words mb-4 last:mb-0 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
          {children}
        </p>
      );
    },
    
    // Blockquote
    blockquote: ({ children }) => (
      <blockquote className="border-l-4 border-gray-200 pl-4 my-4 italic last:mb-0">
        {children}
      </blockquote>
    ),
    pre: ({ children }) => (
      <pre className="font-mono text-sm bg-gray-50 p-4 rounded-lg overflow-x-auto whitespace-pre my-4">
        {children}
      </pre>
    ),
    
    // Inline elements
    strong: ({ children }) => (
      <strong className="font-bold">{children}</strong>
    ),
    em: ({ children }) => (
      <em className="italic">{children}</em>
    ),
    del: ({ children }) => (
      <del className="line-through">{children}</del>
    ),
    
    // Links and images
    a: ({ href, children }) => (
      <a 
        href={href} 
        className="text-blue-600 hover:text-blue-800 underline"
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
    img: ({ src, alt }) => (
      <img 
        src={src} 
        alt={alt} 
        className="max-w-full h-auto my-4 rounded-lg"
        loading="lazy"
      />
    ),
    
    table: ({ children }) => (
      <TableContainer>
        <table className="min-w-full divide-y divide-gray-200 font-mono text-sm">
          {children}
        </table>
      </TableContainer>
    ),
    
    thead: ({ children }) => (
      <thead className="bg-gray-50 border-b border-gray-200">
        {children}
      </thead>
    ),
    
    tbody: ({ children }) => (
      <tbody className="divide-y divide-gray-200 bg-white">
        {children}
      </tbody>
    ),
    
    tr: ({ children, className }) => (
      <tr className={`${className || ''} hover:bg-gray-50`}>
        {children}
      </tr>
    ),
    
    th: ({ children }) => (
      <th className="px-6 py-3 text-center font-medium text-gray-900 whitespace-nowrap border-x border-gray-200">
        {children}
      </th>
    ),
    
    td: ({ children }) => (
      <td className="px-6 py-3 text-center font-mono whitespace-nowrap border-x border-gray-200">
        {children}
      </td>
    ),
    
    // Horizontal rule
    hr: () => (
      <hr className="my-8 border-t border-gray-200" />
    ),
    
    // Definition lists
    dl: ({ children }) => (
      <dl className="mb-4 space-y-2">{children}</dl>
    ),
    dt: ({ children }) => (
      <dt className="font-bold">{children}</dt>
    ),
    dd: ({ children }) => (
      <dd className="ml-4">{children}</dd>
    ),
    
    // Additional inline elements
    sup: ({ children }) => (
      <sup className="text-xs align-super">{children}</sup>
    ),
    sub: ({ children }) => (
      <sub className="text-xs align-sub">{children}</sub>
    ),
    kbd: ({ children }) => (
      <kbd className="px-2 py-1.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">
        {children}
      </kbd>
    ),
    mark: ({ children }) => (
      <mark className="bg-yellow-200 px-1 rounded">
        {children}
      </mark>
    )
  };
  
  const renderContent = (content: Content) => {
    if (content.type === "text") {
      return (
        <ReactMarkdown
          remarkPlugins={[remarkMath,remarkGfm]}
          rehypePlugins={[rehypeKatex, rehypeRaw]}
          components={markdownComponents}
        >
          {processLaTeX(content.content).replace('<br>','\n')}
        </ReactMarkdown>
      );
    }
    
    if (content.type === "media") {
      return <AdaptiveImage src={content.content} alt="Question media" />;
    }
    
    return null;
  };

  const renderMetadata = () => {
    if (!questionData.metadata || !Array.isArray(questionData.metadata)) return null;

    const getMetadataValue = (name: string) => {
      const item = questionData.metadata.find(item => item.name === name);
      return item ? item.value : '';
    };

    const subject = getMetadataValue('subject');
    const category = getMetadataValue('category');
    const keywordsValue = getMetadataValue('keywords');
    const keywords = keywordsValue ? keywordsValue.split(';').filter(Boolean) : [];

    return (
      <div className="mb-4 space-y-3 text-base">
        <div className="flex flex-wrap gap-4 items-center">
          {subject && (
            <div className="flex items-center gap-2">
              <span className="text-gray-600 text-base">Mata Uji:</span>
              <span className="bg-blue-100 text-blue-800 px-3 py-1.5 rounded text-sm">
                {subject}
              </span>
            </div>
          )}
          {category && (
            <div className="flex items-center gap-2">
              <span className="text-gray-600 text-base">Kategori:</span>
              <span className="bg-green-100 text-green-800 px-3 py-1.5 rounded text-sm">
                {category}
              </span>
            </div>
          )}
        </div>
        {keywords.length > 0 && (
          <div className="flex items-start gap-2">
            <span className="text-gray-600 text-base">Topik:</span>
            <div className="flex-1 flex flex-wrap gap-2">
              {keywords.map((keyword, idx: number) => (
                <span 
                  key={idx}
                  className="bg-gray-100 text-gray-800 px-3 py-1.5 rounded text-sm"
                >
                  {keyword.trim()}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderInstructionContent = (questionData: QuestionData) => {
    // Check if instruction exists, is an array, and has items
    if (!questionData.instruction || !Array.isArray(questionData.instruction) || questionData.instruction.length === 0) {
      return null;
    }
  
    // Check if the instruction only contains empty content
    const hasNonEmptyContent = questionData.instruction.some((item: any) => {
      if (!item.contents || !Array.isArray(item.contents)) return false;
      
      return item.contents.some((content: any) => {
        if (typeof content !== 'object' || content === null) return false;
        if (!('content' in content) || typeof content.content !== 'string') return false;
        
        return content.content.trim() !== "";
      });
    });
  
    // Return null if all content is empty
    if (!hasNonEmptyContent) {
      return null;
    }
  
    return (
      <Card className="w-full mb-6 bg-gray-50 border border-gray-200">
        <CardBody className="py-4 px-5">
          <h4 className="text-lg font-semibold text-gray-700 mb-2">Bacalah petunjuk soal berikut.</h4>
          <div className="instruction-content">
            {questionData.instruction.map((item: ItemType, index: number) => (
              <Fragment key={`instruction-${index}`}>
                {item.contents && item.contents.map((content: ContentType, contentIndex: number) => (
                  <Fragment key={`instruction-content-${contentIndex}`}>
                    {renderContent(content)}
                  </Fragment>
                ))}
              </Fragment>
            ))}
          </div>
        </CardBody>
      </Card>
    );
  };

  const renderQuestionContent = (questionData: QuestionData) => {
    return questionData.question.map((item: ItemType, index: number) => (
      <Fragment key={`question-${index}`}>
        {item.contents && item.contents.map((content: ContentType, contentIndex: number) => (
          <Fragment key={`question-content-${contentIndex}`}>
            {renderContent(content)}
          </Fragment>
        ))}
      </Fragment>
    ));
  };

  const renderOptionContent = (option: Option) => {
    return option.data.map((item: ItemType, index: number) => (
      <Fragment key={`option-${index}`}>
        {item.contents && item.contents.map((content: ContentType, contentIndex: number) => (
          <Fragment key={`option-content-${contentIndex}`}>
            {renderContent(content)}
          </Fragment>
        ))}
      </Fragment>
    ));
  };

  return (
    <Card
      className="w-full"
      style={{ fontFamily: "'Nunito', sans-serif", fontSize: "20px" }}
    >
      <CardHeader
        className="px-4 py-4 bg-gray-100 rounded-t-lg flex justify-center items-center"
        style={{ boxShadow: "0 3px 6px rgba(0, 0, 0, 0.08)" }}
      >
        <h4 className="font-bold text-gray-800">
          <span className="font-bold text-lg">{currentPage + 1}</span> dari{" "}
          {totalQuestions}
        </h4>
      </CardHeader>
      <CardBody className="overflow-visible py-6 px-6 flex flex-col">
        {questionLoading ? (
          <div className="w-full h-[400px] flex items-center justify-center">
            <Spinner color="primary" size="lg" />
          </div>
        ) : (
          <>
            {renderMetadata()}
            {renderInstructionContent(questionData)}
            <div className="question-container">
              {renderQuestionContent(questionData)}
            </div>
            <div className="text-default-500 text-small mt-2 py-4">
              <b>Pilih jawaban yang benar</b>
            </div>
            <div className="flex flex-col space-y-2 w-full">
              {questionData.options.values.map((option, optionIndex: number) => (
                <Card
                  key={option.id}
                  isPressable
                  className={`w-full ${
                    selectedOptions[questionData.id] === option.id
                      ? "bg-primary-100"
                      : ""
                  }`}
                  shadow="sm"
                  onPress={() => handleOptionClick(option.id)}
                >
                  <CardBody className="p-3 py-3 flex items-center">
                    <div className="flex items-center w-full">
                      <span className="mr-3 font-bold">
                        {optionLabels[optionIndex]}.
                      </span>
                      <div className="flex-grow">
                        {renderOptionContent(option)}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
            </div>
          </>
        )}
        <div className="flex flex-col items-end sm:flex-row sm:justify-end mt-4">
          <div className="mb-4 sm:mb-0">
            <FlagButton
              isFlagged={flaggedQuestions[questionData.id] || false}
              onToggleFlag={toggleFlag}
            />
          </div>
          <div className="ml-0 sm:ml-4 flex">
            <NextPrev
              currentPage={currentPage + 1}
              totalPages={totalQuestions}
              onNext={handleNext}
              onPrev={handlePrev}
            />
          </div>
        </div>
      </CardBody>
    </Card>
  );
};