import { ExamGradingResponse, PassingGradeEntry, ProgramThreshold } from "./grading-types";

// Helper function to get subject-specific information
export function getSubjectInfo(subject: string, gradingInfo: ExamGradingResponse | null): {
  passingGrade: number | null;
  totalQuestions: number | null;
} {
  if (!gradingInfo) {
    return { passingGrade: null, totalQuestions: null };
  }

  // Find the passing grade for the subject
  for (const category of gradingInfo.grading_info) {
    for (const entry of category.passing_grade) {
      if (entry.nama === subject) {
        return {
          passingGrade: entry.score,
          totalQuestions: entry.question_count,
        };
      }
    }
  }

  // If not found in passing_grade, look in question_counts
  const questionCountEntry = gradingInfo.question_counts.find(
    (qc) => qc.Subject === subject
  );

  if (questionCountEntry) {
    return {
      passingGrade: null, // Unable to determine passing grade
      totalQuestions: questionCountEntry.QuestionCount,
    };
  }

  return { passingGrade: null, totalQuestions: null };
}

// Helper function to get category-specific information
export function getCategoryInfo(category: string, gradingInfo: ExamGradingResponse | null): {
  passingGrades: PassingGradeEntry[];
  programThresholds: ProgramThreshold[];
} {
  if (!gradingInfo) {
    return { passingGrades: [], programThresholds: [] };
  }

  const categoryData = gradingInfo.grading_info.find(
    (info) => info.category === category
  );

  if (categoryData) {
    return {
      passingGrades: categoryData.passing_grade,
      programThresholds: categoryData.program_thresholds,
    };
  }

  return { passingGrades: [], programThresholds: [] };
}