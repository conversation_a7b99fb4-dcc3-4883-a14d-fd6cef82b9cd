import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { 
  orderExams, 
  storePaymentInfo, 
  storePaymentToken, 
  getPaymentToken, 
  updatePaymentStatus, 
  finalRedeemDiscount, 
  removePaymentInfo, 
  getPaymentInfo,
} from '@/app/lib/actions/available-exams/actions';
import DotLottieAnimation from '../shared/dotlottie-animation';
import { fetchPurchasedBundles } from '@/store/slices/purchasedBundlesSlice';
import {
  Button,
  Input,
} from "@heroui/react";
import useLoginModal from "@/hooks/use-login-modal"; // Import the login modal hook
import LoginModal from '@/app/(events)/events/beasiswa-unggulan/login-modal';

// Define a consistent notification type to use across components
export type NotificationType = "success" | "info" | "error" | "warning";

// Interface for bundling purchase button props
export interface BundlingPurchaseButtonProps {
  examIds: string[] | number[];
  discountPercentage?: number;
  onPurchase?: (ids: string[] | number[]) => Promise<void> | void;
  onClose?: () => void;
  showNotification?: (message: string, type: NotificationType) => void;
  bundleId?: string;
  bundleName?: string;
  categoryName?: string;
  bundlePrice?: number;
  bundleDetails?: {
    bundle: any;
    examCount: number;
  } | null;
  bundleDetailsLoading?: boolean;
  referralCode?: string;
  hideReferralInput?: boolean;
  setRefreshData?: React.Dispatch<React.SetStateAction<boolean>>;
  serverDiscountInfo?: {
    isValid: boolean;
    discountPercentage: number | null;
    discountedPrices: Record<string, number>;
  };
  isUserLoggedIn?: boolean;
}

interface PaymentInfo {
  orderId: string;
  examId: string;
  bundleId?: string;
  invoiceId: string;
  paymentId: string;
  refCode?: string;
}

interface PaymentResult {
  transaction_status: string;
  payment_type: string;
  issuer: string;
  acquirer: string;
  transaction_id: string;
  transaction_time: string;
}

interface DiscountInfo {
  originalPrice: number;
  discountPercentage: number;
  discountAmount: number;
  finalPrice: number;
}

// Add this type definition for the purchased bundle item structure
interface PurchasedBundleItem {
  bundle?: {
    id?: string;
    name?: string;
  };
  id?: string;
  purchaseDate?: string;
  orderId?: string;
  orderStatus?: string;
  paymentStatus?: string;
  examsTotal?: number;
  examsCompleted?: number;
  examsCompletedRate?: number;
}

// Declare the global Snap interface
declare global {
  interface Window {
    snap?: {
      pay: (token: string, options: any) => void;
    };
  }
}

const EnhancedBundlingPurchaseButton: React.FC<BundlingPurchaseButtonProps> = ({ 
  examIds = [], 
  discountPercentage = 15, 
  onPurchase,
  onClose,
  showNotification,
  bundleId,
  bundleName,
  categoryName,
  bundlePrice,
  bundleDetails,
  bundleDetailsLoading = false,
  referralCode: initialReferralCode,
  hideReferralInput = false,
  setRefreshData,
  serverDiscountInfo,
  isUserLoggedIn = false
}) => {
  // Access the login modal directly
  const loginModal = useLoginModal();
  
  // Refs to prevent unnecessary renders
  const initialCheckDone = useRef(false);
  const scriptLoadAttempted = useRef(false);

  // Core state variables
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isVisible, setIsVisible] = useState<boolean>(true);
  const [scriptLoaded, setScriptLoaded] = useState<boolean>(false);
  const [pendingPayment, setPendingPayment] = useState<PaymentInfo | null>(null);
  const [isPulsing, setIsPulsing] = useState<boolean>(false);
  const [isAlreadyPurchased, setIsAlreadyPurchased] = useState<boolean>(false);
  const [shouldRender, setShouldRender] = useState<boolean>(false);
  const [isCheckingPurchase, setIsCheckingPurchase] = useState<boolean>(true);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);
  
  // Derived state for better performance
  const [price, setPrice] = useState<number | null>(bundlePrice || null);
  
  // Referral code functionality - now using server-provided data when available
  const [referralCode, setReferralCode] = useState<string>(initialReferralCode || '');
  const [referralError, setReferralError] = useState<string>('');
  const [discountInfo, setDiscountInfo] = useState<DiscountInfo | null>(null);
  
  // Registration state for after purchase
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState<boolean>(false);
  const [userEmail, setUserEmail] = useState<string>('');
  const [userName, setUserName] = useState<string>('');
  const [shouldRegisterAfterPurchase, setShouldRegisterAfterPurchase] = useState<boolean>(false);
  const [purchaseCompleted, setPurchaseCompleted] = useState<boolean>(false);

  const animationTimerRef = useRef<NodeJS.Timeout | null>(null);

  const examCount = useMemo(() => {
    return bundleDetails?.examCount || examIds.length;
  }, [bundleDetails, examIds.length]);
  
  // Redux connections
  const dispatch = useAppDispatch();
  const { loading, error, items: bundles } = useAppSelector((state) => state.bundles);
  const purchasedBundlesState = useAppSelector((state) => state.purchasedBundles) as {
    loading: boolean;
    error: string | null;
    items: PurchasedBundleItem[];
  };
  
  // Format price to Indonesian Rupiah - memoized for better performance
  const formatPrice = useCallback((amount: number): string => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  }, []);

  // Handle close functionality
  const handleClose = useCallback(() => {
    setIsAnimating(false);
    
    setTimeout(() => {
      setIsVisible(false);
      if (onClose) {
        onClose();
      }
    }, 300);
  }, [onClose]);

  const getDiscountPercentage = (discount: any, defaultValue: number = 0): number => {
    if (discount === null || discount === undefined) {
      return defaultValue;
    }
    
    if (typeof discount === 'number') {
      return discount;
    }
    
    if (discount.Valid && typeof discount.Int32 === 'number') {
      return discount.Int32;
    }
    
    return defaultValue;
  };

  // Get effective discount percentage
  const effectiveDiscountPercentage = bundleDetails?.bundle?.discountPercentage ? 
  getDiscountPercentage(bundleDetails.bundle.discountPercentage) : 
  discountPercentage;

  // Apply server-side discount information when available
  useEffect(() => {
    if (serverDiscountInfo?.isValid && serverDiscountInfo.discountPercentage && price) {
      const calculatedDiscountAmount = Math.floor((price * serverDiscountInfo.discountPercentage) / 100);
      
      // Apply bundle discount
      const bundleDiscountAmount = Math.round(price * (effectiveDiscountPercentage / 100));
      const finalPrice = Math.max(0, price - bundleDiscountAmount - calculatedDiscountAmount);
      
      setDiscountInfo({
        originalPrice: price,
        discountPercentage: serverDiscountInfo.discountPercentage,
        discountAmount: calculatedDiscountAmount,
        finalPrice
      });
    }
  }, [serverDiscountInfo, price, effectiveDiscountPercentage]);

  // Check if bundle is already purchased (from Redux state)
  // This effect runs once after initial load
  useEffect(() => {
    const checkingRef = {current: false};
    const checkIfPurchased = async () => {
      // Prevent multiple simultaneous checks
      if (checkingRef.current) return;
      checkingRef.current = true;
      
      setIsCheckingPurchase(true);
      
      // Make sure bundleId exists and is not undefined or null
      if (!bundleId) {
        setIsCheckingPurchase(false);
        checkingRef.current = false;
        return;
      }
      
      try {
        // First, check if we have any purchased bundles in the Redux state
        if (bundleId && purchasedBundlesState.items && Array.isArray(purchasedBundlesState.items)) {
          // Check Redux state first (this is fast and doesn't require API call)
          const isPurchased = purchasedBundlesState.items.some(item => {
            // Safely check if item exists and is an object
            if (!item || typeof item !== 'object') return false;
            
            // Handle direct matching with item.id
            if (item.id && item.id === bundleId) return true;
            
            // Handle nested bundle object matching
            if (item.bundle && typeof item.bundle === 'object' && item.bundle.id && item.bundle.id === bundleId) {
              return true;
            }
            
            return false;
          });
          
          if (isPurchased) {
            console.log(`Bundle ${bundleId} is already purchased (Redux check)`);
            setIsAlreadyPurchased(true);
            setIsCheckingPurchase(false);
            checkingRef.current = false;
            return;
          }
        }
        
        // Only make API call if necessary (prevents network spam)
        const response = await fetch('/api/exam-bundles-purchased');
        
        if (!response.ok) {
          console.error(`Failed to fetch purchased bundles: ${response.statusText}`);
          checkingRef.current = false;
          setIsCheckingPurchase(false);
          return;
        }
        
        const data = await response.json();
        
        if (data && data.bundles && Array.isArray(data.bundles.purchasedBundles)) {
          const isPurchasedFromApi = data.bundles.purchasedBundles.some(
            (item: any) => {
              return item && 
                    item.bundle && 
                    typeof item.bundle === 'object' && 
                    item.bundle.id && 
                    item.bundle.id === bundleId;
            }
          );
          
          if (isPurchasedFromApi) {
            console.log(`Bundle ${bundleId} is already purchased (API check)`);
            setIsAlreadyPurchased(true);
          } else {
            // Reset isAlreadyPurchased to false if not found
            setIsAlreadyPurchased(false);
          }
        }
      } catch (error) {
        console.error("Error checking purchased status:", error);
      } finally {
        // Always make sure to clear flags in finally block
        setIsCheckingPurchase(false);
        checkingRef.current = false;
      }
    };
    
    // Only trigger API fetch when bundleId changes, not when category changes
    if (bundleId) {
      // Use a slight delay to prevent rapid consecutive API calls
      const timer = setTimeout(() => {
        checkIfPurchased();
      }, 300);
      
      return () => clearTimeout(timer);
    }
  }, [dispatch, bundleId, purchasedBundlesState.items]);

  useEffect(() => {
    // Only fetch purchased bundles once on initial mount
    dispatch(fetchPurchasedBundles());
  }, [dispatch]);

  useEffect(() => {
    // When category changes, handle animation with proper sequencing
    const handleCategoryChange = () => {
      // Clear any existing animation timers
      if (animationTimerRef.current) {
        clearTimeout(animationTimerRef.current);
      }
      
      // Gracefully hide with a subtle delay
      setIsAnimating(false);
      
      // Use a slightly longer delay before showing again for smoother transition
      animationTimerRef.current = setTimeout(() => {
        // Only animate if the component should be rendered
        if (shouldRender) {
          setIsAnimating(true);
        }
      }, 400); // Longer delay for smoother transition
    };
  
    // Initial animation on mount
    if (!isAnimating && shouldRender) {
      animationTimerRef.current = setTimeout(() => {
        setIsAnimating(true);
      }, 100);
    }
    
    // Handle animation on category change
    if (categoryName) {
      handleCategoryChange();
    }
    
    // Clean up the timers
    return () => {
      if (animationTimerRef.current) {
        clearTimeout(animationTimerRef.current);
      }
    };
  }, [categoryName, shouldRender]);

  // Set price and exam count based on props
  useEffect(() => {
    if (bundleDetails && bundleDetails.bundle && bundleDetails.bundle.price) {
      setPrice(bundleDetails.bundle.price);
    } else if (bundlePrice) {
      setPrice(bundlePrice);
    } else if (bundleId && bundles && bundles.length > 0) {
      // Find bundle matching current ID
      const foundBundle = bundles.find(bundle => bundle.id === bundleId);
      
      if (foundBundle) {
        setPrice(foundBundle.price);
      }
    }
  }, [bundleDetails, bundlePrice, bundleId, bundles]);

  // Check for pending bundle payments when component mounts - runs once
  useEffect(() => {
    const checkPendingPayment = async () => {
      try {
        const storedPaymentInfo = await getPaymentInfo('bundle');
        
        // Verify this payment info is for the current bundle
        if (storedPaymentInfo && storedPaymentInfo.bundleId === bundleId) {
          setPendingPayment(storedPaymentInfo);
          setIsPulsing(true);
        }
      } catch (error) {
        console.error("Error fetching payment info:", error);
      }
    };

    if (bundleId) {
      checkPendingPayment();
    }
  }, [bundleId]);

  // Determine if component should be rendered based on component state
  useEffect(() => {
    if (isAlreadyPurchased) {
      setShouldRender(false);
      return;
    }
  
    // Check if the bundle is in the current category
    let categoryMatches: boolean = true; // Default to true if no category specified
    
    if (categoryName && bundleId && bundles && bundles.length > 0) {
      // First try exact match
      const exactMatchingBundle = bundles.find(bundle => 
        bundle.id === bundleId && (
          !categoryName || // If no category name provided, any bundle matches
          categoryName === bundle.categoryName ||
          (bundle.category && categoryName === bundle.category.name)
        )
      );
      
      if (exactMatchingBundle) {
        categoryMatches = true;
      } else {
        // If we have a specific category requirement but no matching bundle was found
        if (categoryName) {
          categoryMatches = false;
        }
      }
    }
    
    // Check for valid bundle data
    const hasBundleData: boolean = Boolean(
      (bundleId && (bundlePrice || (bundleDetails && bundleDetails.bundle && bundleDetails.bundle.price))) || 
      (examIds && examIds.length > 0)
    );
    
    // Only render if not checking purchase AND has valid bundle data AND category matches
    if (!isCheckingPurchase && hasBundleData && categoryMatches) {
      setShouldRender(true);
    } else {
      setShouldRender(false);
    }
  }, [isCheckingPurchase, isAlreadyPurchased, bundleId, bundlePrice, bundleDetails, examIds, categoryName, bundles, price]);

  // Load Midtrans script once on mount
  useEffect(() => {
    if (scriptLoadAttempted.current) return;
    scriptLoadAttempted.current = true;
    
    // Only load script if not already loaded
    if (!document.getElementById("midtrans-script")) {
      const loadMidtransScript = () => {
        const script = document.createElement("script");
        const isProduction = process.env.NODE_ENV === "production";
        script.src = isProduction
          ? "https://app.midtrans.com/snap/snap.js"
          : "https://app.sandbox.midtrans.com/snap/snap.js";
        script.id = "midtrans-script";
        script.async = true;
  
        const clientKey = isProduction
          ? process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY_PRODUCTION
          : process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY_SANDBOX;
  
        script.setAttribute("data-client-key", clientKey || "");
        
        script.onload = () => {
          setScriptLoaded(true);
        };
        
        document.body.appendChild(script);
      };
  
      loadMidtransScript();
    } else {
      setScriptLoaded(true);
    }
  }, []);

  useEffect(() => {
    // Check if the URL has a successful payment parameter
    const urlParams = new URLSearchParams(window.location.search);
    const paymentSuccess = urlParams.get('payment_success');
    const paymentBundleId = urlParams.get('bundle_id');
    
    if (paymentSuccess === 'true' && paymentBundleId === bundleId) {
      // If there's a successful payment for this bundle in URL params,
      // immediately mark as purchased and hide the component
      setIsAlreadyPurchased(true);
      setShouldRender(false);
      
      // Clean URL by removing these parameters
      if (window.history && window.history.replaceState) {
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
      }
    }
  }, [bundleId]);

  // Revalidate page data after successful payment
  const revalidatePage = () => {
    // Set as purchased immediately to hide component
    setIsAlreadyPurchased(true);
    setShouldRender(false);
    
    if (setRefreshData) {
      setRefreshData((prev) => !prev);
    }
    
    // Redirect to my-exams page after successful purchase
    window.location.href = "/my-exams";
    
    if (onPurchase) {
      const result = onPurchase(examIds);
      if (result && typeof result.catch === 'function') {
        result.catch((error: Error) => {
          console.error("Error in onPurchase callback:", error);
        });
      }
    }
  };

  // Early return if component should not be rendered
  if (!shouldRender || !isVisible) {
    return null;
  }

  // Function to check payment status
  async function checkPaymentStatus(orderId: string): Promise<PaymentResult> {
    const response = await fetch(`/api/check-payment-status?orderId=${orderId}`, {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });
  
    if (!response.ok) {
      throw new Error(`Failed to fetch payment status: ${response.statusText}`);
    }
  
    const data = await response.json();
    return data as PaymentResult;
  }

  // Handle Midtrans payment
  const handlePayment = async (orderId: string, invoiceId: string, paymentId: string) => {
    if (typeof window.snap === 'undefined') {
      console.error("Snap.js is not loaded");
      showNotification?.("Sistem pembayaran belum siap. Silakan coba lagi nanti.", "error");
      setIsLoading(false);
      return;
    }
  
    try {
      // The token should be directly available in the result from orderExams
      // We shouldn't need to call getPaymentToken separately
      let token = null;
      
      // Check if there's a stored token first
      try {
        token = await getPaymentToken(orderId);
      } catch (tokenError) {
        console.error("Error getting stored token:", tokenError);
      }
      
      // If no token found, try to reprocess the payment
      if (!token) {
        console.log("No stored token found, attempting to reprocess payment");
        showNotification?.("Memproses ulang pembayaran...", "info");
        
        // You may need to implement an API to regenerate the token
        const regenerateTokenUrl = `/api/regenerate-payment-token?orderId=${orderId}`;
        const tokenResponse = await fetch(regenerateTokenUrl);
        
        if (!tokenResponse.ok) {
          throw new Error('Failed to regenerate payment token');
        }
        
        const tokenData = await tokenResponse.json();
        token = tokenData.token;
        
        // Store the new token
        if (token) {
          await storePaymentToken(orderId, token);
        } else {
          throw new Error('Payment token not found after regeneration');
        }
      }
  
      if (!token) {
        throw new Error('Payment token not found');
      }
  
      // Set loading to false before opening the payment window
      setIsLoading(false);
  
      window.snap.pay(token, {
        onSuccess: async (result: any) => {
          console.log("Payment successful", result);
          showNotification?.("Pembayaran berhasil!", "success");
          
          try {
            const paymentStatus = await checkPaymentStatus(orderId);
            
            if (paymentStatus.transaction_status === "settlement") {
              const paymentTypeString = `${paymentStatus.payment_type}_${paymentStatus.issuer}_${paymentStatus.acquirer}`;
              await updatePaymentStatus(
                orderId, 
                invoiceId, 
                paymentId, 
                "PAID", 
                "COMPLETED", 
                "PAID",
                paymentTypeString,
                paymentStatus.transaction_id,
                paymentStatus.transaction_time
              );
              
              if (referralCode) {
                const redeemResult = await finalRedeemDiscount(paymentId, referralCode);
                if (redeemResult.success) {
                  showNotification?.("berhasil diterapkan!", "success");
                } else {
                  showNotification?.(redeemResult.error || "Gagal menerapkan", "error");
                }
              }
        
              showNotification?.("Status pesanan berhasil diperbarui!", "success");
              
              // Set completed state immediately
              setIsAlreadyPurchased(true);
              setShouldRender(false);
              setPurchaseCompleted(true);
              
              // If we have collected user info but user doesn't exist, show registration modal
              if (shouldRegisterAfterPurchase && userEmail && userName) {
                showNotification?.("Pembelian berhasil! Silakan lengkapi pendaftaran akun Anda.", "success");
                // Short delay to let the user read the notification
                setTimeout(() => {
                  setIsRegisterModalOpen(true);
                }, 500);
              } else {
                // Otherwise just finish the process
                showNotification?.("Pembelian selesai! Paket akan segera tersedia di akun Anda.", "success");
                // Redirect to my-exams page after successful purchase
                window.location.href = "/my-exams";
                handleClose();
              }
            } else {
              showNotification?.("Status pembayaran tidak konsisten. Silakan hubungi dukungan pelanggan.", "error");
            }
          } catch (error) {
            console.error("Error updating order status:", error);
            showNotification?.("Gagal memperbarui status pesanan. Silakan hubungi dukungan pelanggan.", "error");
          }
        
          // Clean up payment info after successful payment
          await removePaymentInfo('bundle');
          setPendingPayment(null);
        },
        onPending: (result: any) => {
          console.log("Payment pending", result);
          showNotification?.(
            "Pembayaran tertunda. Anda dapat menutup jendela ini dan melanjutkan pembayaran nanti.",
            "info"
          );
        },
        onError: (result: any) => {
          console.error("Payment error", result);
          showNotification?.("Pembayaran gagal. Silakan coba lagi.", "error");
        },
        onClose: () => {
          console.log("Payment popup closed");
          showNotification?.(
            "Jendela pembayaran ditutup. Anda dapat melanjutkan pembayaran nanti.",
            "info"
          );
        },
      });
    } catch (error) {
      console.error("Error initiating payment:", error);
      showNotification?.("Gagal memulai pembayaran. Silakan coba lagi.", "error");
      setIsLoading(false);
    }
  };

  // Function to continue pending payment
  const continuePendingPayment = async () => {
    if (!pendingPayment) {
      showNotification?.("No pending payment found", "error");
      return;
    }

    setIsLoading(true);
    
    try {
      await handlePayment(
        pendingPayment.orderId,
        pendingPayment.invoiceId,
        pendingPayment.paymentId
      );
    } catch (error) {
      console.error("Error continuing payment:", error);
      showNotification?.("Failed to continue payment. Please try again.", "error");
      setIsLoading(false);
    }
  };

  // Validate that bundle ID exists
  const validateBundle = (): boolean => {
    if (!bundleId) {
      showNotification?.("ID Paket tidak valid", "error");
      return false;
    }

    if (!examIds || examIds.length === 0) {
      showNotification?.("Paket tidak berisi ujian", "error");
      return false;
    }

    return true;
  };

  // Helper function to get user-friendly error message
  const getUserFriendlyError = (error: any): string => {
    if (error?.message?.includes("Failed to validate referral code")) {
      return "tidak valid. Silakan periksa kembali.";
    }
    
    if (error?.message?.includes("Failed to fetch bundle details")) {
      return "Gagal mengambil detail paket. Silakan coba lagi.";
    }
    
    if (error?.message?.includes("500")) {
      return "Terjadi kesalahan server. Silakan coba beberapa saat lagi.";
    }

    if (error?.message?.includes("404")) {
      return "Paket tidak ditemukan. Silakan refresh halaman dan coba lagi.";
    }

    return error?.message || "Gagal memproses pembelian. Silakan coba lagi.";
  };

  // Handle registration completion
  const handleRegistrationComplete = () => {
    setIsRegisterModalOpen(false);
    showNotification?.("Registration successful!", "success");
    // Redirect to my-exams instead of revalidating the page
    window.location.href = "/my-exams";
    handleClose();
  };

  // Handle new purchase
  const startNewPurchase = async () => {
    if (!validateBundle()) {
      return;
    }
  
    setIsLoading(true);
    
    try {
      const placeholderExam = {
        id: examIds[0].toString()
      } as any;
  
      const result = await orderExams(placeholderExam, referralCode, true, bundleId);
      
      if (result.ok) {
        if (result.orderId) {
          const paymentInfo = {
            orderId: result.orderId,
            examId: placeholderExam.id,
            bundleId: bundleId,
            invoiceId: result.invoiceId,
            paymentId: result.paymentId,
            refCode: referralCode
          };
          
          await storePaymentInfo(paymentInfo);
          setPendingPayment(paymentInfo);
  
          if (result.token) {
            await storePaymentToken(result.orderId, result.token);
          }
        }
        
        if (result.finalAmount === 0) {
          showNotification?.("Paket berhasil dibeli!", "success");
          // Immediately set as purchased for free items
          setIsAlreadyPurchased(true);
          setShouldRender(false);
          revalidatePage();
          setIsLoading(false);
        } else {
          await handlePayment(result.orderId, result.invoiceId, result.paymentId);
        }
      } else {
        throw new Error("Gagal memproses pembelian");
      }
    } catch (error: any) {
      console.error("Purchase failed:", error);
      const friendlyError = getUserFriendlyError(error);
      showNotification?.(friendlyError, "error");
      setIsLoading(false);
    }
  };

  // Main handler for the button click
  const handlePurchase = async (): Promise<void> => {
    // Check authentication first before proceeding
    if (!isUserLoggedIn) {
      // Store current path for redirect after login      
      // DIRECTLY open the login modal instead of redirecting
      showNotification?.("Silakan login terlebih dahulu untuk melakukan pembelian", "info");
      loginModal.onOpen(); // This directly opens the login modal using the hook
      return;
    }
  
    if (pendingPayment) {
      continuePendingPayment();
    } else {
      startNewPurchase();
    }
  };

  // Calculate discounted price
  let calculatedDiscountedPrice = price || 0;
  let bundleDiscountAmount = 0;
  
  // Apply bundle discount if available
  if (price && effectiveDiscountPercentage) {
    bundleDiscountAmount = Math.round(price * (effectiveDiscountPercentage / 100));
    calculatedDiscountedPrice = price - bundleDiscountAmount;
  }
  
  // Apply referral discount if available
  if (discountInfo && price) {
    calculatedDiscountedPrice = price - bundleDiscountAmount - discountInfo.discountAmount;
  }

  return (
    <>
      {/* Login Modal */}
      <LoginModal 
        isOpen={loginModal.isOpen} 
        onOpenChange={(isOpen) => isOpen ? loginModal.onOpen() : loginModal.onClose()}
        onSuccess={() => {
          // Handle successful login
          console.log("Login successful");
        }}
      />
      <div 
        className={`p-3 rounded-xl bg-gradient-to-r from-violet-50 to-indigo-50 shadow-sm border border-indigo-100 relative ${
          isAnimating 
            ? 'animate-fadeInSlideUp' 
            : 'opacity-0 transform translate-y-8'
        }`}
        style={{
          transition: 'all 800ms cubic-bezier(0.22, 1, 0.36, 1)'
        }}
      >
        {/* Close Button */}
        <button 
          onClick={handleClose}
          className="absolute -top-2 -right-2 w-6 h-6 rounded-full bg-gray-300 hover:bg-gray-400 flex items-center justify-center transition-colors shadow-sm border border-white"
          aria-label="Tutup"
        >
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="#333" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
        
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <div className="text-base w-24 h-24 -mt-6">
              <DotLottieAnimation src='/dotlotties/bundle-items.lottie'
                  autoplay
                  loop
                  width="100%"
                  height="100%"
              ></DotLottieAnimation>
            </div>
            <h3 className="font-semibold text-gray-800 text-xxl">
              {bundleName || "Paket Bundling"}
              {bundleDetailsLoading && <span className="ml-2 text-xs text-gray-500">(Loading...)</span>}
            </h3>
            <span className="bg-gradient-to-r from-purple-500 to-indigo-500 text-white text-xs px-2 py-0.5 rounded-full">
              Hemat {effectiveDiscountPercentage}%
            </span>
          </div>
          
          <p className="text-xs text-gray-600">
            {bundleId 
              ? `Dapatkan akses ke paket ujian dengan ${examCount} soal dan diskon ${effectiveDiscountPercentage}%!`
              : `Beli ${examIds.length} ujian sekaligus dan hemat ${effectiveDiscountPercentage}% dari total harga!`
            }
          </p>
          
          {/* Bundle Details Section */}
          {bundleDetails && (
            <div className="bg-white p-2 rounded-lg text-xs">
              <div className="font-medium text-indigo-700">Detail Paket:</div>
              <div className="flex justify-between text-gray-700">
                <span>Jumlah Ujian:</span>
                <span className="font-medium">{bundleDetails.examCount}</span>
              </div>
              {bundleDetails.bundle.validFrom && (
                <div className="flex justify-between text-gray-700">
                  <span>Berlaku Dari:</span>
                  <span className="font-medium">
                    {new Date(bundleDetails.bundle.validFrom).toLocaleDateString('id-ID')}
                  </span>
                </div>
              )}
              {bundleDetails.bundle.validUntil && (
                <div className="flex justify-between text-gray-700">
                  <span>Berlaku Hingga:</span>
                  <span className="font-medium">
                    {new Date(bundleDetails.bundle.validUntil).toLocaleDateString('id-ID')}
                  </span>
                </div>
              )}
            </div>
          )}
          
          {/* Referral Code Section - only show if not hidden */}
          {!hideReferralInput && (
            <div className="mt-3">
              <div className="flex flex-col gap-2">
                <label htmlFor="referralCode" className="text-sm font-medium">(Opsional)</label>
                <div className="flex gap-2">
                <Input
                  id="referalCode"
                  value={referralCode}
                  onChange={(e) => {
                    setReferralError("");
                    setReferralCode(e.target.value.toUpperCase());
                    setDiscountInfo(null);
                  }}
                  placeholder="Masukkan kode referral"
                  classNames={{
                    inputWrapper: "!bg-white",
                  }}
                  style={{ 
                    backgroundColor: 'white',
                    ...({
                      "--input-bg": "#ffffff",
                      "--input-bg-opacity": "1",
                      "--heroui-default-100": "255 255 255",
                      "--heroui-default-100-opacity": "1",
                    } as any)
                  }}
                  isInvalid={!!referralError}
                />
                  <Button
                    color="primary"
                    size="md"
                    // No longer need a validate function, since we're using server-validated codes
                    isDisabled={!referralCode}
                  >
                    Terapkan
                  </Button>
                </div>
                {referralError && (
                  <p className="text-sm text-red-500">
                    {referralError}
                  </p>
                )}
              </div>

              {/* Only show detailed discount breakdown if referral code input is visible */}
              {discountInfo && !hideReferralInput && (
                <div className="border-t pt-3 mt-3 space-y-2">
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>Harga Normal:</span>
                    <span className="line-through">
                      {formatPrice(discountInfo.originalPrice)}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Diskon Bundling ({effectiveDiscountPercentage}%):</span>
                    <span>
                      -{formatPrice(Math.round(discountInfo.originalPrice * (effectiveDiscountPercentage / 100)))}
                    </span>
                  </div>
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Diskon Referral ({discountInfo.discountPercentage}%):</span>
                    <span>
                      -{formatPrice(discountInfo.discountAmount)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}
          
          {/* Price Display - Enhanced with discount indicator */}
          {price && (
            <div className="flex flex-col items-center py-2 mt-2">
              <div className="flex items-center gap-2">
                <span className="text-gray-500 line-through text-sm">
                  {formatPrice(price)}
                </span>
                {discountInfo && hideReferralInput && (
                  <span className="bg-green-100 text-green-700 text-xs px-2 py-0.5 rounded-full">
                    Diskon Spesial
                  </span>
                )}
              </div>
              
              {calculatedDiscountedPrice !== null && (
                <span className="text-indigo-700 font-bold text-lg mt-1">
                  {formatPrice(calculatedDiscountedPrice)}
                </span>
              )}
              
              {discountInfo && hideReferralInput && (
                <div className="text-xs text-green-600 mt-1">
                  Harga sudah termasuk diskon spesial
                </div>
              )}
            </div>
          )}
          
          <button
            onClick={() => {
              if (!isLoading && scriptLoaded) {
                handlePurchase();
              }
            }}
            onMouseEnter={() => {
              setIsHovered(true);
              if (pendingPayment) setIsPulsing(false);
            }}
            onMouseLeave={() => {
              setIsHovered(false);
              if (pendingPayment) setIsPulsing(true);
            }}
            disabled={isLoading || !scriptLoaded}
            className={`relative mt-2 w-full py-2 px-3 rounded-full font-medium text-white transition-all duration-300 overflow-hidden ${
              isLoading || !scriptLoaded
                ? "bg-gray-400 cursor-not-allowed" 
                : pendingPayment 
                  ? `bg-gradient-to-r from-amber-500 to-yellow-600 hover:from-amber-600 hover:to-yellow-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5 ${isPulsing ? 'animate-pulse' : ''}`
                  : isUserLoggedIn
                    ? "bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                    : "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
            }`}
          >
            {/* Background animation effect */}
            <div 
              className={`absolute inset-0 transition-opacity duration-500 ${
                isHovered ? "opacity-100" : "opacity-0"
              }`}
              style={{
                background: pendingPayment 
                  ? "radial-gradient(circle at center, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%)"
                  : "radial-gradient(circle at center, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%)",
                transform: isHovered ? "scale(2)" : "scale(0)",
                transition: "transform 0.5s ease-out"
              }}
            />
            
            <div className="relative flex items-center justify-center gap-2">
              {isLoading ? (
                <>
                  <svg className="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span>Memproses...</span>
                </>
              ) : !scriptLoaded ? (
                <span>Loading Payment System...</span>
              ) : pendingPayment ? (
                <>
                  <span className="font-bold mr-1">→</span>
                  <span>Lanjutkan Bayar</span>
                </>
              ) : !isUserLoggedIn ? (
                <span>Login untuk Membeli</span>
              ) : (
                <span>Beli Paket Sekarang</span>
              )}
            </div>
          </button>
          
          <div className="text-xs text-center text-gray-500 mt-0.5">
            {pendingPayment 
              ? "Kamu memiliki transaksi yang belum selesai untuk paket ini" 
              : !isUserLoggedIn
                ? "Silakan login terlebih dahulu untuk melakukan pembelian"
                : hideReferralInput && discountInfo 
                  ? "Paket dengan harga spesial sudah diterapkan" 
                  : "Pembayaran aman • Akses instan • Tanpa ada biaya lain"}
          </div>
        </div>
      </div>
    </>
  );
};

export default EnhancedBundlingPurchaseButton;