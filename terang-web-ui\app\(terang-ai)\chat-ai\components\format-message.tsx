interface MessageContent {
    type: string;
    content?: string;
    text?: string;
    image_url?: {
      url: string;
    };
  }
  
  interface SystemContext {
    question?: {
      contents: MessageContent[];
    }[];
    options?: {
      values: {
        data: {
          contents: MessageContent[];
        }[];
      }[];
    };
    explanation?: {
      contents: MessageContent[];
    }[];
  }
  
  interface FormattedMessage {
    role: string;
    content: string | MessageContent[];
  }
  
  const formatMessagesForAPI = (
    messageHistory: { role: string; content: string }[],
    systemContext?: string
  ): FormattedMessage[] => {
    const messages: FormattedMessage[] = [];
  
    // Add system message if exists
    if (systemContext) {
      try {
        const parsedContext: SystemContext = JSON.parse(systemContext);
        const formattedContents: MessageContent[] = [];
  
        // Process question contents
        if (parsedContext.question) {
          parsedContext.question.forEach(q => {
            q.contents.forEach(content => {
              if (content.type === 'media') {
                formattedContents.push({
                  type: 'image_url',
                  image_url: {
                    url: content.content || ''
                  }
                });
              } else {
                formattedContents.push({
                  type: 'text',
                  text: content.content || ''
                });
              }
            });
          });
        }
  
        // Process options contents
        if (parsedContext.options?.values) {
          parsedContext.options.values.forEach((option, index) => {
            formattedContents.push({
              type: 'text',
              text: `\nOption ${index + 1}:`
            });
            
            option.data.forEach(d => {
              d.contents.forEach(content => {
                if (content.type === 'media') {
                  formattedContents.push({
                    type: 'image_url',
                    image_url: {
                      url: content.content || ''
                    }
                  });
                } else {
                  formattedContents.push({
                    type: 'text',
                    text: content.content || ''
                  });
                }
              });
            });
          });
        }
  
        // Process explanation contents
        if (parsedContext.explanation) {
          formattedContents.push({
            type: 'text',
            text: '\nExplanation:'
          });
          
          parsedContext.explanation.forEach(exp => {
            exp.contents.forEach(content => {
              if (content.type === 'media') {
                formattedContents.push({
                  type: 'image_url',
                  image_url: {
                    url: content.content || ''
                  }
                });
              } else {
                formattedContents.push({
                  type: 'text',
                  text: content.content || ''
                });
              }
            });
          });
        }
  
        messages.push({
          role: 'system',
          content: formattedContents
        });
      } catch (error) {
        // If parsing fails, add the system context as a regular string
        messages.push({
          role: 'system',
          content: systemContext
        });
      }
    }
  
    // Add the rest of the message history
    messages.push(...messageHistory);
  
    return messages;
  };
  
  export default formatMessagesForAPI;