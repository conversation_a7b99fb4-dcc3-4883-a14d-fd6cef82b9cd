import React, { useState, useEffect, useRef } from 'react';
import { Input, Button, Image, useDisclosure } from "@heroui/react";
import { getUserData, type User } from './action';
import { useSession } from 'next-auth/react';
import { updateProfile, type UpdateProfileRequest, uploadProfilePicture } from './action';
import { toast } from 'sonner';

// Helper function to clear user session cookie from client-side
function clearUserSessionCache(email: string) {
  console.log("Clearing profile session cache on client for:", email);
  try {
    // Create encoded cookie name
    const encodedEmail = btoa(email);
    const cookieName = `user_session_${encodedEmail}`;
    
    // Check current cookies
    const allCookies = document.cookie.split(';').map(cookie => cookie.trim());
    console.log("Existing cookies before profile update:", allCookies.length);
    
    const targetCookie = allCookies.find(c => c.startsWith(`${cookieName}=`));
    if (targetCookie) {
      console.log(`Found profile session cookie to clear: ${cookieName}`);
    } else {
      console.log(`No profile session cookie found to clear: ${cookieName}`);
    }
    
    // Create invalid data for overwriting
    const invalidData = JSON.stringify({
      timestamp: 0,
      data: {
        id: '',
        email: '',
        first_name: '',
        last_name: '',
        picture: '',
        role: ''
      },
      isLoggedOut: true
    });
    
    // Determine if we're in local environment
    const isLocal = window.location.hostname === 'localhost' || 
                   window.location.hostname === '127.0.0.1';
    
    // Try multiple methods to ensure the cookie is cleared
    if (isLocal) {
      document.cookie = `${cookieName}=${invalidData}; path=/; max-age=1;`;
      document.cookie = `${cookieName}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT;`;
    } else {
      document.cookie = `${cookieName}=${invalidData}; path=/; domain=.terang.ai; max-age=1; secure`;
      document.cookie = `${cookieName}=; path=/; domain=.terang.ai; expires=Thu, 01 Jan 1970 00:00:00 GMT; secure`;
      // Also try with domain without leading dot
      document.cookie = `${cookieName}=${invalidData}; path=/; domain=terang.ai; max-age=1; secure`;
      document.cookie = `${cookieName}=; path=/; domain=terang.ai; expires=Thu, 01 Jan 1970 00:00:00 GMT; secure`;
    }
    
    console.log("Profile session cookie cleared on client side");
    return true;
  } catch (error) {
    console.error("Error clearing profile session cookie on client:", error);
    return false;
  }
}

interface ProfileFormData extends UpdateProfileRequest {
    email?: string;
    picture?: string;
    phone_number?: string | null;  // Keeping consistent with snake_case
  }


type ValidatedFormFields = 'firstName' | 'lastName' | 'phoneNumber';

interface ProfileFormErrors {
    firstName?: string;
    lastName?: string;
    phone_number?: string;  // Updated to match
  }


const Camera02Icon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width={24} height={24} color={"#000000"} fill={"none"} {...props}>
    <path opacity="0.4" d="M11.9686 1.25H12.0314C12.6355 1.24999 13.1356 1.24998 13.5485 1.28515C13.9812 1.32201 14.3723 1.40069 14.7494 1.58742C15.4214 1.92009 15.8461 2.44877 16.1443 2.979C16.3755 3.39004 16.555 3.85182 16.7071 4.24318C16.743 4.33553 16.7774 4.42396 16.8105 4.50689L16.9569 4.87283C17.0299 5.05536 17.0664 5.14662 17.1425 5.19819C17.2185 5.24975 17.317 5.24991 17.5141 5.25022C18.3986 5.2516 19.0637 5.26171 19.5921 5.33452C20.2753 5.42866 20.7694 5.63092 21.2829 6.02493C21.5429 6.22443 21.7756 6.45715 21.9751 6.71715C22.4019 7.27343 22.5828 7.92195 22.6678 8.67622C22.75 9.40571 22.75 10.3204 22.75 11.4548V16.0549C22.75 17.4225 22.75 18.5248 22.6335 19.3918C22.5125 20.2919 22.2536 21.0497 21.6517 21.6517C21.0497 22.2536 20.2919 22.5125 19.3918 22.6335C18.5248 22.75 17.4225 22.75 16.0549 22.75H7.94513C6.57754 22.75 5.47522 22.75 4.60825 22.6335C3.70814 22.5125 2.95027 22.2536 2.34835 21.6517C1.74644 21.0497 1.48754 20.2919 1.36653 19.3918C1.24997 18.5248 1.24998 17.4225 1.25 16.0549V11.4548C1.24999 10.3204 1.24998 9.4057 1.33222 8.67622C1.41725 7.92195 1.59808 7.27343 2.02493 6.71715C2.22443 6.45715 2.45715 6.22443 2.71715 6.02493C3.23063 5.63092 3.72467 5.42866 4.40786 5.33452C4.93626 5.26171 5.60137 5.2516 6.48592 5.25022C6.68297 5.24991 6.7815 5.24975 6.85754 5.19819C6.93358 5.14662 6.97009 5.05536 7.0431 4.87283L7.18947 4.50689C7.22264 4.42397 7.25701 4.33554 7.2929 4.2432C7.44502 3.85184 7.62451 3.39004 7.85567 2.979C8.15388 2.44877 8.57866 1.92009 9.25059 1.58742C9.62775 1.40069 10.0188 1.32201 10.4515 1.28515C10.8644 1.24998 11.3645 1.24999 11.9686 1.25Z" fill="currentColor" />
    <path d="M8 14C8 11.7909 9.79086 10 12 10C14.2091 10 16 11.7909 16 14C16 16.2091 14.2091 18 12 18C9.79086 18 8 16.2091 8 14Z" fill="currentColor" />
    <path fillRule="evenodd" clipRule="evenodd" d="M11 6C11 5.44772 11.4457 5 11.9955 5C12.5433 5 13 5.44981 13 6C13 6.55228 12.5543 7 12.0045 7C11.4567 7 11 6.55019 11 6Z" fill="currentColor" />
  </svg>
);

const formatPhoneNumber = (phone: string): string => {
  // If phone is empty or just the country code, return empty string
  if (!phone || phone.trim() === '+62' || phone.trim() === '+62 ') {
    return '';
  }

  // Remove all non-numeric characters except '+'
  let cleaned = phone.replace(/[^\d+]/g, '');
  
  // Ensure it starts with +62 if there are numbers
  if (cleaned && !cleaned.startsWith('+62')) {
    cleaned = '+62' + cleaned.replace(/^62/, '');
  }
  
  // Remove leading zeros after country code
  cleaned = cleaned.replace(/^\+62[0]+/, '+62');
  
  // Format the number: +62 xxx-xxxx-xxxx
  if (cleaned.length > 3) {
    const numbers = cleaned.slice(3);
    const parts = [];
    parts.push(numbers.slice(0, 3));
    if (numbers.length > 3) parts.push(numbers.slice(3, 7));
    if (numbers.length > 7) parts.push(numbers.slice(7, 11));
    cleaned = `+62 ${parts.join('-')}`;
  }
  
  return cleaned;
};

const validatePhoneNumber = (phone: string | null | undefined): string | undefined => {
  // Allow empty, null, or undefined phone number
  if (!phone || phone.trim() === '' || phone.trim() === '+62' || phone.trim() === '+62 ') {
    return undefined;
  }
  
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  // If there's any input (not empty), validate it
  if (cleaned.length > 0) {
    if (!cleaned.startsWith('+62')) {
      return 'Phone number must start with +62';
    }
    
    if (cleaned.length < 10 || cleaned.length > 14) {
      return 'Phone number must be between 10 and 14 digits';
    }
    
    // Check if the number after +62 starts with a valid prefix
    const prefix = cleaned.slice(3, 5);
    const validPrefixes = ['81', '82', '83', '85', '87', '88', '89']; // Valid Indonesian mobile prefixes
    if (!validPrefixes.includes(prefix)) {
      return 'Invalid Indonesian mobile number prefix';
    }
  }
  
  return undefined;
};

const ProfileForm = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isUploadingImage, setIsUploadingImage] = useState<boolean>(false);
  const [isInitialLoading, setIsInitialLoading] = useState<boolean>(true);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { data: session, update } = useSession();
  const [formData, setFormData] = useState<ProfileFormData>({
    firstName: '',
    lastName: '',
    phone_number: '',
    email: '',
    picture: ''
  });
  const [errors, setErrors] = useState<ProfileFormErrors>({});

  useEffect(() => {
    const fetchUserData = async () => {
      const loadingToast = toast.loading('Loading profile data...');
      try {
        const response = await getUserData();
        if (response.success && response.data?.user) {
          const user = response.data.user;
          setFormData({
            firstName: user.first_name,
            lastName: user.last_name,
            phone_number: user.phone_number ? formatPhoneNumber(user.phone_number) : '',
            email: user.email,
            picture: user.picture
          });
          toast.dismiss(loadingToast);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast.dismiss(loadingToast);
        toast.error('Failed to load profile data', {
          description: 'Please refresh the page to try again.'
        });
      } finally {
        setIsInitialLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const validateForm = (): boolean => {
    const newErrors: ProfileFormErrors = {};
    if (!formData.firstName) newErrors.firstName = 'First name is required';
    if (!formData.lastName) newErrors.lastName = 'Last name is required';
    
    const phoneError = validatePhoneNumber(formData.phone_number || '');
    if (phoneError) newErrors.phone_number = phoneError;
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (value: string, id: keyof ProfileFormData): void => {
    if (id === 'phone_number') {  // Updated to match the property name
      // Allow clearing the field
      if (!value || value === '+62 ') {
        value = '';
      } else {
        value = formatPhoneNumber(value);
      }
    }
    
    setFormData(prev => ({ ...prev, [id]: value }));
    
    if (id in errors) {
      setErrors(prev => ({ ...prev, [id as ValidatedFormFields]: undefined }));
    }
  };

  const handleProfileImageClick = () => {
    // Trigger file input click when profile image is clicked
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    
    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!validTypes.includes(file.type)) {
      toast.error('Invalid file type', {
        description: 'Only JPG and PNG formats are supported.'
      });
      return;
    }
    
    // Validate file size (max 1MB)
    const maxSize = 1 * 1024 * 1024; // 1MB in bytes
    if (file.size > maxSize) {
      toast.error('File too large', {
        description: 'Maximum file size is 1MB.'
      });
      return;
    }
    
    // Create preview for the selected image
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setFormData(prev => ({
          ...prev,
          picture: e.target?.result as string
        }));
      }
    };
    reader.readAsDataURL(file);
    
    // Upload the image
    await handleImageUpload(file);
  };

  const handleImageUpload = async (file: File) => {
    setIsUploadingImage(true);
    const loadingToast = toast.loading('Uploading image...');
    
    try {
      const fileFormData = new FormData();
      fileFormData.append('file', file);
      
      const result = await uploadProfilePicture(fileFormData);
      
      if (!result.success) {
        throw new Error(result.error);
      }
      
      if (result.data?.picture_url) {
        // First clear session cache from client-side
        if (session?.user?.email) {
          console.log("Clearing session cache before updating profile image");
          clearUserSessionCache(session.user.email);
        }
        
        // To preserve the name in the session, get the current name from component's formData or session
        const firstName = formData.firstName || session?.user?.firstname || '';
        const lastName = formData.lastName || session?.user?.lastname || '';
        const fullName = firstName && lastName ? `${firstName} ${lastName}` : session?.user?.name || '';
        
        // Force update the session with the new data
        await update({
          ...session,
          user: {
            ...session?.user,
            image: result.data.picture_url,
            // Preserve the name in the session
            ...(fullName && { name: fullName })
          }
        });

        toast.dismiss(loadingToast);
        toast.success('Image uploaded', {
          description: 'Your profile picture has been updated.',
          duration: 3000
        });
      
        // Update the form data with the new image URL
        setFormData(prev => ({
          ...prev,
          picture: result.data?.picture_url
        }));
      }
    } catch (error) {
      toast.dismiss(loadingToast);
      toast.error('Upload failed', {
        description: error instanceof Error ? error.message : 'Failed to upload image',
        duration: 4000
      });
      console.error('Error uploading image:', error);
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleSubmit = async (): Promise<void> => {
    if (!validateForm()) return;
    
    setIsLoading(true);
    const loadingToast = toast.loading('Updating profile...');

    try {
      // First clear session cache from client-side
      if (session?.user?.email) {
        console.log("Clearing session cache before profile update");
        clearUserSessionCache(session.user.email);
      }
      
      const updateData: UpdateProfileRequest = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone_number: formData.phone_number ? formData.phone_number.replace(/[^\d+]/g, '') : null,
        picture: formData.picture
      };

      console.log(updateData)

      const result = await updateProfile(updateData);
      if (!result.success) {
        throw new Error(result.error);
      }

      // Update the session with the new profile data
      if (session) {
        await update({
          ...session,
          firstname: formData.firstName,
          lastname: formData.lastName,
          // Add name property combining first and last name
          name: `${formData.firstName} ${formData.lastName}`,
          user: {
            ...session?.user,
            firstname: formData.firstName,
            lastname: formData.lastName,
            // Add name property to user object as well
            name: `${formData.firstName} ${formData.lastName}`,
            // Include the profile picture if available
            ...(formData.picture && { image: formData.picture })
          }
        });
        
      }

      toast.dismiss(loadingToast);
      toast.success('Profile updated', {
        description: 'Your changes have been saved successfully!',
        duration: 3000
      });
    } catch (error) {
      toast.dismiss(loadingToast);
      toast.error('Update failed', {
        description: error instanceof Error ? error.message : 'Failed to update profile',
        duration: 4000
      });
      console.error('Error updating profile:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isInitialLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      <div className="flex flex-col items-center mb-8">
        <div className="relative">
          <Image
            src={formData.picture ?? "/api/placeholder/150/150"}
            alt="Profile"
            classNames={{
              img: "w-32 h-32 rounded-full object-cover bg-gray"
            }}
            isLoading={isUploadingImage}
          />
          <Button
            isIconOnly
            className="absolute bottom-0 right-0 rounded-full z-10"
            size="sm"
            onClick={handleProfileImageClick}
            isLoading={isUploadingImage}
          >
            <Camera02Icon />
          </Button>
          <input 
            type="file" 
            ref={fileInputRef}
            className="hidden"
            accept=".jpg,.jpeg,.png"
            onChange={handleFileChange}
          />
        </div>
        <p className="text-default-500 text-sm mt-2">Click the camera icon to update your photo</p>
      </div>

      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <Input
            label="First Name"
            placeholder="John"
            value={formData.firstName}
            onValueChange={(value) => handleInputChange(value, 'firstName')}
            isInvalid={!!errors.firstName}
            errorMessage={errors.firstName}
          />
          <Input
            label="Last Name"
            placeholder="Doe"
            value={formData.lastName}
            onValueChange={(value) => handleInputChange(value, 'lastName')}
            isInvalid={!!errors.lastName}
            errorMessage={errors.lastName}
          />
        </div>

        <Input
          label="Email"
          type="email"
          value={formData.email ?? ''}
          isDisabled
          description="Contact support to change your email address"
        />

        <Input
            label="Phone Number (Optional)"
            placeholder="+62 812-3456-7890"
            value={formData.phone_number || ''}
            onValueChange={(value) => handleInputChange(value, 'phone_number')}  // Updated to match
            isInvalid={!!errors.phone_number}
            isClearable={true}
            errorMessage={errors.phone_number}
            description="Enter Indonesian mobile number or leave empty"
        />

        <Button
          color="primary"
          onClick={handleSubmit}
          isLoading={isLoading}
        >
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default ProfileForm;