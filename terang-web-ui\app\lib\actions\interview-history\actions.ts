"use server";

import { getUserId } from "@/app/lib/actions/account/actions";
import { getMockScoreForSession } from "@/app/lib/mocks/interview-scores";

const API_URL = process.env.BACKEND_BASE_URL as string;
const API_KEY = process.env.BACKEND_API_KEY as string;

interface BackendInterviewSession {
  id: number;
  sessionId: string;
  userId: string;
  userEmail: string;
  userName: string;
  interviewId: string;
  interviewName: string;
  interviewSubname: string;
  category: string;
  type: string;
  status: string;
  recordingUrl: string | null;
  transcriptUrl: string | null;
  duration: string;
  durationSeconds: number;
  startTime: string;
  endTime: string | null;
  createdAt: string;
  updatedAt: string;
}

interface InterviewSession {
  sessionId: string;
  interviewId: string;
  userId: string | boolean | null;
  roomName: string;
  startTime: number;
  duration: string;
  durationInSeconds: number;
  status: 'active' | 'completed' | 'force_finished' | 'cancelled' | 'disconnected_timeout' | 'disconnected_grace';
  createdAt: string;
  updatedAt?: string;
  endedAt?: string;
  userEmail?: string;
  userName?: string;
  disconnectedAt?: number;
}

interface ScoreData {
  overallScore: number;
  maxScore: number;
  recommendation: string;
  readinessLevel: number;
  categoryScores: Array<{
    category: string;
    score: number;
    maxScore: number;
  }>;
}

interface InterviewHistoryItem extends InterviewSession {
  interviewName?: string;
  interviewSubname?: string;
  categoryName?: string;
  interviewType?: string;
  scoreData?: ScoreData;
}

async function fetchWithHeaders(url: string, options: RequestInit = {}) {
  const response = await fetch(url, {
    headers: {
      'X-API-Key': API_KEY,
      ...(options.headers || {})
    },
    ...options,
    cache: 'no-store',
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return response.json();
}

// Transform backend session to frontend format
function transformSession(backendSession: BackendInterviewSession): InterviewHistoryItem {
  // Map backend status to frontend status format
  const statusMap: Record<string, string> = {
    'in_progress': 'active',
    'completed': 'completed',
    'ended': 'force_finished',
    'cancelled': 'cancelled',
    'disconnected': 'disconnected_timeout'
  };

  const frontendStatus = statusMap[backendSession.status] || backendSession.status;
  
  // Convert start time to timestamp
  const startTimeTimestamp = new Date(backendSession.startTime).getTime();
  
  // Add mock score data for completed interviews
  let scoreData = undefined;
  if (frontendStatus === 'completed' || frontendStatus === 'force_finished') {
    scoreData = getMockScoreForSession(backendSession.sessionId);
  }

  return {
    sessionId: backendSession.sessionId,
    interviewId: backendSession.interviewId,
    userId: backendSession.userId,
    roomName: backendSession.sessionId, // Use sessionId as roomName if not available
    startTime: startTimeTimestamp,
    duration: backendSession.duration,
    durationInSeconds: backendSession.durationSeconds,
    status: frontendStatus as any,
    createdAt: backendSession.createdAt,
    updatedAt: backendSession.updatedAt,
    endedAt: backendSession.endTime || undefined,
    userEmail: backendSession.userEmail,
    userName: backendSession.userName,
    interviewName: backendSession.interviewName,
    interviewSubname: backendSession.interviewSubname,
    categoryName: backendSession.category,
    interviewType: backendSession.type,
    scoreData
  };
}

// Get all interview sessions for the current user
export async function getUserInterviewHistory(): Promise<InterviewHistoryItem[]> {
  const userId = await getUserId();
  
  if (!userId) {
    return [];
  }
  
  try {
    const backendSessions: BackendInterviewSession[] = await fetchWithHeaders(
      `${API_URL}/v0/ai-interview/users/${userId}/sessions`
    );
    
    // Transform backend sessions to frontend format
    const transformedSessions = backendSessions.map(transformSession);
    
    // Sort by startTime descending (newest first)
    return transformedSessions.sort((a, b) => b.startTime - a.startTime);
  } catch (error) {
    console.error("Failed to get user interview history:", error);
    return [];
  }
}

// Get a specific interview session by ID
export async function getInterviewHistoryItem(sessionId: string): Promise<InterviewHistoryItem | null> {
  const userId = await getUserId();
  
  if (!userId || !sessionId) {
    return null;
  }
  
  try {
    const backendSession: BackendInterviewSession = await fetchWithHeaders(
      `${API_URL}/v0/ai-interview/sessions/${sessionId}`
    );
    
    // Verify this session belongs to the current user
    if (backendSession.userId !== userId) {
      return null;
    }
    
    // Transform backend session to frontend format
    return transformSession(backendSession);
  } catch (error) {
    console.error("Failed to get interview history item:", error);
    return null;
  }
}