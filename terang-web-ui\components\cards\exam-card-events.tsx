"use client";

import React, { memo } from "react";
import { <PERSON>, <PERSON>Header, CardBody } from "@heroui/card";
import { Image } from "@heroui/image";
import { Divider } from "@heroui/divider";
import { Popover, PopoverTrigger, PopoverContent } from "@heroui/react";
import { Chip } from "@heroui/react";
import { usePathname } from "next/navigation";

import { PurchaseExam } from "../available-exams-events/purchase-exam";
import { AvailableExamsType } from "../types";

import { CheckIcon } from "./CheckIcon";

import { PurchasedExam } from "../available-exams-events/purchased-exam";

// First, let's check if PurchaseExam expects a discountedPrice prop
// If it does, we need to update its Props interface or check its implementation

interface ExamCardProps {
  examData: AvailableExamsType;
  showNotification?: (
    message: string,
    type: "success" | "info" | "error",
  ) => void;
  setRefreshData?: React.Dispatch<React.SetStateAction<boolean>>;
  referralCode?: string; // Passed from parent
  discountPercentage?: number | null; // Passed from parent
  discountedPrice?: number | null; // Passed from parent - make sure it's nullable
}

// Function to format the duration - Moved outside component to avoid recreating on each render
const formatDuration = (duration: string): string => {
  const parts = duration.split(":");
  const hours = parseInt(parts[0], 10);
  const minutes = parseInt(parts[1], 10);

  // Format in Bahasa: "1 jam 30 menit"
  const hourText = hours > 0 ? `${hours} jam` : "";
  const minuteText = minutes > 0 ? `${minutes} menit` : "";

  return `${hourText} ${minuteText}`.trim();
};

// Function to format the price in Indonesian Rupiah - Moved outside component
const formatPrice = (price: number): string => {
  const formattedPrice = new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
  }).format(price);

  // Remove the space between "Rp" and the number
  return formattedPrice.replace(/\s/g, "");
};

// Function to check if the date is within the last 7 days - Moved outside component
const isWithinLast7Days = (date: string | null): boolean => {
  if (!date) {
    return false;
  }

  const dateObj = new Date(date);

  if (isNaN(dateObj.getTime())) {
    return false;
  }

  const now = new Date();
  const diffTime = Math.abs(now.getTime() - dateObj.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays <= 7;
};

// Using React.memo to prevent unnecessary re-renders
export const ExamCard: React.FC<ExamCardProps> = memo(({
  examData,
  showNotification,
  setRefreshData,
  referralCode = "TEST2025", // Default referral code
  discountPercentage, // Receive from parent
  discountedPrice, // Receive from parent
}) => {
  // Determine whether to show the "New" chip
  const showNewChip =
    (examData.is_purchased && isWithinLast7Days(examData.purchase_date)) ||
    (!examData.is_purchased && isWithinLast7Days(examData.created_at));
  const pathname = usePathname();

  // Create the purchase/purchased button element only when needed
  const renderActionButton = () => {
    if (examData.is_purchased) {
      return <PurchasedExam examData={examData} key={examData.id} />;
    } else {
      // Use the PurchaseExam component with hardcoded referral code
      // Remove discountedPrice prop if PurchaseExam doesn't accept it
      return (
        <PurchaseExam
          examData={examData}
          setRefreshData={setRefreshData}
          showNotification={showNotification}
          hardcodedReferralCode={referralCode}
          hideReferralInput={true}
          // Only pass discountedPrice if it's defined and PurchaseExam expects it
          {...(typeof discountedPrice === 'number' ? { discountedPrice } : {})}
          key={examData.id}
        />
      );
    }
  };

  return (
    <Card className="py-4" shadow="md">
      <CardHeader className="pb-0 pt-2 px-4 flex-col items-start">
        <div className="flex gap-2 mb-2">
          {/* Conditionally render the "New" chip */}
          {showNewChip && (
            <Chip
              classNames={{
                base: "bg-gradient-to-br from-indigo-500 to-pink-500 border-small border-white/50 shadow-pink-500/30",
                content: "drop-shadow shadow-black text-white",
              }}
              variant="shadow"
            >
              New
            </Chip>
          )}
          <Chip color="warning" variant="dot">
            {examData.category_name}
          </Chip>
          
          {/* Show discount badge if applicable */}
          {discountPercentage && !examData.is_purchased && (
            <Chip
              color="success"
              variant="flat"
              classNames={{
                content: "font-semibold",
              }}
            >
              -{discountPercentage}%
            </Chip>
          )}
        </div>
        <small className="text-default-500 truncate overflow-hidden whitespace-nowrap max-w-full">
          {examData.subname}
        </small>

        {/* Popover for the name */}
        <Popover>
          <PopoverTrigger>
            <h4 className="font-bold text-large truncate overflow-hidden whitespace-nowrap max-w-full cursor-pointer">
              {examData.name}
            </h4>
          </PopoverTrigger>
          <PopoverContent>{examData.name}</PopoverContent>
        </Popover>

        {/* Display the formatted duration */}
        {examData.duration && (
          <small className="text-default-500">
            Durasi: {formatDuration(examData.duration)}
          </small>
        )}
      </CardHeader>
      <CardBody className="overflow-visible py-2 flex flex-col justify-center items-center">
        <div className="flex justify-center">
          <div className="w-full h-48 flex justify-center items-center overflow-hidden">
            <Image
              alt="Card background"
              className="object-cover w-full h-full"
              src={examData.media_url}
              loading="lazy"
            />
          </div>
        </div>
        {/* Display the formatted price - Fix type error with null checks */}
        <div className="h-12 flex items-center justify-center">
          {examData.is_purchased && pathname?.startsWith("/available-exams") ? (
            <Chip
              color="success"
              startContent={<CheckIcon size={18} />}
              variant="flat"
            >
              Purchased
            </Chip>
          ) : pathname?.startsWith("/my-exams") || pathname?.startsWith("/my-trials") ? (
            <b>Retake ujian: Unlimited</b>
          ) : (
            <div className="mt-2 flex flex-col items-center">
              {/* Fixed the null/undefined check */}
              {typeof discountedPrice === 'number' && discountedPrice < examData.baseline_price ? (
                <>
                  <p className="text-gray-500 line-through text-sm">
                    {formatPrice(examData.baseline_price)}
                  </p>
                  <p className="text-indigo-700 font-bold text-lg">
                    {formatPrice(discountedPrice)}
                  </p>
                  <div className="text-xs text-green-600">Harga Spesial</div>
                </>
              ) : (
                <p className="text-xl font-bold">
                  {formatPrice(examData.baseline_price)}
                </p>
              )}
            </div>
          )}
        </div>
        <Divider className="my-4" />
        {/* Display purchase buttons */}
        {renderActionButton()}
      </CardBody>
    </Card>
  );
});

// Add display name for better debugging
ExamCard.displayName = 'ExamCard';