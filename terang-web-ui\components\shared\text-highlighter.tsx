import React, { useState, useEffect, useRef, useCallback, ReactNode } from 'react';

interface HighlightData {
  id: string;
  text: string;
  rangeData: {
    startOffset: number;
    endOffset: number;
    startContainer: number[];
    endContainer: number[];
  };
  type: 'highlight' | 'note';
}

interface SelectionData {
  text: string;
  range: Range;
}

interface TextHighlighterProps {
  children: ReactNode;
  containerClassName?: string;
  storageKey?: string; // Add a unique storage key for each question
}

interface NoteCollection {
  [key: string]: string;
}

const TextHighlighter: React.FC<TextHighlighterProps> = ({ 
  children, 
  containerClassName = "",
  storageKey = "highlights" // Default key
}) => {
  const [selection, setSelection] = useState<SelectionData | null>(null);
  const [selectionPosition, setSelectionPosition] = useState({ x: 0, y: 0 });
  const [highlights, setHighlights] = useState<HighlightData[]>([]);
  const [notes, setNotes] = useState<NoteCollection>({});
  const [showNoteInput, setShowNoteInput] = useState(false);
  const [currentNote, setCurrentNote] = useState("");
  const [currentHighlightId, setCurrentHighlightId] = useState<string | null>(null);
  const [showOptionsModal, setShowOptionsModal] = useState(false);
  const [optionsPosition, setOptionsPosition] = useState({ x: 0, y: 0 });
  const [selectedHighlightId, setSelectedHighlightId] = useState<string | null>(null);
  const [selectedHighlightType, setSelectedHighlightType] = useState<'highlight' | 'note' | null>(null);
  const [showNoteViewer, setShowNoteViewer] = useState(false);
  const [viewingNote, setViewingNote] = useState("");
  const containerRef = useRef<HTMLDivElement>(null);
  const noteInputRef = useRef<HTMLTextAreaElement>(null);

  // Use the storageKey for localStorage to allow multiple question highlights
  const highlightsKey = `${storageKey}_highlights`;
  const notesKey = `${storageKey}_notes`;

  // Load highlights from localStorage on component mount
  useEffect(() => {
    const savedHighlights = localStorage.getItem(highlightsKey);
    const savedNotes = localStorage.getItem(notesKey);
    
    if (savedHighlights) {
      try {
        setHighlights(JSON.parse(savedHighlights));
      } catch (e) {
        console.error("Failed to parse highlights", e);
      }
    } else {
      // Clear highlights when no saved data exists for this key
      setHighlights([]);
    }
    
    if (savedNotes) {
      try {
        setNotes(JSON.parse(savedNotes));
      } catch (e) {
        console.error("Failed to parse notes", e);
      }
    } else {
      // Clear notes when no saved data exists for this key
      setNotes({});
    }
    
    // This return function will run when the component unmounts
    // or when the storageKey changes
    return () => {
      // Remove all highlight elements from the DOM when component unmounts
      if (containerRef.current) {
        const highlightElements = containerRef.current.querySelectorAll('.highlighted-text');
        highlightElements.forEach(element => {
          const parent = element.parentNode;
          if (parent) {
            while (element.firstChild) {
              parent.insertBefore(element.firstChild, element);
            }
            parent.removeChild(element);
          }
        });
      }
    };
  }, [highlightsKey, notesKey]);

  // Try to apply saved highlights on component mount
  useEffect(() => {
    // Short delay to ensure the DOM is fully rendered
    const timer = setTimeout(() => {
      highlights.forEach(highlight => {
        applyHighlight(highlight);
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [highlights]);

  // Save highlights to localStorage whenever they change
  useEffect(() => {
    if (highlights.length > 0) {
      localStorage.setItem(highlightsKey, JSON.stringify(highlights));
    } else {
      localStorage.removeItem(highlightsKey);
    }
  }, [highlights, highlightsKey]);

  // Save notes to localStorage whenever they change
  useEffect(() => {
    if (Object.keys(notes).length > 0) {
      localStorage.setItem(notesKey, JSON.stringify(notes));
    } else {
      localStorage.removeItem(notesKey);
    }
  }, [notes, notesKey]);

  // Focus the note input when it appears
  useEffect(() => {
    if (showNoteInput && noteInputRef.current) {
      noteInputRef.current.focus();
    }
  }, [showNoteInput]);

  // Close modals on click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showOptionsModal) {
        const target = event.target as HTMLElement;
        if (!target.closest('.options-modal') && !target.closest('.highlighted-text')) {
          setShowOptionsModal(false);
        }
      }
      
      if (showNoteViewer) {
        const target = event.target as HTMLElement;
        if (!target.closest('.note-viewer-modal')) {
          setShowNoteViewer(false);
        }
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showOptionsModal, showNoteViewer]);

  // Handle text selection
  const handleMouseUp = useCallback((e: React.MouseEvent) => {
    // First, check if we clicked on an existing highlight
    const target = e.target as HTMLElement;
    const highlightEl = target.closest('.highlighted-text') as HTMLElement;
    
    if (highlightEl && highlightEl.dataset.highlightId) {
      // If clicked on a highlight, show options modal
      const highlightId = highlightEl.dataset.highlightId;
      const highlight = highlights.find(h => h.id === highlightId);
      
      if (highlight) {
        const rect = highlightEl.getBoundingClientRect();
        const containerRect = containerRef.current?.getBoundingClientRect() || { left: 0, top: 0 };
        
        setOptionsPosition({
          x: rect.left + rect.width / 2 - containerRect.left,
          y: rect.top - containerRect.top - 10
        });
        
        setSelectedHighlightId(highlightId);
        setSelectedHighlightType(highlight.type);
        setShowOptionsModal(true);
        return;
      }
    }

    // Otherwise handle text selection
    const selection = window.getSelection();
    
    if (selection && selection.toString().trim().length > 0) {
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      
      if (containerRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        
        // Calculate position for the popup relative to the container
        setSelectionPosition({
          x: rect.left + rect.width / 2 - containerRect.left,
          y: rect.top - containerRect.top - 40
        });
        
        setSelection({
          text: selection.toString(),
          range: range
        });
      }
    } else {
      setSelection(null);
    }
  }, [highlights]);

  // Get node path for serializing DOM position
  const getNodePath = (node: Node): number[] => {
    const path: number[] = [];
    let current: Node | null = node;
    
    while (current && current !== containerRef.current) {
      let index = 0;
      let sibling = current;
      
      while ((sibling = sibling.previousSibling as Node)) {
        index++;
      }
      
      path.unshift(index);
      current = current.parentNode;
    }
    
    return path;
  };

  // Find node by path for deserializing DOM position
  const findNodeByPath = (path: number[]): Node | null => {
    if (!containerRef.current) return null;
    
    let current: Node = containerRef.current;
    
    for (let i = 0; i < path.length; i++) {
      if (!current.childNodes[path[i]]) return null;
      current = current.childNodes[path[i]];
    }
    
    return current;
  };

  // Remove a highlight by ID
  const removeHighlight = useCallback((highlightId: string) => {
    // Remove from DOM
    if (containerRef.current) {
      const highlightEl = containerRef.current.querySelector(`[data-highlight-id="${highlightId}"]`) as HTMLElement;
      if (highlightEl) {
        const parent = highlightEl.parentNode;
        if (parent) {
          while (highlightEl.firstChild) {
            parent.insertBefore(highlightEl.firstChild, highlightEl);
          }
          parent.removeChild(highlightEl);
        }
      }
    }
    
    // Remove from state
    setHighlights(prev => prev.filter(h => h.id !== highlightId));
    
    // Remove associated note if it exists
    setNotes(prev => {
      const newNotes = {...prev};
      delete newNotes[highlightId];
      return newNotes;
    });
    
    // Close the options modal
    setShowOptionsModal(false);
  }, []);

  // Update the applyHighlight function to handle invalid ranges
  const applyHighlight = (highlight: HighlightData): void => {
    try {
      const startContainer = findNodeByPath(highlight.rangeData.startContainer);
      const endContainer = findNodeByPath(highlight.rangeData.endContainer);
      
      if (!startContainer || !endContainer) {
        console.warn("Could not find start or end container for highlight", highlight.id);
        return;
      }
      
      const range = document.createRange();
      
      // Get the actual length of the nodes to avoid index errors
      const startNodeLength = startContainer.textContent?.length || 0;
      const endNodeLength = endContainer.textContent?.length || 0;
      
      // Use the minimum between stored offset and actual node length
      const safeStartOffset = Math.min(highlight.rangeData.startOffset, startNodeLength);
      const safeEndOffset = Math.min(highlight.rangeData.endOffset, endNodeLength);
      
      range.setStart(startContainer, safeStartOffset);
      range.setEnd(endContainer, safeEndOffset);
      
      // Skip if the range is collapsed (zero-length)
      if (range.collapsed) {
        console.warn("Range is collapsed for highlight", highlight.id);
        return;
      }
      
      // Create highlight span with appropriate class and data
      const highlightClass = `highlighted-text ${highlight.type}`;
      
      // Use a different approach instead of surroundContents to handle spanning across elements
      // This is more complex but handles cases where the selection crosses element boundaries
      const contents = range.extractContents();
      const highlightEl = document.createElement('span');
      highlightEl.className = highlightClass;
      highlightEl.dataset.highlightId = highlight.id;
      
      if (highlight.type === 'note' && notes[highlight.id]) {
        highlightEl.title = notes[highlight.id];
      }
      
      highlightEl.appendChild(contents);
      range.insertNode(highlightEl);
      
    } catch (error) {
      console.error('Error applying highlight:', error);
      // Remove the problematic highlight from the state
      setHighlights(prev => prev.filter(h => h.id !== highlight.id));
    }
  };
  
  // Handle highlight action
  const handleHighlight = useCallback((type: 'highlight' | 'note') => {
    if (!selection) return;
    
    const newHighlight: HighlightData = {
      id: Date.now().toString(),
      text: selection.text,
      rangeData: {
        startOffset: selection.range.startOffset,
        endOffset: selection.range.endOffset,
        startContainer: getNodePath(selection.range.startContainer),
        endContainer: getNodePath(selection.range.endContainer)
      },
      type: type
    };
    
    setHighlights(prev => [...prev, newHighlight]);
    
    if (type === 'note') {
      setCurrentHighlightId(newHighlight.id);
      setShowNoteInput(true);
    } else {
      // Apply the highlight immediately
      applyHighlight(newHighlight);
      setSelection(null);
    }
  }, [selection]);

  // Save note
  const saveNote = useCallback(() => {
    if (currentHighlightId && currentNote.trim()) {
      setNotes(prev => ({
        ...prev,
        [currentHighlightId]: currentNote
      }));
      
      // Apply the highlight after saving the note
      const highlightToApply = highlights.find(h => h.id === currentHighlightId);
      if (highlightToApply) {
        applyHighlight(highlightToApply);
      }
    }
    
    setShowNoteInput(false);
    setCurrentNote("");
    setCurrentHighlightId(null);
    setSelection(null);
  }, [currentHighlightId, currentNote, highlights]);

  // Cancel note
  const cancelNote = useCallback(() => {
    // Remove the highlight if we cancel the note
    if (currentHighlightId) {
      setHighlights(prev => prev.filter(h => h.id !== currentHighlightId));
    }
    
    setShowNoteInput(false);
    setCurrentNote("");
    setCurrentHighlightId(null);
    setSelection(null);
  }, [currentHighlightId]);

  // View note
  const viewNote = useCallback((highlightId: string) => {
    const noteText = notes[highlightId];
    if (noteText) {
      setViewingNote(noteText);
      setShowNoteViewer(true);
      setShowOptionsModal(false);
    }
  }, [notes]);

  // Edit note
  const editNote = useCallback((highlightId: string) => {
    setCurrentHighlightId(highlightId);
    setCurrentNote(notes[highlightId] || '');
    setShowNoteInput(true);
    setShowOptionsModal(false);
  }, [notes]);

  // Handle hover over highlighted text
  const handleHighlightHover = useCallback((e: React.MouseEvent) => {
    // Find the closest highlighted text element
    const target = e.target as HTMLElement;
    const highlightEl = target.closest('.highlighted-text.note') as HTMLElement;
    
    if (highlightEl && highlightEl.dataset.highlightId) {
      const highlightId = highlightEl.dataset.highlightId;
      const noteText = notes[highlightId];
      
      if (noteText) {
        highlightEl.title = noteText;
      }
    }
  }, [notes]);

  // Handle highlighted element click events for accessibility
  const handleHighlightedElementInteraction = useCallback((e: React.KeyboardEvent) => {
    // If Enter or Space is pressed on a highlighted element, show the options modal
    if (e.key === 'Enter' || e.key === ' ') {
      const target = e.target as HTMLElement;
      const highlightEl = target.closest('.highlighted-text') as HTMLElement;
      
      if (highlightEl && highlightEl.dataset.highlightId) {
        e.preventDefault(); // Prevent default action (like scrolling for Space)
        
        const highlightId = highlightEl.dataset.highlightId;
        const highlight = highlights.find(h => h.id === highlightId);
        
        if (highlight) {
          const rect = highlightEl.getBoundingClientRect();
          const containerRect = containerRef.current?.getBoundingClientRect() || { left: 0, top: 0 };
          
          setOptionsPosition({
            x: rect.left + rect.width / 2 - containerRect.left,
            y: rect.top - containerRect.top - 10
          });
          
          setSelectedHighlightId(highlightId);
          setSelectedHighlightType(highlight.type);
          setShowOptionsModal(true);
        }
      }
    }
  }, [highlights]);

  // Combine all needed event handlers for the container
  const handleContainerMouseUp = useCallback((e: React.MouseEvent) => {
    handleMouseUp(e);
  }, [handleMouseUp]);

  const handleContainerMouseOver = useCallback((e: React.MouseEvent) => {
    handleHighlightHover(e);
  }, [handleHighlightHover]);

  const handleContainerFocus = useCallback((e: React.FocusEvent) => {
    // Similar to highlight hover but for focus events
    const target = e.target as HTMLElement;
    const highlightEl = target.closest('.highlighted-text.note') as HTMLElement;
    
    if (highlightEl && highlightEl.dataset.highlightId) {
      const highlightId = highlightEl.dataset.highlightId;
      const noteText = notes[highlightId];
      
      if (noteText) {
        highlightEl.title = noteText;
      }
    }
  }, [notes]);

  const handleContainerKeyDown = useCallback((e: React.KeyboardEvent) => {
    handleHighlightedElementInteraction(e);
  }, [handleHighlightedElementInteraction]);

  return (
    // Use an interactive element (div with tabIndex) to handle all events
    <div 
      ref={containerRef} 
      className={`text-highlighter-container ${containerClassName}`}
      onMouseUp={handleContainerMouseUp}
      onMouseOver={handleContainerMouseOver}
      onFocus={handleContainerFocus}
      onKeyDown={handleContainerKeyDown}
      role="presentation"
      aria-label="Highlightable text region"
    >
      {/* The content to be highlighted */}
      {children}
      
      {/* Selection popup */}
      {selection && !showNoteInput && (
        <div 
          className="selection-popup"
          style={{
            position: 'absolute',
            left: `${selectionPosition.x}px`,
            top: `${selectionPosition.y}px`,
            transform: 'translateX(-50%)',
            zIndex: 100,
            backgroundColor: '#2d3748',
            borderRadius: '8px',
            padding: '6px 10px',
            display: 'flex',
            gap: '10px',
            boxShadow: '0 2px 10px rgba(0,0,0,0.2)'
          }}
          role="menu"
          aria-label="Highlight options"
        >
          <button 
            onClick={() => handleHighlight('note')}
            className="popup-button"
            style={{
              background: 'none',
              border: 'none',
              color: 'white',
              cursor: 'pointer',
              padding: '5px 10px',
              fontSize: '14px',
              fontWeight: '500'
            }}
            role="menuitem"
          >
            Note
          </button>
          <div style={{ width: '1px', backgroundColor: '#4a5568' }} />
          <button 
            onClick={() => handleHighlight('highlight')}
            className="popup-button"
            style={{
              background: 'none',
              border: 'none',
              color: 'white',
              cursor: 'pointer',
              padding: '5px 10px',
              fontSize: '14px',
              fontWeight: '500'
            }}
            role="menuitem"
          >
            Highlight
          </button>
        </div>
      )}
      
      {/* Options Modal for Highlights */}
      {showOptionsModal && selectedHighlightId && (
        <div 
          className="options-modal"
          style={{
            position: 'absolute',
            left: `${optionsPosition.x}px`,
            top: `${optionsPosition.y}px`,
            transform: 'translateX(-50%)',
            zIndex: 100,
            backgroundColor: '#2d3748',
            borderRadius: '8px',
            padding: '6px 0',
            boxShadow: '0 2px 10px rgba(0,0,0,0.2)',
            minWidth: '150px'
          }}
          role="dialog"
          aria-label="Highlight options"
        >
          {selectedHighlightType === 'note' && (
            <button 
              onClick={() => viewNote(selectedHighlightId)}
              className="option-button"
              style={{
                background: 'none',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                padding: '8px 16px',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                textAlign: 'left'
              }}
            >
              <span style={{ marginRight: '8px' }} aria-hidden="true">👁️</span> View Note
            </button>
          )}
          
          {selectedHighlightType === 'note' && (
            <button 
              onClick={() => editNote(selectedHighlightId)}
              className="option-button"
              style={{
                background: 'none',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                padding: '8px 16px',
                fontSize: '14px',
                fontWeight: '500',
                display: 'flex',
                alignItems: 'center',
                width: '100%',
                textAlign: 'left'
              }}
            >
              <span style={{ marginRight: '8px' }} aria-hidden="true">✏️</span> Edit Note
            </button>
          )}
          
          <button 
            onClick={() => removeHighlight(selectedHighlightId)}
            className="option-button"
            style={{
              background: 'none',
              border: 'none',
              color: 'white',
              cursor: 'pointer',
              padding: '8px 16px',
              fontSize: '14px',
              fontWeight: '500',
              display: 'flex',
              alignItems: 'center',
              width: '100%',
              textAlign: 'left'
            }}
          >
            <span style={{ marginRight: '8px' }} aria-hidden="true">🗑️</span> 
            {selectedHighlightType === 'note' ? 'Remove Note' : 'Remove Highlight'}
          </button>
        </div>
      )}
      
      {/* Note Viewer Modal */}
      {showNoteViewer && (
        <div 
          className="note-viewer-modal"
          style={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'white',
            padding: '20px',
            borderRadius: '8px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            zIndex: 1000,
            width: '90%',
            maxWidth: '400px'
          }}
          role="dialog"
          aria-labelledby="note-viewer-title"
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '15px' }}>
            <h3 id="note-viewer-title" style={{ margin: 0 }}>Note</h3>
            <button
              onClick={() => setShowNoteViewer(false)}
              style={{
                background: 'none',
                border: 'none',
                fontSize: '18px',
                cursor: 'pointer',
                color: '#718096',
                padding: '0'
              }}
              aria-label="Close note viewer"
            >
              ✕
            </button>
          </div>
          <div 
            style={{
              padding: '15px',
              backgroundColor: '#f7fafc',
              borderRadius: '4px',
              fontSize: '14px',
              lineHeight: '1.5',
              color: '#4a5568',
              whiteSpace: 'pre-wrap'
            }}
          >
            {viewingNote}
          </div>
          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '15px' }}>
            <button
              onClick={() => setShowNoteViewer(false)}
              style={{
                padding: '8px 16px',
                backgroundColor: '#4299e1',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Close
            </button>
          </div>
        </div>
      )}
      
      {/* Note input modal */}
      {showNoteInput && (
        <div 
          className="note-input-modal"
          style={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'white',
            padding: '20px',
            borderRadius: '8px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            zIndex: 1000,
            width: '90%',
            maxWidth: '400px'
          }}
          role="dialog"
          aria-labelledby="note-input-title"
        >
          <h3 id="note-input-title" style={{ marginTop: 0, marginBottom: '15px' }}>Add a note</h3>
          <textarea
            ref={noteInputRef}
            value={currentNote}
            onChange={(e) => setCurrentNote(e.target.value)}
            style={{
              width: '100%',
              padding: '10px',
              borderRadius: '4px',
              border: '1px solid #cbd5e0',
              minHeight: '100px',
              marginBottom: '15px'
            }}
            placeholder="Write your note here..."
            aria-label="Note content"
          />
          <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
            <button
              onClick={cancelNote}
              style={{
                padding: '8px 16px',
                backgroundColor: '#e2e8f0',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
            <button
              onClick={saveNote}
              style={{
                padding: '8px 16px',
                backgroundColor: '#4299e1',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Save
            </button>
          </div>
        </div>
      )}
      
      {/* CSS for highlights */}
      <style>{`
        .text-highlighter-container {
          position: relative;
        }
        
        .highlighted-text.highlight {
          background-color: rgba(236, 201, 75, 0.3);
          cursor: pointer;
        }
        
        .highlighted-text.note {
          background-color: rgba(72, 187, 120, 0.2);
          cursor: pointer;
          border-bottom: 1px dashed #48bb78;
        }
        
        .highlighted-text:hover {
          opacity: 0.8;
        }
        
        .option-button:hover {
          background-color: rgba(255, 255, 255, 0.1);
        }

        /* Make the container focusable with a light outline when focused */
        .text-highlighter-container:focus {
          outline: 2px solid rgba(66, 153, 225, 0.3);
          outline-offset: 2px;
        }

        /* Hide outline when not needed */
        .text-highlighter-container:focus:not(:focus-visible) {
          outline: none;
        }
      `}</style>
    </div>
  );
}

export default TextHighlighter;