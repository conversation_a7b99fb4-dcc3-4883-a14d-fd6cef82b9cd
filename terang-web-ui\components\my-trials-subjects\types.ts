export interface PassingGrade {
    nama: string;
    score: number;
  }
  
  export interface AvailableExamsType {
    id: string;
    name: string;
    subname: string;
  }
  
  export interface GroupedExam {
    exam: AvailableExamsType;
    questions: any[];
    metadata?: {
      totalQuestions: number;
      completedQuestions: number;
    };
  }
  
  export interface SubjectGroup {
    [key: string]: GroupedExam[];
  }
  
  export interface ExamState {
    groupedExams: SubjectGroup;
    selectedSubject: string;
  }
  
  export interface QuestionData {
    id: string;
    hints: string[];
    insight: any[];
    options: {
      values: Array<{
        id: string;
        data: Array<{
          contents: Array<{
            type: string;
            content: string;
          }>;
        }>;
        is_correct: boolean;
      }>;
    };
    metadata?: Array<{
      level: number;
      value: string;
    }>;
    isCompleted?: boolean;
  }
  
  export interface QuestionAnswer {
    id: string;
    exam_id: string;
    data: string;
  }
  
  export interface GroupedQuestion {
    subject: string;
    exam: AvailableExamsType;
    questions: QuestionData[];
    metadata: {
      totalQuestions: number;
      completedQuestions: number;
    };
  }
  
  export interface SubjectState {
    questions: GroupedQuestion[];
    selectedExam: string;
  }
  
  export interface MyTrialsProps {
    data: AvailableExamsType[];
    questionAnswers: QuestionAnswer[] | undefined;
  }