import { useCallback, useReducer } from 'react';
import { getUserId } from '../lib/actions/account/actions';
import { fetchLastTrialSession } from '@/components/my-trials-subjects/actions';
import { TrialSession } from '@/components/types';

interface SessionState {
  loading: boolean;
  error: string | null;
  session: TrialSession | null;
  matches: boolean;
}

type SessionAction = 
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'SET_SESSION_DATA'; payload: { session: TrialSession | null; matches: boolean } };

const initialState: SessionState = {
  loading: false,
  error: null,
  session: null,
  matches: false
};

function examReducer(state: SessionState, action: SessionAction): SessionState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'SET_SESSION_DATA':
      return { 
        ...state, 
        session: action.payload.session,
        matches: action.payload.matches
      };
    default:
      return state;
  }
}

interface RawSession {
  id?: string;
  session_id: string;
  user_id: string;
  exam_id: string;
  type: string;
  status: string;
  start_time: string;
  end_time: string;
  answers: string;
  flagged_questions: string;
  created_at: string;
  modified_at: string;
  subject: string;
}

export function useExamSession(examId: string, subject: string) {
  const [state, dispatch] = useReducer(examReducer, initialState);

  const fetchSessionData = useCallback(async () => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const userId = await getUserId();
      const session = await fetchLastTrialSession(examId, userId, subject);

      if (session) {
        const mappedSession: TrialSession = {
          id: session.id || session.session_id,
          session_id: session.session_id,
          user_id: session.user_id,
          exam_id: session.exam_id,
          type: session.type,
          status: session.status,
          start_time: session.start_time,
          end_time: session.end_time,
          answers: session.answers,
          flagged_questions: session.flagged_questions,
          created_at: session.created_at,
          modified_at: session.modified_at,
          subject: session.subject
        };

        dispatch({ 
          type: 'SET_SESSION_DATA', 
          payload: { session: mappedSession, matches: true }
        });
      } else {
        dispatch({ 
          type: 'SET_SESSION_DATA', 
          payload: { session: null, matches: false }
        });
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch session information.";
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [examId, subject]);

  return { state, dispatch, fetchSessionData };
}