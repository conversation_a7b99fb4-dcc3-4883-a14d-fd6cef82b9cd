"use server";

import { z } from "zod";
import { signIn } from "next-auth/react"; // Import for client-side sign-in
import { cookies } from 'next/headers';
import { renderAsync } from "@react-email/components";
import { auth } from "@/auth";
import { JSX } from "react";

import { sendEmail } from "../../emails/mailgun";
import VerifyEmail from "../../emails/templates/Register";
import { addAIToken } from "@/components/auth/actions";

const FormSchema = z.object({
  username: z
    .string({ invalid_type_error: "Please input username." })
    .min(1, "Username cannot be empty!")
    .min(6, "Username must be at least 6 characters long")
    .regex(
      /^[a-zA-Z]+(?=.*\d)[a-zA-Z\d]*$/,
      "Username must start with a letter and contain at least one number",
    )
    .transform((val) => val.toLowerCase()), // Transform username to lowercase,
  email: z
    .string()
    .min(1, "Email cannot be empty!")
    .email({ message: "Please input the correct email address" }),
  firstname: z
    .string({ invalid_type_error: "Please input firstname." })
    .min(1, "Firstname cannot be empty!"),
  lastname: z
    .string({ invalid_type_error: "Please input lastname." })
    .min(1, "Lastname cannot be empty, please also fill out this field"),
  picture: z.string().url({ message: "Please input a valid picture URL." }),
});

const passwordForm = z
  .object({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters long")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/\d/, "Password must contain at least one digit")
      .regex(
        /[^a-zA-Z0-9]/,
        "Password must contain at least one special character",
      ),
    cpassword: z
      .string()
      .min(8, "Confirm password field must be at least 8 characters long"),
  })
  .refine((data) => data.password === data.cpassword, {
    message: "Passwords don't match",
    path: ["cpassword"], // path of error
  });

const insertUser = FormSchema.omit({});

export type State = {
  errors?: {
    username?: string[] | undefined;
    email?: string[] | undefined;
    firstname?: string[] | undefined;
    lastname?: string[] | undefined;
    password?: string[] | undefined;
    cpassword?: string[] | undefined;
    picture?: string[] | undefined;
  };
  message?: string | null;
};

export async function registerUser(formData: FormData) {
  // Store credentials to use for auto-login later
  const username = formData.get("username") as string;
  const password = formData.get("password") as string;
  
  const session = await auth();
  const cookieStore = await cookies();
  // Get referral code from cookie first, if not exists then from form input
  const cookieReferralCode = cookieStore.get('referralCode')?.value;
  const formReferralCode = formData.get("referralCode") as string;
  const referralCode = cookieReferralCode || formReferralCode;

  const validatedFields = insertUser.safeParse({
    username: username,
    email: formData.get("email") || session?.user?.email,
    firstname: formData.get("firstname"),
    lastname: formData.get("lastname"),
    picture: formData.get("picture") || session?.user?.image,
  });

  const pwd = passwordForm.safeParse({
    password: password,
    cpassword: formData.get("cpassword"),
  });

  // If form validation fails, return errors early. Otherwise, continue.
  if (!validatedFields.success) {
    return {
      errors: validatedFields.error.flatten().fieldErrors,
      message: "Missing Fields. Failed to register your account.",
    };
  }

  if (!pwd.success) {
    return {
      errors: pwd.error.flatten().fieldErrors,
      message: "Passwords don't match",
    };
  }

  // Prepare data for API request
  const { username: validatedUsername, email, firstname, lastname, picture } = validatedFields.data;
  const { password: validatedPassword } = pwd.data;

  const userObject = {
    username: validatedUsername,
    email,
    password: validatedPassword,
    firstname,
    lastname,
    picture,
  };

  const formattedUserObject = {
    username: userObject.username,
    email: userObject.email,
    password: userObject.password,
    first_name: userObject.firstname,
    last_name: userObject.lastname,
    picture: userObject.picture,
  };

  try {
    const token = formData.get("recaptchaToken"); // Assuming recaptchaRef is properly defined and initialized

    if (!token) {
      throw new Error("No reCAPTCHA token received, please try again.");
    }

    // Construct data for reCAPTCHA verification
    const recaptchaData = new URLSearchParams({
      secret: process.env.RECAPTCHA_SECRET_KEY as string,
      response: token as string,
    });

    // Verify reCAPTCHA token with Google
    const recaptchaResponse = await fetch(
      "https://www.google.com/recaptcha/api/siteverify",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: recaptchaData.toString(),
      },
    );

    console.log(recaptchaData);
    if (!recaptchaResponse.ok) {
      throw new Error("Failed to verify reCAPTCHA token");
    }

    const recaptchaResult = await recaptchaResponse.json();

    console.log(JSON.stringify(recaptchaResult));

    if (!recaptchaResult.success) {
      throw new Error("reCAPTCHA validation failed");
    }

    // Register the user
    const userResponse = await fetch(
      `${process.env.BACKEND_BASE_URL}/v1/users`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-KEY": process.env.BACKEND_API_KEY as string,
        },
        body: JSON.stringify(formattedUserObject),
      },
    );

    if (!userResponse.ok) {
      const errorData = await userResponse.json();
      console.log(errorData);
      return { error: { message: errorData || "Failed to register user." } };
    }

    const userData = await userResponse.json();
    console.log("User registration successful:", userData);
    
    // This is where we'll make changes to handle auto-login
    // First, manually authenticate the user to get a session
    const loginResponse = await fetch(
      `${process.env.BACKEND_BASE_URL}/v1/auth/login`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-API-KEY": process.env.BACKEND_API_KEY as string,
        },
        body: JSON.stringify({
          username: formattedUserObject.username,
          password: formattedUserObject.password,
        }),
      }
    );
    
    if (!loginResponse.ok) {
      console.error("Auto-login failed:", await loginResponse.text());
      // Continue with the rest of the process even if auto-login fails
    } else {
      const loginData = await loginResponse.json();
      console.log("Auto-login successful. User authenticated on server.");
      
      // Store the auth token in cookie or session (depends on your auth system)
      // This step depends on your auth implementation
    }
    
    // Now we can perform operations that require an authenticated user
    let tokenConfig = {}; // Empty config to use default 2M tokens

    if (referralCode) {
      try {
        // Call the referral redemption API
        const redeemResponse = await fetch(
          `${process.env.BACKEND_BASE_URL}/v0/referral/redeem/${referralCode}`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-API-KEY': process.env.BACKEND_API_KEY as string,
            },
            body: JSON.stringify({
              refereeId: userData.data.id,
              type: 'REGISTRATION'
            }),
          }
        );

        if (redeemResponse.ok) {
          // Successfully redeemed referral code - give 3M tokens
          console.log('Referral code redeemed successfully');
          tokenConfig = {
            tokenAmount: 3000000,
            metadata: {
              source: 'referral_registration_token',
              description: 'Referral Registration Token'
            }
          };
          
          // Only delete the cookie if we used the cookie's referral code
          if (cookieReferralCode) {
            cookieStore.delete('referralCode');
          }
        } else {
          console.error('Failed to redeem referral code:', await redeemResponse.text());
        }
      } catch (error) {
        // Log error but don't fail the registration process
        console.error('Error redeeming referral code:', error);
      }
    }

    try {
      // Use empty config for default 2M tokens, or referral config for 3M tokens
      // Add user ID to the token config to ensure it works even without a session
      const enhancedTokenConfig = {
        ...tokenConfig,
        userId: userData.data.id  // Add the user ID from the registration response
      };
      
      const tokenResult = await addAIToken(enhancedTokenConfig);
    
      if (tokenResult.ok) {
        console.log(`Subscription activated and ${tokenResult.data?.tokensAdded.toLocaleString()} AI Credits added!`, "success");
      } else {
        console.error('Failed to add AI tokens:', tokenResult.message);
        console.log("Subscription activated but failed to add AI tokens. Please contact support.", "warning");
      }
    } catch (error) {
      console.error('An error occurred while adding AI tokens:', error);
      console.log("Subscription activated but encountered an error while adding AI tokens. Please contact support.", "error");
    }

    // Attempt to send verification email with the user ID
    try {
      await sendEmailVerification(formattedUserObject.first_name, formattedUserObject.email, userData.data.id);
      console.log("Email verification sent successfully");
    } catch (error) {
      console.error("Error sending verification email:", error);
      // Continue with the registration process even if email sending fails
    }

    // Return the registration result along with credentials for client-side login
    return { 
      result: userData,
      credentials: {
        username: formattedUserObject.username,
        password: formattedUserObject.password
      }
    };
  } catch (error) {
    if (error instanceof Error) {
      return {
        error: { message: error.message || "Failed to register user." },
      };
    } else {
      throw new Error("Failed to register user.");
    }
  }
}

const getData = async (component: JSX.Element) => {
  console.log(component);
  const html = await renderAsync(component);
  return html;
};

// Updated to accept userId parameter
export async function sendEmailVerification(firstname: string, userEmail?: string, userId?: string) {
  try {
    // Use provided userEmail or get from session
    const session = await auth();
    const email = userEmail || session?.user?.email as string;

    if (!email) {
      throw new Error("No email found for verification");
    }

    // Prepare email parameters
    const from = "Terang AI <<EMAIL>>";
    const to = email;
    const subject = "[Account] Verify Your Email";

    // Generate token with userId if provided
    let token;
    if (userId) {
      token = await generateUserToken(email, userId);
    } else {
      token = await generateUserToken(email);
    }

    if (!token) {
      throw new Error("Error generating user token");
    }
    
    const props = {
      verificationLink: `${process.env.APP_VERIFICATION_BASE_URL}/api/auth/users/verify/${token}`,
      firstName: firstname,
    };

    // Render the component to HTML string
    console.log(await getData(<VerifyEmail {...props} />));
    const component = await getData(<VerifyEmail {...props} />);
    
    // Send email
    const result = await sendEmail(from, to, subject, component);
    console.log("Email sent successfully:", result);

    return true;
  } catch (error) {
    console.error("Error sending verification email:", error);
    throw new Error("Failed to send verification email.");
  }
}

// Updated to accept userId parameter
export async function generateUserToken(email: string, userId?: string) {
  const payload = {
    email: email,
    userId: userId // Include userId if provided
  };

  const userToken = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/users/tokens`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-API-KEY": process.env.BACKEND_API_KEY as string,
      },
      body: JSON.stringify(payload),
    },
  );

  if (!userToken.ok) {
    throw new Error(`API Error: ${userToken.statusText}`);
  }

  const userData = await userToken.json();
  console.log("Generate token successful:", userData.data.token);

  return userData.data.token;
}

// Rest of your code remains the same...
export async function matchUserToken(token: string): Promise<boolean> {
  try {
    const userToken = await fetch(
      `${process.env.BACKEND_BASE_URL}/v1/users/tokens/${token}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-API-KEY": process.env.BACKEND_API_KEY as string,
        },
      },
    );

    if (!userToken.ok) {
      throw new Error(`API Error: ${userToken.statusText}`);
    }

    const userData = await userToken.json();

    console.log("Token:", userData.data.token);
    console.log("Token Status is: ", userData.data.status);
    console.log("Email result from token DB:", userData.data.email);
    console.log("Token expires at:", userData.data.expires);

    // Check if the token is expired
    const currentTime = new Date();
    const tokenExpiryTime = new Date(userData.data.expires);

    if (tokenExpiryTime <= currentTime) {
      console.log("Token has expired");
      return false;
    }

    const session = await auth();
    const email = session?.user?.email as string;

    console.log("Session email result:", email);

    if (userData.data.status === "EXPIRED") {
      return false;
    } else {
      console.log("Matching email result");
      if (email === userData.data.email) {
        console.log("EMAIL MATCH!");
        // Example usage:
        const updatedUser = await updateUserVerificationStatus(email);

        console.log(`USER NOW VERIFIED: ${updatedUser.data.is_verified}`);
        if (updatedUser.data.is_verified) {
          // Update token status to EXPIRED
          const tokenUpdated = await updateTokenStatus(token, "EXPIRED");

          if (!tokenUpdated) {
            console.error("Failed to update token status to EXPIRED");
            return false;
          }

          return tokenUpdated;
        }
      }
    }

    return false;
  } catch (error) {
    console.log(error);
    throw new Error(`API Error: ${error}`);
  }
}

async function updateTokenStatus(
  token: string,
  status: string,
): Promise<boolean> {
  const apiUrl = `${process.env.BACKEND_BASE_URL}/v1/users/tokens/${token}`;
  const apiKey = process.env.BACKEND_API_KEY as string;

  try {
    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "X-API-KEY": apiKey,
      },
      body: JSON.stringify({
        status: status,
      }),
    });

    if (!response.ok) {
      console.error(`Failed to update token status: ${response.statusText}`);
      return false;
    }

    console.log(`Token ${token} status updated to ${status}`);
    return true;
  } catch (error) {
    console.error("Error updating token status:", error);
    return false;
  }
}

async function updateUserVerificationStatus(email: string) {
  const apiUrl = `${process.env.BACKEND_BASE_URL}/v1/users/emails/${email}`;
  const apiKey = process.env.BACKEND_API_KEY as string;

  try {
    const response = await fetch(apiUrl, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "X-API-KEY": apiKey,
      },
      body: JSON.stringify({
        is_verified: true,
      }),
    });

    if (!response.ok) {
      throw new Error("Failed to update user verification status");
    }

    const data = await response.json();
    console.log("Updated user:", data);
    return data;
  } catch (error) {
    console.error("Error updating user verification status:", error);
    throw error;
  }
}