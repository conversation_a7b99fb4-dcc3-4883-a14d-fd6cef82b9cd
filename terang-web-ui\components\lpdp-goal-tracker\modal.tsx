"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  ModalHeader,
  ModalBody
} from "@heroui/react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useSession } from "next-auth/react";

import { LpdpGoalTrackerForm } from "./form";
import {
  LpdpGoalTrackerFormData,
  FormErrors,
  validateLpdpGoalTrackerForm,
  submitLpdpGoalTrackerForm,
  checkLpdpGoalTrackerSubmission
} from "./actions";

export function LpdpGoalTrackerModal() {
  const router = useRouter();
  const { data: session } = useSession();

  // State variables
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const [hasSubmittedSurvey, setHasSubmittedSurvey] = useState(false);
  const [isEligible, setIsEligible] = useState(false);
  const [isCheckingStatus, setIsCheckingStatus] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);
  const [formData, setFormData] = useState<Partial<LpdpGoalTrackerFormData>>({});
  const [formErrors, setFormErrors] = useState<FormErrors>({});

  // Notification helper
  const showNotification = (message: string, type: "success" | "error" | "info") => {
    toast[type](message, {
      position: "top-center",
      autoClose: type === "success" ? 3000 : 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      className: "text-center",
      style: {
        minWidth: '300px',
        borderRadius: '10px',
        fontWeight: type === "success" ? 'bold' : 'normal'
      }
    });
  };

  // Clear error for a specific field
  const handleClearError = (field: string) => {
    setFormErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  };

  // Validate the form
  const validateForm = async (data: LpdpGoalTrackerFormData): Promise<FormErrors> => {
    return await validateLpdpGoalTrackerForm(data);
  };

  // Automatically check survey submission when component mounts
  useEffect(() => {
    const checkStatus = async () => {
      setIsCheckingStatus(true);
      try {
        // Check if user has already submitted the survey
        const hasSubmitted = await checkLpdpGoalTrackerSubmission();
        setHasSubmittedSurvey(hasSubmitted);

        if (hasSubmitted) {
          console.log("[LPDP Goal Tracker] User has already submitted the survey");
          return;
        }

        // If not submitted, set as eligible and show the form
        setIsEligible(true);
        console.log("[LPDP Goal Tracker] User has not submitted the form yet, showing form");

        // Open modal with a slight delay for better UX
        setTimeout(() => {
          setModalIsOpen(true);
        }, 500);
      } catch (error) {
        console.error("[LPDP Goal Tracker] Error checking status:", error);
      } finally {
        setIsCheckingStatus(false);
      }
    };

    checkStatus();
  }, []);

  // Function handlers
  const handleFormDataChange = (data: Partial<LpdpGoalTrackerFormData>) => {
    setFormData(data);
  };

  // Handle form decline (user didn't participate in LPDP)
  const handleFormDecline = async () => {
    try {
      setIsSubmitting(true);

      // Create minimal form data with just name and email
      const minimalFormData: LpdpGoalTrackerFormData = {
        name: formData.name || session?.user?.name || "User",
        email: formData.email || session?.user?.email || "",
        verbalReasoning: 1,
        quantitativeReasoning: 1,
        problemSolving: 1,
        passedLpdpTbs: false,
        feltHelped: false,
        helpfulnessRating: 1,
        mostHelpfulAspect: "Tidak mengikuti LPDP atau latihan yang berkaitan",
        improvementSuggestions: "-",
        contactConsent: false,
        phoneNumber: "-",
      };

      console.log("[LPDP Goal Tracker] Submitting minimal data (user declined):", minimalFormData);
      const result = await submitLpdpGoalTrackerForm(minimalFormData);

      if (result.success) {
        // Mark survey as submitted
        setHasSubmittedSurvey(true);

        // Show success screen
        setShowSuccessScreen(true);

        // Log successful submission
        console.log("[LPDP Goal Tracker] Form declined and submitted successfully");

        // Show success notification
        showNotification("Terima kasih atas respon Anda", "success");

        // Close modal and redirect after a delay
        setTimeout(() => {
          setModalIsOpen(false);
          router.push('/dashboard');
        }, 3000);
      } else {
        // Log submission failure
        console.error("[LPDP Goal Tracker] Form decline submission failed:", result.message);

        // Show error notification
        showNotification(result.message || "Gagal menyimpan data", "error");
      }
    } catch (error) {
      // Log submission error
      console.error("[LPDP Goal Tracker] Error submitting declined form:", error);

      // Show error notification
      const errorMessage = error instanceof Error
        ? error.message
        : "Terjadi kesalahan tidak diketahui";
      showNotification(errorMessage, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFormSubmit = async () => {
    if (!formData || Object.keys(formData).length === 0) {
      showNotification("Silakan isi formulir terlebih dahulu", "error");
      setFormErrors({ form: "Please fill out the form" });
      return;
    }

    // Ensure all numeric fields are actually numbers
    const processedFormData = {
      ...formData,
      verbalReasoning: formData.verbalReasoning !== undefined ? Number(formData.verbalReasoning) : undefined,
      quantitativeReasoning: formData.quantitativeReasoning !== undefined ? Number(formData.quantitativeReasoning) : undefined,
      problemSolving: formData.problemSolving !== undefined ? Number(formData.problemSolving) : undefined,
      helpfulnessRating: formData.helpfulnessRating !== undefined ? Number(formData.helpfulnessRating) : undefined,
    };

    console.log("[LPDP Goal Tracker] Form data before validation:", processedFormData);

    // Check if all required fields are present
    const requiredFields: (keyof LpdpGoalTrackerFormData)[] = [
      'name', 'email', 'verbalReasoning', 'quantitativeReasoning',
      'problemSolving', 'helpfulnessRating'
    ];

    const missingFields = requiredFields.filter(field => {
      const value = processedFormData[field];
      return value === undefined || value === null || value === '';
    });

    if (missingFields.length > 0) {
      console.log("[LPDP Goal Tracker] Missing fields:", missingFields);
      showNotification(`Silakan lengkapi semua field yang diperlukan`, "error");
      const errors: FormErrors = {};
      missingFields.forEach(field => {
        errors[field] = `Field ${field} harus diisi`;
      });
      setFormErrors(errors);
      return;
    }

    // Cast to complete form data since we've verified required fields
    const completeFormData = processedFormData as LpdpGoalTrackerFormData;

    // Validate the form
    const errors = await validateForm(completeFormData);
    if (Object.keys(errors).length > 0) {
      console.log("[LPDP Goal Tracker] Validation errors:", errors);
      const errorMessages = Object.values(errors).join(", ");
      showNotification(`Kesalahan validasi: ${errorMessages}`, "error");
      setFormErrors(errors);
      return;
    }

    try {
      setIsSubmitting(true);

      console.log("[LPDP Goal Tracker] Submitting form data:", completeFormData);
      const result = await submitLpdpGoalTrackerForm(completeFormData);

      if (result.success) {
        // Mark survey as submitted
        setHasSubmittedSurvey(true);

        // Show success screen
        setShowSuccessScreen(true);

        // Log successful submission
        console.log("[LPDP Goal Tracker] Form submitted successfully");

        // Show success notification
        showNotification("Data berhasil disimpan!", "success");

        // Close modal and redirect after a delay
        setTimeout(() => {
          setModalIsOpen(false);
          router.push('/dashboard');
        }, 5000);
      } else {
        // Log submission failure
        console.error("[LPDP Goal Tracker] Form submission failed:", result.message);

        // Show error notification
        showNotification(result.message || "Gagal menyimpan data", "error");
      }
    } catch (error) {
      // Log submission error
      console.error("[LPDP Goal Tracker] Error submitting form:", error);

      // Show error notification
      const errorMessage = error instanceof Error
        ? error.message
        : "Terjadi kesalahan tidak diketahui";
      showNotification(errorMessage, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Prevent modal from being reopened if survey is already submitted
  const handleModalOpenChange = (open: boolean) => {
    if (!hasSubmittedSurvey && !open) {
      if (window.confirm("Yakin ingin menutup formulir? Data kamu tidak akan tersimpan.")) {
        setModalIsOpen(false);
      }
    }
  };

  // If user is not eligible or has already submitted, don't render anything
  if (!isEligible && !isCheckingStatus) {
    return null;
  }

  return (
    <>
      {/* Full-screen loading overlay */}
      {isSubmitting && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="text-center">
            <Spinner color="white" size="lg" />
            <p className="mt-4 text-white text-xl">Mengirim...</p>
          </div>
        </div>
      )}

      {/* Survey Modal */}
      <Modal
        isOpen={modalIsOpen}
        onOpenChange={handleModalOpenChange}
        size="full"
        scrollBehavior="inside"
        classNames={{
          base: "h-[100dvh] max-w-full",
          body: "pb-24", // Add padding at the bottom for the buttons
          wrapper: "h-[100dvh] max-w-full",
          header: "border-b border-neutral-200",
          backdrop: "bg-black/30 backdrop-blur-sm"
        }}
      >
        <ModalContent>
          <ModalHeader className="sticky top-0 z-20 bg-white flex items-center p-3 sm:p-4">
            <div className="w-full flex items-center gap-2 sm:gap-3">
              <img
                src="https://terang.ai/_next/static/media/lpdp.b06d27d5.svg"
                alt="LPDP Logo"
                className="h-8 sm:h-10 w-auto"
              />
              <div>
                <h2 className="text-lg sm:text-xl font-medium">Survei Hasil TBS LPDP Batch 1 2025</h2>
                <p className="text-xs sm:text-sm text-gray-500 mt-1">Bantu kami meningkatkan layanan untuk persiapan LPDP</p>
              </div>
            </div>
          </ModalHeader>
          <ModalBody className="overflow-y-auto px-2 sm:px-4 md:px-6">
            {showSuccessScreen ? (
              <div className="flex flex-col items-center justify-center py-8 sm:py-10 px-3 sm:px-4 text-center max-w-3xl mx-auto">
                <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-green-100 flex items-center justify-center mb-4 sm:mb-6">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 sm:h-10 sm:w-10 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h3 className="text-xl sm:text-2xl font-bold mb-2">Terima Kasih!</h3>
                <p className="text-base sm:text-lg mb-4 sm:mb-6">Jawaban Anda telah berhasil disimpan.</p>
                <p className="text-sm sm:text-base text-gray-600 mb-6 sm:mb-8">
                  Kami sangat menghargai waktu dan masukan Anda. Informasi ini akan membantu kami meningkatkan layanan untuk persiapan LPDP.
                </p>
                <div className="flex items-center justify-center gap-3 sm:gap-4">
                  <img
                    src="https://terang.ai/_next/static/media/lpdp.b06d27d5.svg"
                    alt="LPDP Logo"
                    className="h-10 sm:h-12 w-auto"
                  />
                  <span className="text-lg sm:text-xl font-medium">×</span>
                  <img
                    src="https://cdn.terang.ai/images/logo/logo-terang-ai.svg"
                    alt="Terang AI Logo"
                    className="h-7 sm:h-8 w-auto"
                  />
                </div>
                <p className="text-xs sm:text-sm text-gray-500 mt-6 sm:mt-8">
                  Halaman akan ditutup dalam beberapa detik...
                </p>
              </div>
            ) : (
              <div className="flex flex-col bg-gray-50 rounded-lg max-w-3xl mx-auto">
                <LpdpGoalTrackerForm
                  clearError={handleClearError}
                  formErrors={formErrors}
                  initialData={formData}
                  onFormDataChange={handleFormDataChange}
                  onComplete={handleFormSubmit}
                  onDecline={handleFormDecline}
                  isSubmitting={isSubmitting}
                  userName={session?.user?.name || ""}
                  userEmail={session?.user?.email || ""}
                />
              </div>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>

      <ToastContainer />
    </>
  );
}
