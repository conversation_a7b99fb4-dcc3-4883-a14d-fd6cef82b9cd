import { Card } from "@heroui/card";
import { Spacer } from "@heroui/spacer";

// import Logo from '@/app/(dashboard)/layout/shared/logo/Logo';
import PageContainer from "@/components/page-container/PageContainer";
import AuthRegisterFinal from "@/components/auth/AuthRegisterFinal";
import { auth } from "@/auth";

export default async function RegisterFinal() {
  const session = await auth();

  if (!session?.user) return null;

  return (
    <PageContainer
      description="This is the final registration page you are looking for."
      title="Complete Registration"
    >
      <div
        className="px-3"
        style={{
          paddingTop: "5rem",
          paddingBottom: "5rem",
          position: "relative",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          backgroundSize: "400% 400%",
          animation: "gradient 10s ease infinite",
        }}
      >
        <Card
          style={{
            paddingTop: "2rem",
            paddingBottom: "2rem",
            zIndex: 1,
            width: "100%",
            maxWidth: "600px",
            borderRadius: "50px",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
            }}
          >
            {/* <Logo /> */}
            <Spacer y={1} />
            <div style={{ width: "100%" }}>
              <AuthRegisterFinal
                session={session}
                subtext={
                  <p style={{ textAlign: "center", color: "#999" }}>
                    To ensure your account security, please continue your
                    registration...
                  </p>
                }
                title="Almost Done!"
              />
            </div>
          </div>
        </Card>
      </div>
    </PageContainer>
  );
}
