import React, { useState, useEffect, useRef } from 'react';
import { Input, Spinner } from "@heroui/react";
import { searchUniversities } from "./actions";

interface University {
  name: string;
  country: string;
  domains: string[];
  web_pages: string[];
  alpha_two_code: string;
}

interface UniversitySearchProps {
  onUniversitySelect: (university: string) => void;
}

export const UniversitySearch: React.FC<UniversitySearchProps> = ({ 
  onUniversitySelect 
}) => {
  const [query, setQuery] = useState('');
  const [universities, setUniversities] = useState<University[]>([]);
  const [loading, setLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isSelectingRef = useRef(false);

  // Handle outside clicks to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current && 
        !dropdownRef.current.contains(event.target as Node) &&
        inputRef.current && 
        !inputRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Debounce search to reduce unnecessary API calls
  useEffect(() => {
    if (isSelectingRef.current) {
      isSelectingRef.current = false;
      return;
    }

    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    if (query.length > 2) {
      searchTimeoutRef.current = setTimeout(() => {
        performSearch();
      }, 300);
    } else {
      setUniversities([]);
      setIsDropdownOpen(false);
    }

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [query]);

  const performSearch = async () => {
    setLoading(true);
    try {
      const results = await searchUniversities(query);
      setUniversities(Array.isArray(results) ? results : []);
      setIsDropdownOpen(Array.isArray(results) && results.length > 0);
    } catch (error) {
      console.error('Error searching universities:', error);
      setUniversities([]);
      setIsDropdownOpen(false);
    } finally {
      setLoading(false);
    }
  };

  const handleUniversitySelect = (university: University) => {
    // Set selection flag and clear any pending searches
    isSelectingRef.current = true;
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Update state immediately
    setQuery(university.name);
    onUniversitySelect(university.name);
    setUniversities([]);
    setIsDropdownOpen(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setQuery(inputValue);
    if (inputValue.length > 0) {
      setIsDropdownOpen(true);
    }
  };

  const handleInputFocus = () => {
    if (universities.length > 0) {
      setIsDropdownOpen(true);
    }
  };

  return (
    <div className="relative w-full">
      <Input 
        ref={inputRef}
        type="text"
        value={query}
        onChange={handleInputChange}
        onFocus={handleInputFocus}
        placeholder="Cari universitas..."
        className="w-full"
        endContent={
          loading ? <Spinner size="sm" /> : null
        }
      />
      
      {isDropdownOpen && universities.length > 0 && (
        <div 
          ref={dropdownRef}
          className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-y-auto"
        >
          <div className="py-1">
            {universities.map((uni, index) => (
              <button 
                key={`${uni.name}-${index}`}
                className="w-full px-4 py-2 text-left hover:bg-gray-100 focus:bg-gray-100 focus:outline-none transition-colors duration-150"
                onClick={() => handleUniversitySelect(uni)}
              >
                <div className="flex justify-between items-center">
                  <span className="font-medium">{uni.name}</span>
                  <span className="text-xs text-gray-500 ml-2">{uni.country}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};