/** @type {import('next').NextConfig} */
const nextConfig = {
    serverExternalPackages: [
        '@react-email/components',
        '@react-email/render',
        '@react-email/html',
    ],
    transpilePackages: ['next-auth', 'katex'],
    reactStrictMode: false,
    distDir: 'build',
    poweredByHeader: false,
    images: {
        formats: ['image/webp','image/avif'],
        remotePatterns: [
            {
            protocol: 'https',
            hostname: '**',
            },
            {
            protocol: 'http',
            hostname: '**',
            },
        ],
        // loader: "custom",
        // imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
        // deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    },
};

module.exports = nextConfig;
