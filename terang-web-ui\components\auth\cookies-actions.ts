'use server'

import { cookies } from 'next/headers'
import { revalidatePath } from 'next/cache'

interface CookieResult {
  success: boolean
  value?: string | null
  error?: string
}

export async function getReferralCodeFromCookie(): Promise<CookieResult> {
  try {
    const cookieStore = await cookies()
    const referralCode = cookieStore.get('referralCode')
    return {
      success: true,
      value: referralCode?.value || null
    }
  } catch (error) {
    console.error('Error getting referral code:', error)
    return {
      success: false,
      error: 'Failed to retrieve referral code'
    }
  }
}

export async function setReferralCodeCookie(code: string): Promise<CookieResult> {
  try {
    const cookieStore = await cookies()
    await cookieStore.set('referralCode', code, {
      path: '/',
      maxAge: 7 * 24 * 60 * 60, // 7 days
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax'
    })
    
    revalidatePath('/')
    
    return {
      success: true,
      value: code
    }
  } catch (error) {
    console.error('Error setting referral code:', error)
    return {
      success: false,
      error: 'Failed to set referral code'
    }
  }
}

export async function deleteReferralCodeCookie(): Promise<CookieResult> {
  try {
    const cookieStore = await cookies()
    await cookieStore.delete('referralCode')
    
    revalidatePath('/')
    
    return {
      success: true
    }
  } catch (error) {
    console.error('Error deleting referral code:', error)
    return {
      success: false,
      error: 'Failed to delete referral code'
    }
  }
}