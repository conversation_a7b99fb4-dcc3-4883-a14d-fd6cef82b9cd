import React, { useState, useRef, useEffect, useMemo, useCallback, Fragment } from "react";
import { Card, CardBody } from "@heroui/react";
import { CheckmarkCircle02Icon } from "hugeicons-react";
import DOMPurify from "dompurify";
import ReactMarkdown from "react-markdown";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import 'katex/dist/katex.min.css';

import { AdaptiveImage } from "../shared/adaptive-image";
import { 
  renderEnhancedContent, 
  processHtmlAndLaTeX,
  containsLaTeX,
  processLaTeX 
} from "../shared/latex-handler"; 


interface ContentType {
  type: string;
  content: string;
}

interface ItemType {
  contents: ContentType[];
}

interface FlippableHintCardProps {
  question: any[];
  hints: any[];
}

const FlippableHintCard: React.FC<FlippableHintCardProps> = ({
  question,
  hints,
}) => {
  const [isFlipped, setIsFlipped] = useState<boolean>(false);
  const [cardHeight, setCardHeight] = useState<number>(0);
  const frontContentRef = useRef<HTMLDivElement>(null);
  const backContentRef = useRef<HTMLDivElement>(null);
  const imageRefs = useRef<(HTMLImageElement | null)[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Add these new state variables to track mouse events
  const [isMouseDown, setIsMouseDown] = useState<boolean>(false);
  const [startX, setStartX] = useState<number>(0);
  const [startY, setStartY] = useState<number>(0);
  const [hasMoved, setHasMoved] = useState<boolean>(false);

  const setImageRef = useCallback((index: number) => (el: HTMLImageElement | null) => {
    imageRefs.current[index] = el;
  }, []);

  const calculateHeight = useCallback(() => {
    if (frontContentRef.current && backContentRef.current) {
      const frontHeight = frontContentRef.current.scrollHeight;
      const backHeight = backContentRef.current.scrollHeight;
      const maxHeight = Math.max(frontHeight, backHeight);
      
      // Only update if height has changed significantly (more than 5px)
      if (Math.abs(maxHeight - cardHeight) > 5) {
        setCardHeight(maxHeight + 50); // Add minimal padding
      }
    }
  }, [cardHeight]);

  // Initial height calculation and window resize handler
  useEffect(() => {
    const handleResize = () => {
      requestAnimationFrame(calculateHeight);
    };

    calculateHeight();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [calculateHeight]);

  // Handle image loads
  useEffect(() => {
    const imageLoadPromises = imageRefs.current.map(img => {
      if (img) {
        return new Promise(resolve => {
          if (img.complete) {
            resolve(null);
          } else {
            img.onload = () => resolve(null);
          }
        });
      }
      return Promise.resolve();
    });

    Promise.all(imageLoadPromises).then(() => {
      calculateHeight();
    });
  }, [question, hints, calculateHeight]);

  // Replace the handleFlip function with these mouse handlers
  const handleMouseDown = (event: React.MouseEvent) => {
    setIsMouseDown(true);
    setStartX(event.clientX);
    setStartY(event.clientY);
    setHasMoved(false);
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isMouseDown) return;
    
    // Calculate distance moved
    const deltaX = Math.abs(event.clientX - startX);
    const deltaY = Math.abs(event.clientY - startY);
    
    // If moved more than a small threshold, consider it a drag
    if (deltaX > 3 || deltaY > 3) {
      setHasMoved(true);
    }
  };

  const handleMouseUp = () => {
    if (isMouseDown && !hasMoved) {
      // Only flip if it was a simple click (mouse down and up without movement)
      setIsFlipped(!isFlipped);
    }
    
    // Reset mouse tracking
    setIsMouseDown(false);
  };

  // Handle mouse leaving the component
  const handleMouseLeave = () => {
    setIsMouseDown(false);
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" || event.key === " ") {
      setIsFlipped(!isFlipped);
    }
  };

  const getBaseColor = () => {
    let hue: number;
    do {
      hue = Math.floor(Math.random() * 360);
    } while (hue > 60 && hue < 150);
    return [hue, Math.floor(Math.random() * 20) + 50, Math.floor(Math.random() * 15) + 35];
  };

  const getSofterVariantColor = (hue: number, saturation: number, lightness: number) => {
    return `hsl(${hue}, ${Math.max(30, saturation - 20)}%, ${Math.min(70, lightness + 15)}%)`;
  };

  const [frontGradient, backGradient] = useMemo(() => {
    const [h1, s1, l1] = getBaseColor();
    const [h2, s2, l2] = getBaseColor();
    const grad1 = `linear-gradient(to bottom right, ${getSofterVariantColor(h1, s1, l1)}, hsl(${h1}, ${s1}%, ${l1}%))`;
    const grad2 = `linear-gradient(to bottom right, ${getSofterVariantColor(h2, s2, l2)}, hsl(${h2}, ${s2}%, ${l2}%))`;
    return [grad1, grad2];
  }, []);

  // In Questions.tsx, update the renderContent function
  const renderContent = (content: ContentType) => {
    if (content.type === "text") {
      // Check if content contains both HTML and LaTeX
      if (/<[a-z][\s\S]*>/i.test(content.content) && containsLaTeX(content.content)) {
        // Use our new general-purpose handler for mixed content
        return processHtmlAndLaTeX(content.content);
      }
      
      // Special case for display math with \[ ... \]
      if (content.content.includes('\\[') && content.content.includes('\\]')) {
        return renderEnhancedContent(content);
      }
      
      // Check if contains just HTML tags
      if (/<[a-z][\s\S]*>/i.test(content.content)) {
        return (
          <div
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(content.content, {
                ADD_ATTR: ['target'],
                ALLOWED_TAGS: ['br', 'p', 'div', 'span', 'a', 'b', 'i', 'strong', 'em', 'img', 'ul', 'ol', 'li']
              }),
            }}
          />
        );
      }
      
      // Check if contains just LaTeX
      if (containsLaTeX(content.content)) {
        return renderEnhancedContent(content);
      }
      
      // If no LaTeX or HTML, just render as plain text
      return <span>{content.content}</span>;
    }
    
    // For media content
    if (content.type === "media") {
      return <AdaptiveImage src={content.content} alt="Question media" />;
    }
    
    // For other content types (fallback)
    return null;
  };

  const FlipRightIcon = () => (
    <svg
      fill="none"
      height={16}
      width={16}
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clipRule="evenodd"
        d="M14.753 4.2001C15.2516 4.19865 15.736 4.1996 16.2157 4.21565C16.7677 4.23413 17.2305 3.81292 17.2494 3.27486C17.2684 2.73681 16.8363 2.28566 16.2843 2.26719C15.7647 2.2498 15.2485 2.24903 14.747 2.2505C14.1947 2.25211 13.7484 2.68985 13.75 3.22822C13.7517 3.76659 14.2007 4.20172 14.753 4.2001ZM18.1121 3.31074C17.8631 3.79127 18.0608 4.37762 18.5538 4.62037C19.3203 4.99785 19.9432 5.60728 20.3275 6.35624C20.5745 6.83774 21.1752 7.03283 21.6692 6.79199C22.1631 6.55115 22.3632 5.96557 22.1162 5.48408C21.5396 4.36032 20.6057 3.44656 19.4556 2.88019C18.9626 2.63744 18.3611 2.8302 18.1121 3.31074ZM21.75 8.75072C21.1977 8.75072 20.75 9.18715 20.75 9.72553V14.2746C20.75 14.813 21.1977 15.2494 21.75 15.2494C22.3023 15.2494 22.75 14.813 22.75 14.2746V9.72553C22.75 9.18715 22.3023 8.75072 21.75 8.75072ZM21.6859 17.1467C21.1888 16.912 20.5907 17.1146 20.35 17.5991C19.9678 18.3685 19.3355 18.9947 18.5538 19.3796C18.0608 19.6224 17.8631 20.2087 18.1121 20.6893C18.3611 21.1698 18.9626 21.3626 19.4556 21.1198C20.6285 20.5422 21.5764 19.6034 22.15 18.4489C22.3907 17.9644 22.1829 17.3814 21.6859 17.1467ZM17.2494 20.7251C17.2305 20.1871 16.7677 19.7659 16.2157 19.7843C15.736 19.8004 15.2517 19.8013 14.753 19.7999C14.2007 19.7983 13.7517 20.2334 13.75 20.7718C13.7484 21.3102 14.1947 21.7479 14.747 21.7495C15.2485 21.751 15.7647 21.7502 16.2843 21.7328C16.8363 21.7143 17.2684 21.2632 17.2494 20.7251Z"
        fill="currentColor"
        fillRule="evenodd"
      />
      <path
        d="M5.49378 2.45976C4.1225 2.73883 2.81248 3.39376 1.95349 4.88042C1.57508 5.53536 1.40851 6.2686 1.32843 7.15152C1.24999 8.0163 1.24999 9.09506 1.25 10.4644V13.5365C1.24999 14.9058 1.24999 15.9846 1.32843 16.8493C1.40851 17.7323 1.57508 18.4655 1.95349 19.1204C2.81248 20.6071 4.1225 21.262 5.49378 21.5411C6.60294 21.7668 7.81715 21.7563 8.89367 21.747H8.89369H8.89387C9.10163 21.7452 9.30425 21.7435 9.5 21.7435C10.4531 21.7435 11.3499 21.618 11.97 21.0175C12.5962 20.4113 12.75 19.5087 12.75 18.4954V5.50545C12.75 4.49218 12.5962 3.58959 11.97 2.98332C11.3499 2.38285 10.4531 2.25738 9.5 2.25738C9.30419 2.25738 9.10151 2.25563 8.89369 2.25383C7.81717 2.24452 6.60295 2.23402 5.49378 2.45976Z"
        fill="currentColor"
        opacity="0.4"
      />
    </svg>
  );

  const containerStyle: React.CSSProperties = {
    perspective: "1000px",
    width: "100%",
    height: `${cardHeight}px`,
    transition: "height 0.3s ease-in-out",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  };

  const cardStyle: React.CSSProperties = {
    position: "relative",
    width: "90%",
    height: "100%",
    transition: "transform 0.6s",
    transformStyle: "preserve-3d",
    cursor: "pointer",
    transform: isFlipped ? "rotateY(180deg)" : "rotateY(0deg)",
  };

  const faceStyle: React.CSSProperties = {
    position: "absolute",
    width: "100%",
    height: "100%",
    backfaceVisibility: "hidden",
    display: "flex",
    flexDirection: "column",
    color: "white",
    textShadow: "0 1px 2px rgba(0,0,0,0.3)",
  };

  return (
    <div style={containerStyle} ref={containerRef}>
      <div
        role="button"
        tabIndex={0}
        style={cardStyle}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseLeave}
        onKeyDown={handleKeyPress}
        aria-label={`Flip card. Current side: ${isFlipped ? "Hints" : "Question"}`}
        aria-pressed={isFlipped}
      >
        <Card className="shadow-lg" style={{ ...faceStyle, background: frontGradient }}>
          <CardBody className="flex flex-col justify-between p-4">
            <div ref={frontContentRef}>
              <h3 className="text-xl font-bold mb-4 text-center">Pertanyaan</h3>
              <div className="text-base mb-4">
                {question.map((item, index) => (
                  <div key={index}>
                    {item.contents.map((content: any, contentIndex: number) => (
                      <React.Fragment key={`content-${contentIndex}`}>
                        {renderContent(content)}
                      </React.Fragment>
                    ))}
                  </div>
                ))}
              </div>
            </div>
            <div className="flex items-center justify-center text-sm">
              <FlipRightIcon />
              <span className="ml-1">Flip for hints</span>
            </div>
          </CardBody>
        </Card>

        <Card className="shadow-lg" style={{ ...faceStyle, transform: "rotateY(180deg)", background: backGradient }}>
          <CardBody className="flex flex-col justify-between p-4">
            <div ref={backContentRef}>
              <h3 className="text-xl font-bold mb-4 text-center">Hints</h3>
              <div className="space-y-3">
                {hints.map((hint, index) => {
                  // If hint is a string (simple case)
                  if (typeof hint === 'string') {
                    return (
                      <div key={`hint-${index}`} className="flex items-start space-x-3">
                        <CheckmarkCircle02Icon className="flex-shrink-0 w-5 h-5 mt-1" />
                        <span className="text-left">{hint}</span>
                      </div>
                    );
                  }
                  
                  // If hint is an object with contents property (complex case)
                  else if (hint && typeof hint === 'object' && 'contents' in hint && Array.isArray(hint.contents)) {
                    return (
                      <div key={`hint-${index}`} className="flex items-start space-x-3">
                        <CheckmarkCircle02Icon className="flex-shrink-0 w-5 h-5 mt-1" />
                        <div className="text-left">
                          {hint.contents.map((content: ContentType, contentIndex: number) => (
                            <Fragment key={`hint-content-${index}-${contentIndex}`}>
                              {renderContent(content)}
                            </Fragment>
                          ))}
                        </div>
                      </div>
                    );
                  }
                  
                  // Fallback for other hint formats
                  return (
                    <div key={`hint-${index}`} className="flex items-start space-x-3">
                      <CheckmarkCircle02Icon className="flex-shrink-0 w-5 h-5 mt-1" />
                      <span className="text-left">{JSON.stringify(hint)}</span>
                    </div>
                  );
                })}
              </div>
            </div>
            <div className="flex items-center justify-center text-sm">
              <FlipRightIcon />
              <span className="ml-1">Flip back</span>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default FlippableHintCard;