import React from "react";

interface CompanyCardProps {
  logo: string; // URL or path to the logo image
  name: string;
  location: string;
}

const CompanyCard: React.FC<CompanyCardProps> = ({ logo, name, location }) => {
  return (
    <div style={styles.container}>
      <img alt={`${name} logo`} src={logo} style={styles.logo} />
      {/* <div style={styles.textContainer}>
        <span style={styles.name}>{name}</span>
        <span style={styles.location}>{location}</span>
      </div> */}
    </div>
  );
};

const styles: { [key: string]: React.CSSProperties } = {
  container: {
    display: "flex",
    alignItems: "center",
  },
  logo: {
    width: "auto",
    height: "auto",
    borderRadius: "4px",
    marginRight: "10px",
  },
  textContainer: {
    display: "flex",
    flexDirection: "column",
  },
  name: {
    fontWeight: "bold",
    fontSize: "16px",
    color: "#000",
  },
  location: {
    fontSize: "14px",
    color: "#666",
  },
};

export default CompanyCard;
