"use server";

import { auth } from "@/auth";

export interface UserData {
  name: string;
  email: string;
}

export async function getUserData(email?: string): Promise<UserData | null> {
  try {
    // If email is provided (from URL params), use it directly
    if (email) {
      console.log("[SNBT Survey] Using provided email:", email);
      
      // Try to fetch user data from backend
      const response = await fetch(
        `${process.env.BACKEND_BASE_URL}/v0/users/profile?email=${encodeURIComponent(email)}`,
        {
          method: 'GET',
          headers: {
            'X-API-KEY': process.env.BACKEND_API_KEY as string,
          },
        }
      );

      if (response.ok) {
        const userData = await response.json();
        console.log("[SNBT Survey] User data fetched from backend:", userData);
        return {
          name: userData.name || "",
          email: userData.email || email,
        };
      } else {
        console.log("[SNBT Survey] User not found in backend, using email only");
        return {
          name: "",
          email: email,
        };
      }
    }

    // If no email provided, try to get from session
    const session = await auth();
    if (session?.user) {
      console.log("[SNBT Survey] Using session data:", session.user);
      return {
        name: session.user.name || "",
        email: session.user.email || "",
      };
    }

    console.log("[SNBT Survey] No user data available");
    return null;
  } catch (error) {
    console.error("[SNBT Survey] Error getting user data:", error);
    return null;
  }
}
