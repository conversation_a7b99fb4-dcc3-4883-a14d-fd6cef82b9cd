"use client";

import React from 'react';
import { Card, CardBody, Button, Progress } from "@heroui/react";
import { 
  LegalDocument02Icon,
  Target02Icon,
  Brain02Icon,
  VideoReplayIcon,
  Analytics02Icon,
  Book02Icon,
  Clock02Icon,
  ArrowRight02Icon,
  UserIcon,
  StudyLampIcon,
  File01Icon,
  PresentationBarChart01Icon,
  Globe02Icon,
  Award02Icon,
  Message02Icon,
  LanguageSkillIcon,
  CameraMicrophone02Icon,
  Video02Icon,
  ChartLineData02Icon,
  UserMultipleIcon,
  SchoolIcon,
  TeacherIcon
} from 'hugeicons-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

interface FounderProps {
  name: string;
  position: string;
  img: string;
  quote: string;
}

const Founder: React.FC<FounderProps> = ({ name, position, img, quote }) => (
  <Card className="max-w-sm bg-white shadow-lg rounded-3xl">
    <CardBody className="p-6">
      <div className="flex items-start mb-4">
        <Image
          src={img}
          alt={name}
          width={96}
          height={96}
          className="w-24 h-24 rounded-full object-cover"
        />
        <div className="ml-4 flex flex-col items-start">
          <h3 className="text-2xl font-semibold">
            {name.split(' ')[0]}<br/>{name.split(' ').slice(1).join(' ')}
          </h3>
          <p className="text-blue-500 mt-1">{position}</p>
        </div>
      </div>
      <p className="text-gray-600 italic leading-relaxed">
        &quot;{quote}&quot;
      </p>
    </CardBody>
  </Card>
);

const AboutPage = () => {
  const router = useRouter();
  
  const founders = [
    {
      name: "Alfian Firmansyah",
      position: "CPO & Co-founder",
      img: "https://cdn.terang.ai/landingpage-assets/founders/alfian.jpeg",
      quote: "Kami berkomitmen untuk mendemokratisasi pendidikan berkualitas, mengubah setiap momen belajar menjadi peluang yang dapat diakses oleh semua lapisan masyarakat Indonesia."
    },
    {
      name: "Muhamad Fahriza Novriansyah",
      position: "CEO & Co-founder",
      img: "https://cdn.terang.ai/landingpage-assets/founders/fahriza.jpeg",
      quote: "Visi kami adalah menciptakan ekosistem pembelajaran yang inklusif, di mana teknologi menjadi jembatan untuk menyediakan pendidikan berkualitas tinggi bagi setiap individu, tanpa memkamung latar belakang."
    },
    {
      name: "Syahrul Hidayat",
      position: "CTO & Co-founder",
      img: "https://cdn.terang.ai/landingpage-assets/founders/syahrul.jpeg",
      quote: "Misi kami adalah meruntuhkan hambatan akses pendidikan melalui inovasi teknologi, memastikan bahwa setiap pelajar di Indonesia memiliki kesempatan yang sama untuk meraih potensi terbaik mereka."
    }
  ];

  const learningMethods = [
    {
      title: "Diagnosis Kemampuan Komprehensif",
      description: "Tes awal yang mengukur pemahaman dasar di berbagai bidang dan mengidentifikasi area yang perlu ditingkatkan",
      phases: [
        { name: "Pemetaan Kompetensi Multi-Area", progress: 100 },
        { name: "Analisis Kekuatan & Kelemahan", progress: 100 },
        { name: "Rekomendasi Pembelajaran Personal", progress: 100 }
      ]
    },
    {
      title: "Pembelajaran Adaptif AI",
      description: "Sistem AI yang menyesuaikan materi dengan tingkat pemahaman dan gaya belajar individual untuk setiap jenis ujian",
      phases: [
        { name: "Personalisasi Konten Multi-Platform", progress: 100 },
        { name: "Penyesuaian Kesulitan Real-time", progress: 100 },
        { name: "Optimasi Waktu & Efisiensi", progress: 100 }
      ]
    },
    {
      title: "AI Interview Simulation",
      description: "Latihan wawancara virtual dengan AI untuk persiapan beasiswa dan seleksi dengan feedback mendalam",
      phases: [
        { name: "Simulasi Wawancara Real-time", progress: 100 },
        { name: "Analisis Bahasa Tubuh & Vokal", progress: 100 },
        { name: "Feedback Personal & Improvement Tips", progress: 100 }
      ]
    }
  ];

  const examCategories = [
    {
      title: "Seleksi CPNS",
      description: "Persiapan komprehensif untuk seleksi Calon Pegawai Negeri Sipil",
      subjects: [
        "TWK (Tes Wawasan Kebangsaan)",
        "TIU (Tes Intelegensi Umum)", 
        "TKP (Tes Karakteristik Pribadi)",
        "SKB (Seleksi Kompetensi Bidang)"
      ],
      icon: LegalDocument02Icon,
      color: "blue"
    },
    {
      title: "UTBK-SNBT", 
      description: "Ujian Tulis Berbasis Komputer untuk Seleksi Nasional Berdasarkan Tes",
      subjects: [
        "TPS (Tes Potensi Skolastik)",
        "Literasi dalam Bahasa Indonesia",
        "Penalaran Matematika",
        "Literasi dalam Bahasa Inggris"
      ],
      icon: SchoolIcon,
      color: "green"
    },
    {
      title: "Beasiswa LPDP",
      description: "Persiapan beasiswa Lembaga Pengelola Dana Pendidikan",
      subjects: [
        "TKD (Tes Kemampuan Dasar)",
        "TKB (Tes Kemampuan Bidang)",
        "TBS (Tes Bahasa Inggris)",
        "AI Mock Interview"
      ],
      icon: Award02Icon,
      color: "purple"
    },
    {
      title: "Tes Bahasa Internasional",
      description: "Persiapan tes bahasa untuk keperluan akademik dan profesional",
      subjects: [
        "TOEFL (Test of English as Foreign Language)",
        "IELTS (International English Language Testing)",
        "Tes Bahasa Lainnya",
        "Speaking & Writing Practice"
      ],
      icon: LanguageSkillIcon,
      color: "orange"
    }
  ];

  const aiFeatures = [
    {
      title: "AI Tutor Personal",
      description: "Asisten AI yang memberikan panduan pembelajaran personal 24/7",
      icon: Brain02Icon,
      color: "bg-blue-50 text-blue-600"
    },
    {
      title: "Smart Analytics",
      description: "Analisis mendalam terhadap performa dan pola belajar untuk optimasi hasil",
      icon: Analytics02Icon,
      color: "bg-green-50 text-green-600"
    },
    {
      title: "Adaptive Testing",
      description: "Sistem ujian yang menyesuaikan tingkat kesulitan berdasarkan kemampuan real-time",
      icon: Target02Icon,
      color: "bg-purple-50 text-purple-600"
    },
    {
      title: "AI Interview Coach",
      description: "Simulator wawancara dengan AI untuk persiapan beasiswa dan seleksi kerja",
      icon: Video02Icon,
      color: "bg-orange-50 text-orange-600"
    },
    {
      title: "Voice & Speech Analysis",
      description: "Analisis kemampuan berbicara dan pengucapan untuk tes bahasa",
      icon: CameraMicrophone02Icon,
      color: "bg-indigo-50 text-indigo-600"
    },
    {
      title: "Progress Tracking",
      description: "Pelacakan kemajuan belajar dengan visualisasi data yang komprehensif",
      icon: ChartLineData02Icon,
      color: "bg-pink-50 text-pink-600"
    }
  ];

  const studyResources = [
    {
      title: "Bank Soal Komprehensif",
      description: "Ribuan soal latihan dengan pembahasan lengkap untuk semua jenis ujian",
      icon: Target02Icon,
      color: "bg-green-50 text-green-600"
    },
    {
      title: "Video Pembelajaran",
      description: "Materi pembelajaran interaktif dengan visualisasi yang mudah dipahami",
      icon: VideoReplayIcon,
      color: "bg-blue-50 text-blue-600"
    },
    {
      title: "Mock Test Realistis",
      description: "Simulasi ujian yang mirip dengan kondisi sesungguhnya",
      icon: File01Icon,
      color: "bg-purple-50 text-purple-600"
    },
    {
      title: "AI Interview Practice",
      description: "Latihan wawancara dengan AI untuk persiapan beasiswa dan seleksi",
      icon: Message02Icon,
      color: "bg-orange-50 text-orange-600"
    },
    {
      title: "Performance Analytics",
      description: "Analisis mendalam tentang kekuatan dan area yang perlu diperbaiki",
      icon: PresentationBarChart01Icon,
      color: "bg-indigo-50 text-indigo-600"
    },
    {
      title: "Study Planner",
      description: "Penjadwalan belajar yang optimal berdasarkan target dan kemampuan",
      icon: Clock02Icon,
      color: "bg-pink-50 text-pink-600"
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="mx-auto max-w-6xl px-6 pt-20 pb-32 bg-gradient-to-b from-blue-50 to-white">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl font-bold mb-6">
            <span className="text-gray-900">Terang AI sedang merevolusi</span>
            <br />
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              persiapan ujian di Indonesia
            </span>
          </h1>
          <p className="text-xl text-gray-600 mb-8">
            Platform pembelajaran berbasis AI untuk persiapan CPNS, UTBK, LPDP, dan beasiswa. 
            Dilengkapi dengan AI Interview, analisis mendalam, dan pembelajaran adaptif untuk 
            membantu kamu mencapai target dengan lebih efektif.
          </p>
          <div className="flex flex-wrap gap-4">
            <Button 
              color="primary"
              variant="solid"
              onPress={() => router.push("/available-exams")}
              endContent={<ArrowRight02Icon className="h-4 w-4" />}
            >
              Mulai Belajar
            </Button>
            <Button 
              color="primary"
              variant="bordered"
              onPress={() => router.push("/ai-interview-lpdp")}
              endContent={<Video02Icon className="h-4 w-4" />}
            >
              Coba AI Interview
            </Button>
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="mx-auto max-w-6xl px-6 py-20">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-sm uppercase tracking-wider text-blue-600 mb-4">MISI KAMI</h2>
          <h3 className="text-4xl font-bold mb-8 text-gray-900">
            Menciptakan ekosistem pembelajaran
            yang inklusif dan komprehensif
          </h3>
          <p className="text-lg text-gray-600 mb-6">
            Terang AI hadir untuk mentransformasi cara persiapan ujian di Indonesia dengan 
            menghadirkan teknologi kecerdasan buatan yang adaptif. Dari CPNS hingga beasiswa 
            internasional, dari UTBK hingga tes bahasa, kami menyediakan solusi pembelajaran 
            personal yang membantu setiap individu mencapai potensi terbaiknya.
          </p>
          <p className="text-lg text-gray-600">
            Dengan fitur AI Interview yang inovatif, analisis mendalam, dan kurikulum yang 
            selalu update, kami berkomitmen menjadi partner terpercaya dalam perjalanan 
            pendidikan dan karir kamu.
          </p>
        </div>
      </section>

      {/* Exam Categories Section */}
      <section className="bg-gradient-to-r from-gray-50 to-blue-50 py-20">
        <div className="mx-auto max-w-6xl px-6">
          <h2 className="text-3xl font-bold text-center mb-4">Cakupan Ujian Komprehensif</h2>
          <p className="text-gray-600 text-center mb-12 max-w-3xl mx-auto">
            Satu platform untuk semua kebutuhan persiapan ujian kamu, dari seleksi CPNS hingga beasiswa internasional
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {examCategories.map((category, index) => (
              <Card key={index} className="bg-white hover:shadow-lg transition-shadow">
                <CardBody className="p-6">
                  <div className="flex items-start mb-4">
                    <div className={`p-3 rounded-full bg-${category.color}-100 mr-4`}>
                      <category.icon className={`w-8 h-8 text-${category.color}-600`} />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold mb-2">{category.title}</h3>
                      <p className="text-gray-600 text-sm">{category.description}</p>
                    </div>
                  </div>
                  <ul className="space-y-2">
                    {category.subjects.map((subject, subjectIndex) => (
                      <li key={subjectIndex} className="flex items-center">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                        <span className="text-gray-700 text-sm">{subject}</span>
                      </li>
                    ))}
                  </ul>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* AI Features Section */}
      <section className="mx-auto max-w-6xl px-6 py-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">Fitur AI Terdepan</h2>
          <p className="text-gray-600 max-w-3xl mx-auto">
            Teknologi kecerdasan buatan canggih yang memahami cara belajar kamu dan memberikan pengalaman pembelajaran yang truly personal
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {aiFeatures.map((feature, index) => (
            <Card key={index} className={`${feature.color.split(' ')[0]} hover:shadow-lg transition-shadow`}>
              <CardBody className="p-6 text-center">
                <feature.icon className={`w-12 h-12 mx-auto mb-4 ${feature.color.split(' ')[1]}`} />
                <h4 className="text-xl font-semibold mb-2">{feature.title}</h4>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </CardBody>
            </Card>
          ))}
        </div>
      </section>

      {/* Learning Methodology Section */}
      <section className="bg-gradient-to-r from-blue-50 to-indigo-50 py-20">
        <div className="mx-auto max-w-6xl px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Metodologi Pembelajaran AI</h2>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {learningMethods.map((method, index) => (
              <Card key={index} className="bg-white">
                <CardBody className="p-6">
                  <Brain02Icon className="w-12 h-12 text-blue-600 mb-4" />
                  <h3 className="text-xl font-semibold mb-4">{method.title}</h3>
                  <p className="text-gray-600 mb-6">{method.description}</p>
                  <div className="space-y-4">
                    {method.phases.map((phase, phaseIndex) => (
                      <div key={phaseIndex}>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm font-medium">{phase.name}</span>
                        </div>
                        <Progress 
                          value={phase.progress}
                          color="primary"
                          className="h-2"
                        />
                      </div>
                    ))}
                  </div>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Study Resources Section */}
      <section className="bg-white py-20">
        <div className="w-full max-w-6xl px-6 mx-auto">
          <h2 className="text-3xl font-bold text-center mb-4">Sumber Belajar Komprehensif</h2>
          <p className="text-gray-600 text-center mb-12 max-w-3xl mx-auto">
            Akses ke berbagai materi pembelajaran, latihan soal, dan tools canggih untuk mendukung persiapan ujian kamu
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {studyResources.map((resource, index) => (
              <Card key={index} className={`${resource.color.split(' ')[0]} hover:shadow-lg transition-shadow h-[220px]`}>
                <CardBody className="p-6 flex flex-col items-center justify-center text-center">
                  <resource.icon className={`w-12 h-12 mb-4 ${resource.color.split(' ')[1]}`} />
                  <h4 className="font-semibold mb-2 text-xl">{resource.title}</h4>
                  <p className="text-gray-600 text-sm">{resource.description}</p>
                </CardBody>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Founders Section */}
      <section className="bg-gradient-to-r from-purple-200 to-indigo-200 py-16">
        <div className="mx-auto max-w-6xl px-6">
          <h2 className="text-4xl font-bold text-center mb-4">
            Dibangun oleh yang terbaik{' '}
            <span className="inline-block animate-pulse">⭐</span>
          </h2>
          <p className="text-gray-600 text-center mb-12">
            Tim founder berpengalaman yang berkomitmen menghadirkan revolusi pendidikan melalui teknologi AI
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {founders.map((founder) => (
              <Founder
                key={founder.name}
                {...founder}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Statistics Section */}
      <section className="bg-white py-20">
        <div className="mx-auto max-w-6xl px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <h3 className="text-3xl font-bold text-blue-600 mb-2">5.3K+</h3>
              <p className="text-gray-600">Pengguna Aktif</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold text-green-600 mb-2">4+</h3>
              <p className="text-gray-600">Jenis Ujian</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold text-purple-600 mb-2">10K+</h3>
              <p className="text-gray-600">Soal Latihan</p>
            </div>
            <div>
              <h3 className="text-3xl font-bold text-orange-600 mb-2">90%</h3>
              <p className="text-gray-600">Tingkat Kepuasan</p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 py-20">
        <div className="mx-auto max-w-6xl px-6 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Siap untuk memulai perjalanan menuju kesuksesan?
          </h2>
          <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
            Bergabung dengan ribuan pengguna yang telah merasakan manfaat pembelajaran dengan AI. 
            Mulai persiapan ujian kamu hari ini dan rasakan perbedaannya.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Button 
              color="default"
              variant="solid"
              className="bg-white text-blue-600 hover:bg-gray-100"
              onPress={() => router.push("/available-exams")}
              endContent={<ArrowRight02Icon className="h-4 w-4" />}
            >
              Mulai Belajar Gratis
            </Button>
            <Button 
              color="default"
              variant="bordered"
              className="border-white text-white hover:bg-white hover:text-blue-600"
              onPress={() => router.push("/ai-interview-lpdp")}
              endContent={<Video02Icon className="h-4 w-4" />}
            >
              Coba AI Interview
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;