// mailgunMailer.ts

import FormData from "form-data";
import Mailgun, { MessagesSendResult } from "mailgun.js";

// Initialize Mailgun client
const mailgun = new Mailgun(FormData);
const mg = mailgun.client({
  username: "api",
  key: process.env.MAILGUN_API_KEY || "key-yourkeyhere",
  url: "https://api.eu.mailgun.net",
});

// Function to send email
async function sendEmail(
  from: string,
  to: string,
  subject: string,
  html: string,
): Promise<MessagesSendResult> {
  try {
    const result = await mg.messages.create(process.env.MAILGUN_DOMAIN!, {
      from,
      to: [to],
      subject,
      html,
    });

    console.log("Email sent:", result);

    return result;
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
}

export { sendEmail };
