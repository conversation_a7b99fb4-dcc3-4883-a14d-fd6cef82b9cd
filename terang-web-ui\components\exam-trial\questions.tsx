import React, { useState, useEffect, useRef, Fragment, useCallback, memo, ReactNode } from "react";
import { <PERSON>, CardHeader, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON>dalFooter } from "@heroui/react";
import DOMPurify from "dompurify";
import { NextPrev } from "../shared/buttons/next-prev";
import { FlagButton } from "../shared/buttons/flag";
import FlippableHintCard from "./flippable-card";
import { CircleArrowRight01Icon } from "hugeicons-react";
import { QuestionData, Option, Content } from "../types";
import LivesDisplay from "./lives-display";
import { getUserSubscription } from "@/app/(pricing)/subscription/actions";
import { getUserId } from "@/app/lib/actions/account/actions";
import ReactMarkdown from "react-markdown";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import remarkBreaks from "remark-breaks";
import remarkGfm from 'remark-gfm'; 
import 'katex/dist/katex.min.css';
import { Components } from "react-markdown";
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter";
import { tomorrow } from "react-syntax-highlighter/dist/esm/styles/prism";
import rehypeRaw from 'rehype-raw'

import { AdaptiveImage } from "../shared/adaptive-image";
import { 
  processHtmlAndLaTeX,
  containsLaTeX,
  processLaTeX 
} from "../shared/latex-handler";
import TextHighlighter from "../shared/text-highlighter";

// HighlightableContent component to wrap content
interface HighlightableContentProps {
  children: ReactNode;
  questionId: string;
}

const HighlightableContent = memo(({ 
  children, 
  questionId 
}: { 
  children: React.ReactNode; 
  questionId: string;
}) => {
  // Use question ID as storage key to keep highlights separate for each question
  const storageKey = `question_${questionId}`;
  
  // Add a ref to track if the component has changed questions
  const prevQuestionIdRef = useRef(questionId);
  
  useEffect(() => {
    // If question ID changed, we need to ensure clean-up of any DOM elements
    if (prevQuestionIdRef.current !== questionId) {
      // Clean up any lingering highlight elements in the DOM
      const highlightElements = document.querySelectorAll(
        `[data-highlight-question-id="${prevQuestionIdRef.current}"]`
      );
      
      highlightElements.forEach(element => {
        const parent = element.parentNode;
        if (parent) {
          while (element.firstChild) {
            parent.insertBefore(element.firstChild, element);
          }
          parent.removeChild(element);
        }
      });
      
      // Update the ref
      prevQuestionIdRef.current = questionId;
    }
    
    // Clean up function runs when component unmounts or when questionId changes
    return () => {
      // Additional cleanup can be added here if needed
    };
  }, [questionId]);
  
  // Simple approach: just pass the data attribute directly instead of using containerProps
  return (
    <TextHighlighter 
      containerClassName="highlightable-content" 
      storageKey={storageKey}
      // Add the data attribute directly to the div using the spread operator inside the component
    >
      {children}
    </TextHighlighter>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for memo
  // Only re-render if the questionId changes
  return prevProps.questionId === nextProps.questionId;
});

HighlightableContent.displayName = 'HighlightableContent';

interface ContentType {
  type: string;
  content: string;
}

interface ItemType {
  contents: ContentType[];
}

// Define proper types for the code component props
interface CodeProps {
  node?: any;
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
}

interface TableContainerProps {
  children: React.ReactNode;
}

interface QuestionProps {
  questionData: QuestionData;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  flaggedQuestions: { [key: string]: boolean };
  selectedOptions: { [key: string]: string };
  sessionId: string;
  totalQuestions: number;
  questionLoading: boolean;
  showAnswerStatus: boolean;
  showHints: boolean;
  onUpdateShowStatus: (showAnswer: boolean, showHints: boolean) => void;
  onAnswerCheck: (questionId: string, isCorrect: boolean) => void;
  onOptionClick: (questionId: string, optionId: string) => void;
  onPrevClick: () => void;
  onRestart: () => void;
  onExitExam: () => void;
  gamification?: any;
  canInteract: boolean;
  isPremium: boolean;
}

// Define proper interface for QuestionOption props
interface QuestionOptionProps {
  option: Option;
  optionIndex: number;
  optionLabels: string[];
  isSelected: boolean;
  isDisabled: boolean;
  renderOptionContent: (option: Option | undefined) => React.ReactNode;
  onClick: (optionId: string) => void;
}

// Memoized option component for better performance
const QuestionOption = memo<QuestionOptionProps>(({ 
  option,
  optionIndex,
  optionLabels,
  isSelected,
  isDisabled,
  renderOptionContent,
  onClick
}) => {
  return (
    <Card
      isPressable={!isDisabled}
      className={`w-full ${isSelected ? "bg-primary-100" : ""} ${
        isDisabled ? "opacity-70" : "hover:scale-[1.01]"
      } transition-transform duration-200`}
      shadow="sm"
      onPress={() => onClick(option.id)}
    >
      <CardBody className="p-3 py-3">
        <div className="flex items-start">
          <span className="mr-3 font-bold">{optionLabels[optionIndex]}.</span>
          <div className="flex-grow font-normal" style={{ fontSize: "20px", fontWeight: 400 }}>
            {renderOptionContent(option)}
          </div>
        </div>
      </CardBody>
    </Card>
  );
});

QuestionOption.displayName = 'QuestionOption';

export const Questions: React.FC<QuestionProps> = ({
  questionData,
  currentPage,
  setCurrentPage,
  flaggedQuestions,
  selectedOptions,
  sessionId,
  totalQuestions,
  questionLoading,
  showAnswerStatus,
  showHints,
  onUpdateShowStatus,
  onAnswerCheck,
  onOptionClick,
  onPrevClick,
  onRestart,
  onExitExam,
  gamification,
  canInteract,
  isPremium,
}) => {
  const [showResultModal, setShowResultModal] = useState(false);
  const [lastAnswerCorrect, setLastAnswerCorrect] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const navigationTimeout = useRef<NodeJS.Timeout | null>(null);
  const transitionTimeout = useRef<NodeJS.Timeout | null>(null);
  const restartTimeout = useRef<NodeJS.Timeout | null>(null);
  const [userSubscription, setUserSubscription] = useState<any>(null);
  const hintCardRef = useRef<HTMLDivElement>(null);

  // Use useCallback for event handlers to prevent re-renders
  const fetchSubscription = useCallback(async () => {
    try {
      const userId = await getUserId();
      const subscription = await getUserSubscription(userId);
      setUserSubscription(subscription);
    } catch (error) {
      console.error("Error fetching subscription:", error);
    }
  }, []);

  useEffect(() => {
    fetchSubscription();
  }, [fetchSubscription]);

  // Cleanup timeouts when component unmounts
  useEffect(() => {
    return () => {
      if (navigationTimeout.current) clearTimeout(navigationTimeout.current);
      if (transitionTimeout.current) clearTimeout(transitionTimeout.current);
      if (restartTimeout.current) clearTimeout(restartTimeout.current);
    };
  }, []);

  // Reset states when question changes
  useEffect(() => {
    setIsTransitioning(false);
    setShowResultModal(false);
  }, [questionData.id]);

  const TableContainer: React.FC<TableContainerProps> = useCallback(({ children }) => (
    <div className="max-w-full overflow-hidden my-4">
      <div className="overflow-x-auto rounded-md border border-gray-200 bg-white shadow-sm">
        {children}
      </div>
    </div>
  ), []);

  // Memoize the markdown components to prevent re-creation on each render
  const markdownComponents = React.useMemo<Components>(() => ({
    // Code blocks with syntax highlighting
    code: ({ node, inline, className, children, ...props }: CodeProps) => {
      const match = /language-(\w+)/.exec(className || '');
      return !inline && match ? (
        <div className="max-w-full overflow-hidden my-4">
          <div className="overflow-x-auto">
            <SyntaxHighlighter
              style={tomorrow}
              language={match[1]}
              PreTag="div"
              className="rounded-md"
              customStyle={{
                margin: 0,
                width: 'fit-content',
                minWidth: '100%'
              }}
              {...props}
            >
              {String(children).replace(/\n$/, '')}
            </SyntaxHighlighter>
          </div>
        </div>
      ) : (
        <code className={`${className} bg-gray-100 rounded px-1 break-words`} {...props}>
          {children}
        </code>
      );
    },
    // Headings
    h1: ({ children }) => (
      <h1 className="text-2xl font-bold mb-4 mt-6">{children}</h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-xl font-bold mb-3 mt-5">{children}</h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-lg font-bold mb-2 mt-4">{children}</h3>
    ),
    h4: ({ children }) => (
      <h4 className="text-base font-bold mb-2 mt-4">{children}</h4>
    ),
    h5: ({ children }) => (
      <h5 className="text-sm font-bold mb-2 mt-4">{children}</h5>
    ),
    h6: ({ children }) => (
      <h6 className="text-xs font-bold mb-2 mt-4">{children}</h6>
    ),
    
    // Updated list components with proper LaTeX handling
    ul: ({ children }) => (
      <ul className="mb-4 ml-4 space-y-2 list-disc last:mb-0 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
        {children}
      </ul>
    ),
    ol: ({ children }) => (
      <ol className="mb-4 ml-4 space-y-2 list-decimal last:mb-0 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
        {children}
      </ol>
    ),
    li: ({ children }) => (
      <li className="ml-4 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
        {children}
      </li>
    ),
  
    p: (props: any) => {
      const children = props.children;
      
      // Helper function to extract text and handle special cases
      const extractText = (node: any): string => {
        if (typeof node === 'string') return node;
        if (Array.isArray(node)) return node.map(extractText).join(' ');
        if (node?.props?.children) return extractText(node.props.children);
        return '';
      };
    
      // Get combined text from all children
      const text = extractText(children);
      
      // Use a more robust pattern with custom delimiters
      // Format: @video{title}(url)
      const videoPattern = /@video\{([^}]*)\}\(([^)]*)\)/;
      const match = text.match(videoPattern);
    
      if (match) {
        const [_, title, url] = match;
        return (
          <div className="my-4">
            {/* <VideoPlayer 
              src={url}
              title={title}
              className="w-full rounded-lg"
            /> */}
          </div>
        );
      }
    
      // Regular paragraph rendering
      return (
        <p className="whitespace-pre-wrap break-words mb-4 last:mb-0 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
          {children}
        </p>
      );
    },
    
    // Blockquote
    blockquote: ({ children }) => (
      <blockquote className="border-l-4 border-gray-200 pl-4 my-4 italic last:mb-0">
        {children}
      </blockquote>
    ),
    pre: ({ children }) => (
      <pre className="font-mono text-sm bg-gray-50 p-4 rounded-lg overflow-x-auto whitespace-pre my-4">
        {children}
      </pre>
    ),
    
    // Inline elements
    strong: ({ children }) => (
      <strong className="font-bold">{children}</strong>
    ),
    em: ({ children }) => (
      <em className="italic">{children}</em>
    ),
    del: ({ children }) => (
      <del className="line-through">{children}</del>
    ),
    
    // Links and images
    a: ({ href, children }) => (
      <a 
        href={href} 
        className="text-blue-600 hover:text-blue-800 underline"
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
    img: ({ src, alt }) => (
      <img 
        src={src} 
        alt={alt || "Image"} 
        className="max-w-full h-auto my-4 rounded-lg"
        loading="lazy"
      />
    ),
    
    table: ({ children }) => (
      <TableContainer>
        <table className="min-w-full divide-y divide-gray-200 font-mono text-sm">
          {children}
        </table>
      </TableContainer>
    ),
    
    thead: ({ children }) => (
      <thead className="bg-gray-50 border-b border-gray-200">
        {children}
      </thead>
    ),
    
    tbody: ({ children }) => (
      <tbody className="divide-y divide-gray-200 bg-white">
        {children}
      </tbody>
    ),
    
    tr: ({ children, className }) => (
      <tr className={`${className || ''} hover:bg-gray-50`}>
        {children}
      </tr>
    ),
    
    th: ({ children }) => (
      <th className="px-6 py-3 text-center font-medium text-gray-900 whitespace-nowrap border-x border-gray-200">
        {children}
      </th>
    ),
    
    td: ({ children }) => (
      <td className="px-6 py-3 text-center font-mono whitespace-nowrap border-x border-gray-200">
        {children}
      </td>
    ),
    
    // Horizontal rule
    hr: () => (
      <hr className="my-8 border-t border-gray-200" />
    ),
    
    // Definition lists
    dl: ({ children }) => (
      <dl className="mb-4 space-y-2">{children}</dl>
    ),
    dt: ({ children }) => (
      <dt className="font-bold">{children}</dt>
    ),
    dd: ({ children }) => (
      <dd className="ml-4">{children}</dd>
    ),
    
    // Additional inline elements
    sup: ({ children }) => (
      <sup className="text-xs align-super">{children}</sup>
    ),
    sub: ({ children }) => (
      <sub className="text-xs align-sub">{children}</sub>
    ),
    kbd: ({ children }) => (
      <kbd className="px-2 py-1.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">
        {children}
      </kbd>
    ),
    mark: ({ children }) => (
      <mark className="bg-yellow-200 px-1 rounded">
        {children}
      </mark>
    )
  }), [TableContainer]);
  
  const renderContent = useCallback((content: Content) => {
    if (content.type === "text") {
      return (
        <ReactMarkdown
          remarkPlugins={[remarkMath, remarkGfm]}
          rehypePlugins={[rehypeKatex, rehypeRaw]}
          components={markdownComponents}
        >
          {processLaTeX(content.content).replace('<br>','\n')}
        </ReactMarkdown>
      );
    }
    
    if (content.type === "media") {
      return <AdaptiveImage src={content.content} alt="Question media" />;
    }
    
    return null;
  }, [markdownComponents]);

  const renderOptionContent = useCallback((option: Option | undefined) => {
    if (!option || !option.data) {
      return null;
    }
  
    return option.data.map((item: ItemType, index: number) => (
      <Fragment key={`option-${index}`}>
        {item.contents && item.contents.map((content: ContentType, contentIndex: number) => (
          <Fragment key={`option-content-${contentIndex}`}>
            {renderContent(content)}
          </Fragment>
        ))}
      </Fragment>
    ));
  }, [renderContent]);

  // Access to explanation check - memoized
  const hasAccessToExplanation = useCallback(() => {
    if (!userSubscription) return false;
    const { tierId, paymentStatus } = userSubscription;
    const isFreeOrPending = tierId === 'free_tier_001' || paymentStatus === 'PENDING';
    return !isFreeOrPending;
  }, [userSubscription]);

  // Explanation content - memoized 
  const ExplanationContent = React.useMemo(() => {
    const hasAccess = hasAccessToExplanation();

    // Find the selected option
    const selectedOption = questionData.options.values.find(
      (option) => option.id === selectedOptions[questionData.id]
    );

    // If no selected option, return null or a fallback UI
    if (!selectedOption) {
      return null;
    }

    const explanationContent = (
      <div className="space-y-3 bg-gray-50 p-4 rounded-lg">
        <div className="space-y-2">
          <p className="font-semibold">Jawabanmu:</p>
          <div className="bg-white p-3 rounded border border-green-200">
            {renderOptionContent(selectedOption)}
          </div>
        </div>

        <div className="space-y-2">
          <p className="font-semibold">Penjelasan:</p>
          <div className="bg-white p-3 rounded border border-gray-200">
            {questionData.explanation.map((exp, idx) => (
              <React.Fragment key={idx}>
                {exp.contents.map((content, contentIdx) => (
                  <React.Fragment key={contentIdx}>
                    {renderContent(content)}
                  </React.Fragment>
                ))}
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    );

    const blurredContent = (
      <div className="space-y-3 bg-gray-50 p-4 rounded-lg relative">
        <div className="blur-sm pointer-events-none">
          {explanationContent}
        </div>
        <div className="absolute inset-0 flex items-center justify-center">
          <Button
            onClick={() => (window.location.href = "/subscription")}
            color="primary"
            className="text-center p-4 rounded-lg shadow-lg cursor-pointer transition-all"
            variant="shadow"
          >
            <p className="font-semibold text-white">
              {userSubscription?.subscription?.paymentStatus === "PENDING"
                ? "Tunggu pembayaran diverifikasi untuk melihat penjelasan lengkap!"
                : "Upgrade untuk melihat penjelasan lengkap!"}
            </p>
          </Button>
        </div>
      </div>
    );

    return hasAccess ? explanationContent : blurredContent;
  }, [questionData, selectedOptions, userSubscription, hasAccessToExplanation, renderOptionContent, renderContent]);

  // Optimized handlers with useCallback to prevent unnecessary re-renders
  const handleOptionClick = useCallback((optionId: string): void => {
    if (canInteract && !showAnswerStatus && !isTransitioning) {
      // Use requestAnimationFrame for smooth UI updates
      requestAnimationFrame(() => {
        onOptionClick(questionData.id, optionId);
      });
    }
  }, [canInteract, showAnswerStatus, isTransitioning, onOptionClick, questionData.id]);

  const handlePrev = useCallback(async (): Promise<void> => {
    if (currentPage <= 0 || !canInteract || isTransitioning) return;

    setIsTransitioning(true);
    onUpdateShowStatus(false, false);
    
    navigationTimeout.current = setTimeout(() => {
      onPrevClick();
      transitionTimeout.current = setTimeout(() => {
        setIsTransitioning(false);
      }, 300);
    }, 100);
  }, [currentPage, canInteract, isTransitioning, onUpdateShowStatus, onPrevClick]);

  const handleNext = useCallback(async (): Promise<void> => {
    if (!canInteract || isTransitioning) return;

    const selectedOption = questionData.options.values.find(
      (option) => option.id === selectedOptions[questionData.id]
    );
    
    if (!selectedOption) return;

    setIsTransitioning(true);
    const isCorrect = selectedOption.is_correct;
    setLastAnswerCorrect(isCorrect);
    
    if (isCorrect) {
      setShowResultModal(true);
      setIsTransitioning(false);
    } else {
      // For incorrect answers:
      setShowResultModal(true);
      await onAnswerCheck(questionData.id, isCorrect);
      setIsTransitioning(false);

      // Add slight delay to ensure modal is shown first
      setTimeout(() => {
        hintCardRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }, 100);
    }
  }, [canInteract, isTransitioning, questionData.options.values, selectedOptions, onAnswerCheck, questionData.id]);

  const handleModalClose = useCallback(() => {
    if (!canInteract) return;

    if (!lastAnswerCorrect) {
      // For incorrect answers, just close the modal
      setShowResultModal(false);
    } else {
      handleCorrectAnswer();
    }
  }, [canInteract, lastAnswerCorrect]);

  const handleTryAgain = useCallback(() => {
    if (!canInteract || isTransitioning) return;
    
    setIsTransitioning(true);
    setShowResultModal(false);

    // Reset both showAnswerStatus and showHints to false
    onUpdateShowStatus(false, false);
    onRestart();

    // Re-enable interaction after a delay
    restartTimeout.current = setTimeout(() => {
      setIsTransitioning(false);
    }, 300);
  }, [canInteract, isTransitioning, onUpdateShowStatus, onRestart]);

  const handleCorrectAnswer = useCallback(() => {
    if (!canInteract || isTransitioning) return;
    
    // If we're on the last question, exit the exam
    if (currentPage >= totalQuestions - 1) {
      setIsTransitioning(true);
      setShowResultModal(false);
      
      // Small delay before exiting
      navigationTimeout.current = setTimeout(() => {
        onExitExam();
        setIsTransitioning(false);
      }, 300);
      
      return;
    }
    
    // Normal flow for non-last questions
    setIsTransitioning(true);
    setShowResultModal(false);
    onUpdateShowStatus(false, false);

    navigationTimeout.current = setTimeout(() => {
      setCurrentPage(currentPage + 1);
      
      transitionTimeout.current = setTimeout(() => {
        setIsTransitioning(false);
        setLastAnswerCorrect(false);
        // Add scroll to flip card after the page transition
        hintCardRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
      }, 300);
    }, 100);
  }, [canInteract, isTransitioning, currentPage, totalQuestions, onUpdateShowStatus, setCurrentPage, onExitExam]);

  const renderMetadata = useCallback(() => {
    if (!questionData.metadata || !Array.isArray(questionData.metadata)) return null;

    const getMetadataValue = (name: string) => {
      const item = questionData.metadata.find(item => item.name === name);
      return item ? item.value : '';
    };

    const subject = getMetadataValue('subject');
    const category = getMetadataValue('category');
    const keywordsValue = getMetadataValue('keywords');
    const keywords = keywordsValue ? keywordsValue.split(';').filter(Boolean) : [];

    return (
      <div className="mb-4 space-y-3 text-base">
        <div className="flex flex-wrap gap-4 items-center">
          {subject && (
            <div className="flex items-center gap-2">
              <span className="text-gray-600 text-base">Mata Uji:</span>
              <span className="bg-blue-100 text-blue-800 px-3 py-1.5 rounded text-sm">
                {subject}
              </span>
            </div>
          )}
          {category && (
            <div className="flex items-center gap-2">
              <span className="text-gray-600 text-base">Kategori:</span>
              <span className="bg-green-100 text-green-800 px-3 py-1.5 rounded text-sm">
                {category}
              </span>
            </div>
          )}
        </div>
        {keywords.length > 0 && (
          <div className="flex items-start gap-2">
            <span className="text-gray-600 text-base">Topik:</span>
            <div className="flex-1 flex flex-wrap gap-2">
              {keywords.map((keyword, idx: number) => (
                <span 
                  key={idx}
                  className="bg-gray-100 text-gray-800 px-3 py-1.5 rounded text-sm"
                >
                  {keyword.trim()}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }, [questionData.metadata]);

  const renderInstructionContent = useCallback((questionData: QuestionData) => {
    // Check if instruction exists, is an array, and has items
    if (!questionData.instruction || !Array.isArray(questionData.instruction) || questionData.instruction.length === 0) {
      return null;
    }
  
    // Check if the instruction only contains empty content
    const hasNonEmptyContent = questionData.instruction.some((item: any) => {
      if (!item.contents || !Array.isArray(item.contents)) return false;
      
      return item.contents.some((content: any) => {
        if (typeof content !== 'object' || content === null) return false;
        if (!('content' in content) || typeof content.content !== 'string') return false;
        
        return content.content.trim() !== "";
      });
    });
  
    // Return null if all content is empty
    if (!hasNonEmptyContent) {
      return null;
    }
  
    return (
      <Card className="w-full mb-6 bg-gray-50 border border-gray-200">
        <CardBody className="py-4 px-5">
          <h4 className="text-lg font-semibold text-gray-700 mb-2">Bacalah petunjuk soal berikut.</h4>
          <div className="instruction-content">
            <HighlightableContent 
              questionId={`${questionData.id}_instruction`}
              key={`highlight-instruction-${questionData.id}`} // Add this key prop
            >
              {questionData.instruction.map((item: ItemType, index: number) => (
                <Fragment key={`instruction-${index}`}>
                  {item.contents && item.contents.map((content: ContentType, contentIndex: number) => (
                    <Fragment key={`instruction-content-${contentIndex}`}>
                      {renderContent(content)}
                    </Fragment>
                  ))}
                </Fragment>
              ))}
            </HighlightableContent>
          </div>
        </CardBody>
      </Card>
    );
  }, [renderContent]);

  // Memoize question content rendering
  const renderQuestionContent = useCallback((questionData: QuestionData) => {
    return (
      <HighlightableContent 
        questionId={questionData.id}
        key={`highlight-${questionData.id}`} // Add this key prop
      >
        {questionData.question.map((item: ItemType, index: number) => (
          <Fragment key={`question-${index}`}>
            {item.contents && item.contents.map((content: ContentType, contentIndex: number) => (
              <Fragment key={`question-content-${contentIndex}`}>
                {renderContent(content)}
              </Fragment>
            ))}
          </Fragment>
        ))}
      </HighlightableContent>
    );
  }, [renderContent]);

  const optionLabels: string[] = ["A", "B", "C", "D", "E", "F", "G"];
  const { id } = questionData;
  const selectedOptionId = selectedOptions[id];

  return (
    <>
      <Card ref={hintCardRef} className="w-full relative min-h-screen" style={{ fontFamily: "'Nunito', sans-serif", fontSize: "20px" }}>
        <CardHeader className="px-4 py-4 bg-gray-100 rounded-t-lg flex justify-center items-center sticky top-0 z-30">
          <h4 className="font-bold text-gray-800">
            <span className="font-bold text-lg">{currentPage + 1}</span> dari {totalQuestions}
          </h4>
        </CardHeader>
    
        <CardBody className="overflow-visible py-6 px-6 flex flex-col pb-32">
          {questionLoading ? (
            <div className="w-full h-64 flex items-center justify-center">
              <Spinner color="primary" size="lg" />
            </div>
          ) : (
            <>
              <div>
                {renderMetadata()}
                {renderInstructionContent(questionData)}
                <FlippableHintCard 
                  hints={showHints && questionData.hints.length > 0 
                    ? questionData.hints 
                    : ["Kami yakin kamu bisa mengerjakannya tanpa hints! Hints akan muncul kalau jawaban kamu belum tepat.", "SEMANGATTT!!!"]} 
                  question={questionData.question} 
                />
              </div>
    
              <div className="text-default-500 text-small mt-6 py-4">
                <b>Pilih jawaban yang benar</b>
              </div>
              <div className="flex flex-col space-y-2 w-full mb-16">
                {questionData.options.values.map((option, optionIndex) => {
                  const isSelected = selectedOptionId === option.id;
                  const isDisabled = !canInteract || showAnswerStatus || isTransitioning;
    
                  return (
                    <QuestionOption
                      key={option.id}
                      option={option}
                      optionIndex={optionIndex}
                      optionLabels={optionLabels}
                      isSelected={isSelected}
                      isDisabled={isDisabled}
                      renderOptionContent={renderOptionContent}
                      onClick={handleOptionClick}
                    />
                  );
                })}
              </div>
    
              {showAnswerStatus && !lastAnswerCorrect && (
                <div className="fixed left-1/2 -translate-x-1/2 bottom-32 md:bottom-36 z-50 flex flex-col items-center gap-3">
                  <div className="bg-white/90 px-4 py-2 rounded-full shadow-md animate-pulse">
                    <p className="text-sm font-medium text-gray-700 flex items-center gap-2">
                      💡 <span>Ketuk kartu soal untuk lihat hints</span>
                    </p>
                  </div>
                  <Button
                    color="primary"
                    onClick={handleTryAgain}
                    disabled={!canInteract || isTransitioning}
                    className="px-8 py-4 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105 text-lg font-semibold"
                    size="lg"
                  >
                    Coba Lagi
                  </Button>
                </div>
              )}
    
              <div className="fixed bottom-0 left-0 right-0 p-4 flex justify-center items-center bg-gradient-to-t from-white via-white to-transparent pb-6 pt-8 z-50">
                <div className="flex gap-4 justify-center">
                  {currentPage > 0 && !showAnswerStatus && (
                    <Button
                      color="primary"
                      variant="flat"
                      onClick={handlePrev}
                      disabled={!canInteract || isTransitioning}
                      className="px-6 py-3 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105"
                      size="lg"
                    >
                      Sebelumnya
                    </Button>
                  )}
                  
                  {!showAnswerStatus && (
                    <Button
                      color="primary"
                      onClick={handleNext}
                      disabled={!canInteract || isTransitioning || !selectedOptionId}
                      className="px-6 py-3 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105"
                      size="lg"
                    >
                      Periksa Jawaban
                    </Button>
                  )}
                </div>
              </div>
            </>
          )}
        </CardBody>
    
        <Modal 
          isOpen={showResultModal} 
          onClose={handleModalClose}
          size="lg"
          isDismissable={canInteract && !isTransitioning}
          hideCloseButton={false}
          scrollBehavior="inside"
          classNames={{
            backdrop: "z-[51]",
            wrapper: "z-[51]",
            base: "rounded-2xl max-h-[90vh]",
            body: "p-0",
            header: "border-b"
          }}
        >
          <ModalContent>
            {(onClose) => (
              <>
                <ModalHeader className={`${lastAnswerCorrect ? "text-green-600" : "text-red-600"} text-xl font-bold`}>
                  {lastAnswerCorrect ? "Benar! 🎉" : "Belum Tepat 😕"}
                </ModalHeader>
                <ModalBody>
                  <div className="flex flex-col h-full max-h-[70vh]">
                    <div className="flex-1 overflow-y-auto p-6">
                      {lastAnswerCorrect ? (
                        <>
                          <p className="text-lg mb-4">Hebat! Jawaban kamu benar.</p>
                          {ExplanationContent}
                        </>
                      ) : (
                        <div className="space-y-4">
                          <p className="text-lg">Jangan khawatir! Yuk coba lagi.</p>
                          {gamification && <LivesDisplay 
                            currentLives={gamification.currentLives}
                            maxLives={gamification.maxLives}
                            isPremium={isPremium}
                          />}
                        </div>
                      )}
                    </div>
    
                    <div className="sticky bottom-0 border-t bg-white p-4">
                      {lastAnswerCorrect ? (
                        <Button
                          color="success"
                          className="w-full px-8 py-4 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105 text-white font-bold"
                          onPress={handleCorrectAnswer}
                          disabled={!canInteract || isTransitioning}
                          size="lg"
                        >
                          {currentPage >= totalQuestions - 1 ? "Selesai" : (
                            <span className="flex items-center justify-center gap-2 font-bold text-white">
                              <span>Lanjut</span>
                              <CircleArrowRight01Icon className="w-5 h-5" />
                            </span>
                          )}
                        </Button>
                      ) : (
                        <Button 
                          color="primary"
                          onPress={onClose}
                          className="w-full px-8 py-4 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105"
                          size="lg"
                        >
                          Tutup
                        </Button>
                      )}
                    </div>
                  </div>
                </ModalBody>
              </>
            )}
          </ModalContent>
        </Modal>
      </Card>

      {/* CSS for TextHighlighter */}
      <style dangerouslySetInnerHTML={{
          __html: `
            .highlighted-text.highlight {
              background-color: rgba(236, 201, 75, 0.3);
              cursor: pointer;
            }
            
            .highlighted-text.note {
              background-color: rgba(72, 187, 120, 0.2);
              cursor: pointer;
              border-bottom: 1px dashed #48bb78;
            }
            
            .highlighted-text:hover {
              opacity: 0.8;
            }
            
            .option-button:hover {
              background-color: rgba(255, 255, 255, 0.1);
            }

            .text-highlighter-container {
              position: relative;
            }

            .highlightable-content {
              position: relative;
            }
          `
        }} />
    </>
  );
};