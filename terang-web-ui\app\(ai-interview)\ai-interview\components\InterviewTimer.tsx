"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { Clock, AlertTriangle } from "lucide-react";
import { 
  getInterviewTimeStatus, 
  updateInterviewSessionStatus 
} from "@/app/lib/actions/available-interviews/timer-actions";

interface InterviewTimerProps {
  interviewId: string;
  userId: string | boolean | null;
  onForceFinish: () => void;
  isActive: boolean;
}

interface TimeStatus {
  elapsedSeconds: number;
  remainingSeconds: number;
  totalSeconds: number;
  status: string;
  isNearEnd: boolean;
  shouldForceFinish: boolean;
}

export default function InterviewTimer({ 
  interviewId, 
  userId, 
  onForceFinish, 
  isActive 
}: InterviewTimerProps) {
  const [timeStatus, setTimeStatus] = useState<TimeStatus | null>(null);
  const [showWarning, setShowWarning] = useState(false);
  const [isAutoFinishing, setIsAutoFinishing] = useState(false);
  const [countdown, setCountdown] = useState(0);
  
  // Refs to prevent multiple calls and store stable references
  const hasShownWarning = useRef(false);
  const hasAutoFinished = useRef(false);
  const forceFinishInProgress = useRef(false);
  const warningTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const countdownIntervalRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const finishTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const statusUpdateTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const onForceFinishRef = useRef(onForceFinish);

  // Update the ref when onForceFinish changes
  useEffect(() => {
    onForceFinishRef.current = onForceFinish;
  }, [onForceFinish]);

  const formatTime = useCallback((seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  const handleAutoFinish = useCallback(async () => {
    if (hasAutoFinished.current || forceFinishInProgress.current) {
      console.log("Auto-finish already triggered or in progress, skipping");
      return;
    }

    // Mark as in progress immediately to prevent multiple calls
    forceFinishInProgress.current = true;
    console.log("Triggering auto-finish for interview:", interviewId);

    // Set UI state first so user sees the "finishing" indicator
    setIsAutoFinishing(true);
    hasAutoFinished.current = true;

    // Set localStorage flags
    localStorage.setItem('forcedFinish', 'true');
    localStorage.setItem('forcedFinishTimestamp', Date.now().toString());

    try {
      // Update the session status
      await updateInterviewSessionStatus(interviewId, userId, 'force_finished');
      console.log("Session status updated to force_finished");
    } catch (error) {
      console.error("Error updating session status:", error);
      // Continue with force finish even if status update fails
    }

    // Always attempt to call onForceFinish, even if status update failed
    try {
      // Add a small delay for UI feedback
      await new Promise(resolve => setTimeout(resolve, 500));
      onForceFinishRef.current();
    } catch (error) {
      console.error("Error during force finish:", error);
      // If onForceFinish fails, we need to ensure the interview ends
      // router.push(`/ai-interview-result/${interviewId}`);
    }
  }, [interviewId, userId]);

  const startAutoFinishCountdown = useCallback(() => {
    if (hasAutoFinished.current || isAutoFinishing || forceFinishInProgress.current) {
      console.log("Auto-finish already in progress, skipping countdown");
      return;
    }
    
    console.log("Starting auto-finish countdown (10 seconds)");
    setIsAutoFinishing(true);
    setCountdown(10);
    
    // Clear any existing countdown
    if (countdownIntervalRef.current) {
      clearInterval(countdownIntervalRef.current);
    }
    
    countdownIntervalRef.current = setInterval(() => {
      setCountdown(prev => {
        console.log(`Auto-finish countdown: ${prev - 1}s remaining`);
        
        if (prev <= 1) {
          // Time's up, auto-finish
          console.log("Countdown complete, triggering auto-finish");
          if (countdownIntervalRef.current) {
            clearInterval(countdownIntervalRef.current);
            countdownIntervalRef.current = undefined;
          }
          
          // Use setTimeout to ensure state updates are processed
          setTimeout(() => {
            handleAutoFinish();
          }, 0);
          
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }, [isAutoFinishing, handleAutoFinish]);

  // Fetch initial time status and then run a client-side timer
  useEffect(() => {
    if (!isActive) {
      setTimeStatus(null);
      return;
    }

    let timerInterval: NodeJS.Timeout | undefined;

    const runTimer = (initialStatus: TimeStatus) => {
      setTimeStatus(initialStatus);

      timerInterval = setInterval(() => {
        setTimeStatus(prevStatus => {
          if (!prevStatus || hasAutoFinished.current || forceFinishInProgress.current) {
            if (timerInterval) clearInterval(timerInterval);
            return prevStatus;
          }

          const newRemainingSeconds = prevStatus.remainingSeconds - 1;

          // Handle warnings and auto-finish
          if (newRemainingSeconds <= 120 && !hasShownWarning.current) {
            console.log("Showing 2-minute warning");
            hasShownWarning.current = true;
            setShowWarning(true);
            warningTimeoutRef.current = setTimeout(() => setShowWarning(false), 5000);
          }

          if (newRemainingSeconds <= 10 && !isAutoFinishing) {
            console.log("10 seconds remaining, starting auto-finish countdown");
            startAutoFinishCountdown();
          }

          if (newRemainingSeconds <= 0) {
            console.log("Time expired, emergency auto-finish");
            if (timerInterval) clearInterval(timerInterval);
            handleAutoFinish();
            return { ...prevStatus, remainingSeconds: 0 };
          }

          return {
            ...prevStatus,
            remainingSeconds: newRemainingSeconds,
            elapsedSeconds: prevStatus.elapsedSeconds + 1,
            isNearEnd: newRemainingSeconds <= 300,
            shouldForceFinish: newRemainingSeconds <= 0,
          };
        });
      }, 1000);
    };

    const fetchAndStartTimer = async () => {
      if (!userId) return;
      try {
        console.log("Fetching initial time status...");
        const status = await getInterviewTimeStatus(interviewId, userId);
        if (status) {
          console.log("Initial time status received:", status);
          // Reset states before starting
          hasShownWarning.current = false;
          hasAutoFinished.current = false;
          forceFinishInProgress.current = false;
          setIsAutoFinishing(false);
          setCountdown(0);
          setShowWarning(false);
          runTimer(status);
        }
      } catch (error) {
        console.error("Failed to fetch initial time status:", error);
      }
    };

    fetchAndStartTimer();

    return () => {
      if (timerInterval) clearInterval(timerInterval);
      if (warningTimeoutRef.current) clearTimeout(warningTimeoutRef.current);
    };
  }, [isActive, interviewId, userId, handleAutoFinish, startAutoFinishCountdown, isAutoFinishing]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (warningTimeoutRef.current) {
        clearTimeout(warningTimeoutRef.current);
      }
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
      if (finishTimeoutRef.current) {
        clearTimeout(finishTimeoutRef.current);
      }
      if (statusUpdateTimeoutRef.current) {
        clearTimeout(statusUpdateTimeoutRef.current);
      }
    };
  }, []);

  if (!timeStatus || !isActive) {
    return null;
  }

  const progressPercentage = ((timeStatus.totalSeconds - timeStatus.remainingSeconds) / timeStatus.totalSeconds) * 100;

  return (
    <div className="relative">
      {/* Warning Notification */}
      {showWarning && (
        <div className="absolute top-0 right-0 transform translate-x-full -translate-y-2 z-50">
          <div className="bg-orange-500 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 animate-pulse">
            <AlertTriangle size={16} />
            <span className="text-sm font-medium">2 minutes remaining!</span>
          </div>
        </div>
      )}

      {/* Auto-finish Countdown */}
      {isAutoFinishing && countdown > 0 && (
        <div className="absolute top-0 right-0 transform translate-x-full -translate-y-2 z-50">
          <div className="bg-red-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
            <AlertTriangle size={16} className="animate-bounce" />
            <span className="text-sm font-medium">
              Auto-finishing in {countdown}s
            </span>
          </div>
        </div>
      )}

      {/* Main Timer */}
      <div className={`bg-white shadow-sm rounded-lg px-4 py-2 transition-all duration-300 ${
        isAutoFinishing ? 'ring-2 ring-red-500 bg-red-50' : 
        timeStatus.isNearEnd ? 'ring-2 ring-orange-500 bg-orange-50' : ''
      }`}>
        <div className="flex items-center gap-3">
          <Clock 
            size={16} 
            className={`${
              isAutoFinishing ? 'text-red-600 animate-pulse' :
              timeStatus.isNearEnd ? 'text-orange-600' : 'text-gray-500'
            }`} 
          />
          <div className="flex items-center gap-4">           
            {/* Progress Bar */}
            <div className="w-20 bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-1000 ${
                  isAutoFinishing ? 'bg-red-600 animate-pulse' :
                  timeStatus.isNearEnd ? 'bg-red-500' : 
                  progressPercentage > 75 ? 'bg-orange-500' : 'bg-blue-500'
                }`}
                style={{ width: `${Math.min(progressPercentage, 100)}%` }}
              />
            </div>
            
            {/* Time Display */}
            <div className="flex items-center gap-3 text-sm">             
              <div className="text-center">
                <div className="text-xs text-gray-500 mb-1">Remaining</div>
                <div className={`font-mono font-medium transition-colors duration-300 ${
                  isAutoFinishing ? 'text-red-600 animate-pulse font-bold' :
                  timeStatus.isNearEnd ? 'text-red-600' : 
                  timeStatus.remainingSeconds < 600 ? 'text-orange-600' : 'text-gray-700'
                }`}>
                  {formatTime(Math.max(0, timeStatus.remainingSeconds))}
                </div>
              </div>
            </div>

            {/* Status Indicator */}
            {(timeStatus.isNearEnd || isAutoFinishing) && (
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  isAutoFinishing ? 'bg-red-600 animate-ping' : 
                  'bg-orange-500 animate-pulse'
                }`} />
                <span className={`text-xs font-medium ${
                  isAutoFinishing ? 'text-red-600' : 'text-orange-600'
                }`}>
                  {isAutoFinishing ? 'Finishing...' : 'Near End'}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}