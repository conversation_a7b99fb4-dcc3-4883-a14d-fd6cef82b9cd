import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, <PERSON><PERSON> } from "@heroui/react";

interface GameOverModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId: string;
  subject: string;
  onExitExam: () => Promise<void>;
}

const GameOverModal: React.FC<GameOverModalProps> = ({
  isOpen,
  onClose,
  onExitExam
}) => {
  const handleFinishExam = async () => {
    try {
      await onExitExam();
    } catch (error) {
      console.error("Error finishing exam:", error);
    }
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={onClose}
      hideCloseButton
      isDismissable={false}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <PERSON><PERSON><PERSON>
        </ModalHeader>
        <ModalBody>
          <p>
            <PERSON><PERSON><PERSON> sudah berakhir karena nyawa kamu sudah habis.
            Kamu akan diarahkan ke halaman hasil.
          </p>
        </ModalBody>
        <ModalFooter>
          <Button 
            color="primary" 
            onPress={handleFinishExam}
            className="w-full"
          >
            Lihat Hasil
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default GameOverModal;