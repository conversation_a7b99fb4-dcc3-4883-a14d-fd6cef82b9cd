// Simplified timer-actions.ts
"use server";

import crypto from "crypto";
import { auth } from '@/auth';
import { getUserId } from '@/app/lib/actions/account/actions';

const API_URL = process.env.BACKEND_BASE_URL as string;
const API_KEY = process.env.BACKEND_API_KEY as string;

// Interface aligned with the backend's JSON response
interface InterviewSession {
  id?: number;
  sessionId: string;
  userId: string;
  userEmail: string;
  userName: string;
  interviewId: string;
  category: string;
  type: string;
  status: 'active' | 'completed' | 'force_finished' | 'cancelled' | 'disconnected_timeout' | 'disconnected_grace' | 'in_progress';
  recordingUrl: string;
  transcriptUrl: string;
  duration: string;
  durationSeconds: number;
  startTime: number; // Converted to timestamp for frontend logic
  endTime?: string | null;
  createdAt: string;
  updatedAt: string;
  // Frontend-specific fields
  roomName: string;
  disconnectedAt?: number;
  language: string;
}

// Helper to build API URLs
const api = {
  createSession: () => `${API_URL}/v0/ai-interview/sessions`,
  getSession: (sessionId: string) => `${API_URL}/v0/ai-interview/sessions/${sessionId}`,
  updateSession: (sessionId: string) => `${API_URL}/v0/ai-interview/sessions/${sessionId}`,
  getActiveSession: (userId: string, interviewId: string) => `${API_URL}/v0/ai-interview/users/${userId}/interviews/${interviewId}/active-session`,
};

// Centralized fetch function
async function fetchAPI(url: string, options: RequestInit = {}): Promise<any> {
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,
      ...options.headers,
    },
    cache: 'no-store',
  });

  if (!response.ok) {
    const errorBody = await response.text();
    console.error(`API Error: ${response.status} ${response.statusText}`, { url, options, errorBody });
    throw new Error(`HTTP error! status: ${response.status}, body: ${errorBody}`);
  }
  
  // For 'create' endpoint, we also need the status to check if it's a new or existing session
  if (options.method === 'POST' && url.includes('/sessions')) {
    return { data: await response.json(), status: response.status };
  }

  return response.json();
}

// Maps backend session to frontend session, adding roomName and converting startTime
function mapBackendToFrontendSession(backendSession: any): InterviewSession {
  if (!backendSession) return backendSession;
  return {
    ...backendSession,
    userId: backendSession.userId ?? '',
    startTime: new Date(backendSession.startTime).getTime(), // Convert to number for frontend logic
    roomName: generateRoomName(backendSession.sessionId),
    durationInSeconds: backendSession.durationSeconds,
  };
}

export async function startInterviewSession(
  interviewId: string,
  userId: string | boolean | null,
  duration: string,
  existingSessionId?: string,
  userEmail?: string,
  userName?: string,
  language: string = 'en'
): Promise<{ success: boolean; sessionId: string | null; roomName: string | null; isExisting: boolean }> {
  try {
    if (!userId || typeof userId !== 'string') {
      throw new Error("Valid User ID is required.");
    }

    // If an existingSessionId is provided, try to fetch and validate it first.
    if (existingSessionId) {
      const existingSession = await getInterviewSessionById(existingSessionId);
      if (existingSession && ['active', 'disconnected_grace'].includes(existingSession.status)) {
        console.log(`Using provided existing session: ${existingSessionId}`);
        if (existingSession.status === 'disconnected_grace') {
          await restoreSessionFromGracePeriodById(existingSessionId);
        }
        return {
          success: true,
          sessionId: existingSession.sessionId,
          roomName: existingSession.roomName,
          isExisting: true,
        };
      }
    }

    // The backend's POST /sessions endpoint now handles both creation and retrieval of active sessions.
    const { data: session, status } = await createInterviewSession(interviewId, userId, duration, userEmail, userName, language);
    
    const isExisting = status === 200; // 200 OK for existing, 201 Created for new
    
    console.log(`Session processed. New session: ${!isExisting}. Session ID: ${session.sessionId}`);

    return {
      success: true,
      sessionId: session.sessionId,
      roomName: generateRoomName(session.sessionId),
      isExisting,
    };
  } catch (error) {
    console.error("Failed to start interview session:", error);
    return { success: false, sessionId: null, roomName: null, isExisting: false };
  }
}

export async function restoreSessionFromGracePeriod(
  interviewId: string,
  userId: string | boolean | null
): Promise<boolean> {
    if (!userId || typeof userId !== 'string') return false;
  try {
    const session = await getInterviewSession(interviewId, userId);
    if (session && session.status === 'disconnected_grace') {
      return restoreSessionFromGracePeriodById(session.sessionId);
    }
    return false;
  } catch (error) {
    console.error("Failed to restore session from grace period:", error);
    return false;
  }
}

export async function restoreSessionFromGracePeriodById(
  sessionId: string
): Promise<boolean> {
  try {
    const session = await getInterviewSessionById(sessionId);
    if (!session || session.status !== 'disconnected_grace') {
      console.log(`Session ${sessionId} cannot be restored, status: ${session?.status}`);
      return false;
    }

    // Check grace period expiry (5 minutes)
    const graceElapsed = session.disconnectedAt ? (Date.now() - session.disconnectedAt) : 0;
    if (graceElapsed > 5 * 60 * 1000) {
      console.log(`Session ${sessionId} grace period expired.`);
      await updateInterviewSessionStatus(session.interviewId, session.userId, 'disconnected_timeout');
      return false;
    }

    await fetchAPI(api.updateSession(sessionId), {
      method: 'PUT',
      body: JSON.stringify({ status: 'active' }),
    });
    console.log(`Session ${sessionId} restored from grace period.`);
    return true;
  } catch (error) {
    console.error(`Failed to restore session ${sessionId} from grace period by ID:`, error);
    return false;
  }
}

export async function updateInterviewSessionDisconnected(
  interviewId: string,
  userId: string | boolean | null,
  isGracePeriod: boolean = true
): Promise<boolean> {
  if (!userId || typeof userId !== 'string') return false;
  try {
    const session = await getInterviewSession(interviewId, userId);
    if (!session) {
      console.log("No active session found to update for disconnection.");
      return false;
    }

    const newStatus = isGracePeriod ? 'disconnected_grace' : 'disconnected_timeout';
    const updates: { status: string; endTime?: string } = { status: newStatus };
    if (!isGracePeriod) {
        updates.endTime = new Date().toISOString();
    }

    await fetchAPI(api.updateSession(session.sessionId), {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
    console.log(`Session ${session.sessionId} marked as ${newStatus}.`);
    return true;
  } catch (error) {
    console.error("Failed to update interview session disconnection status:", error);
    return false;
  }
}

// Helper function to convert duration string to seconds
function durationToSeconds(duration: string): number {
  const parts = duration.split(':').map(Number);
  
  if (parts.length === 3) {
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  } else if (parts.length === 2) {
    return parts[0] * 60 + parts[1];
  }
  
  return 3600; // Default 1 hour if parsing fails
}

// Generate unique session ID using crypto
function generateSessionId(interviewId: string, userId: string | boolean | null): string {
  const timestamp = Date.now();
  const randomBytes = crypto.randomBytes(4).toString('hex');
  return `interview_${interviewId}_${userId}_${timestamp}_${randomBytes}`;
}

// Generate consistent room name from session ID
function generateRoomName(sessionId: string): string {
  return `room_${sessionId}`;
}

// This function now directly calls the backend, which handles finding an existing session or creating a new one.
export async function createInterviewSession(
  interviewId: string,
  userId: string,
  duration: string,
  userEmail?: string,
  userName?: string,
  language: string = 'en'
): Promise<{ data: InterviewSession, status: number }> {
  const authSession = await auth();
  const finalUserId = userId || authSession?.user?.id;
  if (!finalUserId) {
    throw new Error("User is not authenticated.");
  }

  const finalUserEmail = userEmail || authSession?.user?.email || '';
  const finalUserName = userName || authSession?.user?.name || '';
  const generatedSessionId = generateSessionId(interviewId, finalUserId);

  const payload = {
    sessionId: generatedSessionId,
    interviewId,
    userId: finalUserId,
    userEmail: finalUserEmail,
    userName: finalUserName,
    status: 'active',
    duration,
    durationSeconds: durationToSeconds(duration),
    language,
  };

  console.log("Creating or getting session with payload:", payload);
  const result = await fetchAPI(api.createSession(), {
    method: 'POST',
    body: JSON.stringify(payload),
  });
  console.log(`Backend returned with status ${result.status}`);
  return result;
}

export async function getInterviewSessionById(sessionId: string): Promise<InterviewSession | null> {
  try {
    const backendSession = await fetchAPI(api.getSession(sessionId));
    return mapBackendToFrontendSession(backendSession);
  } catch (error) {
    // If session not found (404), return null as expected. Otherwise, rethrow.
    if (error instanceof Error && error.message.includes('404')) {
        console.log(`Session not found by ID: ${sessionId}`);
        return null;
    }
    console.error(`Failed to get interview session by ID ${sessionId}:`, error);
    return null;
  }
}

export async function getOrCreateInterviewSessionId(
  interviewId: string,
  userId: string | boolean | null,
  duration: string = "01:00:00"
): Promise<string> {
  if (!userId || typeof userId !== 'string') {
      throw new Error("A valid user ID is required.");
  }
  try {
    const { data: session } = await createInterviewSession(interviewId, userId, duration);
    return session.sessionId;
  } catch (error) {
    console.error("Failed to get/create session ID via API:", error);
    // Fallback to local generation if API fails, though this is not ideal
    return generateSessionId(interviewId, userId);
  }
}

export async function getInterviewSession(
  interviewId: string,
  userId: string | boolean | null
): Promise<InterviewSession | null> {
  if (!userId || typeof userId !== 'string') return null;
  try {
    const backendSession = await fetchAPI(api.getActiveSession(userId, interviewId));
    return mapBackendToFrontendSession(backendSession);
  } catch (error) {
    if (error instanceof Error && error.message.includes('404')) {
        console.log(`No active session found for user ${userId} and interview ${interviewId}`);
        return null;
    }
    console.error("Failed to get active interview session:", error);
    return null;
  }
}

export async function updateInterviewSessionStatus(
  interviewId: string,
  userId: string | boolean | null,
  status: 'completed' | 'force_finished' | 'cancelled' | 'disconnected_timeout' | 'disconnected_grace' | 'active'
): Promise<boolean> {
  if (!userId || typeof userId !== 'string') return false;
  try {
    const session = await getInterviewSession(interviewId, userId);
    if (!session) {
      console.log("No active session found to update status.");
      return false;
    }

    const updates: { status: string; endTime?: string } = { status };
    if (['completed', 'force_finished', 'cancelled', 'disconnected_timeout'].includes(status)) {
      updates.endTime = new Date().toISOString();
    }

    await fetchAPI(api.updateSession(session.sessionId), {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
    console.log(`Successfully updated session ${session.sessionId} to status ${status}`);
    return true;
  } catch (error) {
    console.error("Failed to update interview session status:", error);
    return false;
  }
}

// Cancel session
export async function cancelInterviewSession(
  interviewId: string,
  userId: string | boolean | null,
  reason: string = 'user_cancelled'
): Promise<boolean> {
  return await updateInterviewSessionStatus(interviewId, userId, 'cancelled');
}

// Get elapsed time and remaining time
export async function getInterviewTimeStatus(
  interviewId: string,
  userId: string | boolean | null
): Promise<{
  elapsedSeconds: number;
  remainingSeconds: number;
  totalSeconds: number;
  status: string;
  isNearEnd: boolean;
  shouldForceFinish: boolean;
  sessionId?: string;
  roomName?: string;
} | null> {
  const session = await getInterviewSession(interviewId, userId);
  if (!session) {
    return null;
  }

  // startTime is already a number (timestamp) from mapping
  const startTimeMs = session.startTime;
  const currentTime = Date.now();
  const elapsedSeconds = Math.floor((currentTime - startTimeMs) / 1000);
  const remainingSeconds = Math.max(0, session.durationSeconds - elapsedSeconds);

  const isNearEnd = remainingSeconds <= 300; // 5 minutes
  const shouldForceFinish = remainingSeconds <= 0;

  return {
    elapsedSeconds,
    remainingSeconds,
    totalSeconds: session.durationSeconds,
    status: session.status,
    isNearEnd,
    shouldForceFinish,
    sessionId: session.sessionId,
    roomName: session.roomName,
  };
}

export async function checkActiveSession(
  interviewId: string,
  userId: string | boolean | null
): Promise<{
  hasActiveSession: boolean;
  sessionId?: string;
  roomName?: string;
  isInGracePeriod?: boolean;
  timeStatus?: {
    elapsedSeconds: number;
    remainingSeconds: number;
    totalSeconds: number;
    status: string;
    isNearEnd: boolean;
    shouldForceFinish: boolean;
    disconnectedAt?: number;
    graceTimeRemaining?: number;
  };
} | null> {
  if (!userId || typeof userId !== 'string') return { hasActiveSession: false };

  try {
    const session = await getInterviewSession(interviewId, userId);

    if (!session || !['active', 'disconnected_grace'].includes(session.status)) {
      return { hasActiveSession: false };
    }

    const startTimeMs = session.startTime;
    const currentTime = Date.now();
    const elapsedSeconds = Math.floor((currentTime - startTimeMs) / 1000);

    // Check for dormant or expired sessions
    const isDormant = (currentTime - startTimeMs) > (24 * 60 * 60 * 1000);
    const isExpired = elapsedSeconds >= session.durationSeconds;

    if (isDormant || isExpired) {
      console.log(`Session ${session.sessionId} is ${isDormant ? 'dormant' : 'expired'}, force finishing.`);
      await forceFinishSessionById(session.sessionId);
      return { hasActiveSession: false };
    }

    const remainingSeconds = Math.max(0, session.durationSeconds - elapsedSeconds);
    const isNearEnd = remainingSeconds <= 300;
    const shouldForceFinish = remainingSeconds <= 0;
    
    let graceTimeRemaining = 0;
    if (session.status === 'disconnected_grace' && session.disconnectedAt) {
      const graceElapsed = currentTime - session.disconnectedAt;
      graceTimeRemaining = Math.max(0, (5 * 60 * 1000) - graceElapsed);
    }

    return {
      hasActiveSession: true,
      sessionId: session.sessionId,
      roomName: session.roomName,
      isInGracePeriod: session.status === 'disconnected_grace',
      timeStatus: {
        elapsedSeconds,
        remainingSeconds,
        totalSeconds: session.durationSeconds,
        status: session.status,
        isNearEnd,
        shouldForceFinish,
        disconnectedAt: session.disconnectedAt,
        graceTimeRemaining,
      },
    };
  } catch (error) {
    console.error("Failed to check active session:", error);
    return { hasActiveSession: false };
  }
}

export async function forceFinishSessionById(sessionId: string): Promise<boolean> {
  try {
    const session = await getInterviewSessionById(sessionId);
    if (!session) {
      console.log(`Session not found for force finish: ${sessionId}`);
      return false;
    }
    if (['completed', 'force_finished', 'cancelled', 'disconnected_timeout'].includes(session.status)) {
      console.log(`Session ${sessionId} already in a terminal state: ${session.status}`);
      return true;
    }

    await fetchAPI(api.updateSession(sessionId), {
      method: 'PUT',
      body: JSON.stringify({
        status: 'force_finished',
        endTime: new Date().toISOString(),
      }),
    });
    console.log(`Successfully force-finished session ${sessionId}`);
    return true;
  } catch (error) {
    console.error(`Error force-finishing session ${sessionId}:`, error);
    return false;
  }
}

// Get session info for connection details
export async function getSessionInfoForConnection(
  interviewId: string,
  userId: string | boolean | null
): Promise<{ sessionId: string; roomName: string } | null> {
  try {
    const session = await getInterviewSession(interviewId, userId);
    
    if (!session) {
      return null;
    }
    
    return { sessionId: session.sessionId, roomName: session.roomName };
  } catch (error) {
    console.error("Failed to get session info for connection:", error);
    return null;
  }
}
