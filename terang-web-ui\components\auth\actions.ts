import { addAIChatTokens } from "@/app/(pricing)/subscription/actions";
import { getUserId } from "@/app/lib/actions/account/actions";

type TokenResponse = {
    ok: boolean;
    message: string;
    data?: {
      tokensAdded: number;
      newBalance: number;
    };
};

type TokenMetadata = {
    source: string;
    description: string;
    timestamp: string;
    customData?: Record<string, any>;
}

interface AddAITokenOptions {
    tokenAmount?: number;
    metadata?: Partial<TokenMetadata>;
    userId?: string | boolean | null; // Add userId parameter to allow direct user ID input
}

const DEFAULT_TOKEN_AMOUNT = 2000000; // 2 million tokens
const DEFAULT_METADATA = {
    source: 'initial_registration_token',
    description: 'Initial Registration Token',
    timestamp: new Date().toISOString(),
};

export async function addAIToken(options: AddAITokenOptions = {}): Promise<TokenResponse> {
    try {
      // Try to get userId from the options first, then fall back to getUserId()
      let userId = options.userId;
      
      if (!userId) {
        try {
          userId = await getUserId();
        } catch (error) {
          console.error('Error getting user ID from session:', error);
          // Continue with null userId, we'll check it below
        }
      }
      
      if (!userId) {
        return {
          ok: false,
          message: "User ID not found. Please try logging in again.",
        };
      }

      const tokenAmount = options.tokenAmount ?? DEFAULT_TOKEN_AMOUNT;
      const metadata: TokenMetadata = {
        ...DEFAULT_METADATA,
        ...options.metadata,
        timestamp: options.metadata?.timestamp ?? new Date().toISOString(),
      };
  
      const tokenResult = await addAIChatTokens(
        userId,
        tokenAmount,
        JSON.stringify(metadata)
      );
  
      if (!tokenResult || !tokenResult.ok) {
        return {
          ok: false,
          message: tokenResult?.message || "Failed to add initial AI tokens",
        };
      }
  
      return {
        ok: true,
        message: "AI tokens added successfully",
        data: {
          tokensAdded: tokenAmount,
          newBalance: tokenAmount,
        },
      };
  
    } catch (error) {
      console.error('Error in addAIToken:', error);
      return {
        ok: false,
        message: error instanceof Error ? error.message : "An unexpected error occurred while adding AI tokens",
      };
    }
}