"use client";

import React, { useState, useEffect, Key } from "react";
import dynamic from "next/dynamic";
import { Table, TableHeader, TableColumn, TableBody, TableRow, TableCell, Chip, Popover, PopoverTrigger, <PERSON>overContent, <PERSON><PERSON>, Card, CardBody } from "@heroui/react";
import Image from "next/image";
import WelcomeBanner from "./wa-card";
import Link from "next/link";
import { updateExamSessionStatus } from "@/app/(dashboard)/dashboard/action";
import {useRouter} from 'next/navigation'
import { fetchExamConfig } from "./action";

// Import icons (unchanged)
const ChartAverageIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width={24} height={24} color={"#000000"} fill={"none"} {...props}>
    <path d="M21 21H10C6.70017 21 5.05025 21 4.02513 19.9749C3 18.9497 3 17.2998 3 14V3" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
    <path d="M6 12H6.00898M8.9982 12H9.00718M11.9964 12H12.0054M14.9946 12H15.0036M17.9928 12H18.0018M20.991 12H21" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path opacity="0.4" d="M6 7C6.67348 5.87847 7.58712 5 8.99282 5C14.9359 5 11.5954 17 17.9819 17C19.3976 17 20.3057 16.1157 21 15" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
  </svg>
);

const WorkHistoryIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width={24} height={24} color={"#000000"} fill={"none"} {...props}>
    <path fillRule="evenodd" clipRule="evenodd" d="M17.5 12.25C14.6005 12.25 12.25 14.6005 12.25 17.5C12.25 20.3995 14.6005 22.75 17.5 22.75C20.3995 22.75 22.75 20.3995 22.75 17.5C22.75 14.6005 20.3995 12.25 17.5 12.25ZM18.25 15.6992C18.25 15.285 17.9142 14.9492 17.5 14.9492C17.0858 14.9492 16.75 15.285 16.75 15.6992V17.9492C16.75 18.2 16.8753 18.4342 17.084 18.5733L18.434 19.4733C18.7786 19.703 19.2443 19.6099 19.474 19.2652C19.7038 18.9206 19.6107 18.4549 19.266 18.2252L18.25 17.5478V15.6992Z" fill="currentColor" />
    <path opacity="0.4" d="M13.4653 4.75H9.54836C7.80421 4.74998 6.41746 4.74997 5.33103 4.89881C4.20972 5.05243 3.29737 5.37711 2.5785 6.10965C1.86155 6.84022 1.54553 7.76413 1.39567 8.89997C1.24997 10.0042 1.24999 11.5256 1.25 13.3053C1.24999 15.0851 1.24997 16.4958 1.39567 17.6C1.54553 18.7359 1.86155 19.6598 2.5785 20.3904C3.29737 21.1229 4.20972 21.4476 5.33103 21.6012C6.41745 21.75 7.80415 21.75 9.54827 21.75H12.2557C11.3142 20.5897 10.75 19.1107 10.75 17.5C10.75 13.7721 13.7721 10.75 17.5 10.75C19.1107 10.75 20.5897 11.3142 21.75 12.2557V10.9923L21.7498 10.9845C21.705 8.80913 21.5271 7.22233 20.4352 6.10965C19.7163 5.37711 18.8039 5.05243 17.6826 4.89881C16.5962 4.74997 15.2095 4.74998 13.4653 4.75Z" fill="currentColor" />
    <path d="M13.4896 1.30636C12.9893 1.24994 12.2026 1.24997 11.5006 1.25C10.7986 1.24997 10.0119 1.24994 9.51156 1.30636C8.97215 1.36718 8.46296 1.50167 7.9999 1.8313C7.53975 2.15886 7.25542 2.58653 7.03115 3.06267C6.82087 3.50911 6.62996 4.07485 6.40699 4.73558L6.38477 4.80145C6.98672 4.76671 7.6599 4.75542 8.41038 4.75176C8.54926 4.35488 8.67729 4.00985 8.77292 3.80681C8.92551 3.48287 9.04052 3.36036 9.14281 3.28755C9.24218 3.21681 9.38775 3.15111 9.73601 3.11184C10.1109 3.06957 10.7308 3.06809 11.5006 3.06809C12.2705 3.06809 12.8904 3.06957 13.2652 3.11184C13.6135 3.15111 13.759 3.21681 13.8584 3.28755C13.9607 3.36036 14.0757 3.48287 14.2283 3.80681C14.3239 4.00984 14.4519 4.35484 14.5908 4.75169C15.341 4.75526 16.0141 4.76632 16.6162 4.80065L16.5942 4.73559C16.3713 4.07486 16.1803 3.50911 15.9701 3.06267C15.7458 2.58653 15.4615 2.15886 15.0013 1.8313C14.5382 1.50167 14.0291 1.36718 13.4896 1.30636Z" fill="currentColor" />
  </svg>
);

const ShoppingBag01Icon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width={24} height={24} color={"#000000"} fill={"none"} {...props}>
    <path opacity="0.4" d="M5.39181 7.85346C6.58783 6.99457 8.13833 6.75 10.1184 6.75H13.8816C15.951 6.75 17.5149 7.03438 18.6964 7.91803C19.8808 8.80389 20.5425 10.1886 21.0708 12.0305L21.0831 12.0731C21.557 13.7255 21.9291 15.0227 22.1148 16.0691C22.3034 17.1319 22.3214 18.0413 21.996 18.8829C21.6307 19.8277 20.9838 20.6422 20.1422 21.2265C19.3177 21.7989 18.0369 22.1697 16.644 22.4037C15.2229 22.6425 13.5806 22.7551 11.9529 22.7498C10.3252 22.7446 8.69116 22.6213 7.28633 22.38C5.9116 22.1439 4.65266 21.7783 3.85779 21.2265C3.01618 20.6422 2.36936 19.8277 2.004 18.8829C1.67859 18.0413 1.69665 17.1319 1.88524 16.0691C2.07092 15.0227 2.44298 13.7256 2.91694 12.0732L2.91694 12.0732L2.92918 12.0305C3.48157 10.1046 4.18716 8.71854 5.39181 7.85346Z" fill="currentColor" />
    <path fillRule="evenodd" clipRule="evenodd" d="M10 11C10 10.4477 10.4477 10 11 10H13C13.5523 10 14 10.4477 14 11C14 11.5523 13.5523 12 13 12H11C10.4477 12 10 11.5523 10 11Z" fill="currentColor" />
    <path d="M8.5 6.61364C8.5 4.78473 10.0378 3.25 12 3.25C13.9622 3.25 15.5 4.78473 15.5 6.61364V6.82342C16.2447 6.89945 16.9084 7.03945 17.5 7.26584V6.61364C17.5 3.6226 15.0083 1.25 12 1.25C8.99166 1.25 6.5 3.6226 6.5 6.61364V7.26121C7.09681 7.03189 7.76155 6.89461 8.5 6.82095V6.61364Z" fill="currentColor" />
  </svg>
);

// Define chart-compatible ExamProgressData interface (matching what's in steam.tsx)
interface ChartExamProgressData {
  sessionId: string;
  startTime: string;
  examType: string; // This is the required field causing the type error
  subjectScores: {
    [key: string]: number;
  };
}

const Chart = dynamic(
  () => import('../charts/steam'),
  { 
    ssr: false,
    loading: () => (
      <div className="w-full h-[400px] flex items-center justify-center">
        Loading chart...
      </div>
    )
  }
);

// Updated interface for the new backend API response format
interface SubjectScore {
  score: number;
  correct: number;
  total: number;
}

interface ExamSessionData {
  sessionId: string;
  uniqueSessionId: string;
  userId: string | boolean | null;
  username: string;
  email: string;
  examId: string;
  examName: string;
  examSubname: string;
  examType: string;
  sessionType: string;
  sessionStatus: string;
  startTime: string;
  endTime?: string;
  totalQuestions: number | null;
  correctAnswers: number | null;
  score: number | null;
  accuracy: number | null;
  categories: string;
  tags: string;
  subject: string;
  // New format: subjectScores is now an object with subject keys
  subjectScores: Record<string, SubjectScore>;
  [key: string]: any;
}

interface OrderHistoryData {
  orderId: string;
  orderDate: string;
  username: string;
  examName: string;
  quantity: number;
  orderStatus: string;
  invoiceNumber?: string;
  invoiceStatus?: string;
  dueDate?: string | null;
  paymentAmount?: number | null;
  paymentStatus?: string | null;
  paymentMethod?: string | null;
}

// Local ExamProgressData that matches your API response
interface ExamProgressData {
  sessionId: string;
  startTime: string;
  subjectScores: {
    [key: string]: number;
  };
}

interface ContentProps {
  examSessions?: ExamSessionData[] | { sessions?: ExamSessionData[] };
  examProgress?: { progress: ExamProgressData[] };
  orderHistory?: { orders: OrderHistoryData[] };
  examConfig?: any; // Configuration from backend
  session: any;
}

const calculateElapsedTime = (startTime: string, endTime?: string): string => {
  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : new Date();
  const elapsedMs = end.getTime() - start.getTime();
  const hours = Math.floor(elapsedMs / (1000 * 60 * 60));
  const minutes = Math.floor((elapsedMs % (1000 * 60 * 60)) / (1000 * 60));
  return `${hours}h ${minutes}m`;
};

// Helper function to format the score display
const formatScore = async (session: ExamSessionData): Promise<string> => {
  if (!session) return '-';
  
  // Check if it's UTBK type exam
  const isUtbk = session.examType?.toLowerCase() === 'utbk' || 
                session.categories?.toLowerCase().includes('utbk');
  
  // For UTBK, use the specialized formatter
  if (isUtbk) {
    return formatUtbkTotalScore(session);
  }
  
  // Normal case handling for non-UTBK exams
  if (session.correctAnswers !== null && 
      session.totalQuestions !== null && 
      session.score !== null &&
      session.totalQuestions > 0) {
    // For CPNS/LPDP, the score is calculated as correct * 5
    return `${session.correctAnswers}/${session.totalQuestions}\n(${session.correctAnswers*5} pts)`;
  }
  
  // If we have subjectScores, use those
  if (session.subjectScores && Object.keys(session.subjectScores).length > 0) {
    if (session.sessionType === 'PRACTICE' && session.subject) {
      // For practice sessions with a specific subject, find the matching subject
      const subjectKey = Object.keys(session.subjectScores).find(key => 
        key === session.subject || 
        session.subject.toLowerCase().includes(key.toLowerCase())
      );
      
      if (subjectKey) {
        const subjectScore = session.subjectScores[subjectKey];
        return formatScoreHelper(
          subjectScore.correct,
          subjectScore.total,
          subjectScore.score,
          isUtbk
        );
      }
    }
    
    // For exams or when no specific subject, calculate total
    let totalCorrect = 0;
    let totalQuestions = 0;
    
    Object.values(session.subjectScores).forEach(score => {
      totalCorrect += score.correct || 0;
      totalQuestions += score.total || 0;
    });
    
    if (totalQuestions > 0) {
      // Calculate total score based on exam type
      const totalScore = isUtbk 
      ? Math.ceil((totalCorrect / totalQuestions) * 1000) // Changed from Math.round to Math.ceil
      : totalCorrect * 5;
      
      return `${totalCorrect}/${totalQuestions}\n(${totalScore} pts)`;
    }
  }
  
  // Try to get exam configuration from backend and calculate
  try {
    if (session.categories) {
      const examConfig = await fetchExamConfig(session.categories);
      
      if (examConfig && examConfig.subjects) {
        // Use configuration to process exam data
        if (isUtbk) {
          return formatUtbkTotalScore(session);
        }
      }
    }
  } catch (error) {
    console.error('Error fetching exam config or calculating score:', error);
  }
  
  return '-';
};

// Synchronous version for immediate rendering
const formatScoreSync = (session: ExamSessionData): string => {
  if (!session) return '-';
  
  // Check if it's UTBK type exam
  const isUtbk = session.examType?.toLowerCase() === 'utbk' || 
                session.categories?.toLowerCase().includes('utbk');
  
  // For UTBK, use the specialized formatter
  if (isUtbk) {
    return formatUtbkTotalScore(session);
  }
  
  // Normal case handling for non-UTBK exams
  if (session.correctAnswers !== null && 
      session.totalQuestions !== null && 
      session.score !== null &&
      session.totalQuestions > 0) {
    // For CPNS/LPDP, the score is calculated as correct * 5
    return `${session.correctAnswers}/${session.totalQuestions}\n(${session.correctAnswers*5} pts)`;
  }
  
  // If we have subjectScores, use those
  if (session.subjectScores && Object.keys(session.subjectScores).length > 0) {
    if (session.sessionType === 'PRACTICE' && session.subject) {
      // For practice sessions with a specific subject, find the matching subject
      const subjectKey = Object.keys(session.subjectScores).find(key => 
        key === session.subject || 
        session.subject.toLowerCase().includes(key.toLowerCase())
      );
      
      if (subjectKey) {
        const subjectScore = session.subjectScores[subjectKey];
        return formatScoreHelper(
          subjectScore.correct,
          subjectScore.total,
          subjectScore.score,
          isUtbk
        );
      }
    }
    
    // For exams or when no specific subject, calculate total
    let totalCorrect = 0;
    let totalQuestions = 0;
    
    Object.values(session.subjectScores).forEach(score => {
      totalCorrect += score.correct || 0;
      totalQuestions += score.total || 0;
    });
    
    if (totalQuestions > 0) {
      // Calculate total score based on exam type
      const totalScore = isUtbk 
      ? Math.ceil((totalCorrect / totalQuestions) * 1000) // Changed from Math.round to Math.ceil
      : totalCorrect * 5;
      
      return `${totalCorrect}/${totalQuestions}\n(${totalScore} pts)`;
    }
  }
  
  return '-';
};

// Format a detailed subject scores string for display in the "SUBJECT SCORES" column
const formatSubjectScores = (session: ExamSessionData): React.ReactNode => {
  if (!session.subjectScores || Object.keys(session.subjectScores).length === 0) {
    return '-';
  }
  
  const isUtbk = session.examType?.toLowerCase() === 'utbk';
  const subjectScoreItems: React.ReactNode[] = [];
  
  // For each subject, create a formatted display
  Object.entries(session.subjectScores).forEach(([key, scoreData], index) => {
    const displayName = getSubjectDisplayName(key);
    const scoreText = formatScoreHelper(
      scoreData.correct,
      scoreData.total,
      scoreData.score,
      isUtbk
    );
    
    subjectScoreItems.push(
      <div key={key} className={index > 0 ? "mt-2" : ""}>
        <div className="font-medium">{displayName}</div>
        <div>{scoreText}</div>
      </div>
    );
  });
  
  return (
    <div className="space-y-1">
      {subjectScoreItems}
    </div>
  );
};

// Helper to format a score
const formatScoreHelper = (
  correct: number, 
  total: number, 
  score: number,
  isUtbk: boolean = false
): string => {
  if (total === 0) return '-';
  
  if (isUtbk) {
    // For individual UTBK subject: (correct/total) * 1000
    const calculatedScore = Math.ceil((correct / total) * 1000); // Changed from Math.round to Math.ceil
    return `${correct}/${total} (${calculatedScore} pts)`;
  } else {
    // For CPNS/LPDP: correct * 5
    return `${correct}/${total} (${correct*5} pts)`;
  }
};

// Calculate and format the UTBK total score
const formatUtbkTotalScore = (session: ExamSessionData): string => {
  if (!session.subjectScores || Object.keys(session.subjectScores).length === 0) {
    // Fallback to direct calculation if no subject scores
    if (session.correctAnswers && session.totalQuestions && session.totalQuestions > 0) {
      const calculatedScore = Math.ceil((session.correctAnswers / session.totalQuestions) * 1000); // Changed from Math.round to Math.ceil
      return `${session.correctAnswers}/${session.totalQuestions} (${calculatedScore} pts)`;
    }
    return '-';
  }
  
  // Calculate total correct and questions
  let totalCorrect = 0;
  let totalQuestions = 0;
  let totalPoints = 0;
  let subjectCount = 0;
  
  // Sum up points for each subject
  Object.values(session.subjectScores).forEach(subjectScore => {
    const correct = subjectScore.correct || 0;
    const total = subjectScore.total || 0;
    
    if (total > 0) {
      // Calculate subject score using UTBK formula
      const score = Math.ceil((correct / total) * 1000); // Changed from Math.round to Math.ceil
      totalPoints += score;
      subjectCount++;
      
      // Track totals
      totalCorrect += correct;
      totalQuestions += total;
    }
  });
  
  if (subjectCount === 0) return '-';
  
  // Calculate average score across all subjects
  const averageScore = Math.ceil(totalPoints / subjectCount); // Changed from Math.round to Math.ceil
  
  return `${totalCorrect}/${totalQuestions} (${averageScore} pts)`;
};

// Helper functions
const getStatusColor = (status: string) => {
  switch (status.toUpperCase()) {
    case 'COMPLETED':
      return 'success';
    case 'ABANDONED':
      return 'danger';
    case 'ACTIVE':
      return 'warning';
    default:
      return 'default';
  }
};

const getSessionTypeColor = (type: string) => {
  return type === 'PRACTICE' ? 'warning' : 'primary';
};

const truncateOrderId = (orderId: string) => {
  return orderId.length > 8 ? orderId.substring(0, 8) + '...' : orderId;
};

const formatCurrency = (amount: number | null | undefined) => {
  if (amount === null || amount === undefined) return 'Rp0';
  return new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR' }).format(amount);
};

const checkDurationAndUpdateStatus = async (session: ExamSessionData) => {
  const start = new Date(session.startTime);
  const end = session.endTime ? new Date(session.endTime) : new Date();
  const elapsedMs = end.getTime() - start.getTime();
  const elapsedHours = elapsedMs / (1000 * 60 * 60);

  // If duration > 10 hours and status is still ACTIVE
  if (elapsedHours > 10 && session.sessionStatus === 'ACTIVE') {
    try {
      await updateExamSessionStatus(session.uniqueSessionId, "ABANDONED");
      // Refresh the page after status change
      window.location.reload();
    } catch (error) {
      console.error('Failed to update session status:', error);
    }
  }
};

// Helper function to get a display name for a subject key
const getSubjectDisplayName = (subjectKey: string): string => {
  // Map known subject keys to display names
  const nameMap: Record<string, string> = {
    'twk': 'TWK',
    'tiu': 'TIU',
    'tkp': 'TKP',
    'literasi': 'Literasi',
    'penalaran_kuantitatif': 'Penalaran Kuantitatif',
    'penalaran_matematika': 'Penalaran Matematika',
    'potensi_skolastik': 'Potensi Skolastik',
    'kepribadian': 'Kepribadian',
    'pemecahan_masalah': 'Pemecahan Masalah',
    'penalaran_verbal': 'Penalaran Verbal'
  };
  
  // Return mapped name or format the key
  return nameMap[subjectKey] || subjectKey
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const OrderDetailsPopover = ({ order }: { order: OrderHistoryData }) => (
  <Popover>
    <PopoverTrigger>
      <Button size="sm" variant="light" className="p-0 underline">
        {truncateOrderId(order.orderId)}
      </Button>
    </PopoverTrigger>
    <PopoverContent>
      <div className="p-4">
        <h4 className="font-bold mb-2">Order Details</h4>
        <p><strong>Order ID:</strong> {order.orderId}</p>
        <p><strong>Invoice:</strong> {order.invoiceNumber || 'N/A'}</p>
        <p><strong>Invoice Status:</strong> {order.invoiceStatus || 'N/A'}</p>
        <p><strong>Due Date:</strong> {order.dueDate ? new Date(order.dueDate).toLocaleDateString() : 'N/A'}</p>
        <p><strong>Payment Status:</strong> {order.paymentStatus || 'N/A'}</p>
        <p><strong>Payment Method:</strong> {order.paymentMethod || 'N/A'}</p>
      </div>
    </PopoverContent>
  </Popover>
);

const MobileOrderCard = ({ order }: { order: OrderHistoryData }) => (
  <div className="bg-default-50 p-4 rounded-lg shadow-md mb-4">
    <h4 className="font-semibold mb-2">{order.examName}</h4>
    <div className="grid grid-cols-2 gap-2 text-sm">
      <div>Order ID:</div>
      <div><OrderDetailsPopover order={order} /></div>
      <div>Invoice:</div>
      <div>{order.invoiceNumber || 'N/A'}</div>
      <div>Date:</div>
      <div>{new Date(order.orderDate).toLocaleDateString()}</div>
      <div>Status:</div>
      <div>
        <Chip color={order.orderStatus === 'COMPLETED' ? 'success' : 'warning'} variant="flat" size="sm">
          {order.orderStatus}
        </Chip>
      </div>
      <div>Amount:</div>
      <div>{formatCurrency(order.paymentAmount)}</div>
      <div>Quantity:</div>
      <div>{order.quantity} ticket(s)</div>
    </div>
  </div>
);

const MobileSessionCard = ({ 
  session, 
  score 
}: { 
  session: ExamSessionData;
  score?: string;
}) => {
  // Use provided score or fall back to synchronous calculation
  const displayScore = score || formatScoreSync(session);
  
  return (
    <div className="bg-default-50 p-4 rounded-lg shadow-md mb-4">
      <h4 className="font-semibold mb-2">{session.examName}</h4>
      <div className="grid grid-cols-2 gap-2 text-sm">
        <div>Tipe:</div>
        <div>
          <Chip size="sm" variant="flat" color={getSessionTypeColor(session.sessionType)}>
            {session.sessionType}
          </Chip>
        </div>
        {session.sessionType === 'PRACTICE' && (
          <>
            <div>Subject:</div>
            <div>{session.subject}</div>
          </>
        )}
        <div>Tanggal:</div>
        <div>{new Date(session.startTime).toLocaleDateString()}</div>
        <div>Status:</div>
        <div>
          <Chip color={getStatusColor(session.sessionStatus)} variant="flat" size="sm">
            {session.sessionStatus}
          </Chip>
        </div>
        <div>Durasi:</div>
        <div>{calculateElapsedTime(session.startTime, session.endTime)}</div>
        
        {/* Subject Scores */}
        <div className="col-span-2 mt-2">
          <div className="font-semibold mb-1">Subject Scores:</div>
          <div>{formatSubjectScores(session)}</div>
        </div>
        
        <div>Total Skor:</div>
        <div>{displayScore}</div>
        <div>Pembahasan:</div>
        <div>
          {session.sessionStatus === 'ACTIVE' ? (
            <Button size="sm" color="primary" variant="flat" isDisabled>
              Lihat
            </Button>
          ) : (
            <Button 
              size="sm" 
              color="primary" 
              variant="flat"
              onPress={() => {
                if (session.uniqueSessionId) {
                  window.location.href = `/result/${session.uniqueSessionId}`;
                }
              }}
            >
              Lihat
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export const Content: React.FC<ContentProps> = ({ examSessions, examProgress, orderHistory, examConfig, session }) => {
  const [isClient, setIsClient] = useState(false);
  const [sessionScores, setSessionScores] = useState<Record<string, string>>({});
  const [processedProgressData, setProcessedProgressData] = useState<ChartExamProgressData[]>([]);
  const router = useRouter();

  useEffect(() => {
    setIsClient(true);
  
    // Get sessions and check their status
    const sessions = Array.isArray(examSessions) 
      ? examSessions 
      : (examSessions?.sessions || []);
  
    // Check all active sessions and load scores asynchronously
    sessions.forEach(session => {
      if (session.sessionStatus === 'ACTIVE') {
        checkDurationAndUpdateStatus(session);
      }
      
      // Load scores asynchronously only if we have a unique session ID
      if (session.uniqueSessionId) {
        formatScore(session).then(score => {
          setSessionScores(prev => ({
            ...prev,
            [session.uniqueSessionId]: score
          }));
        }).catch(error => {
          console.error('Error calculating score for session:', session.uniqueSessionId, error);
        });
      }
    });
  
    // Process progress data to ensure it has examType
    const rawProgressData = examProgress?.progress || [];
    const processed = rawProgressData.map(item => {
      // If there's no examType, add it based on the subject keys
      const subjectKeys = Object.keys(item.subjectScores || {});
      
      // Determine exam type based on subject keys
      let examType = 'unknown';
      
      if (subjectKeys.some(key => ['literasi', 'penalaran_matematika', 'potensi_skolastik'].includes(key))) {
        examType = 'UTBK';
      } else if (subjectKeys.some(key => ['twk', 'tiu', 'tkp'].includes(key))) {
        examType = 'CPNS';
      } else if (subjectKeys.some(key => ['kepribadian', 'pemecahan_masalah', 'penalaran_kuantitatif', 'penalaran_verbal'].includes(key))) {
        examType = 'LPDP';
      }
      
      // Create a ChartExamProgressData object with the required examType
      return {
        sessionId: item.sessionId,
        startTime: item.startTime,
        examType: examType,
        subjectScores: item.subjectScores
      };
    });
    
    setProcessedProgressData(processed);
  }, [examSessions, examProgress]);

  const sessions = Array.isArray(examSessions) 
    ? examSessions 
    : (examSessions?.sessions || []);

  const orders = orderHistory?.orders || [];

  // Create column definitions with a single Subject Scores column
  const columns = [
    { key: "examName", label: "NAMA UJIAN" },
    { key: "type", label: "TIPE" },
    { key: "subject", label: "SUBJECT" },
    { key: "status", label: "STATUS" },
    { key: "duration", label: "DURASI" },
    { key: "subjectScores", label: "SUBJECT SCORES" }, // Single column for all subject scores
    { key: "totalScore", label: "TOTAL SKOR" },
    { key: "details", label: "PEMBAHASAN" }
  ];

  // Create a renderCell function to handle complex cell rendering
  const renderCell = (session: ExamSessionData, columnKey: Key): React.ReactNode => {
    // Convert columnKey to string to handle potential number keys
    const key = String(columnKey);
  
    switch (key) {
      case "examName":
        return session.examName || '-';
      case "type":
        return session.sessionType ? (
          <Chip size="sm" variant="flat" color={getSessionTypeColor(session.sessionType)}>
            {session.sessionType}
          </Chip>
        ) : '-';
      case "subject":
        return session.sessionType === 'PRACTICE' && session.subject ? session.subject : '-';
      case "status":
        return (
          <div className="flex flex-col justify-center items-center">
            {session.sessionStatus ? (
              <Chip color={getStatusColor(session.sessionStatus)} variant="flat">
                {session.sessionStatus}
              </Chip>
            ) : '-'}
            {session.startTime ? new Date(session.startTime).toLocaleDateString() : '-'}
          </div>
        );
      case "duration":
        return session.startTime ? calculateElapsedTime(session.startTime, session.endTime) : '-';
      case "subjectScores":
        return formatSubjectScores(session);
      case "totalScore":
        // Use the pre-calculated score from state if available, otherwise use synchronous version
        return session.uniqueSessionId && sessionScores[session.uniqueSessionId] 
          ? sessionScores[session.uniqueSessionId] 
          : formatScoreSync(session);
      case "details":
        return !session.sessionStatus || session.sessionStatus === 'ACTIVE' || session.sessionStatus === 'ABANDONED' ? (
          <Button size="sm" color="primary" variant="flat" isDisabled>
            Lihat
          </Button>
        ) : (
          <Button 
            size="sm" 
            color="primary" 
            variant="flat"
            onPress={() => {
              if (session.uniqueSessionId) {
                window.location.href = `/result/${session.uniqueSessionId}`;
              }
            }}
          >
            Lihat
          </Button>
        );
      default:
        return null;
    }
  };

  return (
    <div className="lg:px-6 pb-8">
      <div className="text-center pt-4 px-4">
        <div className="flex items-center justify-center">
          <Image alt="hi" src="https://cdn.terang.ai/images/icons/wave.gif" width="50" height="50" />
          <h1 className="text-2xl font-bold text-gray-900 ml-2">
            Selamat Datang, {session && session.user ? session.user.firstname : ""}
          </h1>
        </div>
        <WelcomeBanner />
        <p className="text-gray-600 mt-4">
          Ringkasan dari kemajuan Kamu sejauh ini dan hal-hal yang dapat kamu tingkatkan bisa dilihat di sini.
        </p>
      </div>

      <div className="flex justify-center gap-4 xl:gap-6 pt-3 px-4 lg:px-0 flex-wrap xl:flex-nowrap sm:pt-10 max-w-[90rem] mx-auto w-full">
        <div className="gap-6 flex flex-col w-full overflow-visible">
          {/* Chart - Now using processedProgressData that includes examType */}
          <div className="flex flex-col gap-2">
            <div className="flex gap-2.5 justify-center">
              <div className="flex flex-col border-dashed border-2 border-divider py-2 px-6 rounded-xl">
                <span className="text-default-900 text-xl font-semibold flex">
                  <ChartAverageIcon className="mr-2" /> Peningkatan Nilaimu
                </span>
              </div>
            </div>
            {/* Use the processed data that includes examType */}
            <Chart progressData={processedProgressData} />
          </div>

          {/* Exam Session History */}
          <div className="flex flex-col gap-2 mt-6">
            <div className="flex gap-2.5 justify-center">
              <div className="flex flex-col border-dashed border-2 border-divider py-2 px-6 rounded-xl">
                <span className="text-default-900 text-xl font-semibold flex">
                  <WorkHistoryIcon className="mr-2" /> Riwayat Sesi Ujian
                </span>
              </div>
            </div>
            {sessions.length > 0 ? (
              <>
                {/* Mobile view */}
                <div className="lg:hidden">
                  {sessions.map((session) => (
                    <MobileSessionCard 
                      key={session.uniqueSessionId} 
                      session={session} 
                      score={session.uniqueSessionId ? sessionScores[session.uniqueSessionId] : undefined}
                    />
                  ))}
                </div>
                
                {/* Desktop view with single subject scores column */}
                <div className="hidden lg:block">
                  <Table aria-label="Exam Session History" className="w-full bg-default-50 shadow-lg rounded-2xl">
                    <TableHeader columns={columns}>
                      {(column) => (
                        <TableColumn key={column.key}>{column.label}</TableColumn>
                      )}
                    </TableHeader>
                    <TableBody items={sessions}>
                      {(session) => (
                        <TableRow key={session.uniqueSessionId}>
                          {(columnKey) => (
                            <TableCell>{renderCell(session, columnKey)}</TableCell>
                          )}
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </>
            ) : (
              <div className="w-full bg-default-50 shadow-lg rounded-2xl p-6 text-center">
                No exam sessions found. Start an exam to see your history here!
              </div>
            )}
          </div>

          {/* Order History */}
          <div className="flex flex-col gap-2 mt-6">
            <div className="flex gap-2.5 justify-center">
              <div className="flex flex-col border-dashed border-2 border-divider py-2 px-6 rounded-xl">
                <span className="text-default-900 text-xl font-semibold flex">
                  <ShoppingBag01Icon className="mr-2" /> Riwayat Pembelian Kartu
                </span>
              </div>
            </div>
            {orders.length > 0 ? (
              <>
                {/* Mobile view */}
                <div className="lg:hidden">
                  {orders.map((order) => (
                    <MobileOrderCard key={order.orderId} order={order} />
                  ))}
                </div>
                
                {/* Desktop view */}
                <div className="hidden lg:block">
                  <Table aria-label="Order History" className="w-full bg-default-50 shadow-lg rounded-2xl">
                    <TableHeader>
                      <TableColumn>ORDER ID</TableColumn>
                      <TableColumn>INVOICE</TableColumn>
                      <TableColumn>TANGGAL</TableColumn>
                      <TableColumn>NAMA UJIAN</TableColumn>
                      <TableColumn>STATUS</TableColumn>
                      <TableColumn>JUMLAH</TableColumn>
                      <TableColumn>TOTAL</TableColumn>
                    </TableHeader>
                    <TableBody>
                      {orders.map((order) => (
                        <TableRow key={order.orderId}>
                          <TableCell><OrderDetailsPopover order={order} /></TableCell>
                          <TableCell>{order.invoiceNumber || 'N/A'}</TableCell>
                          <TableCell>{new Date(order.orderDate).toLocaleDateString()}</TableCell>
                          <TableCell>{order.examName}</TableCell>
                          <TableCell>
                            <Chip color={order.orderStatus === 'COMPLETED' ? 'success' : 'warning'} variant="flat">
                              {order.orderStatus}
                            </Chip>
                          </TableCell>
                          <TableCell>{order.quantity} ticket(s)</TableCell>
                          <TableCell>{formatCurrency(order.paymentAmount)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </>
            ) : (
              <div className="w-full bg-default-50 shadow-lg rounded-2xl p-6 text-center">
                No order history found. Purchase an exam to see your history here!
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Content;