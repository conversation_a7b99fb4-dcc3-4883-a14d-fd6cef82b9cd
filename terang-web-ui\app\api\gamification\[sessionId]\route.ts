// app/api/gamification/[sessionId]/route.ts
import { fetchGamification } from '@/components/my-trials-subjects/actions';
import { NextResponse } from 'next/server';

export async function GET(request: Request, props: { params: Promise<{ sessionId: string }> }) {
  const params = await props.params;
  try {
    const gamification = await fetchGamification(params.sessionId);
    return NextResponse.json(gamification);
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch gamification' },
      { status: 500 }
    );
  }
}