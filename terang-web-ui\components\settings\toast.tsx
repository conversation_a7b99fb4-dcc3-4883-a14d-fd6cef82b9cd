// app/components/ui/toast.tsx
import { toast } from 'sonner';
import { LucideXCircle, LucideCheckCircle2, LucideX } from 'lucide-react';

// Re-export the Toaster component for consistent usage
export { Toaster } from 'sonner';

// Custom styles to match your design
const toastStyles = {
  success: {
    className: 'bg-green-50 text-green-700 border border-green-200',
    descriptionClassName: 'text-green-600',
    icon: <LucideCheckCircle2 className="h-5 w-5 text-green-600" />,
  },
  error: {
    className: 'bg-red-50 text-red-700 border border-red-200',
    descriptionClassName: 'text-red-600',
    icon: <LucideXCircle className="h-5 w-5 text-red-600" />,
  },
  closeButton: <LucideX className="h-4 w-4" />
};

// Toast utility functions
export const showToast = {
  success: (title: string, description?: string) => {
    toast.success(title, {
      className: toastStyles.success.className,
      descriptionClassName: toastStyles.success.descriptionClassName,
      icon: toastStyles.success.icon,
      duration: 3000,
      description,
      closeButton: true,
    });
  },
  
  error: (title: string, description?: string) => {
    toast.error(title, {
      className: toastStyles.error.className,
      descriptionClassName: toastStyles.error.descriptionClassName,
      icon: toastStyles.error.icon,
      duration: 3000,
      description,
      closeButton: true,
    });
  },
  
  dismiss: (toastId?: string) => {
    if (toastId) {
      toast.dismiss(toastId);
    } else {
      toast.dismiss();
    }
  },

  loading: (message: string) => {
    return toast.loading(message, {
      className: 'bg-white text-gray-900 border border-gray-100',
      duration: Infinity,
    });
  }
};

// Default Toaster configuration
export const ToasterConfig = {
  position: 'bottom-right' as const,
  expand: true,
  richColors: false, // We're using custom colors
  closeButton: true,
  styles: {
    toast: {
      padding: '16px',
      borderRadius: '8px',
      boxShadow: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    },
    description: {
      fontSize: '14px',
      marginTop: '4px',
    },
    actionButton: {
      marginLeft: '8px',
    },
    closeButton: {
      marginLeft: '8px',
    },
  },
};