"use client";

import React, { useMemo, useState, useCallback } from "react";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/breadcrumbs";
import { ExamCard } from "../cards/exam-card";
import { AvailableExamsType } from "../types";
import { ArrowRight02Icon } from "hugeicons-react";
import dynamic from "next/dynamic";

// Dynamically import the animation for better performance
const DotLottieAnimation = dynamic(() => import("../shared/dotlottie-animation"), {
  ssr: false,
  loading: () => <div className="w-full aspect-square bg-gray-100 animate-pulse rounded-lg" />
});

interface Props {
  data: AvailableExamsType[];
}

export const MyExams: React.FC<Props> = ({ data }) => {
  const exams = useMemo(() => data || [], [data]);
  const [refreshKey, setRefreshKey] = useState(0);

  // Memoized notification handler
  const showNotification = useCallback((message: string, type: "success" | "info" | "error") => {
    // Implementation depends on your notification system
    // This could be a toast notification or alert
    console.log(`${type}: ${message}`);
    // If you have a notification library, call it here
  }, []);

  // Refresh handler for child components
  const handleRefreshData = useCallback(() => {
    setRefreshKey(prev => prev + 1);
  }, []);

  // Get the correct grid class based on the number of exams
  const gridClass = useMemo(() => {
    if (exams.length === 0) return "";
    if (exams.length === 1) return "grid-cols-1 max-w-[25rem]";
    if (exams.length <= 6) return "grid-cols-2 sm:grid-cols-2 lg:grid-cols-3";
    return "grid-cols-2 sm:grid-cols-3 lg:grid-cols-4";
  }, [exams.length]);

  // Only render visible exams for larger datasets
  const renderExams = useCallback(() => {
    if (exams.length <= 20) {
      // For small datasets, render all at once
      return exams.map((card, index) => (
        <ExamCard 
          key={`${card.id}-${refreshKey}-${index}`} 
          examData={card} 
          showNotification={showNotification}
          setRefreshData={handleRefreshData}
        />
      ));
    } else {
      // For larger datasets, implement a simple windowing technique
      // This is a simple approach - consider using react-virtualized or react-window for more complex cases
      return (
        <VirtualizedExamGrid 
          exams={exams} 
          refreshKey={refreshKey}
          showNotification={showNotification}
          setRefreshData={handleRefreshData}
        />
      );
    }
  }, [exams, refreshKey, showNotification, handleRefreshData]);

  return (
    <div className="my-10 px-6 lg:px-6 max-w-[95rem] mx-auto w-full flex flex-col gap-4">
      <ul className="flex">
        <Breadcrumbs>
          <BreadcrumbItem href="/dashboard">Dashboard</BreadcrumbItem>
          <BreadcrumbItem>Ujian Dimiliki</BreadcrumbItem>
        </Breadcrumbs>
      </ul>
      
      {exams.length > 0 ? (
        <div className={`my-10 grid ${gridClass} gap-2 lg:gap-4 items-center`}>
          {renderExams()}
        </div>
      ) : (
        <EmptyState />
      )}
    </div>
  );
};

// Separate empty state component
const EmptyState = () => (
  <div className="flex flex-col justify-center items-center h-[calc(100vh-200px)]">
    <div className="relative w-full max-w-[350px] aspect-square">
      <DotLottieAnimation
        src="https://cdn.terang.ai/dotlotties/empty-folder.lottie"
        autoplay
        loop
        width="100%"
        height="100%"
      />
    </div>
    <div className="text-center">
      <p className="text-lg font-semibold">Kamu belum punya kartu ujian nih.</p>
      <p className="text-sm text-gray-500 mt-2">
        Silakan bisa diambil dulu ya kartu ujiannya, dan itu baru akan tampil di halaman ini.
      </p>
    </div>
    <div className="flex flex-col items-center gap-6 mt-8">
      <button 
        onClick={() => window.location.href = '/available-exams'}
        className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 px-8 py-3 text-white shadow-lg transition-all hover:scale-105 hover:shadow-xl"
      >
        <span className="relative z-10 flex items-center gap-2 font-medium">
          Lihat Ujian Tersedia
          <ArrowRight02Icon className="h-4 w-4 transition-transform group-hover:translate-x-1" />
        </span>
        <div className="absolute inset-0 z-0 bg-gradient-to-br from-blue-600 to-indigo-700 opacity-0 transition-opacity group-hover:opacity-100" />
      </button>
      <p className="text-sm text-gray-500 text-center max-w-sm">
        Temukan berbagai ujian yang tersedia dan mulai perjalanan belajarmu
      </p>
    </div>
  </div>
);

// Simple virtualized grid for large datasets
const VirtualizedExamGrid = ({ 
  exams, 
  refreshKey, 
  showNotification, 
  setRefreshData 
}: { 
  exams: AvailableExamsType[], 
  refreshKey: number,
  showNotification: (message: string, type: "success" | "info" | "error") => void,
  setRefreshData: () => void
}) => {
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 20 });
  
  // Load more exams as user scrolls
  const handleScroll = useCallback(() => {
    const scrollPosition = window.scrollY;
    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight;
    
    // If we're near the bottom, load more items
    if (scrollPosition + windowHeight > documentHeight - 500) {
      setVisibleRange(prev => ({
        start: prev.start,
        end: Math.min(prev.end + 10, exams.length)
      }));
    }
  }, [exams.length]);

  // Add scroll listener
  React.useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  return (
    <>
      {exams.slice(0, visibleRange.end).map((card, index) => (
        <ExamCard 
          key={`${card.id}-${refreshKey}-${index}`} 
          examData={card} 
          showNotification={showNotification}
          setRefreshData={setRefreshData}
        />
      ))}
    </>
  );
};

export default MyExams;