"use client";

import {<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>, Button} from "@heroui/react";

export default function App() {
  return (
    <div className="max-w-[900px] gap-2 grid grid-cols-12 grid-rows-2 px-8">
      <Card className="col-span-12 sm:col-span-4 h-[300px]">
        <CardHeader className="absolute z-10 top-1 flex-col !items-start">
          <p className="text-tiny text-white/60 uppercase font-bold">Tips Persiapan</p>
          <h4 className="text-white font-medium text-large">Cara Efektif Menghadapi <PERSON>an</h4>
        </CardHeader>
        <Image
          removeWrapper
          alt="Gambar Tips Persiapan"
          className="z-0 w-full h-full object-cover"
          src="https://nextui.org/images/card-example-4.jpeg"
        />
      </Card>

      <Card className="col-span-12 sm:col-span-4 h-[300px]">
        <CardHeader className="absolute z-10 top-1 flex-col !items-start">
          <p className="text-tiny text-white/60 uppercase font-bold">Belajar Bersama</p>
          <h4 className="text-white font-medium text-large">Ikut Grup WhatsApp Kami</h4>
        </CardHeader>
        <Image
          removeWrapper
          alt="Gambar Belajar Bersama"
          className="z-0 w-full h-full object-cover"
          src="https://nextui.org/images/card-example-3.jpeg"
        />
      </Card>

      <Card className="col-span-12 sm:col-span-4 h-[300px]">
        <CardHeader className="absolute z-10 top-1 flex-col !items-start">
          <p className="text-tiny text-white/60 uppercase font-bold">AI Pembimbing</p>
          <h4 className="text-white font-medium text-large">Belajar dengan AI Canggih</h4>
        </CardHeader>
        <Image
          removeWrapper
          alt="Gambar AI Pembimbing"
          className="z-0 w-full h-full object-cover"
          src="https://nextui.org/images/card-example-2.jpeg"
        />
      </Card>

      <Card isFooterBlurred className="w-full h-[300px] col-span-12 sm:col-span-5">
        <CardHeader className="absolute z-10 top-1 flex-col items-start">
          <p className="text-tiny text-white/60 uppercase font-bold">Kursus Terbaru</p>
          <h4 className="text-black font-medium text-2xl">Materi Ujian Terbaru</h4>
        </CardHeader>
        <Image
          removeWrapper
          alt="Gambar Materi Ujian"
          className="z-0 w-full h-full scale-125 -translate-y-6 object-cover"
          src="https://nextui.org/images/card-example-6.jpeg"
        />
        <CardFooter className="absolute bg-white/30 bottom-0 border-t-1 border-zinc-100/50 z-10 justify-between">
          <div>
            <p className="text-black text-tiny">Segera Tersedia.</p>
            <p className="text-black text-tiny">Dapatkan Notifikasi.</p>
          </div>
          <Button className="text-tiny" color="primary" radius="full" size="sm">
            Beri Tahu Saya
          </Button>
        </CardFooter>
      </Card>

      <Card isFooterBlurred className="w-full h-[300px] col-span-12 sm:col-span-7">
        <CardHeader className="absolute z-10 top-1 flex-col items-start">
          <p className="text-tiny text-white/60 uppercase font-bold">Rekomendasi Hari Ini</p>
          <h4 className="text-white/90 font-medium text-xl">Cara Belajar Lebih Baik</h4>
        </CardHeader>
        <Image
          removeWrapper
          alt="Gambar Rekomendasi Belajar"
          className="z-0 w-full h-full object-cover"
          src="https://nextui.org/images/card-example-5.jpeg"
        />
        <CardFooter className="absolute bg-black/40 bottom-0 z-10 border-t-1 border-default-600 dark:border-default-100">
          <div className="flex flex-grow gap-2 items-center">
            <Image
              alt="Ikon Aplikasi Belajar"
              className="rounded-full w-10 h-11 bg-black"
              src="https://nextui.org/images/breathing-app-icon.jpeg"
            />
            <div className="flex flex-col">
              <p className="text-tiny text-white/60">Aplikasi Belajar</p>
              <p className="text-tiny text-white/60">Belajar dengan lebih mudah dan menyenangkan.</p>
            </div>
          </div>
          <Button radius="full" size="sm">Download Aplikasi</Button>
        </CardFooter>
      </Card>
    </div>
  );
}
