import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { Progress, Chip, Card, CardHeader, CardBody, CardFooter } from "@heroui/react";
import { AvailableExam, retrieveFromRedis } from './actions';
import PurchasedExam from './purchased-exam';
import { Hearts } from './hearts';
import ExamCompletionIndicator from './exam-completion-indicator';

interface TrialSession {
  id: string;
  sessionId: string;
  userId: string | boolean | null;
  examId: string;
  type: string;
  status: string;
  startTime: string;
  endTime: string;
  answers: string;
  flaggedQuestion: string;
  createdAt: string;
  modifiedAt: string;
  subject: string;
  remaining_duration?: string;
  exam_id?: string;
}

interface GamificationData {
  currentLives: number;
  streakCount?: number;
  questionTimes?: Record<string, number>;
}

interface ExamCardProps {
  index: number;
  examId: string;
  subject: string;
  passingGrade?: number | null;
  className?: string;
  maxLives?: number;
  totalQuestions: number;
  userId: string | boolean | null;
  isPremium: boolean;
  trialSession: TrialSession | null;
  trialCompletionData: AvailableExam | null;
}

interface ExamCardState {
  lives: number;
  progress: number;
  loading: boolean;
  error: string | null;
  gamificationData: GamificationData | null;
  progressData: ProgressData;
}

interface ProgressData {
  totalAnswered: number;
  flaggedQuestions: Record<string, boolean>;
  selectedOptions: Record<string, string>;
}

const ExamCard: React.FC<ExamCardProps> = ({
  index,
  examId,
  subject,
  totalQuestions,
  passingGrade,
  className = '',
  maxLives = 3,
  userId,
  isPremium,
  trialSession,
  trialCompletionData
}) => {
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);

  // Initialize state
  const [state, setState] = useState<ExamCardState>({
    lives: maxLives,
    progress: 0,
    loading: true,
    error: null,
    gamificationData: null,
    progressData: {
      totalAnswered: 0,
      flaggedQuestions: {},
      selectedOptions: {}
    }
  });

  // Better fetch function with timeout
  const fetchWithTimeout = useCallback(async (promise: Promise<any>, timeout: number) => {
    try {
      const timeoutPromise = new Promise<null>((_, reject) => {
        setTimeout(() => reject(new Error(`Request timed out after ${timeout}ms`)), timeout);
      });
      
      return await Promise.race([promise, timeoutPromise]);
    } catch (error) {
      console.error("Fetch with timeout error:", error);
      return null; // Return null instead of rejecting, so Promise.all doesn't fail
    }
  }, []);

  // Fetch data from Redis - improved error handling
  const fetchData = useCallback(async () => {
    if (!isMountedRef.current) return;
    
    if (trialSession?.status !== 'ACTIVE' || !trialSession.sessionId) {
      setState(prev => ({ ...prev, loading: false }));
      return;
    }
    
    try {
      const sessionId = trialSession.sessionId;
      
      // Fetch all data in parallel with timeout handling
      const results = await Promise.all([
        fetchWithTimeout(retrieveFromRedis(`${sessionId}_selectedOptions`), 5000),
        fetchWithTimeout(retrieveFromRedis(`${sessionId}_flaggedQuestions`), 5000),
        fetchWithTimeout(retrieveFromRedis(`${sessionId}_gamification`), 5000)
      ]);
      
      if (!isMountedRef.current) return;
      
      const [selectedOptions, flaggedQuestions, gamificationState] = results;
      
      // Only parse valid responses
      const parsedOptions = selectedOptions ? JSON.parse(selectedOptions) : {};
      const parsedFlags = flaggedQuestions ? JSON.parse(flaggedQuestions) : {};
      const parsedGamification = gamificationState ? JSON.parse(gamificationState) : null;
      
      const totalAnswered = Object.keys(parsedOptions).length;
      const progressPercentage = Math.round((totalAnswered / totalQuestions) * 100);
      
      setState(prev => ({
        ...prev,
        loading: false,
        progress: progressPercentage,
        progressData: {
          totalAnswered,
          flaggedQuestions: parsedFlags,
          selectedOptions: parsedOptions
        },
        gamificationData: parsedGamification,
        lives: parsedGamification ? parsedGamification.currentLives ?? maxLives : maxLives
      }));
    } catch (error) {
      console.error('Error fetching session data:', error);
      
      if (isMountedRef.current) {
        setState(prev => ({
          ...prev,
          loading: false,
          error: 'Failed to fetch session information'
        }));
      }
    }
  }, [trialSession?.sessionId, trialSession?.status, totalQuestions, maxLives, fetchWithTimeout]);

  // Improved polling function
  const pollData = useCallback(async () => {
    console.log("ABOUT POLLING THE DATA")
    if (!isMountedRef.current) return;
    if (trialSession?.status !== 'ACTIVE' || !trialSession.sessionId) return;
  
    try {
      const sessionId = trialSession.sessionId;
  
      // Using Promise.all but handling each promise individually
      const [selectedOptionsResult, flaggedQuestionsResult, gamificationStateResult] = await Promise.all([
        retrieveFromRedis(`${sessionId}_selectedOptions`).catch(() => null),
        retrieveFromRedis(`${sessionId}_flaggedQuestions`).catch(() => null),
        retrieveFromRedis(`${sessionId}_gamification`).catch(() => null)
      ]);
  
      if (!isMountedRef.current) return;
      
      setState(prevState => {
        const updates: Partial<ExamCardState> = {};
        let hasUpdates = false;
  
        // Only process valid responses
        if (selectedOptionsResult) {
          try {
            const parsedOptions = JSON.parse(selectedOptionsResult);
            const totalAnswered = Object.keys(parsedOptions).length;
            const progressPercentage = Math.round((totalAnswered / totalQuestions) * 100);
  
            if (progressPercentage !== prevState.progress || 
                totalAnswered !== prevState.progressData.totalAnswered) {
              updates.progress = progressPercentage;
              updates.progressData = {
                ...prevState.progressData,
                totalAnswered,
                selectedOptions: parsedOptions
              };
              hasUpdates = true;
            }
          } catch (error) {
            console.error('Error parsing selectedOptions:', error);
          }
        }
  
        if (gamificationStateResult) {
          try {
            const parsedGamification = JSON.parse(gamificationStateResult);
            if (JSON.stringify(parsedGamification) !== JSON.stringify(prevState.gamificationData)) {
              updates.gamificationData = parsedGamification;
              updates.lives = parsedGamification.currentLives ?? maxLives;
              hasUpdates = true;
            }
          } catch (error) {
            console.error('Error parsing gamificationState:', error);
          }
        }
  
        if (flaggedQuestionsResult) {
          try {
            const parsedFlags = JSON.parse(flaggedQuestionsResult);
            if (JSON.stringify(parsedFlags) !== JSON.stringify(prevState.progressData.flaggedQuestions)) {
              updates.progressData = {
                ...(updates.progressData || prevState.progressData),
                flaggedQuestions: parsedFlags
              };
              hasUpdates = true;
            }
          } catch (error) {
            console.error('Error parsing flaggedQuestions:', error);
          }
        }
  
        return hasUpdates ? { ...prevState, ...updates } : prevState;
      });
    } catch (error) {
      console.error('Error polling updates:', error);
    }
  }, [trialSession?.sessionId, trialSession?.status, totalQuestions, maxLives]);
  
  // Fetch initial data when trialSession changes
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Set up polling interval
  useEffect(() => {
    if (trialSession?.status === 'ACTIVE' && trialSession.sessionId) {
      // Clear any existing interval first
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
      
      // Set up new polling interval
      pollIntervalRef.current = setInterval(pollData, 5000);
    }
    
    // Cleanup function
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
    };
  }, [pollData, trialSession?.status, trialSession?.sessionId]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current);
        pollIntervalRef.current = null;
      }
    };
  }, []);

  // Memoized streak counter
  const streakCounter = useMemo(() => {
    if (!state.gamificationData?.streakCount || state.gamificationData.streakCount <= 0) {
      return null;
    }

    return (
      <div className="text-small text-default-600">
        🔥 Streak: {state.gamificationData.streakCount}
      </div>
    );
  }, [state.gamificationData?.streakCount]);

  // Calculate remaining questions
  const remainingQuestions = totalQuestions - state.progressData.totalAnswered;

  return (
    <Card className={className}>
      <CardHeader className="flex justify-between items-start pb-0">
        <div className="flex flex-col gap-1">
          <h4 className="text-small font-semibold leading-none text-default-600">{subject} - Tipe {index+1}</h4>
          <h5 className="text-small tracking-tight text-default-400">
            {state.loading ? 'Loading...' : `${state.progressData.totalAnswered} dari ${totalQuestions} soal selesai`}
          </h5>
        </div>
        {passingGrade && (
          <Chip
            size="sm"
            variant="flat"
            color="warning"
            className="min-w-fit"
          >
            Min. {passingGrade}
          </Chip>
        )}
      </CardHeader>

      <CardBody className="py-2">
        <div className="flex flex-col gap-3">
          <div className="flex gap-1 items-center">
            <span className="text-small text-default-600">Nyawa tersisa:</span>
            <div className="flex gap-1">
              <Hearts
                lives={state.loading ? maxLives : state.lives}
                maxLives={maxLives}
                isPremium={isPremium}
              />
            </div>
          </div>

          {trialSession?.status === 'ACTIVE' && trialSession.sessionId && streakCounter}

          {examId && subject && (
            <PurchasedExam
              examId={examId}
              subject={subject}
              trialSession={trialSession}
            />
          )}

          <div className="flex items-center gap-2 w-full">
            <Progress
              aria-label="Progress ujian"
              size="sm"
              value={state.loading ? 0 : state.progress}
              color="success"
              className="flex-1"
            />
            <span className="text-small text-default-400 whitespace-nowrap">
              {state.loading ? 'Loading...' : `${state.progress}%`}
            </span>
          </div>
        </div>
      </CardBody>

      <CardFooter className="pt-0 flex-col">
        <div className="flex justify-between w-full">
          <span className="text-tiny text-default-400">
            {state.loading ? 'Loading...' : `Sisa ${remainingQuestions} soal`}
          </span>
          {passingGrade && (
            <span className="text-tiny whitespace-nowrap">
              Target skor: <span className="font-medium">{passingGrade}</span>
            </span>
          )}
        </div>
        
        {!state.loading && userId && examId && (
          <ExamCompletionIndicator
            examId={examId}
            trialCompletionData={trialCompletionData}
          />
        )}
      </CardFooter>
    </Card>
  );
};

ExamCard.displayName = 'ExamCard';

// Use React.memo only once when exporting
export default React.memo(ExamCard);