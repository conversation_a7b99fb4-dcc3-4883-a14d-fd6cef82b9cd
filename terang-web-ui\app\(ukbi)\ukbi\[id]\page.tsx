"use server";

import React from "react";

import { Exam } from "@/components/language-exam";
import { fetchExamQuestionsBySessionId } from "@/components/my-exams/actions";
import { auth } from "@/auth";
import { Option, Content } from "@/components/types";
import { storeToRedis, retrieveFromRedis } from "@/components/shared/actions";

// Move this function outside of the page component
async function getQuestionByIndex(sessionId: string, index: number) {
  "use server";
  const redisKey = `${sessionId}_examQuestions`;
  let parsedData = await retrieveFromRedis(redisKey);

  if (!parsedData) {
    throw new Error("Question data not found");
  }

  parsedData = JSON.parse(parsedData);

  if (index < 0 || index >= parsedData.length) {
    throw new Error("Question index out of bounds");
  }

  const { hints, explanation, ...sanitizedQuestion } = parsedData[index];

  return sanitizedQuestion;
}

export default async function ExamPage(props: { params: Promise<{ id: string }> }) {
  // const params = await props.params;
  // try {
  //   console.log("Starting exam component");

  //   const session = await auth();

  //   if (!session || !session.user) {
  //     console.log("Unauthorized access attempt");

  //     return <div>Unauthorized</div>;
  //   }

  //   const { id } = params;

  //   console.log(`Fetching exam questions for session ID: ${id}`);

  //   const redisKey = `${id}_examQuestions`;
  //   let parsedData = await retrieveFromRedis(redisKey);

  //   if (!parsedData) {
  //     const questionData: any = await fetchExamQuestionsBySessionId(id);

  //     if (
  //       !questionData ||
  //       !questionData.data ||
  //       questionData.data.length === 0
  //     ) {
  //       console.log("No question data found");

  //       return <div>No exam questions found</div>;
  //     }

  //     try {
  //       parsedData = JSON.parse(questionData.data[0].data);
  //       await storeToRedis(redisKey, JSON.stringify(parsedData));
  //     } catch (parseError) {
  //       console.error("Error parsing question data:", parseError);

  //       return <div>Error parsing exam data</div>;
  //     }
  //   } else {
  //     parsedData = JSON.parse(parsedData);
  //   }

  //   // Sanitize the data
  //   const sanitizedData = parsedData.map((question: any) => {
  //     const { hints, explanation, ...sanitizedQuestion } = question;

  //     return {
  //       ...sanitizedQuestion,
  //       options: {
  //         ...sanitizedQuestion.options,
  //         values: sanitizedQuestion.options.values.map((option: Option) => ({
  //           id: option.id,
  //           data: option.data.map((item) => ({
  //             contents: item.contents.map((content: Content) => ({
  //               content: content.content,
  //               type: content.type,
  //             })),
  //           })),
  //           is_correct: option.is_correct,
  //         })),
  //       },
  //     };
  //   });

  //   // Get the total number of questions
  //   const totalQuestions = sanitizedData.length;

  //   // Store sanitized data in Redis
  //   await storeToRedis(redisKey, JSON.stringify(sanitizedData));

  const sanitizedData = [
    {
        "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_001",
        "insight": [],
        "options": {
            "values": [
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_001_a",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Pariwisata yang fokus pada pertumbuhan ekonomi",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_001_b",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Pariwisata yang menyeimbangkan kepentingan ekonomi, sosial, dan lingkungan",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": true
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_001_c",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Pariwisata yang dikelola oleh masyarakat lokal",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_001_d",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Pariwisata yang dipromosikan oleh pemerintah",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_001_e",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Pariwisata berbasis teknologi digital",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                }
            ],
            "shuffle": true
        },
        "metadata": [
            {
                "name": "institute",
                "level": 0,
                "value": "UKBI"
            },
            {
                "name": "subject",
                "level": 1,
                "value": "Membaca"
            },
            {
                "name": "category",
                "level": 2,
                "value": "Pemahaman"
            },
            {
                "name": "package_name",
                "level": 1000,
                "value": "UKBI Level 5"
            },
            {
                "name": "keywords",
                "level": 100,
                "value": "pariwisata;lingkungan"
            }
        ],
        "question": [
            {
                "contents": [
                    {
                        "type": "text",
                        "content": "Apa yang dimaksud dengan pariwisata berkelanjutan menurut bacaan?"
                    }
                ]
            }
        ],
        "instruction": [
            {
                "contents": [
                    {
                        "path": null,
                        "type": "text",
                        "content": "Bacalah dengan seksama bacaan berikut untuk menjawab soal-soal di bawah!"
                    }
                ]
            },
            {
                "contents": [
                    {
                        "path": null,
                        "type": "text",
                        "content": "Pariwisata berkelanjutan adalah konsep yang berupaya menyeimbangkan kepentingan ekonomi, sosial, dan lingkungan. Di Indonesia, beberapa daerah telah mulai menerapkan prinsip-prinsip pariwisata berkelanjutan. Misalnya, desa wisata di Bali yang mempromosikan konsep \"subak\" atau sistem irigasi tradisional yang ramah lingkungan.\n\nPemerintah Indonesia juga telah mengambil langkah-langkah untuk mendorong pariwisata berkelanjutan melalui berbagai program, seperti sertifikasi wisata ramah lingkungan dan kampanye sadar wisata. Selain itu, pelibatan masyarakat lokal dalam pengembangan pariwisata menjadi kunci penting untuk memastikan bahwa manfaat ekonomi dapat dirasakan langsung oleh penduduk setempat.\n\nMeskipun demikian, masih banyak tantangan yang dihadapi dalam implementasi pariwisata berkelanjutan di Indonesia. Salah satunya adalah kurangnya kesadaran wisatawan tentang pentingnya menjaga kelestarian lingkungan. Oleh karena itu, edukasi kepada wisatawan dan pelaku industri pariwisata perlu ditingkatkan."
                    }
                ]
            }
        ],
        "hints": [], 
        "explanation": [
            {
                "contents": [
                    {
                        "type": "text",
                        "content": "Jawaban yang benar adalah 'Pariwisata yang menyeimbangkan kepentingan ekonomi, sosial, dan lingkungan'. Ini dinyatakan pada kalimat pertama bacaan: 'Pariwisata berkelanjutan adalah konsep yang berupaya menyeimbangkan kepentingan ekonomi, sosial, dan lingkungan.'"
                    }
                ]
            }
        ]
    },
    {
        "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_media_002",
        "insight": [],
        "options": {
            "values": [
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_media_002_a",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Nyepi",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_media_002_b",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Kasada/Kesodo",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": true
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_media_002_c",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Ngaben",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_media_002_d",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Grebeg Suro",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_media_002_e",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Sekaten",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                }
            ],
            "shuffle": true
        },
        "metadata": [
            {
                "name": "institute",
                "level": 0,
                "value": "UKBI"
            },
            {
                "name": "subject",
                "level": 1,
                "value": "Membaca"
            },
            {
                "name": "category",
                "level": 2,
                "value": "Budaya"
            },
            {
                "name": "package_name",
                "level": 1000,
                "value": "UKBI Level 5"
            },
            {
                "name": "keywords",
                "level": 100,
                "value": "upacara;tradisi;tengger"
            }
        ],
        "question": [
            {
                "contents": [
                    {
                        "type": "text",
                        "content": "Upacara adat yang tampak pada gambar di atas adalah..."
                    }
                ]
            }
        ],
        "instruction": [
            {
                "contents": [
                    {
                        "path": null,
                        "type": "text",
                        "content": "Perhatikan gambar berikut, kemudian jawablah pertanyaan di bawah ini."
                    }
                ]
            },
            {
                "contents": [
                    {
                        "path": "files/ukbi/upacara_kesodo.jpg",
                        "type": "media",
                        "content": "https://example.com/files/ukbi/upacara_kesodo.jpg"
                    }
                ]
            }
        ],
        "hints": [],
        "explanation": [
            {
                "contents": [
                    {
                        "type": "text",
                        "content": "Jawaban yang benar adalah 'Kasada/Kesodo'. Gambar menunjukkan upacara adat Kasada yang dilakukan oleh masyarakat Tengger di kawasan Gunung Bromo, dimana mereka membawa sesaji ke kawah gunung."
                    }
                ]
            }
        ]
    },
    {
        "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_audio_003",
        "insight": [],
        "options": {
            "values": [
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_audio_003_a",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Kamis pagi",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_audio_003_b",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Kamis siang",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_audio_003_c",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Jumat pagi",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": true
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_audio_003_d",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Jumat sore",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                },
                {
                    "id": "eyJpdiI6IjFrcEVsZnRGXC9ONWhYeEFMd1o5UjJnPT0iLCJ2YWx1ZSI6IkJtSTNpTTh1aHNyMlR1dWN1bU9GK1E9PSIsIm1hYyI6IjU3MWY2ODE5ZjU5NjU5YzU3NmU5OTY5N2E3ZjI5NDRkNDZkYjkzZDI2OTY2ZmE1YTI4YWY2ZDg4OWQ3YTY0NGUifQ==_text_audio_003_e",
                    "data": [
                        {
                            "contents": [
                                {
                                    "content": "Sabtu pagi",
                                    "type": "text"
                                }
                            ]
                        }
                    ],
                    "is_correct": false
                }
            ],
            "shuffle": true
        },
        "metadata": [
            {
                "name": "institute",
                "level": 0,
                "value": "UKBI"
            },
            {
                "name": "subject",
                "level": 1,
                "value": "Mendengarkan"
            },
            {
                "name": "category",
                "level": 2,
                "value": "Percakapan"
            },
            {
                "name": "package_name",
                "level": 1000,
                "value": "UKBI Level 5"
            },
            {
                "name": "keywords",
                "level": 100,
                "value": "wisata;yogyakarta;percakapan"
            }
        ],
        "question": [
            {
                "contents": [
                    {
                        "type": "text",
                        "content": "Menurut percakapan, kapan mereka berencana berangkat ke Yogyakarta?"
                    }
                ]
            }
        ],
        "instruction": [
            {
                "contents": [
                    {
                        "path": null,
                        "type": "text",
                        "content": "Dengarkan rekaman berikut, kemudian jawablah pertanyaan di bawahnya."
                    }
                ]
            },
            {
                "contents": [
                    {
                        "path": "files/ukbi/dialog_yogyakarta.mp3",
                        "type": "audio",
                        "content": "https://example.com/files/ukbi/dialog_yogyakarta.mp3"
                    }
                ]
            }
        ],
        "hints": [],
        "explanation": [
            {
                "contents": [
                    {
                        "type": "text",
                        "content": "Jawaban yang benar adalah 'Jumat pagi'. Dalam percakapan audio, orang kedua mengatakan \"Saya usulkan hari Jumat pagi. Kita bisa sampai siang dan langsung mengunjungi Keraton.\""
                    }
                ]
            }
        ]
    }
]

const id = 'test'
const totalQuestions = 3;
    return (
      <Exam
        getQuestionByIndex={getQuestionByIndex}
        sanitizedData={sanitizedData}
        sessionId={id}
        totalQuestions={totalQuestions}
      />
    );
  }