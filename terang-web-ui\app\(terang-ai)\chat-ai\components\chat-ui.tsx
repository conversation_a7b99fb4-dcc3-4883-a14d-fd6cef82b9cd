"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { ConversationStyle, IChatGPTPayload } from "@/app/api/azure-openai/[id]/route";
import { Header } from "./header";
import { PromptInput } from "./prompt-input";
import ReactMarkdown from "react-markdown";
import { Prism as <PERSON><PERSON>ta<PERSON><PERSON><PERSON><PERSON>er } from "react-syntax-highlighter";
import { tomorrow } from "react-syntax-highlighter/dist/esm/styles/prism";
import { Components } from "react-markdown";
import remarkGfm from 'remark-gfm'; 
import rehypeKatex from "rehype-katex";
import remarkMath from "remark-math";
import { usePathname } from "next/navigation";
import DotLottieAnimation from "@/components/shared/dotlottie-animation";
import { deductAITokens, getAITokenBalance } from "@/app/(pricing)/subscription/actions";
import Link from 'next/link';
import formatMessagesForAPI from "./format-message";
import { useInView } from 'react-intersection-observer';
import VideoPlayer from './video-player';

import 'katex/dist/katex.min.css'

interface Message {
  type: 'user' | 'assistant';
  content: string | React.ReactNode;
  hasVideo?: boolean;
}

interface Props {
  id: string;
  fromPage: string;
  systemContext?: string;
  selectedOptions?: string;
}

// Define proper types for the code component props
interface CodeProps {
  node?: any;
  inline?: boolean;
  className?: string;
  children?: React.ReactNode;
}

interface CustomVideoProps {
  src?: string;
  title?: string;
  className?: string;
  children?: React.ReactNode;
}

interface CustomParagraphProps {
  children?: React.ReactNode;
}

interface TableContainerProps {
  children: React.ReactNode;
}

// First let's create the ChatScrollAnchor component
interface ChatScrollAnchorProps {
  trackVisibility: boolean;
  isAtBottom: boolean;
  scrollAreaRef: React.RefObject<HTMLDivElement>;
}

interface ScrollAnchorProps {
  messages: Message[];
  isLoading: boolean;
  scrollAreaRef: React.RefObject<HTMLDivElement | null>;
  setShowScrollButton: React.Dispatch<React.SetStateAction<boolean>>;
}

const ChatScrollAnchor: React.FC<ScrollAnchorProps> = ({ messages, isLoading, scrollAreaRef, setShowScrollButton }) => {
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);
  const { ref, inView } = useInView({
    threshold: 0,
  });

  useEffect(() => {
    if (!scrollAreaRef.current || !shouldAutoScroll) return;

    const scrollToBottom = () => {
      const scrollArea = scrollAreaRef.current;
      if (!scrollArea) return;

      requestAnimationFrame(() => {
        scrollArea.scrollTo({
          top: scrollArea.scrollHeight,
          behavior: isLoading ? 'auto' : 'smooth'
        });
      });
    };

    // Only scroll when there are actual changes
    if (messages.length > 0 || isLoading) {
      scrollToBottom();
    }
  }, [messages, isLoading, shouldAutoScroll, scrollAreaRef]);

  useEffect(() => {
    const scrollArea = scrollAreaRef.current;
    if (!scrollArea) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = scrollArea;
      const isAtBottom = Math.abs(scrollHeight - clientHeight - scrollTop) < 50;
      const distanceFromBottom = scrollHeight - clientHeight - scrollTop;

      setShouldAutoScroll(isAtBottom);
      setShowScrollButton(distanceFromBottom > 100);
    };

    scrollArea.addEventListener('scroll', handleScroll);
    return () => scrollArea.removeEventListener('scroll', handleScroll);
  }, [scrollAreaRef]);

  return <div ref={ref} className="h-px w-full" />;
};

export const ChatUI: React.FC<Props> = ({ id, fromPage, systemContext, selectedOptions }) => {
  // States
  const [style, setStyle] = useState<ConversationStyle>("NEUTRAL");
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [tokenBalance, setTokenBalance] = useState<number | null>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const responseRef = useRef<string>("");
  const [emptyContainerLikeLoading, setEmptyContainerLikeLoading] = useState<boolean>(false);

  // Refs
  const assistantMessageContentRef = useRef<string>("");
  const pathname = usePathname();
  // Add a ref to track if user manually scrolled
  const userScrolledManually = useRef(false);


  const TableContainer: React.FC<TableContainerProps> = ({ children }) => (
    <div className="max-w-full overflow-hidden my-4">
      <div className="overflow-x-auto rounded-md border border-gray-200 bg-white shadow-sm">
        {children}
      </div>
    </div>
  );

  // Token Balance Handler
  useEffect(() => {
    const fetchTokenBalance = async () => {
      try {
        const response = await getAITokenBalance();
        if (response.ok && response.balance) {
          setTokenBalance(response.balance.tokenBalance);
        } else {
          console.error('Failed to fetch token balance:', response.message);
          setTokenBalance(0);
        }
      } catch (error) {
        console.error('Error fetching token balance:', error);
        setTokenBalance(0);
      }
    };
    fetchTokenBalance();
  }, []);

  const getWelcomeMessage = () => {
    switch (fromPage) {
      case 'result':
        return (
          <>
            <div className="flex justify-center items-center mb-4">
              <DotLottieAnimation
                src="https://cdn.terang.ai/dotlotties/ai-chat-start.lottie"
                autoplay
                loop
                width={"100%"}
                height={"100%"}
              />
            </div>
            Halo! 👋 Aku sudah melihat jawabannya. Apa yang ingin kamu tanyakan? Aku bisa membantu menjelaskan langkah-langkah gimana cara solve soal ini atau bagian tertentu yang masih membingungkan.
          </>
        );
      case 'general':
      case 'navbar':
        return (
          <>
            <div className="flex justify-center items-center mb-4">
              <DotLottieAnimation
                src="https://cdn.terang.ai/dotlotties/ai-chat-start.lottie"
                autoplay
                loop
                width={"50%"}
                height={"50%"}
              />
            </div>
            Halo! 👋 Aku asisten Terang AI yang siap membantumu. Silakan tanyakan apa saja yang ingin kamu ketahui!
          </>
        );
      default:
        return (
          <>
            <div className="flex justify-center items-center mb-4">
              <DotLottieAnimation
                src="https://cdn.terang.ai/dotlotties/ai-chat-start.lottie"
                autoplay
                loop
                width={"50%"}
                height={"50%"}
              />
            </div>
            Halo! 👋 Ada yang bisa Aku bantu hari ini?
          </>
        );
    }
  };

  // Chat History Handler
  useEffect(() => {
    const fetchChatHistory = async () => {
      try {
        const response = await fetch(`/api/azure-openai/${id}/chat-history`);
        if (!response.ok) throw new Error('Failed to fetch chat history');

        const history = await response.json();
        const formattedMessages = history
          .filter((msg: any) => msg.role !== 'system')
          .map((msg: any) => ({
            type: msg.role === 'user' ? 'user' : 'assistant',
            content: msg.content
          }));

        if (formattedMessages.length === 0) {
          formattedMessages.push({
            type: 'assistant',
            content: getWelcomeMessage()
          });
        }

        setMessages(formattedMessages);
      } catch (err) {
        console.error('Error fetching chat history:', err);
        setError('Failed to load chat history');
        
        setMessages([{
          type: 'assistant',
          content: getWelcomeMessage()
        }]);
      }
    };

    fetchChatHistory();
  }, [id, fromPage]);

  const preprocessContent = (content: string | React.ReactNode): string | React.ReactNode => {
    if (typeof content !== 'string') return content
    
    let processedContent = content
    .replace(/\\#/g, '#')  // Replace escaped hash signs
    // Fix LaTeX notation
    .replace(/\\\$/g, '$')  // Replace escaped dollar signs
    .replace(/\\\{/g, '{')  // Replace escaped curly braces
    .replace(/\\\}/g, '}')
    .replace(/\\\\/g, '\\') // Replace double backslashes with single
    .replace(/\\text\{([^}]*)\}/g, '\\text{$1}') // Fix text commands
    .replace(/\^{}\{/g, '^{')  // Fix superscript notation
    .replace(/\^\{\}(\d)/g, '^{$1}'); // Fix numbered superscripts

    return processedContent;
  };

  const getTokenCount = async (text: string): Promise<number> => {
    try {
      const response = await fetch('/tokenizer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text }),
      });

      if (!response.ok) {
        throw new Error(`Token count failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data.tokenCount;
    } catch (error) {
      console.error('Error getting token count:', error);
      throw error;
    }
  };

  const promptChatGPT = async (payload: IChatGPTPayload): Promise<void> => {
    try {
      // Set loading at the start of the request
      setIsLoading(true);
      setEmptyContainerLikeLoading(true)
      setError("");
      console.log(isLoading)

      // Add user message immediately
      const newMessage: Message = { type: "user", content: payload.prompt };
      setMessages(prev => [...prev, newMessage]);

      const messageHistory = [...messages, newMessage].map(msg => ({
        role: msg.type === "user" ? "user" : "assistant",
        content: typeof msg.content === 'string' ? msg.content : ''
      }));

      const response = await fetch(`/api/azure-openai/${id}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          ...payload,
          messageHistory: formatMessagesForAPI(messageHistory, systemContext),
          fromPage,
          systemContext,
          selectedOptions
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error("Response body is null");

      const decoder = new TextDecoder();
      let completeResponse = "";

      // Start with an empty assistant message
      setMessages(prev => [...prev, { type: "assistant", content: "" }]);

      while (true) {
        setIsLoading(false);
        const { value, done } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value);
        completeResponse += chunk;
        
        // Update the last message with accumulated content
        setMessages(prev => {
          const newMessages = [...prev];
          if (newMessages[newMessages.length - 1]?.type === 'assistant') {
            newMessages[newMessages.length - 1].content = completeResponse;
          }
          return newMessages;
        });

        // Small delay for smooth rendering
        await new Promise(resolve => setTimeout(resolve, 5));
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      // Remove the last assistant message if there was an error
      setMessages(prev => prev.filter(msg => msg.type !== 'assistant' || msg.content !== ""));
    } finally {
      // Clear loading state after everything is done
      setIsLoading(false);
      setEmptyContainerLikeLoading(false)
    }
  };
  
  const markdownComponents: Components = {
    // Code blocks with syntax highlighting
    code: ({ node, inline, className, children, ...props }: CodeProps) => {
      const match = /language-(\w+)/.exec(className || '');
      return !inline && match ? (
        <div className="max-w-full overflow-hidden my-4">
          <div className="overflow-x-auto">
            <SyntaxHighlighter
              style={tomorrow}
              language={match[1]}
              PreTag="div"
              className="rounded-md"
              customStyle={{
                margin: 0,
                width: 'fit-content',
                minWidth: '100%'
              }}
              {...props}
            >
              {String(children).replace(/\n$/, '')}
            </SyntaxHighlighter>
          </div>
        </div>
      ) : (
        <code className={`${className} bg-gray-100 rounded px-1 break-words`} {...props}>
          {children}
        </code>
      );
    },
    // Headings
    h1: ({ children }) => (
      <h1 className="text-2xl font-bold mb-4 mt-6">{children}</h1>
    ),
    h2: ({ children }) => (
      <h2 className="text-xl font-bold mb-3 mt-5">{children}</h2>
    ),
    h3: ({ children }) => (
      <h3 className="text-lg font-bold mb-2 mt-4">{children}</h3>
    ),
    h4: ({ children }) => (
      <h4 className="text-base font-bold mb-2 mt-4">{children}</h4>
    ),
    h5: ({ children }) => (
      <h5 className="text-sm font-bold mb-2 mt-4">{children}</h5>
    ),
    h6: ({ children }) => (
      <h6 className="text-xs font-bold mb-2 mt-4">{children}</h6>
    ),
    
    // Updated list components with proper LaTeX handling
    ul: ({ children }) => (
      <ul className="mb-4 ml-4 space-y-2 list-disc last:mb-0 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
        {children}
      </ul>
    ),
    ol: ({ children }) => (
      <ol className="mb-4 ml-4 space-y-2 list-decimal last:mb-0 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
        {children}
      </ol>
    ),
    li: ({ children }) => (
      <li className="ml-4 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
        {children}
      </li>
    ),

    p: (props: any) => {
      const children = props.children;
      
      // Helper function to extract text and handle special cases
      const extractText = (node: any): string => {
        if (typeof node === 'string') return node;
        if (Array.isArray(node)) return node.map(extractText).join(' ');
        if (node?.props?.children) return extractText(node.props.children);
        return '';
      };
    
      // Get combined text from all children
      const text = extractText(children);
      
      // Use a more robust pattern with custom delimiters
      // Format: @video{title}(url)
      const videoPattern = /@video\{([^}]*)\}\(([^)]*)\)/;
      const match = text.match(videoPattern);
    
      if (match) {
        const [_, title, url] = match;
        return (
          <div className="my-4">
            <VideoPlayer 
              src={url}
              title={title}
              className="w-full rounded-lg"
            />
          </div>
        );
      }
    
      // Regular paragraph rendering
      return (
        <p className="whitespace-pre-wrap break-words mb-4 last:mb-0 prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
          {children}
        </p>
      );
    },
    
    // Blockquote
    blockquote: ({ children }) => (
      <blockquote className="border-l-4 border-gray-200 pl-4 my-4 italic last:mb-0">
        {children}
      </blockquote>
    ),
    pre: ({ children }) => (
      <pre className="font-mono text-sm bg-gray-50 p-4 rounded-lg overflow-x-auto whitespace-pre my-4">
        {children}
      </pre>
    ),
    
    // Inline elements
    strong: ({ children }) => (
      <strong className="font-bold">{children}</strong>
    ),
    em: ({ children }) => (
      <em className="italic">{children}</em>
    ),
    del: ({ children }) => (
      <del className="line-through">{children}</del>
    ),
    
    // Links and images
    a: ({ href, children }) => (
      <a 
        href={href} 
        className="text-blue-600 hover:text-blue-800 underline"
        target="_blank"
        rel="noopener noreferrer"
      >
        {children}
      </a>
    ),
    img: ({ src, alt }) => (
      <img 
        src={src} 
        alt={alt} 
        className="max-w-full h-auto my-4 rounded-lg"
        loading="lazy"
      />
    ),
    
    table: ({ children }) => (
      <TableContainer>
        <table className="min-w-full divide-y divide-gray-200 font-mono text-sm">
          {children}
        </table>
      </TableContainer>
    ),
    
    thead: ({ children }) => (
      <thead className="bg-gray-50 border-b border-gray-200">
        {children}
      </thead>
    ),
    
    tbody: ({ children }) => (
      <tbody className="divide-y divide-gray-200 bg-white">
        {children}
      </tbody>
    ),
    
    tr: ({ children, className }) => (
      <tr className={`${className || ''} hover:bg-gray-50`}>
        {children}
      </tr>
    ),
    
    th: ({ children }) => (
      <th className="px-6 py-3 text-center font-medium text-gray-900 whitespace-nowrap border-x border-gray-200">
        {children}
      </th>
    ),
    
    td: ({ children }) => (
      <td className="px-6 py-3 text-center font-mono whitespace-nowrap border-x border-gray-200">
        {children}
      </td>
    ),
    
    // Horizontal rule
    hr: () => (
      <hr className="my-8 border-t border-gray-200" />
    ),
    
    // Definition lists
    dl: ({ children }) => (
      <dl className="mb-4 space-y-2">{children}</dl>
    ),
    dt: ({ children }) => (
      <dt className="font-bold">{children}</dt>
    ),
    dd: ({ children }) => (
      <dd className="ml-4">{children}</dd>
    ),
    
    // Additional inline elements
    sup: ({ children }) => (
      <sup className="text-xs align-super">{children}</sup>
    ),
    sub: ({ children }) => (
      <sub className="text-xs align-sub">{children}</sub>
    ),
    kbd: ({ children }) => (
      <kbd className="px-2 py-1.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">
        {children}
      </kbd>
    ),
    mark: ({ children }) => (
      <mark className="bg-yellow-200 px-1 rounded">
        {children}
      </mark>
    )
  };

  return (
    <div className="absolute inset-0 flex flex-col">
      {pathname?.startsWith('/chat-ai/general/') || pathname?.startsWith('/chat-ai/navbar/') ? (
        <div className="flex-none p-4 pt-[5.5rem] border-b border-gray-200 bg-white">
          <Header />
        </div>
      ) : (
        <div className="flex-none p-4 border-b border-gray-200 bg-white">
          <Header />
        </div>
      )}

      <div ref={scrollAreaRef} className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message, index) => (
          <div
            key={index}
            className={`p-3 rounded-lg ${
              message.type === 'user' ? 'bg-blue-100 ml-auto' : 'bg-gray-100'
            } max-w-[90%] break-words`}
          >
            <div className="prose prose-sm max-w-none [&_.katex-display]:my-0 [&_.katex]:my-0">
              {typeof message.content === 'string' ? (
                <ReactMarkdown
                  remarkPlugins={[remarkGfm, remarkMath]}
                  rehypePlugins={[rehypeKatex]}
                  components={markdownComponents}
                >
                  {preprocessContent(message.content) as string}
                </ReactMarkdown>
              ) : (
                message.content
              )}
            </div>
          </div>
        ))}

        {/* Only show loading indicator when isLoading is true AND there's no current assistant message being streamed */}
        {emptyContainerLikeLoading && !messages.some(m => m.type === 'assistant' && m.content === "") && (
          <div className="">
          </div>
        )}

        <ChatScrollAnchor
          messages={messages}
          isLoading={emptyContainerLikeLoading}
          scrollAreaRef={scrollAreaRef}
          setShowScrollButton={setShowScrollButton}
        />

        {/* Only show loading indicator when isLoading is true AND there's no current assistant message being streamed */}
        {isLoading && !messages.some(m => m.type === 'assistant' && m.content === "") && (
          <div className="rounded-lg bg-gray-100 text-gray-500">
            <DotLottieAnimation
              src="https://cdn.terang.ai/dotlotties/loading.lottie"
              width="100px"
              height="65px"
            />
          </div>
        )}

        {tokenBalance !== null && tokenBalance <= 0 && (
          <div className="text-center p-6 bg-gradient-to-r from-grey-50 to-orange-50 rounded-lg border border-grey-200 shadow-sm">
            <DotLottieAnimation
              src="https://cdn.terang.ai/dotlotties/purchase-ai-credit.lottie"
              width="100%"
              height="100%"
            />
            <p className="text-yellow-800 font-medium text-lg mb-3">
              Sayangnya, kredit AI kamu sudah habis
            </p>
            <Link 
              href="/subscription" 
              className="inline-flex items-center px-5 py-2.5 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              Beli Kredit AI
            </Link>
          </div>
        )}

        {error && (
          <div className="text-red-500 break-words p-3">{error}</div>
        )}
      </div>

      {showScrollButton && (
        <button
          onClick={() => {
            if (scrollAreaRef.current) {
              scrollAreaRef.current.scrollTo({
                top: scrollAreaRef.current.scrollHeight,
                behavior: 'smooth'
              });
            }
          }}
          className="fixed bottom-24 right-4 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-200 z-50 flex items-center justify-center transform hover:scale-110"
          aria-label="Scroll to bottom"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-6 w-6" 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path 
              fillRule="evenodd" 
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" 
              clipRule="evenodd" 
            />
          </svg>
        </button>
      )}

      {/* Input Area */}
      <div className="flex-none p-4 border-t border-gray-200 bg-white">
        <PromptInput
          isLoading={isLoading}
          disabled={tokenBalance !== null && tokenBalance <= 0}
          onSubmit={(prompt) =>
            promptChatGPT({
              conversationStyle: style,
              prompt,
              messageHistory: [],
            })
          }
        />
      </div>
    </div>
  );
};

export default ChatUI;