'use client';

import React, { useState, useEffect, useCallback, useMemo, useRef, JSX } from 'react';
import { Card, Avatar, Chip, Tooltip } from "@heroui/react";
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { getSubscriptionTiers, getUserSubscription, checkSubscriptionRemainingTime } from './actions';
import { CrownIcon } from 'hugeicons-react';
import PremiumButton from './premium-button';
import { getAITokenBalance } from '@/app/(pricing)/subscription/actions';

// Debug flag - only active in development
const IS_DEV = process.env.NODE_ENV === 'development';

// Debug utilities
const debugLog = (...args: unknown[]): void => {
  // if (IS_DEV) {
  //   console.log(`[SubscriptionDebug]`, ...args);
  // }
};

// Enhanced debug log that always logs for tracking fetches
const logFetch = (...args: any[]) => {
  if (IS_DEV) {
    console.log(`[FetchDebug]`, ...args);
  }
};

// Interface types
interface TierDetails {
  id: string;
  price: number;
  billingPeriod: 'monthly' | 'yearly';
  features: string[];
  name: string;
}

interface Subscription {
  tierId: string;
  startDate: string;
  nextBillingDate: string;
  isActive: boolean;
  paymentStatus?: 'PAID' | 'PENDING' | 'FAILED';
}

interface TokenBalance {
  id: string;
  userId: string | boolean | null;
  tokenBalance: number;
  metadata: string;
  createdAt: string;
  modifiedAt: string;
}

interface CachedItem<T> {
  value: T;
  expiry: number;
  timestamp: number;
}

type ChipColorType = "secondary" | "warning" | "danger" | "default" | "primary" | "success" | undefined;

interface RawTierData {
  id?: string;
  name?: string;
  priceMonthly?: number;
  billingInterval?: string;
  features?: string | string[] | Record<string, any>;
  [key: string]: any;
}

// Cache configuration
const CACHE_KEYS = {
  SUBSCRIPTION: 'user_subscription',
  TIERS: 'subscription_tiers',
  TOKENS: 'token_balance',
  TIME: 'remaining_time'
} as const;

type CacheKey = typeof CACHE_KEYS[keyof typeof CACHE_KEYS];

const CACHE_EXPIRY: Record<CacheKey, number> = {
  [CACHE_KEYS.SUBSCRIPTION]: 15 * 60 * 1000, // 15 minutes
  [CACHE_KEYS.TIERS]: 60 * 60 * 1000, // 1 hour
  [CACHE_KEYS.TOKENS]: 5 * 60 * 1000, // 5 minutes
  [CACHE_KEYS.TIME]: 30 * 60 * 1000 // 30 minutes
};

// Debug: Fetch counter to track API calls
const fetchCounters = {
  [CACHE_KEYS.SUBSCRIPTION]: 0,
  [CACHE_KEYS.TIERS]: 0,
  [CACHE_KEYS.TOKENS]: 0,
  [CACHE_KEYS.TIME]: 0
};

const DEFAULT_FREE_SUBSCRIPTION: Subscription = {
  tierId: 'free_tier_001',
  startDate: new Date().toISOString(),
  nextBillingDate: new Date().toISOString(),
  isActive: true,
  paymentStatus: 'PAID'
};

// Utility functions
const formatTokenBalance = (balance: number): string => {
  const formatted = balance / 1000;
  return isNaN(formatted) ? 'Loading...' : formatted.toString();
};

const getChipColor = (subscription?: Subscription | null): ChipColorType => {
  if (!subscription) return 'default';
  
  if (subscription.tierId === 'premium_tier_001') {
    switch (subscription.paymentStatus) {
      case 'PAID': return 'secondary';
      case 'PENDING': return 'warning';
      case 'FAILED': return 'danger';
      default: return 'default';
    }
  }
  return subscription.tierId === 'free_tier_001' ? 'primary' : 'default';
};

// Cache functions for client-side storage
const cacheData = <T,>(key: CacheKey, value: T): void => {
  if (typeof window === 'undefined') return;
  
  const now = Date.now();
  const expiryTime = now + (CACHE_EXPIRY[key] || 5 * 60 * 1000);
  
  const item: CachedItem<T> = {
    value,
    expiry: expiryTime,
    timestamp: now
  };
  
  try {
    localStorage.setItem(key, JSON.stringify(item));
    debugLog(`Data cached: ${key}`, value);
  } catch (e) {
    console.warn('Error caching data:', e);
  }
};

const getCachedData = <T,>(key: CacheKey): { value: T, timestamp: number } | null => {
  if (typeof window === 'undefined') return null;
  
  try {
    const itemStr = localStorage.getItem(key);
    if (!itemStr) return null;
    
    const item = JSON.parse(itemStr) as CachedItem<T>;
    if (Date.now() > item.expiry) {
      localStorage.removeItem(key);
      debugLog(`Cache expired: ${key}`);
      return null;
    }
    
    debugLog(`Cache hit: ${key}`, item.value);
    return {
      value: item.value,
      timestamp: item.timestamp
    };
  } catch (e) {
    localStorage.removeItem(key);
    debugLog(`Cache error: ${key}`, e);
    return null;
  }
};

// Simplified loading component
const LoadingSkeleton = (): JSX.Element => (
  <Card className="mx-1 mb-4 bg-background/60 backdrop-blur-lg">
    <div className="p-4 flex items-center gap-3">
      <div className="animate-pulse w-10 h-10 rounded-full bg-default-200" />
      <div className="flex-1 space-y-2">
        <div className="animate-pulse h-4 w-1/3 bg-default-200 rounded" />
        <div className="animate-pulse h-3 w-2/3 bg-default-200 rounded" />
      </div>
    </div>
  </Card>
);

const UnauthenticatedCard = (): JSX.Element => (
  <Card className="mx-1 mb-4 bg-background/60 backdrop-blur-lg p-4">
    <p className="text-default-500 text-sm">Please sign in to view subscription details</p>
  </Card>
);

// Main component (optimized for Next.js 15)
const SubscriptionTierCard = (): JSX.Element => {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  // Component state with default values
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [tierDetails, setTierDetails] = useState<TierDetails[]>([]);
  const [tokenBalance, setTokenBalance] = useState<TokenBalance | null>(null);
  const [remainingTime, setRemainingTime] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  
  // IMPORTANT FIX: Add last fetch timing tracking
  const lastFetchTimeRef = useRef<Record<CacheKey, number>>({
    [CACHE_KEYS.SUBSCRIPTION]: 0,
    [CACHE_KEYS.TIERS]: 0,
    [CACHE_KEYS.TOKENS]: 0,
    [CACHE_KEYS.TIME]: 0
  });
  
  // Refs to manage component lifecycle
  const isMountedRef = useRef<boolean>(true);
  const abortControllerRef = useRef<AbortController>(new AbortController());
  const timersRef = useRef<number[]>([]);
  
  // FIX 1: Add utility functions for debounce and throttle to reduce excessive API calls
  const debounce = useCallback((fn: Function, ms = 300) => {
    let timeoutId: number;
    return function(this: any, ...args: any[]) {
      clearTimeout(timeoutId);
      timeoutId = window.setTimeout(() => fn.apply(this, args), ms);
      timersRef.current.push(timeoutId);
    };
  }, []);
  
  const throttle = useCallback((fn: Function, delay = 300) => {
    let lastCall = 0;
    return function(this: any, ...args: any[]) {
      const now = Date.now();
      if (now - lastCall < delay) return;
      lastCall = now;
      return fn.apply(this, args);
    };
  }, []);
  
  // FIX 2: Create a simplified fetch function with proper caching and throttling
  const cachedFetch = useCallback(async <T,>(
    key: CacheKey,
    fetchFn: () => Promise<any>,
    minInterval: number = 120000 // 2 minutes minimum between fetches by default
  ): Promise<T | null> => {
    if (!session?.user || !isMountedRef.current) return null;
    
    // Check if we've fetched this data recently
    const now = Date.now();
    const lastFetch = lastFetchTimeRef.current[key] || 0;
    
    if (now - lastFetch < minInterval) {
      logFetch(`SKIPPED fetch for ${key}: too soon (${Math.round((now - lastFetch)/1000)}s < ${Math.round(minInterval/1000)}s)`);
      return null;
    }
    
    // Update last fetch time
    lastFetchTimeRef.current[key] = now;
    
    // Increment the fetch counter for debugging
    fetchCounters[key]++;
    logFetch(`FETCHING ${key} - count: ${fetchCounters[key]}`);
    
    try {
      // Create a new AbortController for this fetch
      const controller = new AbortController();
      const timeoutId = window.setTimeout(() => controller.abort(), 15000); // 15s timeout
      timersRef.current.push(timeoutId);
      
      debugLog(`Fetching ${key} data`);
      const result = await fetchFn();
      clearTimeout(timeoutId);
      
      if (result) {
        logFetch(`SUCCESS fetched ${key} data:`, result);
        // Cache successful responses
        cacheData(key, result);
        return result as T;
      }
      
      throw new Error(`Invalid or empty response for ${key}`);
    } catch (error) {
      console.error(`Error fetching ${key}:`, error);
      return null;
    }
  }, [session]);
  
  // FIX 3: Drastically reduce the frequency of fetch operations with specific functions
  const fetchSubscription = useCallback(async () => {
    const result = await cachedFetch<{ subscription: Subscription }>(
      CACHE_KEYS.SUBSCRIPTION,
      getUserSubscription,
      120000 // Only fetch once every 2 minutes at most
    );
    
    if (result?.subscription && isMountedRef.current) {
      setSubscription(result.subscription);
    }
  }, [cachedFetch]);
  
  const validateBillingPeriod = useCallback((period?: string): "monthly" | "yearly" => {
    return period === "yearly" ? "yearly" : "monthly";
  }, []);
  
  const normalizeTiersData = useCallback((tiersData: RawTierData[]): TierDetails[] => {
    if (!tiersData || !Array.isArray(tiersData)) {
      return [];
    }

    return tiersData.map((tier: RawTierData) => {
      try {
        const normalizedTier: TierDetails = {
          id: tier.id || '',
          name: tier.name || '',
          price: typeof tier.priceMonthly === 'number' ? tier.priceMonthly / 100 : 0,
          billingPeriod: validateBillingPeriod(tier.billingInterval),
          features: []
        };
          
        if (typeof tier.features === 'string') {
          try {
            const parsedFeatures = JSON.parse(tier.features);
            const featuresList: string[] = [];
            
            if (parsedFeatures.ai_credits) {
              const credits = parsedFeatures.ai_credits;
              featuresList.push(`${credits.amount} AI credits ${credits.frequency === 'monthly' ? 'per month' : 'one-time'}`);
            }
            
            if (parsedFeatures.practice_exam) {
              const practice = parsedFeatures.practice_exam;
              featuresList.push(`${practice.hearts === 'unlimited' ? 'Unlimited' : practice.hearts_count} exam attempts`);
              featuresList.push(`${practice.packages === 'unlimited' ? 'Unlimited' : practice.packages} practice packages`);
            }
            
            if (parsedFeatures.simulation_exam?.personalized_report) {
              featuresList.push('Personalized exam reports');
            }
            
            if (parsedFeatures.priority_support) {
              featuresList.push('Priority support');
            }
            
            if (parsedFeatures.personalized_test?.available) {
              featuresList.push('Personalized testing');
            }
            
            normalizedTier.features = featuresList;
          } catch (e) {
            normalizedTier.features = ['Error parsing features'];
          }
        } else if (Array.isArray(tier.features)) {
          normalizedTier.features = tier.features;
        }
        
        return normalizedTier;
      } catch (error) {
        return {
          id: tier.id || 'unknown',
          name: tier.name || 'Unknown Tier',
          price: 0,
          billingPeriod: 'monthly',
          features: ['Error processing features']
        };
      }
    });
  }, [validateBillingPeriod]);
  
  const fetchTiers = useCallback(async () => {
    const result = await cachedFetch<{ tiers: RawTierData[] }>(
      CACHE_KEYS.TIERS,
      getSubscriptionTiers,
      300000 // Only fetch once every 5 minutes at most
    );
    
    if (result?.tiers && Array.isArray(result.tiers) && isMountedRef.current) {
      const normalizedTiers = normalizeTiersData(result.tiers);
      setTierDetails(normalizedTiers);
    }
  }, [cachedFetch, normalizeTiersData]);
  
  const fetchTokens = useCallback(async () => {
    // FIX: Drastically reduce token balance fetch frequency
    const result = await cachedFetch<{ ok: boolean, balance: TokenBalance }>(
      CACHE_KEYS.TOKENS,
      getAITokenBalance,
      180000 // Only fetch once every 3 minutes at most
    );
    
    if (result?.ok && result?.balance && isMountedRef.current) {
      setTokenBalance(result.balance);
    }
  }, [cachedFetch]);
  
  const fetchTime = useCallback(async () => {
    // Only fetch time if we have a premium subscription
    if (!(subscription?.tierId === 'premium_tier_001' && 
          subscription?.paymentStatus === 'PAID' &&
          subscription?.isActive)) {
      return;
    }
    
    const result = await cachedFetch<{ remaining_days: number, remaining_hours: number, remaining_minutes: number }>(
      CACHE_KEYS.TIME,
      checkSubscriptionRemainingTime,
      300000 // Only fetch once every 5 minutes at most
    );
    
    if (result && 'remaining_days' in result && isMountedRef.current) {
      // Format the result as a string before setting state
      const newTimeString = `${result.remaining_days} days, ${result.remaining_hours} hours, ${result.remaining_minutes} minutes`;
      setRemainingTime(newTimeString);
      
      // Also cache the formatted string
      cacheData(CACHE_KEYS.TIME, newTimeString);
    }
  }, [cachedFetch, subscription]);  

  // DEBUG COMPONENT - shows fetch status and counters
  const FetchDebugPanel = (): JSX.Element | null => {
    if (!IS_DEV) return null;
    
    const formatTime = (timestamp: number): string => {
      if (!timestamp) return 'Never';
      const date = new Date(timestamp);
      return date.toLocaleTimeString();
    };
    
    const timeUntilNextFetch = (key: CacheKey, interval: number): string => {
      const lastFetch = lastFetchTimeRef.current[key] || 0;
      if (!lastFetch) return 'Ready';
      
      const now = Date.now();
      const nextFetch = lastFetch + interval;
      const remainingMs = nextFetch - now;
      
      if (remainingMs <= 0) return 'Ready';
      return `${Math.round(remainingMs / 1000)}s`;
    };
    
    return (
      <div className="mb-3 p-2 text-xs border border-yellow-300 rounded bg-yellow-50 dark:bg-yellow-900/30 dark:border-yellow-700/50 overflow-hidden">
        <p className="font-bold border-b border-yellow-200 dark:border-yellow-800 pb-1 mb-1">Debug - Fetch Status</p>
        <div className="grid grid-cols-4 gap-1 mb-1">
          <div className="font-medium">Data Type</div>
          <div className="font-medium">Fetch Count</div>
          <div className="font-medium">Last Fetch</div>
          <div className="font-medium">Next Fetch</div>
        </div>
        <div className="grid grid-cols-4 gap-1 text-xs">
          <div>Subscription</div>
          <div>{fetchCounters[CACHE_KEYS.SUBSCRIPTION]}</div>
          <div>{formatTime(lastFetchTimeRef.current[CACHE_KEYS.SUBSCRIPTION])}</div>
          <div>{timeUntilNextFetch(CACHE_KEYS.SUBSCRIPTION, 120000)}</div>
        </div>
        <div className="grid grid-cols-4 gap-1 text-xs">
          <div>Tiers</div>
          <div>{fetchCounters[CACHE_KEYS.TIERS]}</div>
          <div>{formatTime(lastFetchTimeRef.current[CACHE_KEYS.TIERS])}</div>
          <div>{timeUntilNextFetch(CACHE_KEYS.TIERS, 300000)}</div>
        </div>
        <div className="grid grid-cols-4 gap-1 text-xs">
          <div>Token Balance</div>
          <div>{fetchCounters[CACHE_KEYS.TOKENS]}</div>
          <div>{formatTime(lastFetchTimeRef.current[CACHE_KEYS.TOKENS])}</div>
          <div>{timeUntilNextFetch(CACHE_KEYS.TOKENS, 180000)}</div>
        </div>
        <div className="grid grid-cols-4 gap-1 text-xs">
          <div>Time Remaining</div>
          <div>{fetchCounters[CACHE_KEYS.TIME]}</div>
          <div>{formatTime(lastFetchTimeRef.current[CACHE_KEYS.TIME])}</div>
          <div>{timeUntilNextFetch(CACHE_KEYS.TIME, 300000)}</div>
        </div>
        <div className="mt-1 pt-1 border-t border-yellow-200 dark:border-yellow-800 text-xs">
          <div>Premium: {isPremiumActive ? 'Yes' : 'No'}</div>
          <div>Cache Status: {subscription ? 'Subscription ✓' : '❌'} | 
          {tierDetails.length > 0 ? ' Tiers ✓' : ' ❌'} | 
          {tokenBalance ? ' Token ✓' : ' ❌'} | 
          {remainingTime ? ' Time ✓' : ' ❌'}</div>
        </div>
      </div>
    );
  };

  // FIX 4: Load cached data only once
  useEffect(() => {
    if (typeof window === 'undefined' || !session?.user) {
      setIsLoading(false);
      return;
    }
    
    // Load cached data
    const cachedSubscription = getCachedData<{ subscription: Subscription }>(CACHE_KEYS.SUBSCRIPTION);
    if (cachedSubscription?.value?.subscription) {
      setSubscription(cachedSubscription.value.subscription);
    } else {
      setSubscription(DEFAULT_FREE_SUBSCRIPTION);
    }
    
    const cachedTiers = getCachedData<{ tiers: TierDetails[] }>(CACHE_KEYS.TIERS);
    if (cachedTiers?.value?.tiers) {
      setTierDetails(cachedTiers.value.tiers);
    }
    
    const cachedTokens = getCachedData<{ ok: boolean, balance: TokenBalance }>(CACHE_KEYS.TOKENS);
    if (cachedTokens?.value?.balance) {
      setTokenBalance(cachedTokens.value.balance);
    }
    
    const cachedTime = getCachedData<string>(CACHE_KEYS.TIME);
    if (cachedTime?.value && typeof cachedTime.value === 'string') {
      setRemainingTime(cachedTime.value);
    }
    
    setIsLoading(false);
    
    // Clean up function for component unmount
    return () => {
      isMountedRef.current = false;
      abortControllerRef.current.abort();
      
      // Clear all timeouts
      timersRef.current.forEach(id => window.clearTimeout(id));
      timersRef.current = [];
    };
  }, [session]);
  
  // FIX 5: Fetch data with reduced frequency and staggered timing
  useEffect(() => {
    if (typeof window === 'undefined' || !session?.user || isLoading) return;
    
    // Initial data load - staggered to prevent network congestion
    const initialFetchSubscription = window.setTimeout(() => {
      if (isMountedRef.current) fetchSubscription();
    }, 1000);
    
    const initialFetchTiers = window.setTimeout(() => {
      if (isMountedRef.current) fetchTiers();
    }, 3000);
    
    const initialFetchTokens = window.setTimeout(() => {
      if (isMountedRef.current) fetchTokens();
    }, 5000);
    
    // Store timeout IDs for cleanup
    timersRef.current.push(initialFetchSubscription, initialFetchTiers, initialFetchTokens);
    
    // Set up periodic fetches with longer intervals
    let fetchIntervals: number[] = [];
    
    if (isMountedRef.current) {
      // Subscription - once every 3 minutes
      const subscriptionInterval = window.setInterval(() => {
        if (isMountedRef.current) fetchSubscription();
      }, 180000);
      
      // Tiers - once every 10 minutes
      const tiersInterval = window.setInterval(() => {
        if (isMountedRef.current) fetchTiers();
      }, 600000);
      
      // Tokens - once every 5 minutes
      const tokensInterval = window.setInterval(() => {
        if (isMountedRef.current) fetchTokens();
      }, 300000);
      
      fetchIntervals.push(subscriptionInterval, tiersInterval, tokensInterval);
      timersRef.current.push(...fetchIntervals);
    }
    
    return () => {
      // Clear intervals
      fetchIntervals.forEach(interval => window.clearInterval(interval));
    };
  }, [session, isLoading, fetchSubscription, fetchTiers, fetchTokens]);
  
  // FIX 6: Only fetch time when needed
  useEffect(() => {
    if (!session?.user || !isMountedRef.current) return;
    
    // Only fetch time for premium users
    if (subscription?.tierId === 'premium_tier_001' && 
        subscription?.paymentStatus === 'PAID' &&
        subscription?.isActive) {
      
      // Initial fetch
      fetchTime();
      
      // Set up interval - once every 10 minutes
      const timeInterval = window.setInterval(() => {
        if (isMountedRef.current) fetchTime();
      }, 600000);
      
      timersRef.current.push(timeInterval);
      
      return () => {
        window.clearInterval(timeInterval);
      };
    }
  }, [session, subscription, fetchTime]);
  
  // FIX 7: Memoized values with proper dependencies
  const isPremiumActive = useMemo(() => {
    return subscription?.tierId === 'premium_tier_001' && 
           subscription?.paymentStatus === 'PAID' && 
           !!subscription?.isActive;
  }, [subscription?.tierId, subscription?.paymentStatus, subscription?.isActive]);
  
  // FIX 8: Better memoized function for tier name
  const getTierName = useCallback((sub?: Subscription | null): string => {
    if (!sub) return 'Loading...';
    
    // Use fallback if no tier details
    if (!Array.isArray(tierDetails) || tierDetails.length === 0) {
      return sub.tierId === 'free_tier_001' ? 'FREE' : 
             sub.tierId === 'premium_tier_001' ? 
               (sub.paymentStatus !== 'PAID' ? 
                 `PREMIUM (${sub.paymentStatus?.toLowerCase() || 'pending'})` : 
                 'PREMIUM') : 
             'Loading...';
    }
    
    const tier = tierDetails.find(t => t.id === sub.tierId);
    
    const baseName = tier?.name || 
      (sub.tierId === 'free_tier_001' ? 'FREE' : 
       sub.tierId === 'premium_tier_001' ? 'PREMIUM' : 
       'Loading...');
    
    if (sub.tierId === 'premium_tier_001' && sub.paymentStatus !== 'PAID') {
      return `${baseName} (${sub.paymentStatus?.toLowerCase() || 'pending'})`;
    }
    
    return baseName;
  }, [tierDetails]);
  
  // FIX 9: Stable derived values
  const cardDetails = useMemo(() => {
    const isFreeTrial = subscription?.tierId === 'free_tier_001';
    const showPremiumButton = isFreeTrial || 
      (subscription?.tierId === 'premium_tier_001' && subscription?.paymentStatus !== 'PAID');
    
    return {
      isFreeTrial,
      showPremiumButton
    };
  }, [subscription?.tierId, subscription?.paymentStatus]);
  
  // Show loading skeleton or unauthenticated card
  if (status === "loading") return <LoadingSkeleton />;
  if (!session?.user) return <UnauthenticatedCard />;
  if (isLoading) return <LoadingSkeleton />;

  return (
    <Card className="mx-1 mb-4 shadow-md">
      <div className="p-4">
        {/* Debug Panel for development */}
        <FetchDebugPanel />
        
        <div className="flex items-start gap-3">
          <Avatar 
            src={session.user.image || undefined}
            name={session.user.name || 'User'}
            className="w-10 h-10 text-small 
              border-2 border-white dark:border-black
              relative
              shadow-lg
              ring-2 ring-violet-500/30 dark:ring-violet-400/30
              bg-gradient-to-br from-violet-100 to-fuchsia-100 dark:from-violet-900 dark:to-fuchsia-900
              transition-transform duration-300 hover:scale-105
              cursor-pointer"
            imgProps={{
              className: "object-cover hover:scale-110 transition-transform duration-300",
              loading: "lazy"
            }}
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1 mb-0.5">
              <h3 className="text-sm font-semibold text-foreground">
                {session.user.name}
              </h3>
              {isPremiumActive && (
                <Tooltip content="Premium Member">
                  <span className="cursor-help">
                    <CrownIcon size={30} className="text-warning" />
                  </span>
                </Tooltip>
              )}
            </div>
            
            <div className="space-y-1.5">
              <div className="flex items-center gap-1">
                <Chip
                  size="sm"
                  color={subscription ? getChipColor(subscription) : 'default'}
                  variant="flat"
                  className="capitalize h-5 px-2 text-xs font-medium"
                >
                  {getTierName(subscription)}
                </Chip>
              </div>
              
              <div className="space-y-0.5">
                <p className="text-xs text-default-500">
                  AI Credits: {tokenBalance ? formatTokenBalance(tokenBalance.tokenBalance) : 'Loading...'}
                </p>
                {!cardDetails.isFreeTrial && subscription?.tierId === 'premium_tier_001' && subscription?.paymentStatus === 'PAID' && (
                  <p className="text-xs text-default-500">
                    Time Remaining: {remainingTime || 'Calculating...'}
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>

        {cardDetails.showPremiumButton && (
          <div className="mt-3">
            <PremiumButton />
          </div>
        )}
      </div>
    </Card>
  );
};

// FIX 10: Use React.memo to prevent unnecessary re-renders
export default React.memo(SubscriptionTierCard);