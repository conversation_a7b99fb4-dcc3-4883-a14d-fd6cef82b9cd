"use client";

import React, { Suspense, useState, useEffect, useMemo } from "react";
import {
  Ta<PERSON>,
  Tab,
  Card,
  CardBody,
  CardHeader,
  Divider,
  Select,
  SelectItem,
  Selection
} from "@heroui/react";
import dynamic from "next/dynamic";
import { Session } from "next-auth";

import Dot<PERSON>ottieAnimation from "../shared/dotlottie-animation";
import { fetchCategories, CategoryOption } from "./actions"; 

interface AvailableExamsType {
  id: string;
  name: string;
  subname: string;
  description: string;
  baseline_price: number;
  visibility: string;
  duration: string;
  media_url?: string;
  category_name?: string;
  category_id?: string;
  is_purchased?: boolean;
  purchase_date?: string;
}

interface LeaderboardPageProps {
  session: Session | null;
  availableExams: AvailableExamsType[] | {
    data: AvailableExamsType[];
    msg: string;
    status: string;
  };
  availablePractices: AvailableExamsType[] | {
    data: AvailableExamsType[];
    msg: string;
    status: string;
  };
}

// Constants for localStorage keys
const LOCAL_STORAGE_CATEGORY_KEY = 'activeLeaderboardCategory';
const getCategoryTheme = (categoryName: string) => {
  const themes = {
    LPDP: {
      icon: "🎓", 
      bgColor: "bg-gradient-to-r from-blue-400 to-indigo-500",
      textColor: "text-white"
    },
    CPNS: {
      icon: "🏛️", 
      bgColor: "bg-gradient-to-r from-teal-600 to-emerald-400",
      textColor: "text-white"
    },
    UTBK: {
      icon: "📚", 
      bgColor: "bg-gradient-to-r from-orange-400 to-pink-500",
      textColor: "text-white"
    },
    SNBT: {
      icon: "📝", 
      bgColor: "bg-gradient-to-r from-purple-400 to-purple-600",
      textColor: "text-white"
    },
    Uncategorized: {
      icon: "✨", 
      bgColor: "bg-gradient-to-r from-gray-400 to-gray-600",
      textColor: "text-white"
    }
  };

  // Return the theme for the category or the uncategorized theme if not found
  return themes[categoryName as keyof typeof themes] || themes.Uncategorized;
};

const TryoutForm = dynamic(() => import("./tryout-leaderboard"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center min-h-[200px]">
      <p>Loading tryout leaderboard...</p>
    </div>
  )
}) as React.ComponentType<{ selectedExam: string; selectedCategory?: string }>;

const LatihanForm = dynamic(() => import("./latihan-leaderboard"), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center min-h-[200px]">
      <p>Loading latihan leaderboard...</p>
    </div>
  )
}) as React.ComponentType<{ selectedExam: string; selectedCategory?: string }>;
const LeaderboardPage: React.FC<LeaderboardPageProps> = ({ session, availableExams, availablePractices }) => {
  const [selected, setSelected] = React.useState<string | number>("tryout");
  const [examValue, setExamValue] = React.useState<Selection>(new Set([]));
  const [practiceValue, setPracticeValue] = React.useState<Selection>(new Set([]));
  const [categories, setCategories] = useState<CategoryOption[]>([]);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Get arrays regardless of input format
  const rawExams = Array.isArray(availableExams) ? availableExams : availableExams?.data || [];
  const rawPractices = Array.isArray(availablePractices) ? availablePractices : availablePractices?.data || [];

  // Fetch categories when component mounts
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await fetchCategories();
        const reversedCategories = [...categoriesData].reverse();
        console.log(categoriesData)
    
        setCategories(reversedCategories);
        setIsLoading(false);
        
        // Try to retrieve active category from localStorage or set default
        try {
          const savedCategory = localStorage.getItem(LOCAL_STORAGE_CATEGORY_KEY);
          if (savedCategory && categoriesData.some(cat => cat.value === savedCategory)) {
            setActiveCategory(savedCategory);
          } else if (categoriesData.length > 0) {
            setActiveCategory(categoriesData[0].value);
          }
        } catch (error) {
          console.error("Failed to access localStorage:", error);
          if (categoriesData.length > 0) {
            setActiveCategory(categoriesData[0].value);
          }
        }
      } catch (error) {
        console.error("Failed to fetch categories:", error);
        setIsLoading(false);
      }
    };

    loadCategories();
  }, []);

  // Filter exams and practices by active category
  const filteredExams = useMemo(() => {
    if (!activeCategory) return rawExams;
    return rawExams.filter(exam => exam.category_id === activeCategory);
  }, [rawExams, activeCategory]);

  const filteredPractices = useMemo(() => {
    if (!activeCategory) return rawPractices;
    return rawPractices.filter(practice => practice.category_id === activeCategory);
  }, [rawPractices, activeCategory]);

  // Handle category change with localStorage caching
  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId);
    // Reset selections when changing category
    setExamValue(new Set([]));
    setPracticeValue(new Set([]));
    
    // Save to localStorage
    try {
      localStorage.setItem(LOCAL_STORAGE_CATEGORY_KEY, categoryId);
    } catch (error) {
      console.error("Failed to save category to localStorage:", error);
    }
  };

  // Handle selection changes
  const handleExamChange = (keys: Selection) => {
    setExamValue(keys);
  };

  const handlePracticeChange = (keys: Selection) => {
    setPracticeValue(keys);
  };

  // Get selected values as strings
  const selectedExamId = examValue ? Array.from(examValue)[0] as string : "";
  const selectedPracticeId = practiceValue ? Array.from(practiceValue)[0] as string : "";

  // Get the active category label for display
  const activeCategoryLabel = useMemo(() => {
    if (!activeCategory) return null;
    const category = categories.find(cat => cat.value === activeCategory);
    return category ? category.label : null;
  }, [activeCategory, categories]);
  // Authentication check
  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Please sign in to access leaderboard.</p>
      </div>
    );
  }

  // Early return if no data is available and not loading
  if (!isLoading && !rawExams.length && !rawPractices.length) {
    return (
      <div className="flex w-full flex-col p-6">
        <div className="flex items-center justify-center min-h-[200px]">
          <p>No content available at the moment.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col p-6">
      {/* Category Selection Tabs - Top Level */}
      {!isLoading && categories.length > 0 && (
        <div className="mb-6 overflow-x-auto p-2">
          <div className="flex space-x-2 justify-center">
            {categories.map(category => {
              // Use either the extracted label or fallback to a default icon
              const categoryName = category.label || "Uncategorized";
              const theme = getCategoryTheme(categoryName);
              
              // Count items in this category for both exams and practices
              const examCount = rawExams.filter(exam => exam.category_id === category.value).length;
              const practiceCount = rawPractices.filter(practice => practice.category_id === category.value).length;
              const totalCount = examCount + practiceCount;
              
              return (
                <button
                  key={category.value}
                  onClick={() => handleCategoryChange(category.value)}
                  className={`px-4 py-2 rounded-full flex items-center transition-all transform hover:scale-105 ${
                    activeCategory === category.value
                      ? `${theme.bgColor} ${theme.textColor} shadow-lg`
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  <span className="mr-2">{theme.icon}</span>
                  <span className="font-medium">{category.label}</span>
                  <span className="ml-2 bg-white bg-opacity-25 text-xs px-2 py-1 rounded-full">
                    {totalCount || 0}
                  </span>
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Tryout/Latihan Tabs - Second Level */}
      <Tabs
        aria-label="Leaderboard Types"
        selectedKey={selected}
        onSelectionChange={(key) => {
          setSelected(key);
          setExamValue(new Set([]));
          setPracticeValue(new Set([]));
        }}
        className="flex justify-center mb-6"
      >
        <Tab key="tryout" title="Tryout" />
        <Tab key="latihan" title="Latihan" />
      </Tabs>
      {/* Conditional Select based on tab - Third Level */}
      <div className="max-w-xl mx-auto mb-6 w-full relative z-1">
        {selected === "tryout" ? (
          <Select
            label={`Select ${activeCategoryLabel || ""} Tryout Exam`}
            placeholder={`Select a ${activeCategoryLabel || ""} tryout exam`}
            selectedKeys={examValue}
            onSelectionChange={handleExamChange}
            className="w-full"
            isRequired
            popoverProps={{
              classNames: {
                base: "before:bg-default-200",
                content: "py-1 px-1 border border-default-200 bg-background"
              }
            }}
          >
            {filteredExams.length > 0 ? (
              filteredExams.map((exam) => (
                <SelectItem 
                  key={exam.id} 
                  textValue={exam.name}
                  className="data-[hover=true]:bg-default-100"
                >
                  <div className="flex flex-col">
                    <span>{exam.name}</span>
                    <span className="text-tiny text-default-400">{exam.subname}</span>
                  </div>
                </SelectItem>
              ))
            ) : (
              <SelectItem key="no-exams" textValue="No exams available">
                No exams available for this category
              </SelectItem>
            )}
          </Select>
        ) : (
          <Select
            label={`Select ${activeCategoryLabel || ""} Practice Exam`}
            placeholder={`Select a ${activeCategoryLabel || ""} practice exam`}
            selectedKeys={practiceValue}
            onSelectionChange={handlePracticeChange}
            className="w-full"
            isRequired
            popoverProps={{
              classNames: {
                base: "before:bg-default-200",
                content: "py-1 px-1 border border-default-200 bg-background"
              }
            }}
          >
            {filteredPractices.length > 0 ? (
              filteredPractices.map((practice) => (
                <SelectItem 
                  key={practice.id} 
                  textValue={practice.name}
                  className="data-[hover=true]:bg-default-100"
                >
                  <div className="flex flex-col">
                    <span>{practice.name}</span>
                    <span className="text-tiny text-default-400">{practice.subname}</span>
                  </div>
                </SelectItem>
              ))
            ) : (
              <SelectItem key="no-practices" textValue="No practices available">
                No practices available for this category
              </SelectItem>
            )}
          </Select>
        )}
      </div>

      <Suspense
        fallback={
          <div className="flex items-center justify-center min-h-[200px]">
            <p>Loading content...</p>
          </div>
        }
      >
        {(!selectedExamId && !selectedPracticeId) ? (
          <Card>
            <CardBody className="py-8">
              <div className="flex flex-col items-center justify-center text-center gap-2">
                <DotLottieAnimation
                  src="https://cdn.terang.ai/dotlotties/leaderboard.lottie"
                  width="100%"
                  height="100%"
                />
                <p className="text-lg">Kamu perlu pilih ujian yang tersedia dulu sebelum leaderboardnya muncul</p>
                <p className="text-small text-default-500">Pilih ujian di dropdown di atas untuk melihat leaderboard</p>
              </div>
            </CardBody>
          </Card>
        ) : selected === "tryout" ? (
          <Card>
            <CardHeader className="flex flex-col items-start px-6 py-5">
              <h4 className="text-xl font-bold">
                Si Paling Ambis Tryout Nasional 
                {activeCategoryLabel && <span className="ml-2 text-sm font-normal bg-gray-100 px-3 py-1 rounded-full">{activeCategoryLabel}</span>}
              </h4>
              <p className="text-default-500">
                Julukan buat yang nggak setengah-setengah ngejar target! Latihan tryout non-stop, siap jadi jagoan di hari H.
              </p>
            </CardHeader>
            <Divider />
            <CardBody className="px-6 py-8">
              <TryoutForm 
                selectedExam={selectedExamId} 
                selectedCategory={activeCategory || undefined} 
              />
            </CardBody>
          </Card>
        ) : (
          <Card>
            <CardHeader className="flex flex-col items-start px-6 py-5">
              <h4 className="text-xl font-bold">
                Si Paling Ambis Latihan
                {activeCategoryLabel && <span className="ml-2 text-sm font-normal bg-gray-100 px-3 py-1 rounded-full">{activeCategoryLabel}</span>}
              </h4>
              <p className="text-default-500">
                Julukan buat kalian yang nggak setengah-setengah latihan, konsisten raih level tertinggi di berbagai bidang soal. Pantang mundur!
              </p>
            </CardHeader>
            <Divider />
            <CardBody className="px-6 py-8">
              <LatihanForm 
                selectedExam={selectedPracticeId} 
                selectedCategory={activeCategory || undefined}
              />
            </CardBody>
          </Card>
        )}
      </Suspense>
    </div>
  );
};

export default LeaderboardPage;