// components/settings/index.tsx
"use client";

import React, { useState } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON>b, 
  <PERSON>, 
  CardBody, 
  CardHeader,
  Divider
} from "@heroui/react";
import ProfileForm from './profile-form';
import PasswordForm from './password-form';
import { Session } from "next-auth";

interface SettingsPageProps {
  session: Session | null;
}

const SettingsPage: React.FC<SettingsPageProps> = ({ session }) => {
  const [selected, setSelected] = React.useState<string | number>("profile");

  if (!session) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Please sign in to access settings.</p>
      </div>
    );
  }

  return (
    <div className="flex w-full flex-col p-6">
      <Tabs 
        aria-label="Settings"
        selectedKey={selected}
        onSelectionChange={setSelected}
        className="flex justify-center mb-6"
      >
        <Tab key="profile" title="Profile" />
        <Tab key="password" title="Password" />
      </Tabs>
      
      {selected === "profile" && (
        <Card>
          <CardHeader className="flex flex-col items-start px-6 py-5">
            <h4 className="text-xl font-bold">Profile Settings</h4>
            <p className="text-default-500">Update your profile information and settings</p>
          </CardHeader>
          <Divider/>
          <CardBody className="px-6 py-8">
            <ProfileForm />
          </CardBody>
        </Card>
      )}

      {selected === "password" && (
        <Card>
          <CardHeader className="flex flex-col items-start px-6 py-5">
            <h4 className="text-xl font-bold">Change Password</h4>
            <p className="text-default-500">Update your password to keep your account secure</p>
          </CardHeader>
          <Divider/>
          <CardBody className="px-6 py-8">
            <PasswordForm />
          </CardBody>
        </Card>
      )}
    </div>
  );
};

export default SettingsPage;