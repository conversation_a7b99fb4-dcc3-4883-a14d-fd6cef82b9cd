import React from 'react';

// Interface for the wrapper component props
interface IconWrapperProps extends React.SVGProps<SVGSVGElement> {
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  size?: number;
}

export const IconWrapper: React.FC<IconWrapperProps> = ({
  icon: IconComponent,
  size = 24,
  ...props
}) => {
  return (
    <IconComponent
      width={size}
      height={size}
      {...props}
    />
  );
};