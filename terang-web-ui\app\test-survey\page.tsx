"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import ReactMarkdown from "react-markdown";
import 'katex/dist/katex.min.css';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON>eader, 
  <PERSON>dalBody,
  But<PERSON>
} from "@heroui/react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { UserDemographicsForm } from "@/components/shared/form-survey";
import { submitUserDemographics, validateUserDemographicsForm } from "@/components/shared/actions";
import { UserDemographicsFormData } from "@/components/shared/actions";

const LaTeXTest = () => {
  // Content for LaTeX display
  const content = `
  Polanya adalah setiap bilangan dijumlahkan $3\\frac{1}{2}$.
  $$ \\frac{100\\ \\text{unit}}{5\\ \\text{hour}} = 20\\ \\text{unit/hour} $$
  // ... rest of LaTeX content
  `;

  const router = useRouter();

  // Notification helper function
  const showNotification = (
    message: string,
    type: "success" | "info" | "error" | "warning"
  ) => {
    toast[type](message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  // State for survey form
  const [formData, setFormData] = useState<UserDemographicsFormData | null>(null);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  
  // State for modal control
  const [modalIsOpen, setModalIsOpen] = useState<boolean>(false);
  const [hasSubmittedSurvey, setHasSubmittedSurvey] = useState<boolean>(false);

  // Automatically open the modal when the component mounts
  useEffect(() => {
    // Check if the user has already submitted the survey
    const hasSubmitted = localStorage.getItem('surveySubmitted') === 'true';
    setHasSubmittedSurvey(hasSubmitted);
    
    // If they haven't submitted, show the modal
    if (!hasSubmitted) {
      setTimeout(() => {
        setModalIsOpen(true);
      }, 1000); // Slight delay for better UX
    }
  }, []);

  // Function handlers
  const handleFormDataChange = (data: UserDemographicsFormData) => {
    setFormData(data);
    console.log("Form data changed:", data);
  };

  const handleClearError = (field: keyof UserDemographicsFormData) => {
    setFormErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  };

  const validateForm = async (data: UserDemographicsFormData): Promise<Record<string, string>> => {
    // This could be your actual validation logic or API call
    return await validateUserDemographicsForm(data);
  };

  const handleFormSubmit = async () => {
    if (!formData) {
      showNotification("Silakan isi formulir terlebih dahulu", "error");
      setFormErrors({ form: "Please fill out the form" });
      return;
    }
  
    // Validate the form
    const errors = await validateForm(formData);
    if (Object.keys(errors).length > 0) {
      const errorMessages = Object.values(errors).join(", ");
      showNotification(`Kesalahan validasi: ${errorMessages}`, "error");
      setFormErrors(errors);
      return;
    }
  
    try {
      setIsSubmitting(true);
      
      const result = await submitUserDemographics(formData);
      
      if (result.success) {
        // Mark survey as submitted
        localStorage.setItem('surveySubmitted', 'true');
        setHasSubmittedSurvey(true);
        
        // Show success notification
        showNotification("Data berhasil disimpan!", "success");
        
        // Close modal and redirect after a short delay
        setTimeout(() => {
          setModalIsOpen(false);
          router.push('/dashboard');
        }, 2000);
      } else {
        // Show error notification
        showNotification(result.message || "Gagal menyimpan data", "error");
        console.error("Form submission failed:", result.message);
      }
    } catch (error) {
      // Show error notification
      const errorMessage = error instanceof Error 
        ? error.message 
        : "Terjadi kesalahan tidak diketahui";
      showNotification(errorMessage, "error");
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };  

  return (
    <div className="prose max-w-none p-4 bg-gray-50 rounded-lg">
      <ReactMarkdown
        remarkPlugins={[remarkMath]}
        rehypePlugins={[rehypeKatex]}
      >
        {content}
      </ReactMarkdown>

      {/* Full-screen loading overlay */}
      {isSubmitting && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="text-center">
            <Spinner color="white" size="lg" />
            <p className="mt-4 text-white text-xl">Mengirim...</p>
          </div>
        </div>
      )}

      {/* Survey Modal */}
      <Modal 
        isOpen={modalIsOpen} 
        onOpenChange={(open) => {
          if (hasSubmittedSurvey) {
            setModalIsOpen(open);
          } else if (!open) {
            // Optional: ask for confirmation before closing
            if (window.confirm("Yakin ingin menutup formulir? Data kamu tidak akan tersimpan.")) {
              setModalIsOpen(false);
            }
          }
        }} 
        size="full"
        scrollBehavior="inside"
        classNames={{
          base: "h-[100dvh]",
          body: "pb-24", // Add padding at the bottom for the buttons
          wrapper: "h-[100dvh]",
          header: "border-b border-neutral-200",
          backdrop: "bg-black/30 backdrop-blur-sm"
        }}
      >
        <ModalContent>
          <ModalHeader className="sticky top-0 z-20 bg-white flex items-center">
            <div className="w-full">
              <h2 className="text-xl font-medium">Check up dulu yuk</h2>
              <p className="text-sm text-gray-500 mt-1">Bantu kami mengenal kamu lebih baik</p>
            </div>
          </ModalHeader>
          <ModalBody className="overflow-y-auto">
            <div className="flex flex-col bg-gray-50 rounded-lg">
              <UserDemographicsForm
                clearError={handleClearError}
                formErrors={formErrors}
                initialData={formData}
                onFormDataChange={handleFormDataChange}
                onComplete={handleFormSubmit}
              />
            </div>
          </ModalBody>
        </ModalContent>

        {/* Fixed buttons outside the modal content but inside the modal */}
        <div className="fixed bottom-0 left-0 right-0 flex justify-between px-6 py-4 bg-white border-t z-30 shadow-[0_-4px_10px_rgba(0,0,0,0.05)]">
          <Button
            color="secondary"
            variant="light"
            onPress={() => setModalIsOpen(false)}
            disabled={isSubmitting}
            className="px-6"
          >
            Lewati
          </Button>
          <Button
            color="primary"
            disabled={isSubmitting}
            onPress={handleFormSubmit}
            className="px-6 bg-gradient-to-r from-primary to-primary-600 shadow-md"
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <Spinner size="sm" color="white" className="mr-2" />
                <span className="text-white">Mengirim...</span>
              </div>
            ) : (
              "Simpan Data"
            )}
          </Button>
        </div>
      </Modal>

      {/* Toast Container for Notifications */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
};

export default LaTeXTest;