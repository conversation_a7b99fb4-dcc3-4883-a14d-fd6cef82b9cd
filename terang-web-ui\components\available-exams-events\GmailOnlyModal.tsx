import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Input,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  ModalFooter
} from "@heroui/react";

interface GmailOnlyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { email: string; name: string }) => void;
  showNotification?: (message: string, type: "success" | "info" | "error" | "warning") => void;
}

const GmailOnlyModal: React.FC<GmailOnlyModalProps> = ({ 
  isOpen, 
  onClose, 
  onSubmit,
  showNotification
}) => {
  const [email, setEmail] = useState<string>('');
  const [name, setName] = useState<string>('');
  const [emailError, setEmailError] = useState<string>('');
  const [nameError, setNameError] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Clear errors when input changes
  useEffect(() => {
    if (email) setEmailError('');
  }, [email]);

  useEffect(() => {
    if (name) setNameError('');
  }, [name]);

  // Validate email must be gmail
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@gmail\.com$/i;
    return emailRegex.test(email);
  };

  const handleSubmit = (): void => {
    let isValid = true;

    // Validate name
    if (!name.trim()) {
      setNameError('Nama lengkap wajib diisi');
      isValid = false;
    }

    // Validate email
    if (!email.trim()) {
      setEmailError('Alamat email wajib diisi');
      isValid = false;
    } else if (!validateEmail(email)) {
      setEmailError('Hanya alamat @gmail.com yang diperbolehkan');
      isValid = false;
    }

    if (isValid) {
      setIsLoading(true);
      
      // Submit data to parent component
      try {
        onSubmit({ email, name });
        // Reset form fields after successful submission
        setEmail('');
        setName('');
      } catch (error) {
        if (showNotification) {
          showNotification('Terjadi kesalahan saat memproses data', 'error');
        }
        console.error('Error submitting user data:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} backdrop="blur">
      <ModalContent>
        <ModalHeader className="text-xl font-semibold">
          Lengkapi Informasi Kamu
        </ModalHeader>
        
        <ModalBody>
          <div className="space-y-4">
            <div>
              <Input
                label="Nama Lengkap"
                value={name}
                onChange={(e) => setName(e.target.value)}
                isInvalid={!!nameError}
                errorMessage={nameError}
                variant="bordered"
                fullWidth
              />
            </div>
            
            <div>
              <Input
                label="Alamat Email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                isInvalid={!!emailError}
                errorMessage={emailError}
                placeholder="<EMAIL>"
                variant="bordered"
                fullWidth
              />
              {!emailError && email && !validateEmail(email) && (
                <p className="text-amber-500 text-sm mt-1">
                  Hanya alamat @gmail.com yang dapat digunakan
                </p>
              )}
            </div>
            
            <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-md">
              <p>Informasi ini akan digunakan untuk membuat akun dan memproses pembelian. 
              Kami hanya menerima alamat Gmail untuk memastikan verifikasi yang mudah.</p>
            </div>
          </div>
        </ModalBody>
        
        <ModalFooter>
          <Button 
            variant="ghost" 
            onPress={onClose}
            isDisabled={isLoading}
          >
            Batal
          </Button>
          <Button 
            color="primary"
            onPress={handleSubmit}
            isLoading={isLoading}
          >
            Lanjutkan
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default GmailOnlyModal;