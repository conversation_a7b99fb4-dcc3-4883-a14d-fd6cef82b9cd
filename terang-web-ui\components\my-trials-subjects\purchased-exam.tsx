import React, { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON>dalHeader,
  ModalBody,
} from "@heroui/react";
import {
  examTrialSession,
} from "./actions";
import { submitUserDemographics } from '../shared/actions';
import { UserDemographicsForm } from '../shared/form-survey';
import { UserDemographicsFormData, validateUserDemographicsForm } from "@/components/shared/actions"

interface TrialSession {
  id: string;
  sessionId: string;
  userId: string | boolean | null;
  examId: string;
  type: string;
  status: string;
  startTime: string;
  endTime: string;
  answers: string;
  flaggedQuestion: string;
  createdAt: string;
  modifiedAt: string;
  subject: string;
  remaining_duration?: string;
  exam_id?: string;
}

interface Props {
  examId: string;
  subject: string;
  trialSession: TrialSession | null;
}

const PurchasedExam: React.FC<Props> = React.memo(({ examId, subject, trialSession }) => {
  const pathname = usePathname();
  const router = useRouter();

  // Modal State
  const [modalIsOpen, setModalIsOpen] = useState(false);
  const [modalCurrentStep, setModalCurrentStep] = useState(0);

  // Loading State
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Error State
  const [error, setError] = useState<string | null>(null);

  // Form State
  const [formData, setFormData] = useState<UserDemographicsFormData>({} as UserDemographicsFormData);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const isMyTrialsPath = pathname?.startsWith("/my-trials");

  // Derived State
  const hasActiveSession = trialSession?.status === 'ACTIVE' && trialSession?.examId === examId;
  const isLoadingStatus = isLoading;

  const getButtonText = () => {
    if (isLoadingStatus) return 'Loading...';
    return hasActiveSession ? 'Lanjutkan Latihan' : 'Mulai Latihan';
  };

  const formatDuration = (durationString?: string) => {
    if (!durationString) return '0h 0m 0s';
    const [hours, minutes, seconds] = durationString.split(":").map(Number);
    return `${hours}h ${minutes}m ${seconds}s`;
  };

  const validateForm = async (formData: UserDemographicsFormData): Promise<{ [key: string]: string }> => {
    return await validateUserDemographicsForm(formData);
  };

  const startTrialExam = async () => {
    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append('id', examId);
      formData.append('subject', subject);
  
      await examTrialSession(formData);
      setModalIsOpen(false);
    } catch (err) {
      // Handle Next.js redirects specially
      if ((err as any)?.digest?.startsWith('NEXT_REDIRECT')) {
        throw err; // Re-throw to let Next.js handle the redirect
      }
  
      console.error("Error starting trial exam:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to start trial exam";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormSubmit = async (data: UserDemographicsFormData) => {
    const errors = await validateForm(data);
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setIsSubmitting(true);
      const result = await submitUserDemographics(data);
      if (result.success) {
        await startTrialExam();
      } else {
        setError(result.message);
      }
    } catch (err) {
      console.error("Error in handleFormSubmit:", err);
      setError("An unexpected error occurred. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Simple modal open function
  const handleModalOpen = () => {
    setModalCurrentStep(0);
    setModalIsOpen(true);
  };

  const handleButtonClick = () => {
    if (hasActiveSession && trialSession?.sessionId) {
      router.push(`/trial/${trialSession.sessionId}/${subject}`);
      return;
    }

    handleModalOpen();
  };

  const renderExamConfirmation = () => {
    return (
      <div>
        {hasActiveSession && trialSession ? (
          <div>
            <p>Kamu memiliki sesi aktif untuk latihan {subject}:</p>
            <ul className="list-disc list-inside mt-2">
              <li>Status: {trialSession.status}</li>
              <li>Waktu Tersisa: {formatDuration(trialSession.remaining_duration)}</li>
            </ul>
            <p className="mt-2">Apakah kamu ingin melanjutkan sesi ini?</p>
          </div>
        ) : (
          <p>
            Kamu yakin akan memulai latihan ujian: <br /> <br />
            <b>{subject}</b>?
          </p>
        )}
        <div className="flex justify-end mt-4">
          <Button
            color="danger"
            variant="light"
            className="mr-2"
            onPress={() => setModalIsOpen(false)}
          >
            Batal
          </Button>
          <Button
            color="primary"
            onPress={startTrialExam}
          >
            {getButtonText()}
          </Button>
        </div>
      </div>
    );
  };

  // Debug effect to track modal state
  useEffect(() => {
    console.log('Modal state changed:', modalIsOpen);
  }, [modalIsOpen]);

  if (!isMyTrialsPath) return null;

  if (isSubmitting) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="text-center">
          <Spinner color="white" size="lg" />
          <p className="mt-4 text-white text-xl">Submitting...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <Button
        className="w-full bg-secondary-100 text-secondary-700 hover:bg-secondary-200"
        disabled={isLoadingStatus}
        variant="shadow"
        fullWidth
        size="lg"
        onPress={handleButtonClick}
      >
        {isLoadingStatus ? (
          <Spinner size="sm" />
        ) : (
          getButtonText()
        )}
      </Button>

      <Modal
        isOpen={modalIsOpen}
        onOpenChange={(open) => setModalIsOpen(open)}
        size="md"
        className="py-3"
        scrollBehavior="inside"
        classNames={{
          base: "h-[100dvh] sm:h-auto"
        }}
      >
        <ModalContent>
          <ModalHeader className="sticky top-0 z-20 bg-background">
            Mulai Latihan Ujian
          </ModalHeader>
          <ModalBody className="py-4">
            {isLoadingStatus ? (
              <div className="flex justify-center">
                <Spinner label="Memuat informasi..." />
              </div>
            ) : error ? (
              <div className="p-4 text-danger bg-danger-50 rounded-lg">
                <p>{error}</p>
              </div>
            ) : (
              renderExamConfirmation()
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </div>
  );
});

PurchasedExam.displayName = 'PurchasedExam';
export default PurchasedExam;