import React from "react";
import styled from "styled-components";
import Image from "next/image";

const Section = styled.section`
  width: 100vw;
  height: 25rem;
  position: relative;
  border-top: 2px solid ${(props) => props.theme.text};
  background-color: ${(props) => `rgba(${props.theme.textRgba},0.9)`};
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;

  @media (max-width: 48em) {
    height: 15rem;
    flex-direction: column;
  }
`;

const ImgContainer = styled.div`
  width: 100%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.2;

  img {
    width: 15rem;
    height: auto;
  }

  @media (max-width: 48em) {
    img {
      width: 10rem;
      height: auto;
    }
  }
`;

const Title = styled.h1`
  font-size: 60px;
  color: ${(props) => props.theme.body};
  padding: 1rem 2rem;
  z-index: 10;
  width: 35%;
  text-transform: capitalize;
  text-shadow: 1px 1px 2px ${(props) => props.theme.text};

  @media (max-width: 64em) {
    font-size: ${(props) => props.theme.fontxxl};
    text-align: center;
    width: 40%;
  }
  @media (max-width: 48em) {
    font-size: ${(props) => props.theme.fontxl};
    padding: 2rem 0;
    width: 100%;
  }
`;

const BtnContainer = styled.div`
  width: 35%;
  display: flex;
  justify-content: flex-end;

  @media (max-width: 48em) {
    width: 100%;
    justify-content: center;
  }
`;

const JoiNow = styled.a`
  display: inline-block;
  background-color: ${(props) => props.theme.body};
  color: ${(props) => props.theme.text};
  outline: none;
  border: none;
  font-weight: 600;
  font-size: ${(props) => props.theme.fontlg};
  padding: 1.5rem 3rem;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;

  @media (max-width: 48em) {
    padding: 1rem 2rem;
  }
  @media (max-width: 30em) {
    padding: 0.5rem 2rem;
    font-size: ${(props) => props.theme.fontsm};
  }
  &:hover {
    transform: scale(0.9);
  }

  &::after {
    content: " ";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    border: 2px solid ${(props) => props.theme.body};
    width: 100%;
    height: 100%;
    border-radius: 50px;
    transition: all 0.2s ease;
  }

  &:hover::after {
    transform: translate(-50%, -50%) scale(1);
    padding: 0.3rem;
  }
`;

const ShimmerDiv = styled.div`
  width: 15rem;
  height: 15rem;
  background: linear-gradient(
    to right,
    ${props => props.theme.text}20 0%,
    ${props => props.theme.text}40 50%,
    ${props => props.theme.text}20 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 4px;

  @keyframes shimmer {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }

  @media (max-width: 48em) {
    width: 10rem;
    height: 10rem;
  }
`;

const animals = [
  {
    name: "Anoa",
    src: "https://cdn.terang.ai/landingpage-assets/vectorised-bg-animal/anoa.svg",
    alt: "Anoa - Indonesian dwarf buffalo",
    blurDataURL: "data:image/svg+xml;base64,..."  // Add your blur data URL here
  },
  {
    name: "Komodo",
    src: "https://cdn.terang.ai/landingpage-assets/vectorised-bg-animal/komodo.svg",
    alt: "Komodo Dragon - Indonesian giant lizard"
  },
  {
    name: "Orangutan",
    src: "https://cdn.terang.ai/landingpage-assets/vectorised-bg-animal/rangutan.svg",
    alt: "Orangutan - Indonesian great ape"
  },
  {
    name: "Rhino",
    src: "https://cdn.terang.ai/landingpage-assets/vectorised-bg-animal/rhino.svg",
    alt: "Javan Rhino - Indonesian rhinoceros"
  },
  {
    name: "Squirrel",
    src: "https://cdn.terang.ai/landingpage-assets/vectorised-bg-animal/squirrel.svg",
    alt: "Native Indonesian squirrel"
  },
  {
    name: "Tiger",
    src: "https://cdn.terang.ai/landingpage-assets/vectorised-bg-animal/tiger.svg",
    alt: "Sumatran Tiger - Indonesian tiger"
  }
];

const Banner = () => {
  return (
    <Section>
      <ImgContainer>
        {animals.map((animal, index) => (
          <React.Fragment key={animal.name}>
            <Image
              src={animal.src}
              alt={animal.alt}
              width={240}
              height={240}
              quality={75}
              placeholder="blur"
              blurDataURL={animal.blurDataURL || 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQwIiBoZWlnaHQ9IjI0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PC9zdmc+'}
              {...(index === 0 
                ? { 
                    priority: true,
                    fetchPriority: "high"
                  } 
                : { 
                    loading: "lazy" 
                  }
              )}
            />
            <noscript>
              <ShimmerDiv />
            </noscript>
          </React.Fragment>
        ))}
      </ImgContainer>
      <Title><strong>Join Channel Terang</strong></Title>
      <BtnContainer>
        <JoiNow 
          href="https://whatsapp.com/channel/0029VaqSaX6DTkK2qxC3eR3T" 
          target="_blank"
          rel="noopener noreferrer"
        >
          Join WhatsApp Channel
        </JoiNow>
      </BtnContainer>
    </Section>
  );
};

export default Banner;