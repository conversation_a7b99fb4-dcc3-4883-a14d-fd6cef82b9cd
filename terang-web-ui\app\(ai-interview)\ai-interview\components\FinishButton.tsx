"use client";

import { useRouter } from "next/navigation";
import { useState, useCallback } from "react";
import { CheckCircle, X } from "lucide-react";

interface FinishButtonProps {
  interviewId?: string;
  sessionId?: string;
  className?: string;
  onFinish?: () => void;
  variant?: "finish" | "close";
  size?: "sm" | "md" | "lg";
}

export function FinishButton({ 
  interviewId, 
  sessionId,
  className = "", 
  onFinish,
  variant = "finish",
  size = "md"
}: FinishButtonProps) {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  const handleFinish = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isNavigating) return;
    
    setIsNavigating(true);
    
    try {
      // Call custom onFinish handler if provided
      if (onFinish) {
        await Promise.resolve(onFinish());
      }

      // Add a delay to ensure any cleanup is completed
      await new Promise(resolve => setTimeout(resolve, 500));

      // Force full page navigation (most reliable)
      if (interviewId && variant === "finish") {
        if (sessionId) {
          router.push(`/ai-interview-result/${interviewId}/${sessionId}`);
        } else {
          router.push(`/ai-interview-result/${interviewId}`);
        }
      } else {
        router.push('/available-interviews');
      }
    } catch (error) {
      console.error("Navigation error:", error);
      // Ensure navigation happens even if there's an error
      if (interviewId && variant === "finish") {
        if (sessionId) {
          router.push(`/ai-interview-result/${interviewId}/${sessionId}`);
        } else {
          router.push(`/ai-interview-result/${interviewId}`);
        }
      } else {
        router.push('/available-interviews');
      }
    }
  }, [interviewId, sessionId, onFinish, variant, isNavigating, router]);

  // Size configurations
  const sizeClasses = {
    sm: "px-3 py-1.5 text-sm gap-1.5",
    md: "px-4 py-2 text-sm gap-2",
    lg: "px-6 py-3 text-base gap-2.5"
  };

  const iconSizes = {
    sm: 14,
    md: 16,
    lg: 18
  };

  // Variant configurations
  const variantConfig = {
    finish: {
      icon: CheckCircle,
      text: "Finish Interview",
      loadingText: "Finishing...",
      baseClasses: "bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700",
      disabledClasses: "bg-green-400 border-green-400 cursor-not-allowed"
    },
    close: {
      icon: X,
      text: "End Session",
      loadingText: "Ending...",
      baseClasses: "bg-red-600 hover:bg-red-700 border-red-600 hover:border-red-700",
      disabledClasses: "bg-red-400 border-red-400 cursor-not-allowed"
    }
  };

  const config = variantConfig[variant];
  const IconComponent = config.icon;

  return (
    <button
      onClick={handleFinish}
      disabled={isNavigating}
      className={`
        inline-flex items-center justify-center
        border-2 rounded-lg
        text-white font-medium
        transition-all duration-200
        transform hover:scale-105 active:scale-95
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50
        shadow-sm hover:shadow-md
        ${sizeClasses[size]}
        ${isNavigating ? config.disabledClasses : config.baseClasses}
        ${variant === 'finish' ? 'focus:ring-green-500' : 'focus:ring-red-500'}
        ${className}
      `}
      title={isNavigating ? config.loadingText : config.text}
    >
      {/* Icon with loading state */}
      <div className={`transition-transform duration-200 ${isNavigating ? 'animate-spin' : ''}`}>
        <IconComponent size={iconSizes[size]} />
      </div>
      
      {/* Text */}
      <span className="font-semibold">
        {isNavigating ? config.loadingText : config.text}
      </span>
      
      {/* Loading indicator */}
      {isNavigating && (
        <div className="ml-1">
          <div className="animate-pulse w-1 h-1 bg-white rounded-full"></div>
        </div>
      )}
    </button>
  );
}

// Icon-only version that matches the original usage but with rectangle design
export function FinishIcon({ 
  interviewId, 
  sessionId,
  className = "", 
  onFinish,
  variant = "finish",
}: Omit<FinishButtonProps, 'size'>) {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  const handleFinish = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isNavigating) return;
    
    setIsNavigating(true);
    
    try {
      if (onFinish) {
        await Promise.resolve(onFinish());
      }

      await new Promise(resolve => setTimeout(resolve, 500));

      // Force full page navigation (most reliable)
      if (interviewId && variant === "finish") {
        if (sessionId) {
          router.push(`/ai-interview-result/${interviewId}/${sessionId}`);
        } else {
          router.push(`/ai-interview-result/${interviewId}`);
        }
      } else {
        router.push('/available-interviews');
      }
    } catch (error) {
      console.error("Navigation error:", error);
      // Ensure navigation happens even if there's an error
      if (interviewId && variant === "finish") {
        if (sessionId) {
          router.push(`/ai-interview-result/${interviewId}/${sessionId}`);
        } else {
          router.push(`/ai-interview-result/${interviewId}`);
        }
      } else {
        router.push('/available-interviews');
      }
    }
  }, [interviewId, sessionId, onFinish, variant, isNavigating, router]);

  const variantClasses = {
    finish: "bg-green-600 hover:bg-green-700 border-green-600 hover:border-green-700",
    close: "bg-red-600 hover:bg-red-700 border-red-600 hover:border-red-700"
  };

  const IconComponent = variant === "finish" ? CheckCircle : X;

  return (
    <button
      onClick={handleFinish}
      disabled={isNavigating}
      className={`
        inline-flex items-center justify-center gap-2
        px-4 py-2
        border-2 rounded-md
        text-white font-medium text-sm
        transition-all duration-200
        transform hover:scale-105 active:scale-95
        focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50
        shadow-sm hover:shadow-md
        ${isNavigating ? 
          (variant === 'finish' ? 'bg-green-400 border-green-400' : 'bg-red-400 border-red-400') + ' cursor-not-allowed' : 
          variantClasses[variant]
        }
        ${variant === 'finish' ? 'focus:ring-green-500' : 'focus:ring-red-500'}
        ${className}
      `}
      title={variant === "finish" ? "Finish Interview" : "End Session"}
    >
      <div className={`transition-transform duration-200 ${isNavigating ? 'animate-spin' : ''}`}>
        <IconComponent size={16} />
      </div>
      <span className="font-semibold">
        {isNavigating 
          ? (variant === "finish" ? "Finishing..." : "Ending...") 
          : (variant === "finish" ? "Finish Interview" : "End Session")
        }
      </span>
    </button>
  );
}

// New enhanced version with better styling for finish functionality
export function EnhancedFinishButton({
  interviewId,
  onFinish,
  className = "",
  sessionId,
}: {
  interviewId?: string;
  onFinish?: () => void;
  className?: string;
  sessionId?: string;
}) {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  const handleFinish = useCallback(async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isNavigating) return;
    
    setIsNavigating(true);
    
    try {
      if (onFinish) {
        await Promise.resolve(onFinish());
      }

      // Longer delay for better UX
      await new Promise(resolve => setTimeout(resolve, 500));

      if (interviewId) {
        // Use sessionId in the URL if available
        if (sessionId) {
          router.push(`/ai-interview-result/${interviewId}/${sessionId}`);
        } else {
          router.push(`/ai-interview-result/${interviewId}`);
        }
      } else {
        router.push('/available-interviews');
      }
    } catch (error) {
      console.error("Navigation error:", error);
      // Ensure navigation happens even if there's an error
      if (interviewId) {
        if (sessionId) {
          router.push(`/ai-interview-result/${interviewId}/${sessionId}`);
        } else {
          router.push(`/ai-interview-result/${interviewId}`);
        }
      } else {
        router.push('/available-interviews');
      }
    }
  }, [router, interviewId, sessionId, onFinish, isNavigating]);

  return (
    <button
      onClick={handleFinish}
      disabled={isNavigating}
      className={`
        group
        inline-flex items-center justify-center
        px-6 py-3
        bg-gradient-to-r from-green-600 to-emerald-600
        hover:from-green-700 hover:to-emerald-700
        border-2 border-green-600 hover:border-green-700
        text-white font-semibold
        rounded-xl
        transition-all duration-300 ease-out
        transform hover:scale-105 active:scale-95
        focus:outline-none focus:ring-4 focus:ring-green-500/30
        shadow-lg hover:shadow-xl
        ${isNavigating ? 'opacity-75 cursor-not-allowed' : 'hover:shadow-green-500/25'}
        ${className}
      `}
      title={isNavigating ? "Finishing Interview..." : "Finish Interview"}
    >
      {/* Animated background overlay */}
      <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 rounded-xl transition-opacity duration-300"></div>
      
      {/* Content */}
      <div className="relative flex items-center gap-3">
        {/* Icon */}
        <div className={`transition-all duration-300 ${isNavigating ? 'animate-spin scale-110' : 'group-hover:scale-110'}`}>
          <CheckCircle size={20} className="drop-shadow-sm" />
        </div>
        
        {/* Text */}
        <span className="text-lg tracking-wide">
          {isNavigating ? "Finishing..." : "Finish Interview"}
        </span>
        
        {/* Progress dots */}
        {isNavigating && (
          <div className="flex gap-1 ml-2">
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
            <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
          </div>
        )}
      </div>
    </button>
  );
}