import React from 'react';
import { FavouriteIcon, HeartbreakIcon, Infinity02Icon } from 'hugeicons-react';

interface HeartsProps {
  lives: number;
  maxLives: number;
  isPremium?: boolean;
}

export const Hearts: React.FC<HeartsProps> = React.memo(({ lives, maxLives, isPremium = false }) => {
  if (isPremium) {
    return (
      <div className="flex items-center gap-1">
        <FavouriteIcon 
          size={24}
          className="text-red-500 fill-current"
        />
        <Infinity02Icon 
          size={24}
          className="text-blue-500"
        />
      </div>
    );
  }

  return (
    <div className="flex gap-1">
      {Array(maxLives).fill(null).map((_, index) => {
        const isActive = index < lives;
        return (
          <div 
            key={index}
            className={`transition-all duration-300 ${
              isActive ? 'animate-bounce' : ''
            }`}
            style={{
              animationDelay: `${index * 100}ms`,
              animationDuration: '1s',
            }}
          >
            <FavouriteIcon 
              size={24}
              className={`text-red-500 ${isActive ? 'fill-current' : 'stroke-current'}`}
              style={{
                fill: isActive ? 'currentColor' : 'none',
                stroke: isActive ? 'none' : 'currentColor',
                strokeWidth: isActive ? '0' : '1.5',
              }}
            />
          </div>
        );
      })}
    </div>
  );
});

Hearts.displayName = 'Hearts';