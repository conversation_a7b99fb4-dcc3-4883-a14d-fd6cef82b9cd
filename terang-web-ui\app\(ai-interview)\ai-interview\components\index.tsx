"use client";

import { NoAgentNotification } from "./NoAgentNotification";
import TranscriptionView from "./TranscriptionView";
import InterviewTimer from "./InterviewTimer";
import { ResumeDialog } from "./ResumeDialog";
import {
  BarVisualizer,
  DisconnectButton,
  RoomAudioRenderer,
  RoomContext,
  VideoTrack,
  VoiceAssistantControlBar,
  useVoiceAssistant,
} from "@livekit/components-react";
import { AnimatePresence, motion } from "framer-motion";
import { Room, RoomEvent } from "livekit-client";
import { useCallback, useEffect, useState, useRef} from "react";
import { useRouter, useParams, useSearchParams } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Users, Wifi, WifiOff } from "lucide-react";
import type { ConnectionDetails } from "@/app/api/connection-details/route";
import { FinishIcon } from "./FinishButton";
import { 
  startInterviewSession, 
  updateInterviewSessionStatus,
  cancelInterviewSession,
  checkActiveSession,
  getInterviewSessionById,
  updateInterviewSessionDisconnected,
  restoreSessionFromGracePeriod,
  forceFinishSessionById,
} from "@/app/lib/actions/available-interviews/timer-actions";
import { auth } from "@/auth";
import { getUserId } from "@/app/lib/actions/account/actions";
import { storeSessionInfo, clearStoredSessionInfo, hasValidStoredSession } from "@/utils/session-helpers";

interface ResumeInfo {
  questionsAsked: number;
  elapsedMinutes: number;
  remainingMinutes: number;
  isNearEnd: boolean;
  sessionId?: string;
}

// Update the page component for Next.js App Router
export default function Page() {
  const params = useParams();
  const searchParams = useSearchParams();
  
  // Extract interviewId from params or searchParams
  const interviewId = 
    typeof params?.interviewId === 'string' ? params.interviewId :
    typeof searchParams?.get('interviewId') === 'string' ? searchParams.get('interviewId') as string :
    '';
  
  // Extract sessionId from params or searchParams
  const sessionId = 
    typeof params?.sessionId === 'string' ? params.sessionId :
    typeof searchParams?.get('sessionId') === 'string' ? searchParams.get('sessionId') as string :
    undefined;
  
  // State for interview data and userId
  const [interviewData, setInterviewData] = useState<{
    name: string;
    subname: string;
    category_name: string;
    duration: string;
    interview_type: string;
  } | undefined>();
  const [userId, setUserId] = useState<string | boolean | null>(null);
  
  // Fetch interview data on mount
  useEffect(() => {
    if (!interviewId) return;
    
    const fetchInterviewData = async () => {
      try {
        // Replace with your actual API call to get interview data
        const response = await fetch(`/api/interviews/${interviewId}`);
        if (response.ok) {
          const data = await response.json();
          setInterviewData(data);
        }
      } catch (error) {
        console.error("Failed to fetch interview data:", error);
      }
    };

    fetchInterviewData();
  }, [interviewId]);

  // Fetch user ID on mount
  useEffect(() => {
    const fetchUserId = async () => {
      try {
        const id = await getUserId();
        if (id) {
          setUserId(id);
        }
      } catch (error) {
        console.error("Failed to get user ID:", error);
      }
    };

    fetchUserId();
  }, []);
  
  // Return the AIInterviewPage component with props from state/params
  return (
    <AIInterviewPage 
      interviewId={interviewId}
      sessionId={sessionId}
      interviewData={interviewData}
      userId={userId}
    />
  );
}

// Make AIInterviewPage exportable for reuse in other routes
// Update the AIInterviewPage component to pass sessionId to AIInterviewSession
export function AIInterviewPage({ interviewId, sessionId, interviewData, userId }: {
  interviewId: string;
  sessionId?: string;
  interviewData?: {
    name: string;
    subname: string;
    category_name: string;
    duration: string;
    interview_type: string;
  };
  userId?: string | boolean | null;
}) {
  const [room] = useState(new Room());
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [isTimerActive, setIsTimerActive] = useState(false);
  const [currentUserId, setCurrentUserId] = useState<string | boolean | null>(userId || null);
  const [hasExistingSession, setHasExistingSession] = useState(false);
  const [isSessionChecked, setIsSessionChecked] = useState(false);
  const [showResumeDialog, setShowResumeDialog] = useState(false);
  const [resumeInfo, setResumeInfo] = useState<ResumeInfo | null>(null);
  const [shouldAutoConnect, setShouldAutoConnect] = useState(false);
  const [isIntentionalDisconnect, setIsIntentionalDisconnect] = useState(false);
  const [isInitialMount, setIsInitialMount] = useState(true);
  
  // New states for reconnection grace period
  const [disconnectTimer, setDisconnectTimer] = useState<NodeJS.Timeout | null>(null);
  const [isDisconnectedButActive, setIsDisconnectedButActive] = useState(false);
  const [disconnectedAt, setDisconnectedAt] = useState<number | null>(null);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [isReconnecting, setIsReconnecting] = useState(false);
  
  const router = useRouter();

  useEffect(() => {
    const checkExistingSession = async () => {
      if (currentUserId) {
        try {
          console.log("Checking for existing session...");
          
          // If we have a sessionId from URL, validate it first
          if (sessionId) {
            console.log(`Validating session from URL: ${sessionId}`);
            const urlSession = await getInterviewSessionById(sessionId);
            
            // Check if session exists at all
            if (!urlSession) {
              console.log("URL session not found");
              setConnectionError("Session not found. Please start a new interview.");
              return;
            }
            
            // Check if session is already in a terminal state
            if (['completed', 'force_finished', 'cancelled', 'disconnected_timeout'].includes(urlSession.status)) {
              console.log(`URL session is in terminal state: ${urlSession.status}`);
              
              // If force_finished, redirect to results
              if (urlSession.status === 'force_finished') {
                console.log("Session was force finished, redirecting to results");
                
                // Set localStorage flags for result page
                localStorage.setItem('forcedFinish', 'true');
                localStorage.setItem('forcedFinishTimestamp', Date.now().toString());
                
                // Redirect to results page
                const resultUrl = `/ai-interview-result/${interviewId}/${sessionId}`;
                console.log(`Redirecting to results: ${resultUrl}`);
                router.push(resultUrl);
                return;
              }
              
              setConnectionError(`Session ${urlSession.status}. Please start a new interview.`);
              return;
            }
            
            // Check if session is dormant (more than 24 hours old) or expired
            const currentTime = Date.now();
            const startTimeMs = new Date(urlSession.startTime).getTime();
            const isDormant = (currentTime - startTimeMs) > (24 * 60 * 60 * 1000);
            const elapsedSeconds = Math.floor((currentTime - startTimeMs) / 1000);
            const isExpired = elapsedSeconds >= urlSession.durationSeconds;
            
            // If session is dormant or expired, force finish and redirect to results
            if (isDormant || isExpired) {
              console.log(`Session ${sessionId} is ${isDormant ? 'dormant' : 'expired'}, force finishing directly`);
              
              try {
                // Force finish the session using the direct method
                // This avoids issues with active session mapping
                const forceFinishResult = await forceFinishSessionById(sessionId);
                console.log("Force finish result:", forceFinishResult);
                
                // Set localStorage flags for result page
                localStorage.setItem('forcedFinish', 'true');
                localStorage.setItem('forcedFinishTimestamp', Date.now().toString());
                
                // Redirect to results page
                const resultUrl = `/ai-interview-result/${interviewId}/${sessionId}`;
                console.log(`Redirecting to results: ${resultUrl}`);
                router.push(resultUrl);
                return;
              } catch (error) {
                console.error("Error force finishing dormant session:", error);
                setConnectionError("Error processing session. Please start a new interview.");
                return;
              }
            }
            
            if (urlSession.status === 'active' || urlSession.status === 'disconnected_grace') {
              if (urlSession.interviewId === interviewId) {
                console.log("URL session is valid, using it directly");
                
                // Calculate current session info
                const remainingSeconds = Math.max(0, urlSession.durationSeconds - elapsedSeconds);
                
                setResumeInfo({
                  questionsAsked: Math.floor(elapsedSeconds / 240),
                  elapsedMinutes: elapsedSeconds / 60,
                  remainingMinutes: remainingSeconds / 60,
                  isNearEnd: remainingSeconds <= 300,
                  sessionId: urlSession.sessionId
                });
                
                // If session was in grace period, show that it's reconnecting
                if (urlSession.status === 'disconnected_grace') {
                  setIsDisconnectedButActive(true);
                  setDisconnectedAt(urlSession.disconnectedAt || Date.now());
                }
                
                setShouldAutoConnect(true);
                return;
              } else {
                console.log("URL session has different interview ID");
                setConnectionError("Session belongs to a different interview. Please start a new interview.");
                return;
              }
            } else {
              console.log("URL session invalid or expired");
              setConnectionError("Session expired or invalid. Please start a new interview.");
              return;
            }
          }
          
          // Check both client-side storage and server-side session
          const hasLocalSession = hasValidStoredSession(interviewId, currentUserId);
          const serverSession = await checkActiveSession(interviewId, currentUserId);
          
          console.log("Local session found:", hasLocalSession);
          console.log("Server session:", serverSession);
          
          // NEW: Handle force finished sessions
          // If server session is null but we have a local session, check if it was force finished
          if (!serverSession?.hasActiveSession && hasLocalSession) {
            // Get session ID from local storage
            const localStorageKey = `session_info:${interviewId}:${currentUserId}`;
            const storedInfo = localStorage.getItem(localStorageKey);
            
            if (storedInfo) {
              try {
                const { sessionId } = JSON.parse(storedInfo);
                if (sessionId) {
                  // Get session details
                  const sessionDetails = await getInterviewSessionById(sessionId);
                  
                  // Check if session was force finished
                  if (sessionDetails && sessionDetails.status === 'force_finished') {
                    console.log("Session was force finished, redirecting to results");
                    
                    // Clear local storage
                    clearStoredSessionInfo(interviewId, currentUserId);
                    
                    // Set localStorage flags for result page
                    localStorage.setItem('forcedFinish', 'true');
                    localStorage.setItem('forcedFinishTimestamp', Date.now().toString());
                    
                    // Redirect to results page
                    const resultUrl = `/ai-interview-result/${interviewId}/${sessionId}`;
                    console.log(`Redirecting to results: ${resultUrl}`);
                    router.push(resultUrl);
                    return;
                  }
                }
              } catch (error) {
                console.error("Error parsing stored session info:", error);
              }
            }
          }
          
          if (serverSession && serverSession.hasActiveSession) {
            setHasExistingSession(true);
            
            // Prepare resume info
            const resumeData: ResumeInfo = {
              questionsAsked: Math.floor((serverSession.timeStatus?.elapsedSeconds || 0) / 240),
              elapsedMinutes: (serverSession.timeStatus?.elapsedSeconds || 0) / 60,
              remainingMinutes: (serverSession.timeStatus?.remainingSeconds || 0) / 60,
              isNearEnd: serverSession.timeStatus?.isNearEnd || false,
              sessionId: serverSession.sessionId
            };
            
            setResumeInfo(resumeData);
            
            // If in grace period, show reconnection UI instead of dialog
            if (serverSession.isInGracePeriod) {
              setIsDisconnectedButActive(true);
              setDisconnectedAt(serverSession.timeStatus?.disconnectedAt || Date.now());
              setShouldAutoConnect(true); // Auto-reconnect for grace period
            } else {
              setShowResumeDialog(true);
            }
            
            console.log("Found existing active session:", serverSession.sessionId);
          } else if (hasLocalSession) {
            console.log("Found local session but server session inactive, will create new one");
            clearStoredSessionInfo(interviewId, currentUserId);
            setShouldAutoConnect(true);
          } else {
            console.log("No existing session found, will auto-connect");
            setShouldAutoConnect(true);
          }
        } catch (error) {
          console.error("Error checking existing session:", error);
          setShouldAutoConnect(true);
        } finally {
          setIsSessionChecked(true);
        }
      } else {
        setIsSessionChecked(true);
      }
    };

    checkExistingSession();
  }, [interviewId, sessionId, currentUserId]);

  // Mark initial mount as complete
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsInitialMount(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  // Get user ID if not provided
  useEffect(() => {
    if (!currentUserId) {
      const getUId = async () => {
        try {
          const userId = await getUserId();
          if (userId) {
            setCurrentUserId(userId);
          }
        } catch (error) {
          console.error("Failed to get user ID:", error);
        }
      };
      getUId();
    }
  }, [currentUserId]);

  const connectToInterview = useCallback(async (forceResume = false, isReconnectAttempt = false) => {
    try {
      console.log(`Starting connection attempt (forceResume: ${forceResume}, reconnect: ${isReconnectAttempt})...`);
      setIsConnecting(true);
      setConnectionError(null);
      
      if (isReconnectAttempt) {
        setIsReconnecting(true);
        setReconnectAttempts(prev => prev + 1);
      }

      // Disconnect from any existing connection first
      if (room.state === 'connected') {
        console.log("Disconnecting from existing room connection...");
        setIsIntentionalDisconnect(true);
        await room.disconnect();
        setIsIntentionalDisconnect(false);
      }

      // Generate room connection details with interview context
      const url = new URL(
        process.env.NEXT_PUBLIC_CONN_DETAILS_ENDPOINT ?? "/api/connection-details",
        window.location.origin
      );
      
      url.searchParams.set('interviewId', interviewId);
      if (interviewData?.category_name) {
        url.searchParams.set('category', interviewData.category_name);
      }
      if (interviewData?.interview_type) {
        url.searchParams.set('type', interviewData.interview_type);
      }
      
      // Add resume parameter if forcing resume OR if we have a sessionId from URL
      if ((forceResume && resumeInfo?.sessionId) || sessionId) {
        url.searchParams.set('resume', 'true');
        const targetSessionId = forceResume ? resumeInfo?.sessionId : sessionId;
        if (targetSessionId) {
          url.searchParams.set('sessionId', targetSessionId);
          console.log(`Attempting to resume session: ${targetSessionId}`);
        }
      }

      if (typeof window !== 'undefined') {
        const urlLanguage = new URLSearchParams(window.location.search).get('language');
        if (urlLanguage) {
          url.searchParams.set('language', urlLanguage);
          console.log('Adding language to connection details request:', urlLanguage);
        }
      }      

      console.log("Fetching connection details from:", url.toString());
      const response = await fetch(url.toString());
      if (!response.ok) {
        const errorText = await response.text();
        console.error("Connection details fetch failed:", response.status, errorText);
        throw new Error(`Failed to get connection details: ${response.status} ${errorText}`);
      }
      
      const connectionDetailsData: ConnectionDetails = await response.json();
      console.log("Received connection details:", {
        serverUrl: connectionDetailsData.serverUrl,
        sessionId: connectionDetailsData.sessionId,
        roomName: connectionDetailsData.roomName,
        isExistingSession: connectionDetailsData.isExistingSession
      });

      console.log("Connecting to room...");
      await room.connect(connectionDetailsData.serverUrl, connectionDetailsData.participantToken);
      console.log("Room connected successfully");
      
      console.log("Enabling microphone...");
      await room.localParticipant.setMicrophoneEnabled(true);

      // Store session info locally for persistence across refreshes
      if (currentUserId && connectionDetailsData.sessionId) {
        storeSessionInfo(interviewId, currentUserId, connectionDetailsData.sessionId, connectionDetailsData.roomName);
        console.log("Session info stored locally");
      }

    // In the connectToInterview function, after successful room connection:
    if (currentUserId && interviewData?.duration) {
      console.log("Starting/resuming interview session timer...");
      try {
        const sessionResult = await startInterviewSession(
          interviewId, 
          currentUserId, 
          interviewData.duration,
          sessionId
        );
        console.log("Session timer result:", sessionResult);
        
        // Make sure to set isTimerActive even if sessionResult structure is unexpected
        if (sessionResult && (sessionResult.success || sessionResult.sessionId)) {
          setIsTimerActive(true);
          console.log("Timer activated successfully");
        } else {
          console.error("Failed to activate timer - unexpected session result:", sessionResult);
        }
      } catch (error) {
        console.error("Error starting session timer:", error);
      }
    }

      // Clear connection states on successful connection
      setConnectionError(null);
      setIsDisconnectedButActive(false);
      setDisconnectedAt(null);
      setReconnectAttempts(0);
      setIsReconnecting(false);
      
      // Clear disconnect timer if it exists
      if (disconnectTimer) {
        clearTimeout(disconnectTimer);
        setDisconnectTimer(null);
      }

      console.log("Connection process completed successfully");

    } catch (error) {
      console.error('Connection failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to connect to interview';
      setConnectionError(errorMessage);
      setIsReconnecting(false);
      
      // If this was a reconnection attempt, don't show full error UI
      // The reconnection status component will handle the retry logic
      if (!isReconnectAttempt) {
        setIsDisconnectedButActive(false);
      }
    } finally {
      setIsConnecting(false);
    }
  }, [room, interviewId, interviewData, currentUserId, resumeInfo, sessionId, disconnectTimer]);

  // Auto-connect when component mounts
  useEffect(() => {
    if (!isSessionChecked || showResumeDialog) return;
    
    if (shouldAutoConnect) {
      console.log("Auto-connecting to interview...");
      const timer = setTimeout(() => {
        const isReconnectingToGracePeriod = isDisconnectedButActive;
        connectToInterview(false, isReconnectingToGracePeriod);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [connectToInterview, isSessionChecked, showResumeDialog, shouldAutoConnect, isDisconnectedButActive]);

  // Enhanced handleInterviewEnd function with better cleanup and navigation
  const handleInterviewEnd = useCallback(async () => {
    try {
      console.log("Handling interview end...");
      // Set intentional disconnect flag first to prevent reconnection attempts
      setIsIntentionalDisconnect(true);
      
      // Clear disconnect timer if it exists
      if (disconnectTimer) {
        clearTimeout(disconnectTimer);
        setDisconnectTimer(null);
      }
      
      // Clear disconnection states
      setIsDisconnectedButActive(false);
      setDisconnectedAt(null);
      
      // Store the destination URL for later navigation
      const resultUrl = sessionId 
        ? `/ai-interview-result/${interviewId}/${sessionId}`
        : `/ai-interview-result/${interviewId}`;
      
      // Update session status if active
      if (currentUserId && isTimerActive) {
        console.log("Updating session status to completed...");
        try {
          await updateInterviewSessionStatus(interviewId, currentUserId, 'completed');
          console.log("Session status updated successfully");
        } catch (error) {
          console.error("Error updating session status:", error);
          // Continue with cleanup even if status update fails
        }
        
        setIsTimerActive(false);
        
        // Clear local storage session info
        try {
          console.log("Clearing stored session info...");
          clearStoredSessionInfo(interviewId, currentUserId);
        } catch (error) {
          console.error("Error clearing stored session info:", error);
          // Continue even if clearing storage fails
        }
      }
      
      // Disconnect from room if still connected
      if (room.state === 'connected') {
        try {
          console.log("Disconnecting from room...");
          await room.disconnect();
          console.log("Room disconnected successfully");
        } catch (error) {
          console.error("Error disconnecting from room:", error);
          // Continue even if disconnect fails
        }
      }
      
      // Add a delay before navigation to ensure cleanup is complete
      console.log("Preparing for navigation to results page...");
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Add a special flag to localStorage to indicate this is an intentional navigation
      // This helps the result page know that we're coming from a controlled navigation
      localStorage.setItem('intentionalNavigation', 'true');
      localStorage.setItem('navigationTimestamp', Date.now().toString());
      
      // Use window.location.href for most reliable navigation
      console.log(`Navigating to: ${resultUrl}`);
      router.push(resultUrl);
      
    } catch (error) {
      console.error("Error during interview end:", error);
      
      // Fallback navigation if there's an error
      const resultUrl = sessionId 
        ? `/ai-interview-result/${interviewId}/${sessionId}`
        : `/ai-interview-result/${interviewId}`;
      
      // Still set the navigation flag even in error case
      localStorage.setItem('intentionalNavigation', 'true');
      localStorage.setItem('navigationTimestamp', Date.now().toString());
      
      console.log(`Error occurred. Fallback navigation to: ${resultUrl}`);
      router.push(resultUrl);
    }
  }, [interviewId, currentUserId, isTimerActive, room, disconnectTimer, sessionId]);

  // Modified handleForceFinish function 
  const handleForceFinish = useCallback(async () => {
    try {
      console.log("Force finishing interview due to time limit");
      
      // Set the intentional disconnect flag immediately
      setIsIntentionalDisconnect(true);
      
      // Store the destination URL for later navigation
      const resultUrl = sessionId 
        ? `/ai-interview-result/${interviewId}/${sessionId}`
        : `/ai-interview-result/${interviewId}`;
      
      // Update the session status to force_finished before doing anything else
      if (currentUserId && isTimerActive) {
        try {
          console.log("Updating session status to force_finished from handleForceFinish");
          
          // Wait for the status update to complete
          const updateResult = await updateInterviewSessionStatus(interviewId, currentUserId, 'force_finished');
          console.log("Status update result:", updateResult ? "Success" : "Failed");
          
          // Set the timer as inactive
          setIsTimerActive(false);
          
          // Clear local storage session info
          try {
            console.log("Clearing stored session info...");
            clearStoredSessionInfo(interviewId, currentUserId);
          } catch (error) {
            console.error("Error clearing stored session info:", error);
          }
        } catch (error) {
          console.error("Error updating session status during force finish:", error);
        }
      }
      
      // Disconnect from room if still connected
      if (room.state === 'connected') {
        try {
          console.log("Disconnecting from room...");
          await room.disconnect();
          console.log("Room disconnected successfully");
        } catch (error) {
          console.error("Error disconnecting from room:", error);
        }
      }
      
      // Clear disconnect timer if it exists
      if (disconnectTimer) {
        clearTimeout(disconnectTimer);
        setDisconnectTimer(null);
      }
      
      // Clear disconnection states
      setIsDisconnectedButActive(false);
      setDisconnectedAt(null);
      
      // Add a delay before navigation to ensure cleanup is complete
      console.log("Preparing for navigation to results page...");
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Add a special flag to localStorage to indicate this is a force finish
      localStorage.setItem('forcedFinish', 'true');
      localStorage.setItem('forcedFinishTimestamp', Date.now().toString());
      
      // Use window.location.href for most reliable navigation
      console.log(`Navigating to: ${resultUrl}`);
      router.push(resultUrl);
      
    } catch (error) {
      console.error("Error during force finish:", error);
      
      // Fallback navigation if there's an error
      const resultUrl = sessionId 
        ? `/ai-interview-result/${interviewId}/${sessionId}`
        : `/ai-interview-result/${interviewId}`;
      
      // Still set the navigation flag even in error case
      localStorage.setItem('forcedFinish', 'true');
      localStorage.setItem('forcedFinishTimestamp', Date.now().toString());
      
      console.log(`Error during force finish. Fallback navigation to: ${resultUrl}`);
      // router.push(resultUrl);
    }
  }, [interviewId, currentUserId, isTimerActive, room, disconnectTimer, sessionId]);

  // Force disconnect after grace period
  const handleForceDisconnectAfterGracePeriod = useCallback(async () => {
    console.log("Grace period expired, forcing interview end");
    setIsDisconnectedButActive(false);
    setDisconnectedAt(null);
    setDisconnectTimer(null);
    
    if (currentUserId && isTimerActive) {
      await updateInterviewSessionStatus(interviewId, currentUserId, 'disconnected_timeout');
      setIsTimerActive(false);
      clearStoredSessionInfo(interviewId, currentUserId);
    }
    
    router.push('/available-interviews');
  }, [currentUserId, isTimerActive, interviewId, router]);

  // Manual reconnect function
  const handleManualReconnect = useCallback(async () => {
    console.log("Manual reconnect triggered");
    await connectToInterview(false, true);
  }, [connectToInterview]);

  // Room event listeners
  useEffect(() => {
    room.on(RoomEvent.MediaDevicesError, onDeviceFailure);
    
    // Handle room disconnect with grace period for reconnection
    const handleDisconnect = async () => {
      console.log("Room disconnected, isInitialMount:", isInitialMount, "isIntentionalDisconnect:", isIntentionalDisconnect);
      
      if (!isInitialMount && !isIntentionalDisconnect) {
        console.log("Unexpected disconnect detected, starting 5-minute grace period for reconnection");
        
        const disconnectTime = Date.now();
        setDisconnectedAt(disconnectTime);
        setIsDisconnectedButActive(true);
        setReconnectAttempts(0);
        
        // Update session status to grace period
        if (currentUserId) {
          await updateInterviewSessionDisconnected(interviewId, currentUserId, true);
        }
        
        // Set up 5-minute timer to force end interview
        const timer = setTimeout(() => {
          handleForceDisconnectAfterGracePeriod();
        }, 5 * 60 * 1000); // 5 minutes
        
        setDisconnectTimer(timer);
        
        console.log("Grace period started - user has 5 minutes to reconnect");
        
        // Auto-retry connection after a short delay
        setTimeout(() => {
          if (isDisconnectedButActive) {
            console.log("Auto-retry reconnection...");
            connectToInterview(false, true);
          }
        }, 3000); // 3 seconds delay for auto-retry
      } else {
        console.log("Ignoring disconnect event (initial mount or intentional)");
      }
    };
    
    // Handle successful reconnection
    const handleReconnected = async () => {
      console.log("Room reconnected successfully");
      
      if (disconnectTimer) {
        clearTimeout(disconnectTimer);
        setDisconnectTimer(null);
        console.log("Reconnection successful - grace period cancelled");
      }
      
      setIsDisconnectedButActive(false);
      setDisconnectedAt(null);
      setReconnectAttempts(0);
      setIsReconnecting(false);
      
      // Restore session from grace period if needed
      if (currentUserId) {
        await restoreSessionFromGracePeriod(interviewId, currentUserId);
      }
    };
    
    room.on(RoomEvent.Disconnected, handleDisconnect);
    room.on(RoomEvent.Connected, handleReconnected);

    return () => {
      room.off(RoomEvent.MediaDevicesError, onDeviceFailure);
      room.off(RoomEvent.Disconnected, handleDisconnect);
      room.off(RoomEvent.Connected, handleReconnected);
      
      if (disconnectTimer) {
        clearTimeout(disconnectTimer);
      }
    };
  }, [room, router, isInitialMount, isIntentionalDisconnect, handleInterviewEnd, disconnectTimer, handleForceDisconnectAfterGracePeriod, connectToInterview, currentUserId, interviewId, isDisconnectedButActive]);

  // Resume Dialog Handlers
  const handleResumeSession = useCallback(async () => {
    console.log("Resume session button clicked");
    setShowResumeDialog(false);
    setIsTimerActive(true);
    
    try {
      await connectToInterview(true);
    } catch (error) {
      console.error("Failed to resume session:", error);
      setConnectionError("Failed to resume session. Please try starting a new session.");
      setIsTimerActive(false);
    }
  }, [connectToInterview]);

  const handleStartNewSession = useCallback(async () => {
    console.log("Start new session button clicked");
    setShowResumeDialog(false);
    
    if (currentUserId) {
      try {
        console.log("Cancelling previous session...");
        await cancelInterviewSession(interviewId, currentUserId, 'session_replaced');
        clearStoredSessionInfo(interviewId, currentUserId);
        console.log("Previous session cancelled successfully");
      } catch (error) {
        console.error("Error clearing previous session:", error);
      }
    }
    
    setResumeInfo(null);
    
    try {
      await connectToInterview(false);
    } catch (error) {
      console.error("Failed to start new session:", error);
      setConnectionError("Failed to start new session. Please try again.");
    }
  }, [connectToInterview, currentUserId, interviewId]);

  const handleCancelResumeDialog = useCallback(() => {
    console.log("Cancel resume dialog clicked");
    setShowResumeDialog(false);
    setIsIntentionalDisconnect(true);
    router.push('/available-interviews');
  }, [router]);

return (
  <main data-lk-theme="default" className="h-screen bg-[var(--lk-bg)] flex flex-col ">
    <RoomContext.Provider value={room}>
      {/* Resume Dialog */}
      <ResumeDialog
        isOpen={showResumeDialog}
        onResume={handleResumeSession}
        onStartNew={handleStartNewSession}
        onCancel={handleCancelResumeDialog}
        resumeInfo={resumeInfo || undefined}
        interviewName={interviewData?.name}
      />

      {/* Reconnection Status */}
      <ReconnectionStatus
        isDisconnected={isDisconnectedButActive}
        disconnectedAt={disconnectedAt}
        onReconnect={handleManualReconnect}
        reconnectAttempts={reconnectAttempts}
        isReconnecting={isReconnecting}
      />

      {/* Header - Mobile Responsive */}
      <div className="bg-white border-b border-gray-200 p-3 sm:p-4">
        <div className="max-w-7xl mx-auto">
          {/* Mobile Header - Stack vertically */}
          <div className="flex flex-col gap-3 sm:hidden">
            {/* Top row - Back button and connection status */}
            <div className="flex items-center justify-between">
              <Link 
                href="/available-interviews"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft size={18} />
                <span className="text-sm">Kembali</span>
              </Link>
              
              {/* Connection Status - Mobile */}
              {isDisconnectedButActive ? (
                <div className="flex items-center gap-1 text-yellow-600">
                  <WifiOff size={14} />
                  <span className="text-xs">Reconnecting...</span>
                </div>
              ) : room.state === 'connected' ? (
                <div className="flex items-center gap-1 text-green-600">
                  <Wifi size={14} />
                  <span className="text-xs">Connected</span>
                </div>
              ) : null}
            </div>
            
            {/* Interview Info */}
            <div className="flex items-center gap-2">
              <img 
                src="https://cdn.terang.ai/images/logo/logo-terang-ai.svg" 
                alt="Terang AI" 
                className="h-6 w-auto flex-shrink-0"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
              <div className="min-w-0 flex-1">
                <h1 className="text-base font-semibold text-gray-900 truncate">
                  {interviewData?.name || `Interview ${interviewId}`}
                </h1>
                {interviewData?.subname && (
                  <p className="text-xs text-gray-600 truncate">{interviewData.subname}</p>
                )}
              </div>
            </div>
            
            {/* Timer and Tags - Mobile */}
            <div className="flex items-center justify-between gap-2">
              <div className="flex items-center gap-2 flex-wrap">
                {interviewData?.category_name && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                    {interviewData.category_name}
                  </span>
                )}
                {interviewData?.interview_type && (
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    interviewData.interview_type === 'full' 
                      ? 'bg-indigo-100 text-indigo-800' 
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {interviewData.interview_type === 'full' ? 'Full' : 'Focused'}
                  </span>
                )}
              </div>
              
              {/* Timer Component - Mobile */}
              {isTimerActive && currentUserId && (
                <InterviewTimer
                  interviewId={interviewId}
                  userId={currentUserId}
                  onForceFinish={handleForceFinish}
                  isActive={isTimerActive}
                />
              )}
            </div>
          </div>

          {/* Desktop Header - Original layout */}
          <div className="hidden sm:flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link 
                href="/available-interviews"
                className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft size={20} />
                <span>Kembali ke Interview</span>
              </Link>
              <div className="h-6 w-px bg-gray-300" />
              
              <div className="flex items-center gap-3">
                <img 
                  src="https://cdn.terang.ai/images/logo/logo-terang-ai.svg" 
                  alt="Terang AI" 
                  className="h-8 w-auto"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
                <div>
                  <h1 className="text-lg font-semibold text-gray-900">
                    {interviewData?.name || `Interview ${interviewId}`}
                  </h1>
                  {interviewData?.subname && (
                    <p className="text-sm text-gray-600">{interviewData.subname}</p>
                  )}
                </div>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {/* Connection Status Indicator */}
              {isDisconnectedButActive ? (
                <div className="flex items-center gap-2 text-yellow-600">
                  <WifiOff size={16} />
                  <span className="text-sm">Reconnecting...</span>
                </div>
              ) : room.state === 'connected' ? (
                <div className="flex items-center gap-2 text-green-600">
                  <Wifi size={16} />
                  <span className="text-sm">Connected</span>
                </div>
              ) : null}
              
              {/* Timer Component */}
              {isTimerActive && currentUserId && (
                <InterviewTimer
                  interviewId={interviewId}
                  userId={currentUserId}
                  onForceFinish={handleForceFinish}
                  isActive={isTimerActive}
                />
              )}
              
              {interviewData?.category_name && (
                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                  {interviewData.category_name}
                </span>
              )}
              {interviewData?.interview_type && (
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  interviewData.interview_type === 'full' 
                    ? 'bg-indigo-100 text-indigo-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {interviewData.interview_type === 'full' ? 'Full Interview' : 'Focused Session'}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Interview Area - Mobile Responsive */}
      <div className="h-[calc(100vh-140px)] sm:h-[calc(100vh-80px)] flex flex-col">
        <div className="flex-1 w-full mx-auto p-2 sm:p-4 max-w-[1024px]">
          <AIInterviewSession 
            onConnectButtonClicked={() => connectToInterview(false)}
            isConnecting={isConnecting}
            connectionError={connectionError}
            interviewId={interviewId}
            sessionId={sessionId}
            onInterviewEnd={handleInterviewEnd}
            isDisconnectedButActive={isDisconnectedButActive}
          />
        </div>
      </div>
    </RoomContext.Provider>
  </main>
);

// Reconnection Status Component - Mobile Responsive
function ReconnectionStatus({ 
  isDisconnected, 
  disconnectedAt, 
  onReconnect,
  reconnectAttempts,
  isReconnecting
}: { 
  isDisconnected: boolean; 
  disconnectedAt: number | null; 
  onReconnect: () => void;
  reconnectAttempts: number;
  isReconnecting: boolean;
}) {
  const [timeRemaining, setTimeRemaining] = useState<number>(0);

  useEffect(() => {
    if (!isDisconnected || !disconnectedAt) return;

    const interval = setInterval(() => {
      const now = Date.now();
      const elapsed = now - disconnectedAt;
      const remaining = Math.max(0, (5 * 60 * 1000) - elapsed);
      
      setTimeRemaining(remaining);
      
      if (remaining <= 0) {
        clearInterval(interval);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isDisconnected, disconnectedAt]);

  if (!isDisconnected) return null;

  const minutes = Math.floor(timeRemaining / 60000);
  const seconds = Math.floor((timeRemaining % 60000) / 1000);

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      className="fixed top-16 sm:top-20 left-2 right-2 sm:left-1/2 sm:right-auto sm:transform sm:-translate-x-1/2 z-50"
    >
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 sm:p-4 shadow-lg sm:max-w-md">
        <div className="flex items-start sm:items-center gap-3">
          <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse flex-shrink-0 mt-1 sm:mt-0"></div>
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-yellow-800 text-sm sm:text-base">Connection Lost</h3>
            <p className="text-xs sm:text-sm text-yellow-700 mt-1">
              {isReconnecting ? (
                <>Reconnecting... (Attempt {reconnectAttempts})</>
              ) : (
                <>Time remaining: {minutes}:{seconds.toString().padStart(2, '0')}</>
              )}
            </p>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 mt-2">
              <button
                onClick={onReconnect}
                disabled={isReconnecting}
                className="w-full sm:w-auto px-3 py-1 bg-yellow-600 text-white text-xs sm:text-sm rounded hover:bg-yellow-700 transition-colors disabled:opacity-50"
              >
                {isReconnecting ? 'Connecting...' : 'Reconnect Now'}
              </button>
              <span className="text-xs text-yellow-600">
                Interview continues after reconnection
              </span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

// AI Interview Session Component - Mobile Responsive
function AIInterviewSession(props: { 
  onConnectButtonClicked: () => void;
  isConnecting: boolean;
  connectionError: string | null;
  interviewId: string;
  sessionId?: string;
  onInterviewEnd: () => void;
  isDisconnectedButActive?: boolean;
}) {
  const { state: agentState } = useVoiceAssistant();

  if (props.connectionError) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 sm:p-6 w-full max-w-md">
          <h3 className="text-base sm:text-lg font-semibold text-red-800 mb-2">Connection Failed</h3>
          <p className="text-sm sm:text-base text-red-600 mb-4">{props.connectionError}</p>
          <button
            onClick={props.onConnectButtonClicked}
            className="w-full sm:w-auto px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm sm:text-base"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // Show reconnecting state when disconnected but still active (grace period)
  if (props.isDisconnectedButActive) {
    return (
      <motion.div
        key="reconnecting"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="flex flex-col items-center justify-center h-full text-center p-4"
      >
        <div className="space-y-4 max-w-md">
          <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto">
            <div className="animate-spin rounded-full h-full w-full border-4 border-yellow-200 border-t-yellow-600"></div>
          </div>
          <div>
            <h2 className="text-base sm:text-xl font-semibold text-gray-900 mb-2">
              Reconnecting to Interview...
            </h2>
            <p className="text-sm sm:text-base text-gray-600">
              Connection lost. Attempting to reconnect automatically. 
              Your interview session is still active and will continue where you left off.
            </p>
          </div>
          <div className="flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500">
            <WifiOff size={14} className="sm:w-4 sm:h-4" />
            <span>Restoring connection with Professor Terra...</span>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <>
      <AnimatePresence mode="wait">
        {(agentState === "disconnected" || props.isConnecting) ? (
          <motion.div
            key="connecting"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.3, ease: [0.09, 1.04, 0.245, 1.055] }}
            className="flex flex-col items-center justify-center h-full text-center p-4"
          >
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="space-y-4 max-w-md"
            >
              <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto">
                <div className="animate-spin rounded-full h-full w-full border-4 border-gray-200 border-t-blue-600"></div>
              </div>
              <div>
                <h2 className="text-base sm:text-xl font-semibold text-gray-900 mb-2">
                  {props.isConnecting ? "Connecting to Interview..." : "Preparing Interview Room"}
                </h2>
                <p className="text-sm sm:text-base text-gray-600">
                  Please wait while we set up your interview session with Professor Terra. 
                  Make sure your microphone is working properly.
                </p>
              </div>
              <div className="flex items-center justify-center gap-2 text-xs sm:text-sm text-gray-500">
                <Users size={14} className="sm:w-4 sm:h-4" />
                <span>Setting up AI interviewer...</span>
              </div>
            </motion.div>
          </motion.div>
        ) : (
          <motion.div
            key="connected"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3, ease: [0.09, 1.04, 0.245, 1.055] }}
            className="flex flex-col h-full"
          >
            <AgentVisualizer />
            <div className="flex-1 mt-4 sm:mt-6 min-h-0">
              <TranscriptionView />
            </div>
            <div className="mt-2 sm:mt-4">
              <ControlBar 
                interviewId={props.interviewId} 
                sessionId={props.sessionId}
                onInterviewEnd={props.onInterviewEnd}
              />
            </div>
            <RoomAudioRenderer />
            <NoAgentNotification state={agentState} />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

function AgentVisualizer() {
  const { state: agentState, videoTrack, audioTrack } = useVoiceAssistant();
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const videoElement = videoRef.current;
    if (videoElement && videoTrack?.publication?.track) {
      const track = videoTrack.publication.track;
      
      // Attach the track to the video element
      track.attach(videoElement);
      
      // Cleanup function
      return () => {
        track.detach(videoElement);
      };
    }
  }, [videoTrack]);

  if (videoTrack) {
    return (
      <div className="h-[250px] sm:h-[300px] md:h-[400px] w-full max-w-[300px] sm:max-w-[400px] mx-auto rounded-lg overflow-hidden">
        <video
          ref={videoRef}
          className="h-full w-full object-cover"
          autoPlay
          playsInline
          muted
        />
      </div>
    );
  }
  
  return (
    <div className="h-[200px] sm:h-[250px] md:h-[300px] w-full px-4 sm:px-6">
      <BarVisualizer
        state={agentState}
        barCount={5}
        trackRef={audioTrack}
        className="agent-visualizer"
        options={{ minHeight: 20 }}
      />
    </div>
  );
}

function ControlBar({ 
  interviewId, 
  sessionId,
  onInterviewEnd 
}: { 
  interviewId: string;
  sessionId?: string;
  onInterviewEnd: () => void;
}) {
  const { state: agentState } = useVoiceAssistant();

  return (
    <div className="relative h-[50px] sm:h-[60px]">
      <AnimatePresence>
        {agentState !== "disconnected" && agentState !== "connecting" && (
          <motion.div
            initial={{ opacity: 0, top: "10px" }}
            animate={{ opacity: 1, top: 0 }}
            exit={{ opacity: 0, top: "-10px" }}
            transition={{ duration: 0.4, ease: [0.09, 1.04, 0.245, 1.055] }}
            className="flex h-8 absolute left-1/2 -translate-x-1/2 justify-center items-center"
          >
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="scale-90 sm:scale-100">
                <VoiceAssistantControlBar controls={{ leave: false }} />
              </div>
              
              <div className="scale-90 sm:scale-100">
                <FinishIcon 
                  interviewId={interviewId}
                  sessionId={sessionId}
                  onFinish={onInterviewEnd}
                />
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

function onDeviceFailure(error: Error) {
  console.error(error);
  alert(
    "Error acquiring camera or microphone permissions. Please make sure you grant the necessary permissions in your browser and reload the tab"
  );
}
}