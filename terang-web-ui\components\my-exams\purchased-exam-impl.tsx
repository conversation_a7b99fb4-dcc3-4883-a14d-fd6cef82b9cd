"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  examSession,
  examTrialSession,
  fetchLastExamSession,
} from "./actions";

import { usePathname, useRouter } from "next/navigation";
import {
  <PERSON><PERSON>,
  <PERSON>dalB<PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Spin<PERSON>,
} from "@heroui/react";

import { AvailableExamsType, ExamSession } from "../types";

interface Props {
  examData: AvailableExamsType;
  initialTrialMode?: boolean;
  onClose: () => void;
}

interface ExamConfirmationStepProps {
  examData: AvailableExamsType;
  isTrialExam: boolean;
  activeSession: ExamSession | null;
  onConfirm: () => void;
}

// Simplified ExamConfirmationStep component
const ExamConfirmationStep = React.memo(({ 
  examData, 
  isTrialExam, 
  activeSession, 
  onConfirm
}: ExamConfirmationStepProps) => {
  const formatDuration = (durationString: string): string => {
    const [hours, minutes, seconds] = durationString.split(":").map(Number);
    return `${hours}h ${minutes}m ${seconds}s`;
  };

  const getButtonText = (): string => {
    if (activeSession && activeSession.status === "ACTIVE") {
      return isTrialExam ? "Lanjutkan Latihan" : "Lanjutkan Ujian";
    }
    return isTrialExam ? "Mulai Latihan" : "Mulai Ujian";
  };

  return (
    <div>
      {activeSession && activeSession.status === "ACTIVE" ? (
        <div>
          <p>
            Kamu memiliki sesi aktif untuk {isTrialExam ? "latihan" : "ujian"}{" "}
            {examData.name}:
          </p>
          <ul className="list-disc list-inside mt-2">
            <li>Status: {activeSession.status}</li>
            <li>
              Waktu Tersisa:{" "}
              {formatDuration(activeSession.remaining_duration || "")}
            </li>
          </ul>
          <p className="mt-2">Apakah kamu ingin melanjutkan sesi ini?</p>
        </div>
      ) : (
        <p>
          Kamu yakin akan memulai {isTrialExam ? "latihan ujian" : "ujian"}:{" "}
          <br /> <br />
          <b>{examData.name}</b>?
        </p>
      )}
      <div className="flex justify-end mt-4">
        <Button color="primary" onClick={onConfirm}>
          {getButtonText()}
        </Button>
      </div>
    </div>
  );
});

ExamConfirmationStep.displayName = 'ExamConfirmationStep';

// Main PurchasedExam Component 
const PurchasedExamImpl: React.FC<Props> = ({ examData, initialTrialMode = false, onClose }) => {
  const [lastExamSession, setLastExamSession] = useState<ExamSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);  // Start with loading state
  const [error, setError] = useState<string | null>(null);
  const [isTrialExam, setIsTrialExam] = useState(initialTrialMode);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const router = useRouter();

  // Memoize session data fetching to avoid unnecessary re-fetches
  const fetchSessionData = useCallback(async () => {
    if (!examData.id) return;
    
    setIsLoading(true);
    setError(null);
    try {
      const examSession = await fetchLastExamSession(examData.id);
      setLastExamSession(examSession);
    } catch (error) {
      console.error("Error fetching exam sessions:", error);
      setError("Failed to fetch session information. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [examData.id]);

  // Only fetch on first render, not on every component update
  useEffect(() => {
    fetchSessionData();
  }, [fetchSessionData]);

  const startExam = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      const formDataToSend = new FormData();
      formDataToSend.append("id", examData.id);

      if (isTrialExam) {
        await examTrialSession(formDataToSend);
      } else {
        await examSession(formDataToSend);
      }
      onClose();
    } catch (err: any) {
      // Handle Next.js redirects specially
      if ((err)?.digest?.startsWith('NEXT_REDIRECT')) {
        throw err; // Re-throw to let Next.js handle the redirect
      }
  
      console.error("Error starting exam:", err);
      const errorMessage = err instanceof Error ? err.message : "Failed to start trial exam";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [examData.id, isTrialExam, onClose]);

  const getActiveSession = useCallback((): ExamSession | null => {
    return isTrialExam ? null : lastExamSession;
  }, [isTrialExam, lastExamSession]);

  if (isSubmitting) {
    return (
      <ModalContent>
        <ModalBody>
          <div className="text-center py-8">
            <Spinner color="primary" size="lg" />
            <p className="mt-4 text-xl">Submitting...</p>
          </div>
        </ModalBody>
      </ModalContent>
    );
  }

  return (
    <ModalContent>
      <ModalHeader className="flex flex-col gap-1">
        {isTrialExam ? "Mulai Latihan Ujian" : "Mulai Ujian"}
      </ModalHeader>
      <ModalBody>
        {isLoading ? (
          <div className="flex justify-center items-center py-6">
            <Spinner label="Memuat informasi..." />
          </div>
        ) : error ? (
          <div className="p-4 text-danger bg-danger-50 rounded-lg">
            <p className="text-danger">{error}</p>
          </div>
        ) : (
          <ExamConfirmationStep
            activeSession={getActiveSession()}
            examData={examData}
            isTrialExam={isTrialExam}
            onConfirm={startExam}
          />
        )}
      </ModalBody>
    </ModalContent>
  );
};

export default PurchasedExamImpl;