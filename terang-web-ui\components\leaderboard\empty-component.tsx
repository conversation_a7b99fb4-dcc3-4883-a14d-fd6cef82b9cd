import React from 'react';
import { Card, CardBody, Button } from "@heroui/react";
import { Alert02Icon } from 'hugeicons-react';

interface EmptyStateMessageProps {
  title?: string;
  message?: string;
  actionLabel?: string;
  onAction?: () => void;
}

const EmptyStateMessage: React.FC<EmptyStateMessageProps> = ({ 
  title = "No Data Available", 
  message = "There are no entries in the leaderboard yet.", 
  actionLabel,
  onAction 
}) => {
  return (
    <Card className="w-full">
      <CardBody className="py-8">
        <div className="flex flex-col items-center justify-center text-center gap-4">
          <Alert02Icon size={48} className="text-default-400" />
          <div className="space-y-2">
            <h3 className="text-xl font-semibold">{title}</h3>
            <p className="text-default-500">{message}</p>
          </div>
          {actionLabel && onAction && (
            <Button 
              color="primary" 
              variant="flat"
              onPress={onAction}
              className="mt-2"
            >
              {actionLabel}
            </Button>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

export default EmptyStateMessage;