"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import Image from "next/image";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>, 
  ChevronRight, 
  Play, 
  CheckCircle,
  ChevronLeft,
  Globe
} from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dalFooter,
  ModalHeader,
  useDisclosure,
  RadioGroup,
  Radio
} from "@heroui/react";
import { AvailableInterview } from "@/components/types";
import DotLottieAnimation from "../shared/dotlottie-animation";
import { createInterviewSession } from "@/app/lib/actions/available-interviews/timer-actions";
import { getUserId } from "@/app/lib/actions/account/actions";

interface InterviewCardProps {
  interviewData: AvailableInterview;
  setRefreshData: React.Dispatch<React.SetStateAction<boolean>>;
  showNotification: (message: string, type: "success" | "info" | "error") => void;
}

// Helper function to format duration (HH:MM:SS to readable format)
const formatDuration = (durationString: string): string => {
  // Handle empty or invalid input
  if (!durationString) {
    return "Durasi tidak diketahui";
  }

  // Parse the duration string (HH:MM:SS)
  const parts = durationString.split(':');
  if (parts.length !== 3) {
    return durationString; // Return as-is if not in expected format
  }

  const hours = parseInt(parts[0], 10);
  const minutes = parseInt(parts[1], 10);

  // Build the formatted string
  const formattedParts = [];

  if (hours > 0) {
    formattedParts.push(`${hours} jam`);
  }

  if (minutes > 0) {
    formattedParts.push(`${minutes} menit`);
  }

  return formattedParts.join(' ');
};

export const InterviewCard: React.FC<InterviewCardProps> = ({
  interviewData,
  setRefreshData,
  showNotification,
}) => {
  console.log(interviewData)
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [isCreatingSession, setIsCreatingSession] = useState(false);
  const [currentStep, setCurrentStep] = useState(1); // 1 = language selection, 2 = interview details
  const [selectedLanguage, setSelectedLanguage] = useState<string>("id");
  console.log("selectedLanguage", selectedLanguage)

  // Determine if the interview is free
  const isFree = interviewData.baseline_price === 0 || interviewData.is_free_access === "true";

  // Custom style for the type badge
  const typeBadgeStyle = interviewData.interview_type === "full" 
    ? "bg-indigo-100 text-indigo-800" 
    : "bg-green-100 text-green-800";

  // Get section count
  const sectionCount = interviewData.sections ? interviewData.sections.length : 0;

  // Language options
  const languageOptions = [
    {
      value: "id",
      label: "Bahasa Indonesia",
      description: "Interview akan dilakukan dalam Bahasa Indonesia",
      flag: "🇮🇩"
    },
    {
      value: "en",
      label: "English",
      description: "Interview will be conducted in English",
      flag: "🇺🇸"
    }
  ];

  // Use actual interview data
  const interviewDetails = {
    interviewer: {
      name: "Professor Terra",
      title: "LPDP Senior Interviewer",
      experience: "20+ years experience",
      description: "Experienced LPDP interviewer specializing in scholarship assessment and candidate evaluation."
    },
    format: {
      type: interviewData.interview_type === "full" ? "Full Interview" : "Focused Session",
      duration: formatDuration(interviewData.duration),
      language: selectedLanguage === "id" ? "Bahasa Indonesia" : "English",
      style: "Professional conversation with constructive feedback"
    },
    sections: interviewData.sections || [],
    tips: [
      "Speak clearly and confidently",
      "Connect your answers to Indonesia's development",
      "Provide specific examples and experiences",
      "Show your passion for your chosen field",
      "Demonstrate your commitment to returning to Indonesia"
    ],
    technicalRequirements: [
      "Stable internet connection",
      "Working microphone and speakers/headphones",
      "Quiet environment for optimal experience",
      "Chrome or Firefox browser recommended"
    ]
  };

  // Function to handle starting interview with session creation
  const handleStartInterview = async () => {
    setIsCreatingSession(true);
    try {
      // Get user ID
      const userId = await getUserId();
      if (!userId || typeof userId !== 'string') {
        showNotification("Please log in to start the interview", "error");
        setIsCreatingSession(false);
        return;
      }

      // Create new session with interview duration and selected language
      const { data: session } = await createInterviewSession(
        interviewData.id,
        userId,
        interviewData.duration,
        undefined,  // userEmail
        undefined,  // userName
        selectedLanguage
      );
      console.log("Selected language:", selectedLanguage);
      console.log("Created session:", session.sessionId);
      
      // Navigate to the interview with session ID
      window.location.href = `/ai-interview/${interviewData.id}/${session.sessionId}?language=${selectedLanguage}`;
      
    } catch (error) {
      console.error("Failed to create interview session:", error);
      showNotification("Failed to start interview. Please try again.", "error");
    } finally {
      setIsCreatingSession(false);
    }
  };

  // Reset modal state when closed
  const handleModalClose = () => {
    setCurrentStep(1);
    setSelectedLanguage("id");
  };

  // Handle next step
  const handleNextStep = () => {
    setCurrentStep(2);
  };

  // Handle back step
  const handleBackStep = () => {
    setCurrentStep(1);
  };

  return (
    <>
      <div className="relative w-full h-full bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden transform transition-all hover:shadow-md hover:scale-[1.01]">
        {/* Interview Image */}
        <div className="relative h-48 w-full bg-gray-100">
          {interviewData.media_url ? (
            <Image
              src={interviewData.media_url}
              alt={interviewData.name}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              style={{ objectFit: "cover" }}
              className="transition-opacity"
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-blue-50">
              <span className="text-blue-300 text-4xl">
                {interviewData.category_name === "LPDP" && "🎓"}
                {interviewData.category_name === "CPNS" && "🏛️"}
                {interviewData.category_name === "BUMN" && "🏢"}
                {!["LPDP", "CPNS", "BUMN"].includes(interviewData.category_name || "") && "✨"}
              </span>
            </div>
          )}

          {/* Badges */}
          <div className="absolute top-3 left-3 flex gap-2">
            <span className={`px-2 py-1 text-xs font-medium rounded-full ${typeBadgeStyle}`}>
              {interviewData.interview_type === "full" ? "Full Interview" : "Focused Session"}
            </span>
          </div>

          {/* Price Badge */}
          <div className="absolute top-3 right-3">
            <span 
              className={`px-2 py-1 rounded-full text-xs font-medium ${
                isFree ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"
              }`}
            >
              {isFree ? "Gratis" : `Rp ${interviewData.baseline_price.toLocaleString()}`}
            </span>
          </div>
        </div>

        {/* Interview Info */}
        <div className="p-4">
          <h3 className="font-semibold text-lg line-clamp-2">{interviewData.name}</h3>
          <p className="text-sm text-gray-600 mb-3">{interviewData.subname}</p>
          <p className="text-xs text-gray-500 line-clamp-2 h-8 mb-3">
            {interviewData.description}
          </p>

          {/* Stats */}
          <div className="flex flex-col space-y-2 mb-4">
            <div className="flex items-center text-xs text-gray-600">
              <Clock size={14} className="mr-2" />
              <span>{formatDuration(interviewData.duration)}</span>
            </div>
            
            <div className="flex items-center text-xs text-gray-600">
              <Award size={14} className="mr-2" />
              <span>{interviewData.category_name} Interview</span>
            </div>
            
            {sectionCount > 0 && (
              <div className="flex items-center text-xs text-gray-600">
                <UserCheck size={14} className="mr-2" />
                <span>{sectionCount} {sectionCount === 1 ? 'Bagian' : 'Bagian'}</span>
              </div>
            )}
          </div>

          {/* Section Pills (if applicable) */}
          {interviewData.sections && interviewData.sections.length > 0 && (
            <div className="mb-4">
              <div className="flex flex-wrap gap-1 mt-1">
                {interviewData.sections.map((section) => (
                  <span 
                    key={section.id} 
                    className="inline-block px-2 py-0.5 bg-gray-100 text-gray-700 rounded text-xs"
                    title={section.name}
                  >
                    {section.name.split(' ')[0]}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* View Details Button */}
          <Button
            onPress={onOpen}
            className="mt-2 w-full bg-blue-600 text-white hover:bg-blue-700"
            endContent={<ChevronRight size={16} />}
          >
            Lihat Detail Interview
          </Button>
        </div>
      </div>

      {/* Interview Details Modal with Language Selection */}
      <Modal 
        isOpen={isOpen} 
        onOpenChange={(open) => {
          onOpenChange();
          if (!open) handleModalClose();
        }}
        size="4xl"
        scrollBehavior="inside"
        classNames={{
          base: "max-h-[90vh]",
          header: "border-b-1 border-gray-200",
          footer: "border-t-1 border-gray-200",
          body: "py-6"
        }}
        hideCloseButton={true}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                {/* Progress Indicator */}
                <div className="flex items-center gap-2 mb-4">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
                  }`}>
                    1
                  </div>
                  <div className={`flex-1 h-1 ${currentStep >= 2 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600'
                  }`}>
                    2
                  </div>
                </div>

                {currentStep === 1 ? (
                  <div className="flex items-center gap-3">
                    <Globe className="text-blue-600" size={24} />
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900">Pilih Bahasa Interview</h2>
                      <p className="text-gray-600 font-normal">Pilih bahasa yang akan digunakan dalam interview</p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-start">
                    <div className="w-20 h-20 flex-shrink-0">
                      <DotLottieAnimation
                        src="/dotlotties/ai-voice.lottie"
                        autoplay
                        loop
                        width={"100%"}
                        height={"100%"}
                      />
                    </div>
                    <div className="flex flex-col gap p-4">
                      <h2 className="text-2xl font-bold text-gray-900">{interviewData.name}</h2>
                      <p className="text-gray-600 font-normal">{interviewData.subname}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Globe size={16} className="text-blue-600" />
                        <span className="text-sm text-blue-600 font-medium">
                          {selectedLanguage === "id" ? "Bahasa Indonesia" : "English"}
                        </span>
                      </div>
                    </div>
                  </div>
                )}
              </ModalHeader>

              <ModalBody className="gap-6">
                {currentStep === 1 ? (
                  // Step 1: Language Selection
                  <div className="space-y-6">
                    <RadioGroup
                      value={selectedLanguage}
                      onValueChange={setSelectedLanguage}
                      className="gap-4"
                    >
                      {languageOptions.map((option) => (
                        <Radio
                          key={option.value}
                          value={option.value}
                          classNames={{
                            base: "inline-flex m-0 bg-content1 hover:bg-content2 items-center justify-between flex-row-reverse max-w-full cursor-pointer rounded-lg gap-4 p-4 border-2 border-transparent data-[selected=true]:border-primary",
                            label: "text-base"
                          }}
                        >
                          <div className="flex items-start gap-4 w-full">
                            <span className="text-2xl">{option.flag}</span>
                            <div className="flex-1">
                              <h3 className="text-lg font-semibold text-gray-900">{option.label}</h3>
                              <p className="text-sm text-gray-600 mt-1">{option.description}</p>
                            </div>
                          </div>
                        </Radio>
                      ))}
                    </RadioGroup>

                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h3 className="font-medium text-blue-900 mb-2">📝 Catatan Penting</h3>
                      <ul className="text-sm text-blue-800 space-y-1">
                        <li>• Pastikan Anda memilih bahasa yang paling nyaman untuk digunakan</li>
                        <li>• Bahasa yang dipilih akan digunakan sepanjang sesi interview</li>
                        <li>• Anda dapat menggunakan bahasa yang berbeda untuk menjawab pertanyaan</li>
                      </ul>
                    </div>
                  </div>
                ) : (
                  // Step 2: Interview Details (existing content)
                  <>
                    {/* Interviewer Info */}
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <h3 className="font-semibold text-lg mb-2 flex items-center gap-2">
                        <span className="text-2xl">👩‍🏫</span>
                        Your Interviewer
                      </h3>
                      <div className="space-y-1">
                        <p className="font-medium">{interviewDetails.interviewer.name}</p>
                        <p className="text-sm text-gray-600">{interviewDetails.interviewer.title}</p>
                        <p className="text-sm text-gray-600">{interviewDetails.interviewer.experience}</p>
                        <p className="text-sm text-gray-700 mt-2">{interviewDetails.interviewer.description}</p>
                      </div>
                    </div>

                    {/* Interview Format */}
                    <div className="bg-green-50 p-4 rounded-lg">
                      <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                        <span className="text-2xl">📋</span>
                        Interview Format
                      </h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-700">Type</p>
                          <p className="text-sm text-gray-600">{interviewDetails.format.type}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-700">Duration</p>
                          <p className="text-sm text-gray-600">{interviewDetails.format.duration}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-700">Language</p>
                          <p className="text-sm text-gray-600">{interviewDetails.format.language}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-700">Style</p>
                          <p className="text-sm text-gray-600">{interviewDetails.format.style}</p>
                        </div>
                      </div>
                    </div>

                    {/* Interview Sections */}
                    <div>
                      <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                        <span className="text-2xl">📚</span>
                        Interview Sections
                      </h3>
                      <div className="space-y-3">
                        {interviewDetails.sections.map((section) => (
                          <div key={section.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-medium text-gray-900">{section.name}</h4>
                              <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                {section.duration}
                              </span>
                            </div>
                            <p className="text-sm text-gray-600">
                              {section.id === "personal" && "Questions about your personal background, motivation, and character"}
                              {section.id === "study_plans" && "Discussion about your university choice, program selection, and academic plans"}
                              {section.id === "contributions" && "Your plans to contribute to Indonesia after completing your studies"}
                              {section.id === "qualifications" && "Your academic achievements and qualifications for the program"}
                              {section.id === "leadership" && "Your leadership and organizational experience"}
                              {section.id === "knowledge_indonesia" && "Your understanding of Indonesia's challenges and opportunities"}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Tips for Success */}
                    <div className="bg-yellow-50 p-4 rounded-lg">
                      <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                        <span className="text-2xl">💡</span>
                        Tips for Success
                      </h3>
                      <ul className="space-y-2">
                        {interviewDetails.tips.map((tip, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm text-gray-700">
                            <CheckCircle size={16} className="text-green-500 mt-0.5 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Technical Requirements */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                        <span className="text-2xl">⚙️</span>
                        Technical Requirements
                      </h3>
                      <ul className="space-y-2">
                        {interviewDetails.technicalRequirements.map((req, index) => (
                          <li key={index} className="flex items-start gap-2 text-sm text-gray-700">
                            <CheckCircle size={16} className="text-blue-500 mt-0.5 flex-shrink-0" />
                            {req}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </>
                )}
              </ModalBody>

              <ModalFooter>
                {currentStep === 1 ? (
                  <>
                    <Button color="danger" variant="flat" onPress={onClose}>
                      Batal
                    </Button>
                    <Button
                      color="primary"
                      onPress={handleNextStep}
                      endContent={<ChevronRight size={16} />}
                      isDisabled={!selectedLanguage}
                    >
                      Lanjutkan
                    </Button>
                  </>
                ) : (
                  <>
                    <Button
                      color="default"
                      variant="flat"
                      onPress={handleBackStep}
                      startContent={<ChevronLeft size={16} />}
                    >
                      Kembali
                    </Button>
                    <Button color="danger" variant="flat" onPress={onClose}>
                      Batal
                    </Button>
                    <Button
                      color="primary"
                      onPress={handleStartInterview}
                      startContent={<Play size={16} />}
                      isLoading={isCreatingSession}
                    >
                      {isCreatingSession ? "Creating Session..." : "Mulai Interview"}
                    </Button>
                  </>
                )}
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
};