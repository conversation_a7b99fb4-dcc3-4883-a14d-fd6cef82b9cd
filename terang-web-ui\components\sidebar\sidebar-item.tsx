import NextLink from "next/link";
import React from "react";
import clsx from "clsx";

import { useSidebarContext } from "../layout/layout-context";

interface Props {
  title: string;
  icon: React.ReactNode;
  isActive?: boolean;
  href?: string;
}

export const SidebarItem = ({ icon, title, isActive, href = "" }: Props) => {
  const { collapsed, setCollapsed } = useSidebarContext();

  const handleClick = () => {
    if (window.innerWidth < 768) {
      setCollapsed();
    }
  };

  return (
    <NextLink
      className="text-default-900 active:bg-none max-w-full"
      href={href}
    >
      <div
        className={clsx(
          isActive
            ? "bg-primary-100 [&_svg_path]:fill-primary-500"
            : "hover:bg-default-100",
          "flex gap-1 w-full min-h-[32px] h-full items-center px-2.5 rounded-lg cursor-pointer transition-all duration-150 active:scale-[0.98]",
        )}
        role="presentation"
        onClick={handleClick}
      >
        {icon}
        <span className="text-default-900 text-sm">{title}</span>
      </div>
    </NextLink>
  );
};