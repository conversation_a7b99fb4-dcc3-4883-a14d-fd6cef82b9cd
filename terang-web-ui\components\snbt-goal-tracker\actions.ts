"use server";

import { auth } from "@/auth";

export interface SnbtGoalTrackerFormData {
  name: string;
  email: string;
  penalaranUmum: number;
  pengetahuanKuantitatif: number;
  pengetahuanPemahamanUmum: number;
  pemahamanBacaanMenulis: number;
  literasiBahasaIndonesia: number;
  literasiBahasaInggris: number;
  penalaranMatematika: number;
  passedSnbt: boolean;
  feltHelped: boolean;
  helpfulnessRating: number;
  mostHelpfulAspect: string;
  improvementSuggestions: string;
  contactConsent: boolean;
  phoneNumber: string;
}

export interface FormErrors {
  [key: string]: string;
}

export interface SubmissionResult {
  success: boolean;
  message?: string;
  data?: any;
}

// Validate SNBT goal tracker form data
export async function validateSnbtGoalTrackerForm(data: SnbtGoalTrackerFormData): Promise<FormErrors> {
  const errors: FormErrors = {};

  // Validate required fields
  if (!data.name || data.name.trim().length === 0) {
    errors.name = "Nama harus diisi";
  }

  if (!data.email || data.email.trim().length === 0) {
    errors.email = "Email harus diisi";
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.email = "Format email tidak valid";
  }

  // Validate score fields (should be non-negative numbers)
  const scoreFields = [
    { field: 'penalaranUmum', label: 'Penalaran Umum' },
    { field: 'pengetahuanKuantitatif', label: 'Pengetahuan Kuantitatif' },
    { field: 'pengetahuanPemahamanUmum', label: 'Pengetahuan dan Pemahaman Umum' },
    { field: 'pemahamanBacaanMenulis', label: 'Pemahaman Bacaan dan Menulis' },
    { field: 'literasiBahasaIndonesia', label: 'Literasi Bahasa Indonesia' },
    { field: 'literasiBahasaInggris', label: 'Literasi Bahasa Inggris' },
    { field: 'penalaranMatematika', label: 'Penalaran Matematika' }
  ];

  scoreFields.forEach(({ field, label }) => {
    const value = data[field as keyof SnbtGoalTrackerFormData] as number;
    if (value === undefined || value === null || isNaN(value) || value < 0) {
      errors[field] = `${label} harus berupa angka yang valid (minimal 0)`;
    }
  });

  // Validate helpfulness rating
  if (data.helpfulnessRating === undefined || data.helpfulnessRating === null || 
      isNaN(data.helpfulnessRating) || data.helpfulnessRating < 1 || data.helpfulnessRating > 10) {
    errors.helpfulnessRating = "Rating kepuasan harus antara 1-10";
  }

  // Validate phone number if contact consent is given
  if (data.contactConsent && (!data.phoneNumber || data.phoneNumber.trim().length === 0)) {
    errors.phoneNumber = "Nomor telepon harus diisi jika Anda setuju untuk dihubungi";
  }

  return errors;
}

// Submit SNBT goal tracker form
export async function submitSnbtGoalTrackerForm(data: SnbtGoalTrackerFormData): Promise<SubmissionResult> {
  try {
    console.log("[SNBT Goal Tracker Actions] Submitting form data:", data);

    const response = await fetch(`${process.env.BACKEND_BASE_URL}/v0/snbt-goal-tracker`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': process.env.BACKEND_API_KEY as string,
      },
      body: JSON.stringify({
        name: data.name,
        email: data.email,
        penalaran_umum: data.penalaranUmum,
        pengetahuan_kuantitatif: data.pengetahuanKuantitatif,
        pengetahuan_pemahaman_umum: data.pengetahuanPemahamanUmum,
        pemahaman_bacaan_menulis: data.pemahamanBacaanMenulis,
        literasi_bahasa_indonesia: data.literasiBahasaIndonesia,
        literasi_bahasa_inggris: data.literasiBahasaInggris,
        penalaran_matematika: data.penalaranMatematika,
        passed_snbt: data.passedSnbt,
        felt_helped: data.feltHelped,
        helpfulness_rating: data.helpfulnessRating,
        most_helpful_aspect: data.mostHelpfulAspect,
        improvement_suggestions: data.improvementSuggestions,
        contact_consent: data.contactConsent,
        phone_number: data.phoneNumber,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      console.error("[SNBT Goal Tracker Actions] Submission failed:", result);
      return {
        success: false,
        message: result.message || `HTTP error! status: ${response.status}`,
      };
    }

    console.log("[SNBT Goal Tracker Actions] Submission successful:", result);
    return {
      success: true,
      message: "Form berhasil dikirim",
      data: result.data,
    };
  } catch (error) {
    console.error("[SNBT Goal Tracker Actions] Error submitting form:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Terjadi kesalahan tidak diketahui",
    };
  }
}

// Check if user has already submitted SNBT goal tracker form
export async function checkSnbtGoalTrackerSubmission(email?: string): Promise<boolean> {
  try {
    // If no email provided, try to get from session
    let userEmail = email;
    if (!userEmail) {
      const session = await auth();
      userEmail = session?.user?.email || "";
    }

    if (!userEmail) {
      console.log("[SNBT Goal Tracker Actions] No email available for submission check");
      return false;
    }

    console.log("[SNBT Goal Tracker Actions] Checking submission status for:", userEmail);

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/snbt-goal-tracker/check-submission?email=${encodeURIComponent(userEmail)}`,
      {
        method: 'GET',
        headers: {
          'X-API-KEY': process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!response.ok) {
      console.error("[SNBT Goal Tracker Actions] Failed to check submission status:", response.status);
      return false;
    }

    const result = await response.json();
    console.log("[SNBT Goal Tracker Actions] Submission check result:", result);

    return result.submitted || false;
  } catch (error) {
    console.error("[SNBT Goal Tracker Actions] Error checking submission:", error);
    return false;
  }
}
