import { Player, Controls } from '@lottiefiles/react-lottie-player';

type Props = {
  jsonData: any;
  width?: any | '300px';
  height?: any | '300px';
};

const LottieAnimation = ({ jsonData, width, height }: Props) => {

  return (
    <Player
        autoplay
        loop
        src={jsonData}
        style={{ height: height, width: width }}
    >
        <Controls visible={false} buttons={['play', 'repeat', 'frame', 'debug']} />
    </Player>
  );
};

export default LottieAnimation;
