"use client";

import React, { useEffect, useCallback, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  Input,
  Spinner
} from "@heroui/react";

import { AvailableExamsType } from "../types";
import { 
  orderExams, 
  updatePaymentStatus, 
  finalRedeemDiscount, 
  validateAndTrackReferralCode,
  storePaymentInfo,
  getPaymentInfo,
  removePaymentInfo,
  storePaymentToken,
  getPaymentToken
} from "@/app/lib/actions/available-exams/actions";

interface Props {
  examData: AvailableExamsType;
  showNotification?: (
    message: string,
    type: "success" | "info" | "error",
  ) => void;
  setRefreshData?: React.Dispatch<React.SetStateAction<boolean>>;
  onOpenChange: () => void;
  continuePayment?: boolean;
  onAfterClose?: () => void; // New callback prop
}

interface PaymentInfo {
  orderId: string;
  examId: string;
  invoiceId: string;
  paymentId: string;
  refCode?: string;
}

interface DiscountInfo {
  originalPrice: number;
  discountPercentage: number;
  discountAmount: number;
  finalPrice: number;
}

interface PaymentResult {
  transaction_status: string;
  payment_type: string;
  issuer: string;
  acquirer: string;
  transaction_id: string;
  transaction_time: string;
}

declare global {
  interface Window {
    snap?: {
      pay: (token: string, options: any) => void;
    };
  }
}

// Helper function to get user-friendly error message
const getUserFriendlyError = (error: any): string => {
  // If it's an error response from the server
  if (error?.message?.includes("Failed to validate referral code")) {
    return "Kode referral tidak valid. Silakan periksa kembali.";
  }
  
  // Handle other specific error cases
  if (error?.message?.includes("500")) {
    return "Terjadi kesalahan. Silakan coba beberapa saat lagi.";
  }

  // Default error message
  return "Gagal memproses pembelian. Silakan coba lagi.";
};

const PurchaseExamModal: React.FC<Props> = ({
  examData,
  showNotification,
  setRefreshData,
  onOpenChange,
  continuePayment = false,
  onAfterClose
}) => {
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo | null>(null);
  const [referralCode, setReferralCode] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [discountInfo, setDiscountInfo] = useState<DiscountInfo | null>(null);
  const [scriptLoaded, setScriptLoaded] = useState(false);
  const [isInitiatingContinuePayment, setIsInitiatingContinuePayment] = useState(continuePayment);

  // Enhanced onOpenChange that also calls onAfterClose
  const handleModalClose = useCallback(() => {
    onOpenChange();
    
    // Call onAfterClose callback after a small delay to ensure state updates
    if (onAfterClose) {
      setTimeout(() => {
        onAfterClose();
      }, 100);
    }
  }, [onOpenChange, onAfterClose]);

  const revalidatePage = useCallback(() => {
    setRefreshData?.((prev) => !prev);
  }, [setRefreshData]);

  // Handle payment with Midtrans
  const handlePayment = useCallback(async (orderId: string, invoiceId: string, paymentId: string) => {
    if (typeof window.snap === 'undefined') {
      console.error("Snap.js is not loaded");
      showNotification?.("Payment system is not ready. Please try again later.", "error");
      handleModalClose();
      return;
    }

    try {
      const token = await getPaymentToken(orderId);
      const storedPaymentInfo = await getPaymentInfo();

      if (!token) {
        throw new Error('Payment token not found');
      }

      window.snap.pay(token, {
        onSuccess: async (result: any) => {
          console.log("Payment successful", result);
          showNotification?.("Payment successful!", "success");
          
          try {
            const paymentStatus = await checkPaymentStatus(orderId);
            
            if (paymentStatus.transaction_status === "settlement") {
              const paymentTypeString = `${paymentStatus.payment_type}_${paymentStatus.issuer}_${paymentStatus.acquirer}`;
              await updatePaymentStatus(
                orderId, 
                invoiceId, 
                paymentId, 
                "PAID", 
                "COMPLETED", 
                "PAID",
                paymentTypeString,
                paymentStatus.transaction_id,
                paymentStatus.transaction_time
              );
              
              if (storedPaymentInfo?.refCode) {
                const redeemResult = await finalRedeemDiscount(paymentId, storedPaymentInfo.refCode);
                if (redeemResult.success) {
                  showNotification?.("Referral code applied successfully!", "success");
                } else {
                  showNotification?.(redeemResult.error || "Failed to apply referral code", "error");
                  console.error("Referral redemption failed:", redeemResult.error);
                }
              }

              showNotification?.("Order status updated successfully!", "success");
            } else {
              showNotification?.("Payment status inconsistent. Please contact support.", "error");
            }
          } catch (error) {
            console.error("Error updating order status:", error);
            showNotification?.("Error updating order status. Please contact support.", "error");
          }

          await removePaymentInfo();
          setPaymentInfo(null);
          revalidatePage();
          handleModalClose();
        },
        onPending: (result: any) => {
          console.log("Payment pending", result);
          showNotification?.(
            "Payment is pending. You can close this window and continue payment later.",
            "info"
          );
          handleModalClose();
        },
        onError: (result: any) => {
          console.error("Payment error", result);
          showNotification?.("Payment failed. Please try again.", "error");
          handleModalClose();
        },
        onClose: () => {
          console.log("Payment popup closed");
          showNotification?.(
            "Payment window closed. You can continue payment later.",
            "info"
          );
          handleModalClose();
        },
      });
    } catch (error) {
      console.error("Error initiating payment:", error);
      showNotification?.("Error initiating payment. Please try again.", "error");
      handleModalClose();
    }
  }, [showNotification, revalidatePage, handleModalClose]);

  async function checkPaymentStatus(orderId: string): Promise<PaymentResult> {
    const response = await fetch(`/api/check-payment-status?orderId=${orderId}`, {
      headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
      },
    });
  
    if (!response.ok) {
      throw new Error(`Failed to fetch payment status: ${response.statusText}`);
    }
  
    const data = await response.json();
    return data as PaymentResult;
  }

  // Load payment info and script, then automatically continue payment if needed
  useEffect(() => {
    let isMounted = true;
    let scriptLoadTimeout: NodeJS.Timeout;
    
    const loadMidtransAndPaymentInfo = async () => {
      // Only load script if not already loaded
      if (!document.getElementById("midtrans-script")) {
        const script = document.createElement("script");
        const isProduction = process.env.NODE_ENV === "production";
        script.src = isProduction
          ? "https://app.midtrans.com/snap/snap.js"
          : "https://app.sandbox.midtrans.com/snap/snap.js";
        script.id = "midtrans-script";
        script.async = true;
  
        const clientKey = isProduction
          ? process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY_PRODUCTION
          : process.env.NEXT_PUBLIC_MIDTRANS_CLIENT_KEY_SANDBOX;
  
        script.setAttribute("data-client-key", clientKey || "");
        
        // Set a timeout in case script loading takes too long
        scriptLoadTimeout = setTimeout(() => {
          if (isMounted) {
            if (continuePayment) {
              showNotification?.("Payment system is taking too long to load. Please try again later.", "error");
              handleModalClose();
            } else {
              setScriptLoaded(false);
            }
          }
        }, 10000); // 10 seconds timeout
        
        script.onload = () => {
          clearTimeout(scriptLoadTimeout);
          if (isMounted) {
            setScriptLoaded(true);
            
            // If in continue payment mode, immediately process the payment
            if (continuePayment) {
              handleContinuePayment();
            }
          }
        };
        
        document.body.appendChild(script);
      } else {
        // Script already exists
        setScriptLoaded(true);
        
        // If in continue payment mode, immediately process the payment
        if (continuePayment) {
          handleContinuePayment();
        }
      }
    };
    
    // Function to handle continuing payment
    const handleContinuePayment = async () => {
      try {
        const storedPaymentInfo = await getPaymentInfo();
        
        if (!isMounted) return;
        
        if (storedPaymentInfo && storedPaymentInfo.examId === examData.id) {
          setPaymentInfo(storedPaymentInfo);
          
          // If payment info exists and script is loaded, immediately continue payment
          if (typeof window.snap !== 'undefined') {
            await handlePayment(
              storedPaymentInfo.orderId, 
              storedPaymentInfo.invoiceId, 
              storedPaymentInfo.paymentId
            );
          }
        } else {
          // No payment info found for this exam
          showNotification?.(
            "No pending payment found for this exam. Please start a new purchase.",
            "error"
          );
          handleModalClose();
        }
      } catch (error) {
        console.error("Error fetching payment info:", error);
        if (continuePayment) {
          showNotification?.(
            "Error retrieving payment information. Please try again.",
            "error"
          );
          handleModalClose();
        }
      } finally {
        if (isMounted) {
          setIsInitiatingContinuePayment(false);
        }
      }
    };
    
    loadMidtransAndPaymentInfo();
    
    return () => {
      isMounted = false;
      clearTimeout(scriptLoadTimeout);
    };
  }, [continuePayment, examData.id, handlePayment, handleModalClose, showNotification]);

  const handleValidateReferral = async () => {
    if (!referralCode) return;
    
    setIsValidating(true);
    setErrorMessage("");
    
    try {
      const validation = await validateAndTrackReferralCode(referralCode);
      
      if (validation.isValid && validation.refereeRewardAmount) {
        // Convert the reward amount directly (it's already a percentage)
        const discountPercentage = validation.refereeRewardAmount;
        const discountAmount = Math.floor((examData.baseline_price * discountPercentage) / 100);
        
        // Calculate final price and ensure it's not negative
        const finalPrice = Math.max(0, examData.baseline_price - discountAmount);
  
        setDiscountInfo({
          originalPrice: examData.baseline_price,
          discountPercentage,
          discountAmount,
          finalPrice
        });
        
        showNotification?.(
          "Kode referral berhasil divalidasi",
          "success"
        );
      } else {
        // Clear any existing discount info
        setDiscountInfo(null);
        setErrorMessage(validation.errorMessage || "Kode referral tidak valid");
        showNotification?.(
          validation.errorMessage || "Kode referral tidak valid",
          "error"
        );
      }
    } catch (error) {
      // Handle any unexpected errors
      console.error("Error validating referral code:", error);
      setDiscountInfo(null);
      setErrorMessage("Gagal memvalidasi kode referral. Silakan coba lagi");
      showNotification?.(
        "Gagal memvalidasi kode referral. Silakan coba lagi",
        "error"
      );
    } finally {
      setIsValidating(false);
    }
  };

  const onBuy = useCallback(async () => {
    setIsProcessing(true);
    setErrorMessage("");
    try {
      const response = await orderExams(examData, referralCode || undefined);

      if (!response.ok) {
        throw new Error(response.message || "Failed to process order");
      }

      const { orderId, token, examId, invoiceId, paymentId, refCode } = response;
      const newPaymentInfo = { orderId, examId, invoiceId, paymentId, refCode };
      setPaymentInfo(newPaymentInfo);

      await storePaymentInfo(newPaymentInfo);
      await storePaymentToken(orderId, token);

      if (examData.baseline_price === 0) {
        showNotification?.("Exam ticket acquired successfully!", "success");
        revalidatePage();
        handleModalClose();
      } else {
        await handlePayment(orderId, invoiceId, paymentId);
      }
    } catch (error: any) {
      console.error("Error during transaction:", error);
      const friendlyError = getUserFriendlyError(error);
      setErrorMessage(friendlyError);
      showNotification?.(friendlyError, "error");
      setIsProcessing(false);
    }
  }, [examData, handlePayment, handleModalClose, referralCode, revalidatePage, showNotification]);

  const formatPrice = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // If we're in continue payment mode, just show a loading spinner
  // The useEffect will handle opening the payment window automatically
  if (continuePayment) {
    return (
      <ModalContent>
        <ModalBody>
          <div className="flex flex-col items-center justify-center p-10 gap-4">
            <Spinner size="lg" />
            <p>Mempersiapkan pembayaran...</p>
          </div>
        </ModalBody>
      </ModalContent>
    );
  }

  // Regular purchase flow modal content
  return (
    <ModalContent>
      <ModalHeader className="flex flex-col gap-1">
        Beli Kartu
      </ModalHeader>
      <ModalBody className="gap-4">
        <div className="flex flex-col gap-4">
          <div className="flex flex-col gap-2">
            <label htmlFor="referalCode" className="text-sm font-medium">Kode Referral (Opsional)</label>
            <div className="flex gap-2">
              <Input
                id="referalCode"
                value={referralCode}
                onChange={(e) => {
                  setErrorMessage("");
                  setReferralCode(e.target.value.toUpperCase());
                  setDiscountInfo(null);
                }}
                placeholder="Masukkan kode referral"
                className="flex-1"
                isInvalid={!!errorMessage}
              />
              <Button
                color="primary"
                size="md"
                onPress={handleValidateReferral}
                isDisabled={!referralCode || isValidating}
              >
                {isValidating ? "Validasi..." : "Terapkan"}
              </Button>
            </div>
            {errorMessage && (
              <p className="text-sm text-red-500">
                {errorMessage}
              </p>
            )}
          </div>

          <div className="border-t pt-4">
            {discountInfo ? (
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-gray-500">
                  <span>Harga Normal:</span>
                  <span className="line-through">
                    {formatPrice(discountInfo.originalPrice)}
                  </span>
                </div>
                <div className="flex justify-between text-sm text-green-600">
                  <span>Diskon ({discountInfo.discountPercentage}%):</span>
                  <span>
                    -{formatPrice(discountInfo.discountAmount)}
                  </span>
                </div>
                <div className="flex justify-between font-semibold">
                  <span>Total:</span>
                  <span>
                    {formatPrice(discountInfo.finalPrice)}
                  </span>
                </div>
              </div>
            ) : (
              <div className="flex justify-between font-semibold">
                <span>Total:</span>
                <span>
                  {formatPrice(examData.baseline_price)}
                </span>
              </div>
            )}
          </div>
        </div>
      </ModalBody>
      <ModalFooter>
        <Button color="danger" variant="flat" onClick={handleModalClose}>
          Close
        </Button>
        <Button
          color="primary"
          onPress={onBuy}
          isLoading={isProcessing || !scriptLoaded}
          isDisabled={!!errorMessage || isValidating || !scriptLoaded}
        >
          Beli Kartu
        </Button>
      </ModalFooter>
    </ModalContent>
  );
};

export default PurchaseExamModal;