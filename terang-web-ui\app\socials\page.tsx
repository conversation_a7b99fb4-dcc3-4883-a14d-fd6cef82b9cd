"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ody,
  CardFooter,
  Avatar,
  Button,
  Input,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Image,
  Badge,
  Tooltip,
} from "@heroui/react";
import { motion } from "framer-motion";

interface User {
  name: string;
  level: number;
  likePoints: number;
  isVerified: boolean;
  role: "student" | "teacher" | "admin";
}

interface Post {
  id: number;
  author: User;
  content: string;
  likes: number;
  comments: Comment[];
  image?: string;
  tags: string[];
}

interface Comment {
  id: number;
  author: User;
  content: string;
}

const dummyUsers: User[] = [
  {
    name: "Ari Quantum",
    level: 5,
    likePoints: 150,
    isVerified: true,
    role: "teacher",
  },
  {
    name: "Sinta Biologi",
    level: 4,
    likePoints: 120,
    isVerified: false,
    role: "student",
  },
  {
    name: "<PERSON><PERSON>",
    level: 3,
    likePoints: 80,
    isVerified: false,
    role: "student",
  },
  {
    name: "<PERSON><PERSON>",
    level: 4,
    likePoints: 110,
    isVerified: true,
    role: "teacher",
  },
  {
    name: "Galang Filosofi",
    level: 5,
    likePoints: 200,
    isVerified: true,
    role: "admin",
  },
];

const TerangMediaSosial = () => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [newPost, setNewPost] = useState("");
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [newComment, setNewComment] = useState("");
  const { isOpen, onOpen, onClose } = useDisclosure();

  useEffect(() => {
    // Initialize with dummy posts
    setPosts([
      {
        id: 1,
        author: dummyUsers[0],
        content:
          "Guys, baru aja upload materi keren tentang Teori Kuantum! Mind = Blown 🤯",
        likes: 15,
        comments: [
          {
            id: 1,
            author: dummyUsers[2],
            content: "Wah, gak sabar nih buat deep dive ke materinya, Ari!",
          },
          {
            id: 2,
            author: dummyUsers[3],
            content: "Kira-kira ada hubungannya dengan astrofisika gak?",
          },
        ],
        tags: ["#QuantumTheory", "#Physics"],
      },
      {
        id: 2,
        author: dummyUsers[2],
        content:
          "Update progress belajar semester ini. Alhamdulillah ada peningkatan! 📈",
        likes: 25,
        comments: [
          {
            id: 1,
            author: dummyUsers[1],
            content: "Keren banget, Den! Keep it up! 💪",
          },
          {
            id: 2,
            author: dummyUsers[0],
            content:
              "Niceee! Jadi inget dulu kita sering begadang ngerjain tugas bareng 😄",
          },
        ],
        image: "/images/laporan.png",
        tags: ["#StudyProgress", "#Motivation"],
      },
      {
        id: 3,
        author: dummyUsers[1],
        content:
          "Reminder guys: Besok praktikum Biologi Molekuler. Jangan lupa bawa jas lab ya! 🧪🔬",
        likes: 8,
        comments: [],
        tags: ["#BiologyLab", "#Reminder"],
      },
      {
        id: 4,
        author: dummyUsers[4],
        content:
          "Open slot bimbingan skripsi nih. Yang butuh konsul, gue free setiap Selasa & Kamis. Sliding into my DMs aja ya! 📚",
        likes: 20,
        comments: [
          {
            id: 1,
            author: dummyUsers[2],
            content:
              "Thanks infonya, Galang! Gue bakal mampir Selasa depan ya.",
          },
        ],
        tags: ["#ThesisConsultation", "#OpenSlot"],
      },
      {
        id: 5,
        author: dummyUsers[3],
        content:
          "Guys, ada yang mau join study group Kalkulus buat persiapan UAS minggu depan? Kita bisa collab bareng nih! 🚀📊",
        likes: 5,
        comments: [],
        tags: ["#StudyGroup", "#Calculus"],
      },
    ]);
  }, []);

  // Assume we have a current user
  const currentUser: User = {
    name: "Kamu",
    level: 1,
    likePoints: 0,
    isVerified: false,
    role: "student",
  };

  const handlePostSubmit = () => {
    if (newPost.trim()) {
      const post: Post = {
        id: posts.length + 1,
        author: currentUser,
        content: newPost,
        likes: 0,
        comments: [],
        tags: [], // You might want to add a way for users to input tags
        image: undefined, // Optional image, you can add UI for image upload if needed
      };

      setPosts([post, ...posts]);
      setNewPost("");
    }
  };

  const handleLike = (postId: number) => {
    setPosts(
      posts.map((post) => {
        if (post.id === postId) {
          // Increase post likes
          const updatedPost = { ...post, likes: post.likes + 1 };

          // Increase author's like points
          updatedPost.author = {
            ...updatedPost.author,
            likePoints: updatedPost.author.likePoints + 1,
          };

          return updatedPost;
        }

        return post;
      }),
    );
  };

  const handleComment = (post: Post) => {
    setSelectedPost(post);
    onOpen();
  };

  const submitComment = () => {
    if (newComment.trim() && selectedPost) {
      const comment: Comment = {
        id: selectedPost.comments.length + 1,
        author: currentUser,
        content: newComment,
      };

      setPosts(
        posts.map((post) =>
          post.id === selectedPost.id
            ? { ...post, comments: [...post.comments, comment] }
            : post,
        ),
      );
      setNewComment("");
      onClose();
    }
  };

  const createDummyPost = () => {
    const author = dummyUsers[Math.floor(Math.random() * dummyUsers.length)];
    const content = `Update hasil exam terakhir dari ${author.name}: ${getRandomExamResult()}`;
    const post: Post = {
      id: posts.length + 1,
      author,
      content,
      likes: 0,
      comments: [],
      image: "/images/laporan.png",
      tags: ["#ExamResult", "#StudyProgress"],
    };

    setPosts([post, ...posts]);
  };

  const getRandomExamResult = () => {
    const subjects = ["Matematika", "Fisika", "Kimia", "Biologi", "Sejarah"];
    const grades = ["A", "A-", "B+", "B", "B-"];
    const subject = subjects[Math.floor(Math.random() * subjects.length)];
    const grade = grades[Math.floor(Math.random() * grades.length)];

    return `Alhamdulillah, dapet ${grade} di ujian ${subject}! 🎉📚`;
  };

  const Home = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      color={"#000"}
      fill={"none"}
      height={24}
      viewBox="0 0 24 24"
      width={24}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M12 1.25C11.1339 1.25 10.3597 1.52688 9.52381 1.99594C8.7139 2.45043 7.78586 3.12145 6.61887 3.96524L5.10984 5.05632C4.1733 5.73346 3.42528 6.27429 2.86109 6.77487C2.27855 7.29173 1.84274 7.807 1.5663 8.45513C1.28925 9.10469 1.22225 9.77045 1.25941 10.5381C1.29528 11.2789 1.43264 12.1727 1.60393 13.2872L1.91918 15.3387C2.16256 16.9225 2.35635 18.1836 2.64105 19.1662C2.93544 20.1821 3.35016 20.9887 4.0914 21.6052C4.82957 22.2192 5.7089 22.4926 6.78306 22.6231C7.828 22.75 9.14615 22.75 10.8111 22.75H13.1889C14.8539 22.75 16.172 22.75 17.2169 22.6231C18.2911 22.4926 19.1704 22.2192 19.9086 21.6052C20.6499 20.9887 21.0646 20.1821 21.359 19.1662C21.6437 18.1837 21.8374 16.9225 22.0808 15.3387L22.3961 13.2871C22.5674 12.1726 22.7047 11.2789 22.7406 10.5381C22.7778 9.77045 22.7108 9.10469 22.4337 8.45513C22.1573 7.807 21.7215 7.29173 21.1389 6.77487C20.5747 6.2743 19.8267 5.73347 18.8902 5.05633L17.3811 3.96525C16.2142 3.12146 15.2861 2.45043 14.4762 1.99594C13.6403 1.52688 12.8661 1.25 12 1.25Z"
        fill="currentColor"
        opacity="0.4"
      />
      <path
        clipRule="evenodd"
        d="M9 18C9 17.4477 9.44772 17 10 17L14 17C14.5523 17 15 17.4477 15 18C15 18.5523 14.5523 19 14 19L10 19C9.44772 19 9 18.5523 9 18Z"
        fill="currentColor"
        fillRule="evenodd"
      />
    </svg>
  );
  const Search = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      color={"#000"}
      fill={"none"}
      height={24}
      viewBox="0 0 24 24"
      width={24}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.5 17.5L22 22"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
      />
      <path
        d="M20 11C20 6.02944 15.9706 2 11 2C6.02944 2 2 6.02944 2 11C2 15.9706 6.02944 20 11 20C15.9706 20 20 15.9706 20 11Z"
        fill="currentColor"
        opacity="0.4"
      />
      <path
        d="M20 11C20 6.02944 15.9706 2 11 2C6.02944 2 2 6.02944 2 11C2 15.9706 6.02944 20 11 20C15.9706 20 20 15.9706 20 11Z"
        stroke="currentColor"
        strokeLinejoin="round"
        strokeWidth="1.5"
      />
    </svg>
  );

  const Bell = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      color={"#000"}
      fill={"none"}
      height={24}
      viewBox="0 0 24 24"
      width={24}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M6.91458 10.3577C8.6263 7.85593 12.1899 7.52473 14.3334 9.66817C16.4768 11.8116 16.1456 15.3752 13.6439 17.087L13.5246 17.1685C12.5817 17.8137 12.0179 18.8826 12.0179 20.0251V20.12C12.0179 21.5706 10.2641 22.2971 9.23834 21.2714L2.73017 14.7632C1.70443 13.7375 2.4309 11.9836 3.88151 11.9836H3.97648C5.11897 11.9836 6.18785 11.4198 6.83299 10.4769L6.91458 10.3577Z"
        fill="currentColor"
        opacity="0.4"
      />
      <path
        d="M20.8764 6.36077C21.9584 5.50653 22.0526 3.89942 21.0778 2.92459C20.103 1.94977 18.4959 2.04398 17.6416 3.12603L14.3877 7.24759C14.2571 7.41309 14.1918 7.49585 14.2047 7.58163C14.2177 7.66742 14.3089 7.73 14.4911 7.85515C14.8084 8.0731 15.1112 8.32375 15.3949 8.60751C15.6787 8.89124 15.9293 9.19398 16.1473 9.51129C16.2724 9.69352 16.335 9.78463 16.4208 9.79762C16.5066 9.81062 16.5893 9.74529 16.7548 9.61463L20.8764 6.36077Z"
        fill="currentColor"
      />
      <path
        d="M3.90775 20.0949C3.19352 19.3807 3.06269 18.304 3.51527 17.4569C3.57432 17.3464 3.72224 17.3327 3.81084 17.4213L6.58141 20.1918C6.67 20.2804 6.65625 20.4283 6.54574 20.4874C5.69871 20.94 4.62199 20.8091 3.90775 20.0949Z"
        fill="currentColor"
      />
      <path
        clipRule="evenodd"
        d="M20.1819 15.0168C20.725 15.1172 21.0839 15.6388 20.9836 16.1819C20.7651 17.3642 20.1937 18.4823 19.2676 19.3762C18.397 20.2165 17.3284 20.7474 16.2021 20.9796C15.6612 21.0911 15.1323 20.743 15.0208 20.2021C14.9093 19.6612 15.2574 19.1323 15.7983 19.0208C16.5782 18.86 17.2981 18.4975 17.8786 17.9372C18.4944 17.3429 18.8715 16.6053 19.0168 15.8185C19.1172 15.2754 19.6388 14.9165 20.1819 15.0168Z"
        fill="currentColor"
        fillRule="evenodd"
      />
      <path
        clipRule="evenodd"
        d="M8.97814 3.79131C9.09352 4.33141 8.74921 4.86277 8.20911 4.97814C7.42701 5.14521 6.70061 5.52256 6.11159 6.11159C5.52256 6.70061 5.14521 7.42701 4.97814 8.20911C4.86277 8.74921 4.3314 9.09352 3.79131 8.97814C3.25121 8.86277 2.9069 8.33141 3.02227 7.79131C3.26601 6.65025 3.82237 5.57238 4.69737 4.69737C5.57238 3.82237 6.65025 3.26601 7.79131 3.02227C8.33141 2.9069 8.86277 3.25121 8.97814 3.79131Z"
        fill="currentColor"
        fillRule="evenodd"
      />
    </svg>
  );

  const User = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      color={"#000"}
      fill={"none"}
      height={24}
      viewBox="0 0 24 24"
      width={24}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M17.8061 14.8372C17.9224 14.9064 18.0661 14.9875 18.2288 15.0793C18.9416 15.4814 20.0191 16.0893 20.7573 16.8118C21.2189 17.2637 21.6576 17.8592 21.7373 18.5888C21.8221 19.3646 21.4837 20.0927 20.8047 20.7396C19.6332 21.8556 18.2274 22.75 16.4091 22.75H7.59086C5.77255 22.75 4.36677 21.8556 3.19532 20.7396C2.5163 20.0927 2.17784 19.3646 2.26265 18.5888C2.34239 17.8592 2.78104 17.2637 3.24271 16.8118C3.98088 16.0893 5.05839 15.4814 5.7712 15.0793L5.77123 15.0792C5.93389 14.9875 6.07755 14.9064 6.19386 14.8372C9.74791 12.7209 14.2521 12.7209 17.8061 14.8372Z"
        fill="currentColor"
      />
      <path
        d="M6.75 6.5C6.75 3.6005 9.1005 1.25 12 1.25C14.8995 1.25 17.25 3.6005 17.25 6.5C17.25 9.39949 14.8995 11.75 12 11.75C9.1005 11.75 6.75 9.39949 6.75 6.5Z"
        fill="currentColor"
        opacity="0.4"
      />
    </svg>
  );

  const LikePoints = () => (
    <svg
      fill="none"
      height="24"
      viewBox="0 0 24 24"
      width="24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"
        fill="#FFD700"
        stroke="#FFD700"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
    </svg>
  );

  const VerifiedBadge = () => (
    <svg
      fill="none"
      height="16"
      viewBox="0 0 24 24"
      width="16"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
        stroke="#3B82F6"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      />
    </svg>
  );

  return (
    <div className="max-w-2xl mx-auto p-4 bg-gradient-to-r from-blue-100 to-purple-100 min-h-screen pb-20">
      <h1 className="flex items-center justify-center mb-6 gap-2">
        <Image
          alt="Terang Media Sosial Logo"
          height={40}
          src="/images/logo-terang.png"
          width={40}
        />
        <span className="text-4xl font-extrabold text-center bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-blue-600 drop-shadow-lg">
          Terang Medsos
        </span>
      </h1>

      <Card className="mb-6 bg-white shadow-lg">
        <CardBody>
          <Input
            className="mb-2"
            placeholder="Apa yang lagi lo pikirin?"
            value={newPost}
            onChange={(e) => setNewPost(e.target.value)}
          />
          <div className="flex justify-between pt-5">
            <Button color="primary" onClick={handlePostSubmit}>
              Post
            </Button>
            <Button color="secondary" onClick={createDummyPost}>
              Post Hasil Exam Terakhir
            </Button>
          </div>
        </CardBody>
      </Card>

      {posts.map((post) => (
        <Card
          key={post.id}
          className="mb-4 bg-white shadow-md hover:shadow-lg transition-shadow duration-300"
        >
          <CardHeader className="justify-between">
            <div className="flex gap-3">
              <Avatar
                radius="full"
                size="md"
                src={`https://i.pravatar.cc/150?u=${post.author.name}`}
              />
              <div className="flex flex-col gap-1 items-start justify-center">
                <h4 className="text-small font-semibold leading-none text-default-600 flex items-center gap-1">
                  {post.author.name}
                  {post.author.isVerified && (
                    <Tooltip content="Verified User">
                      <VerifiedBadge />
                    </Tooltip>
                  )}
                </h4>
                <h5 className="text-xs text-default-400">
                  Level {post.author.level} {post.author.role}
                </h5>
              </div>
            </div>
            <Tooltip content={`${post.author.likePoints} Like Points`}>
              <div className="flex items-center gap-1">
                <LikePoints />
                <span className="text-small text-default-400">
                  {post.author.likePoints}
                </span>
              </div>
            </Tooltip>
          </CardHeader>
          <CardBody className="px-3 py-0 text-small">
            <p className="py-2 text-default-700 font-medium">{post.content}</p>
            {post.image && (
              <Image
                alt="Laporan Belajar"
                className="object-cover rounded-xl"
                height={300}
                src={post.image}
                width={400}
              />
            )}
            <div className="flex gap-1 mt-2">
              {post.tags.map((tag, index) => (
                <Badge key={index} color="primary" variant="flat">
                  {tag}
                </Badge>
              ))}
            </div>
            {post.comments.length > 0 && (
              <div className="mt-4">
                <h5 className="text-small font-semibold mb-2 text-default-700">
                  Komentar:
                </h5>
                {post.comments.map((comment) => (
                  <div
                    key={comment.id}
                    className="bg-gray-50 p-2 rounded mb-2 shadow-sm"
                  >
                    <p className="text-xs font-semibold text-default-700 flex items-center gap-1">
                      {comment.author.name}
                      {comment.author.isVerified && <VerifiedBadge />}
                    </p>
                    <p className="text-sm text-default-600">
                      {comment.content}
                    </p>
                  </div>
                ))}
              </div>
            )}
          </CardBody>
          <CardFooter className="gap-3">
            <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
              <Button
                color="primary"
                variant="light"
                onClick={() => handleLike(post.id)}
              >
                👍 {post.likes}
              </Button>
            </motion.div>
            <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
              <Button
                color="primary"
                variant="light"
                onClick={() => handleComment(post)}
              >
                💬 {post.comments.length}
              </Button>
            </motion.div>
          </CardFooter>
        </Card>
      ))}

      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          <ModalHeader>Tambahin Komentar</ModalHeader>
          <ModalBody>
            <Input
              placeholder="Tulis komentar lo di sini"
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
            />
          </ModalBody>
          <ModalFooter>
            <Button color="danger" variant="light" onClick={onClose}>
              Batal
            </Button>
            <Button color="primary" onClick={submitComment}>
              Kirim
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
      <div className="fixed bottom-0 left-0 right-0 bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] md:hidden z-50">
        <nav className="flex justify-around items-center h-16">
          <Button isIconOnly className="h-full" variant="light">
            <Home />
          </Button>
          <Button isIconOnly className="h-full" variant="light">
            <Search />
          </Button>
          <Button isIconOnly className="h-full relative" variant="light">
            <Badge
              className="absolute -top-1 -right-1"
              color="danger"
              content=""
              shape="circle"
              size="sm"
            >
              <Bell />
            </Badge>
          </Button>
          <Button isIconOnly className="h-full" variant="light">
            <User />
          </Button>
        </nav>
      </div>
    </div>
  );
};

export default TerangMediaSosial;
