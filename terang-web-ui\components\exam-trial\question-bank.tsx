"use client";

import React, { useRef, useEffect, useState } from "react";
import { <PERSON>, <PERSON>Header, CardBody } from "@heroui/react";
import { SquareLock02Icon } from "hugeicons-react";
import { ExitExam } from "./buttons/exit-exam";
import LivesDisplay from "./lives-display";
import { retrieveFromRedis, storeToRedis } from "./actions";

interface QuestionBankProps {
  totalQuestions: number;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  flaggedQuestions: { [key: string]: boolean };
  selectedOptions: { [key: string]: string };
  isTimeUp: boolean;
  onExitExam: () => void;
  isLoading: boolean;
  questionIds: string[];
  correctAnswers: { [key: string]: boolean };
  sessionId: string;
  gamification: any;
  isPremium: boolean
}

export const QuestionBank: React.FC<QuestionBankProps> = ({
  totalQuestions,
  currentPage,
  setCurrentPage,
  flaggedQuestions,
  selectedOptions,
  isTimeUp,
  onExitExam,
  isLoading,
  questionIds,
  correctAnswers,
  sessionId,
  gamification,
  isPremium,
}) => {
  const [showScrollbar, setShowScrollbar] = useState(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [highestUnlockedQuestion, setHighestUnlockedQuestion] = useState<number>(0);
  // Track visited questions to prevent re-locking
  const [visitedQuestions, setVisitedQuestions] = useState<Set<number>>(new Set());

  // Load the last accessed page, highest unlocked question, and visited questions from Redis
  useEffect(() => {
    const loadSavedProgress = async () => {
      try {
        // Get the last page from Redis
        const savedPage = await retrieveFromRedis(`${sessionId}_currentPage`);
        const savedHighestQuestion = await retrieveFromRedis(`${sessionId}_highestUnlockedQuestion`);
        const savedVisitedQuestions = await retrieveFromRedis(`${sessionId}_visitedQuestions`);
        
        // Calculate the highest question that should be unlocked based on correct answers
        let calculatedHighestQuestion = 0;
        for (let i = 0; i < questionIds.length; i++) {
          if (correctAnswers[questionIds[i]]) {
            calculatedHighestQuestion = i + 1;
          } else {
            break;
          }
        }

        // Use the higher value between saved and calculated
        const highestQuestion = Math.max(
          calculatedHighestQuestion,
          savedHighestQuestion ? parseInt(savedHighestQuestion) : 0
        );

        setHighestUnlockedQuestion(highestQuestion);

        // Initialize visited questions set
        let initialVisitedSet = new Set([0]); // Always make first question visited

        // If there's a saved page, mark all questions up to that page as visited
        if (savedPage) {
          const pageNumber = parseInt(savedPage);
          // Mark all questions from 0 to the current page as visited
          for (let i = 0; i <= pageNumber; i++) {
            initialVisitedSet.add(i);
          }
        }

        // Load additional visited questions from Redis if available
        if (savedVisitedQuestions) {
          try {
            const parsedVisited = JSON.parse(savedVisitedQuestions);
            // Merge with existing set
            parsedVisited.forEach((index: number) => initialVisitedSet.add(index));
          } catch (e) {
            console.error("Error parsing visited questions:", e);
          }
        }

        setVisitedQuestions(initialVisitedSet);

        // If there's a saved page, set it as current
        if (savedPage) {
          const pageNumber = parseInt(savedPage);
          setCurrentPage(pageNumber);
        }
      } catch (error) {
        console.error("Error loading saved progress:", error);
      }
    };

    loadSavedProgress();
  }, [sessionId, correctAnswers, questionIds, setCurrentPage]);

  // Mark current page as visited whenever it changes and ensure all previous questions are also marked as visited
  useEffect(() => {
    setVisitedQuestions(prev => {
      const newSet = new Set(prev);
      // Mark all questions from 0 to currentPage as visited
      for (let i = 0; i <= currentPage; i++) {
        newSet.add(i);
      }
      return newSet;
    });
  }, [currentPage]);

  // Save progress to Redis whenever it changes
  useEffect(() => {
    const saveProgress = async () => {
      try {
        await storeToRedis(`${sessionId}_currentPage`, currentPage.toString());
        await storeToRedis(`${sessionId}_highestUnlockedQuestion`, highestUnlockedQuestion.toString());
        // Save visited questions as JSON array
        await storeToRedis(`${sessionId}_visitedQuestions`, JSON.stringify(Array.from(visitedQuestions)));
      } catch (error) {
        console.error("Error saving progress to Redis:", error);
      }
    };

    saveProgress();
  }, [currentPage, highestUnlockedQuestion, sessionId, visitedQuestions]);

  useEffect(() => {
    const checkOverflow = () => {
      if (scrollContainerRef.current) {
        const isOverflowing =
          scrollContainerRef.current.scrollHeight >
          scrollContainerRef.current.clientHeight;
        setShowScrollbar(isOverflowing);
      }
    };

    checkOverflow();
    window.addEventListener("resize", checkOverflow);
    return () => window.removeEventListener("resize", checkOverflow);
  }, [questionIds]);

  // Update highestUnlockedQuestion when correctAnswers changes
  useEffect(() => {
    let newHighestQuestion = highestUnlockedQuestion;
    
    for (let i = 0; i <= highestUnlockedQuestion; i++) {
      if (correctAnswers[questionIds[i]]) {
        newHighestQuestion = Math.max(newHighestQuestion, i + 1);
      }
    }

    if (newHighestQuestion !== highestUnlockedQuestion) {
      setHighestUnlockedQuestion(newHighestQuestion);
      
      // Also mark any newly unlocked questions as visited
      setVisitedQuestions(prev => {
        const newSet = new Set(prev);
        for (let i = 0; i <= newHighestQuestion; i++) {
          newSet.add(i);
        }
        return newSet;
      });
    }
  }, [correctAnswers, questionIds, highestUnlockedQuestion]);

  // Calculate insights
  const answeredCount = Object.keys(selectedOptions).length;
  const unansweredCount = totalQuestions - answeredCount;
  const flaggedCount = Object.keys(flaggedQuestions).length;

  const handleQuestionClick = (index: number) => {
    if (isQuestionClickable(index)) {
      setCurrentPage(index);
    }
  };

  const isQuestionClickable = (index: number): boolean => {
    // A question is clickable if:
    // 1. It's the current page
    // 2. It has been visited before (user has seen it)
    // 3. It's within the unlocked range based on correct answers
    return (
      index === currentPage || 
      visitedQuestions.has(index) || 
      index <= highestUnlockedQuestion
    );
  };

  const shouldShowLock = (index: number): boolean => {
    return !isQuestionClickable(index);
  };

  return (
    <Card className="w-full py-4" style={{ fontFamily: "'Nunito', sans-serif" }}>
      <CardHeader className="flex justify-center items-center pb-0 pt-2 px-4">
        <h4 className="font-bold text-large text-center">Nomor Soal</h4>
      </CardHeader>
      {gamification && (
        <LivesDisplay 
          currentLives={gamification.currentLives} 
          maxLives={3}
          isPremium={isPremium}
        />
      )}
      <CardBody className="overflow-visible py-2 flex flex-col items-center">
        <div
          ref={scrollContainerRef}
          className={`h-[400px] overflow-y-auto ${showScrollbar ? "pr-2" : ""} mb-4`}
          style={{
            scrollbarWidth: showScrollbar ? "thin" : "none",
            scrollbarColor: showScrollbar ? "#9CA3AF #E5E7EB" : "transparent transparent",
          }}
        >
          <div className="grid grid-cols-5 lg:grid-cols-4 gap-2 min-h-[calc(400px+1px)]">
            {questionIds.map((questionId, index) => {
              const isFlagged = flaggedQuestions[questionId];
              const isAnsweredCorrectly = correctAnswers[questionId];
              const isCurrent = currentPage === index;
              const isClickable = isQuestionClickable(index);
              const showLock = shouldShowLock(index);
              const isVisited = visitedQuestions.has(index);

              return (
                <button
                  key={questionId}
                  aria-label={`Question ${index + 1}`}
                  aria-pressed={isCurrent}
                  className={`
                    relative w-[48px] h-[48px] lg:w-[48px] lg:h-[48px] rounded-lg
                    ${isCurrent ? "bg-blue-500 text-white" : isClickable ? "bg-white border border-gray-300 text-black" : "bg-gray-200 text-black"}
                    ${isAnsweredCorrectly ? "flex flex-col justify-start" : "flex justify-center"}
                    ${!isClickable && !isCurrent ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:bg-gray-100"}
                    items-center
                    focus:outline-none
                    transition-colors duration-200
                  `}
                  disabled={isTimeUp || (!isClickable && !isCurrent)}
                  onClick={() => handleQuestionClick(index)}
                  title={showLock ? "Complete previous questions correctly first" : ""}
                >
                  <span className="text-base font-semibold">{index + 1}</span>

                  {/* Visited/Completed indicator */}
                  {isVisited && !isCurrent && !showLock && (
                    <span className="absolute top-0.5 left-0.5 w-3 h-3 bg-green-500 rounded-full" />
                  )}

                  {isFlagged && (
                    <span className="absolute top-0.5 right-0.5 w-3 h-3 bg-yellow-500 rounded-full" />
                  )}

                  {isAnsweredCorrectly && (
                    <div className="absolute bottom-0 left-0 w-full h-1/2 bg-green-500 rounded-b-lg" />
                  )}

                  {showLock && (
                    <span className="absolute -top-0 -right-0 w-4 h-4 flex items-center justify-center">
                      <SquareLock02Icon size={16} />
                    </span>
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {/* Insights Section */}
        <div className="grid px-6 grid-cols-2 gap-4 w-full mt-4 text-sm">
          <div className="flex items-center">
            <span className="w-3 h-3 bg-green-500 rounded-full mr-1" /> Benar:{" "}
            {Object.values(correctAnswers).filter(Boolean).length}
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-gray-200 rounded-full mr-1" /> Belum terisi: {unansweredCount}
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-yellow-500 rounded-full mr-1" /> Ragu: {flaggedCount}
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-blue-500 rounded-full mr-1" /> Lagi dikerjakan
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-green-500 rounded-full mr-1" /> Sudah dikunjungi
          </div>
        </div>

        <div className="flex justify-center w-full mt-4">
          <ExitExam isLoading={isLoading} isTimeUp={isTimeUp} onExitExam={onExitExam} />
        </div>
      </CardBody>
    </Card>
  );
};