"use client";

import { Avatar } from "@heroui/react";
import React, { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

import CustomSocialButton from "./CustomSocialButton";

import { signInType } from "@/app/(auth)/types/auth/auth";
import { authenticateGoogle } from "@/app/lib/auth/login";

// Extend the signInType to include an optional currentPath prop
interface AuthSocialButtonsProps extends signInType {
  currentPath?: string;
}

const AuthSocialButtons = ({ title, currentPath }: AuthSocialButtonsProps) => {
  const pathname = usePathname();
  const [redirectPath, setRedirectPath] = useState('');
  const [errorGoogleMessage, dispatchGoogle] = React.useActionState(
    authenticateGoogle,
    undefined,
  );

  // Store the current path in localStorage and state when the component mounts
  useEffect(() => {
    // Use the provided currentPath or fall back to the current pathname
    const pathToStore = currentPath || pathname;
    
    if (pathToStore) {
      localStorage.setItem('auth_redirect_uri', pathToStore);
      setRedirectPath(pathToStore);
    } else if (typeof window !== 'undefined') {
      // Try to get from localStorage if no direct path is provided
      const savedPath = localStorage.getItem('auth_redirect_uri');
      if (savedPath) {
        setRedirectPath(savedPath);
      }
    }
  }, [currentPath, pathname]);

  return (
    <div className="flex flex-col items-center space-y-2">
      <div className="flex justify-center">
        <div className="flex justify-center space-x-2">
          <form action={dispatchGoogle}>
            {/* Hidden input to include the redirect path */}
            <input 
              type="hidden" 
              name="currentPath" 
              value={redirectPath} 
            />
            <CustomSocialButton className="flex items-center" type="submit">
              <Avatar
                alt="Google Icon"
                className="w-8 h-8 rounded"
                size="lg"
                src="/images/svgs/google-icon.svg"
                style={{ backgroundColor: "transparent" }}
              />
              <span>{title} Google</span>
            </CustomSocialButton>
          </form>
        </div>
      </div>
    </div>
  );
};

export default AuthSocialButtons;