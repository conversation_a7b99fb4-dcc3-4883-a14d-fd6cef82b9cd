"use client"
import { Card, Button } from "@heroui/react";
import { useRouter } from 'next/navigation';
import LottieAnimation from "@/components/shared/lottie-animation";
import Error404 from '@/public/jsons/404error.json';

const NotFound = () => {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  return (
    <Card 
      className="px-4"
      style={{ 
        display: "flex", 
        flexDirection: "column", 
        height: "100vh", 
        textAlign: "center", 
        justifyContent: "center",
        alignItems: "center",
        maxWidth: "100%",
        margin: "0 auto",
        padding: "2.5rem"
      }}
    >
      <LottieAnimation jsonData={Error404} width={'100%'} height={'auto'} />
      <h1 style={{ fontSize: '3rem', marginBottom: '1rem' }}>
        Oops!!!
      </h1>
      <p style={{ fontSize: '1.2rem', marginBottom: '2rem' }}>
        Halaman ini sepertinya tidak ada. Aku tebak kamu mau cari exam ya? coba bisa login dulu yaa..
      </p>
      <Button 
        onClick={handleBack}
        color="primary"
      >
        Kembali ke Halaman Sebelumnya
      </Button>
    </Card>
  );
};

export default NotFound;