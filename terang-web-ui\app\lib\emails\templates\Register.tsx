import {
  Body,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";

interface VerifyEmailProps {
  verificationLink?: string;
  firstName?: string;
}

const logoUrl = `https://cdn.terang.ai/images/logo/logo-terang-ai-combined.png`;

const currentYear = new Date().getFullYear(); // Get current year dynamically

const VerifyEmail: React.FC<VerifyEmailProps> = ({
  verificationLink = "https://terang.ai",
  firstName,
}: VerifyEmailProps) => (
  <Html>
    <Head />
    <Preview>Terang AI Email Verification</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={coverSection}>
          <Section style={imageSection}>
            <Img
              alt="Terang AI's Logo"
              src={logoUrl}
              style={{
                margin: "0 auto",
                display: "block",
                maxWidth: "250px",
                height: "auto",
              }}
            />
          </Section>
          <Section style={upperSection}>
            <Heading style={h1}>Confirm your email address</Heading>
            <Text style={mainText}>
              Hi {firstName}, thanks for starting the new terang.ai account
              creation process. We want to make sure it&apos;s really you.
              Please enter the following verification code when prompted. If you
              don&apos;t want to create an account, you can ignore this message.
            </Text>
            <Section style={verificationSection}>
              <Text style={verifyText}>Verification code</Text>

              <Section style={buttonContainer}>
                <Link href={verificationLink} style={button}>
                  Verify Account
                </Link>
              </Section>
              <Text style={validityText}>
                (This link is valid for 10 minutes)
              </Text>
            </Section>
          </Section>
          <Hr />
          <Section style={lowerSection}>
            <Text style={cautionText}>
              Terang.ai will never email you and ask you to disclose or verify
              your password, credit card, or banking account number.
            </Text>
          </Section>
        </Section>
        <Text style={footerText}>
          This message was produced and distributed by Terang.ai, Inc., Gedung Bursa Efek Indonesia (IDX) Tower 1, SCBD Jl. Jendral Sudirman Kav 52-53, Kebayoran Baru, Jakarta Selatan, DKI Jakarta, Indonesia 12190 © {currentYear}, All rights reserved.
          Terang.ai is a registered copyright of{" "}
          <Link href="https://terang.ai" style={link} target="_blank">
            Terang.ai
          </Link>
          , Inc. View our{" "}
          <Link href="https://terang.a/privacy-policyi" style={link} target="_blank">
            privacy policy
          </Link>
          .
        </Text>
      </Container>
    </Body>
  </Html>
);

export default VerifyEmail;

const main = {
  backgroundColor: "#fff",
  color: "#212121",
};

const container = {
  padding: "20px",
  margin: "0 auto",
  backgroundColor: "#eee",
  maxWidth: "600px", // Adjust max-width for responsiveness
};

const h1 = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "20px",
  fontWeight: "bold",
  marginBottom: "15px",
};

const link = {
  color: "#2754C5",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  textDecoration: "underline",
};

const text = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  margin: "24px 0",
};

const imageSection = {
  backgroundColor: "#252f3d",
  alignItems: "center",
  justifyContent: "center",
  padding: "20px 0",
  textAlign: "center" as const,
};

const coverSection = { backgroundColor: "#fff" };

const upperSection = { padding: "25px 35px" };

const lowerSection = { padding: "25px 35px" };

const footerText = {
  ...text,
  fontSize: "12px",
  padding: "0 20px",
};

const verifyText = {
  ...text,
  margin: 0,
  fontWeight: "bold",
  textAlign: "center" as const,
};

const validityText = {
  ...text,
  margin: "20px",
  fontSize: "8px",
  textAlign: "center" as const,
};

const verificationSection = {
  alignItems: "center",
  justifyContent: "center",
  alignText: "center" as const,
};

const buttonContainer = {
  marginTop: "24px",
  alignItems: "center",
  justifyContent: "center",
  textAlign: "center" as const,
};

const button = {
  alignItems: "center",
  justifyContent: "center",
  textAlign: "center" as const,
  background: "linear-gradient(135deg, #89CFF0, #0095ff)",
  border: "none",
  fontSize: "20px",
  lineHeight: "1.2",
  padding: "20px 25px",
  borderRadius: "10px",
  maxWidth: "250px",
  color: "#fff",
  cursor: "pointer",
  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)", // Optional: adds a subtle shadow
};

const mainText = { ...text, marginBottom: "14px" };

const cautionText = { ...text, margin: "0px" };
