// File: app/api/search-engine/youtube/route.ts
import { NextRequest, NextResponse } from 'next/server';

const YOUTUBE_API_BASE_URL = 'https://www.googleapis.com/youtube/v3';
const API_KEY = process.env.SEARCH_ENGINE_API_KEY;

interface YouTubeSearchResponse {
  items: any[];
  nextPageToken?: string;
  error?: {
    message: string;
    code: number;
  };
}

interface YouTubeVideoDetailsResponse {
  items: any[];
  error?: {
    message: string;
    code: number;
  };
}

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Get the query from the URL
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    
    if (!query) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      );
    }

    // First, search for videos
    const searchResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/search?part=snippet&q=${encodeURIComponent(query)}&type=video&maxResults=6&key=${API_KEY}`
    );

    if (!searchResponse.ok) {
      const errorData = await searchResponse.json() as YouTubeSearchResponse;
      console.error('YouTube API error:', errorData);
      return NextResponse.json(
        { error: `Failed to fetch data from YouTube API: ${errorData.error?.message || searchResponse.statusText}` },
        { status: searchResponse.status }
      );
    }

    const searchData = await searchResponse.json() as YouTubeSearchResponse;
    
    if (!searchData.items || searchData.items.length === 0) {
      return NextResponse.json({ items: [] });
    }

    // Extract video IDs for fetching additional details
    const videoIds = searchData.items.map(item => item.id.videoId).join(',');

    // Get additional video details including statistics and content details
    const videoDetailsResponse = await fetch(
      `${YOUTUBE_API_BASE_URL}/videos?part=snippet,statistics,contentDetails&id=${videoIds}&key=${API_KEY}`
    );

    if (!videoDetailsResponse.ok) {
      const errorData = await videoDetailsResponse.json() as YouTubeVideoDetailsResponse;
      console.error('YouTube API error (video details):', errorData);
      return NextResponse.json(
        { error: `Failed to fetch video details from YouTube API: ${errorData.error?.message || videoDetailsResponse.statusText}` },
        { status: videoDetailsResponse.status }
      );
    }

    const videoDetailsData = await videoDetailsResponse.json() as YouTubeVideoDetailsResponse;

    // Merge search results with additional details
    const enhancedResults = videoDetailsData.items.map(item => {
      return {
        id: { videoId: item.id },
        snippet: item.snippet,
        statistics: item.statistics,
        contentDetails: item.contentDetails
      };
    });

    return NextResponse.json({ items: enhancedResults });
  } catch (error) {
    console.error('Error in YouTube API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}