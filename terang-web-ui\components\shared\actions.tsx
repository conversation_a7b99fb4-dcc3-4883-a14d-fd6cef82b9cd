// app/actions/redisActions.ts
"use server";

import { createClient } from "redis";

import { fetchExamQuestionsBySessionId } from "../my-exams/actions";
import { ExamSession } from "../types";

import { getUserId } from "@/app/lib/actions/account/actions";

import { auth } from "@/auth";

import { updateGamification, updateGamificationWithRedis, Gamification, getRedisWriteClient } from "./actions-gamification";
import { EducationLevel, ProgramStudy } from "../types";

const createWriteClient = () => createClient({
  url: process.env.REDIS_URL_WRITE as string,
});

const createReadClient = () => createClient({
  url: process.env.REDIS_URL_READ as string,
});

export async function storeToRedis(key: string, value: any) {
  const redis = createWriteClient();
  try {
    await redis.connect();
    await redis.set(key, JSON.stringify(value), {
      EX: 21600, // Set expiration to 6 hours (21600 seconds)
    });
  } catch (error) {
    console.error("Failed to store data in Redis:", error);
  } finally {
    await redis.disconnect();
  }
}

export async function retrieveFromRedis(key: string): Promise<any | null> {
  const redis = createReadClient();

  try {
    await redis.connect();
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error("Failed to retrieve data from Redis:", error);
    return null;
  } finally {
    await redis.disconnect();
  }
}

export async function updateExamSessionDone(
  sessionId: string,
  examType: string = ""  // Make examType parameter optional with default empty string
): Promise<string> {
  const userId = await getUserId();

  if (!userId) throw new Error("User ID is undefined or null.");

  const storedSelectedOptions = await retrieveFromRedis(
    `${sessionId}_selectedOptions`,
  );
  const storedFlaggedQuestions = await retrieveFromRedis(
    `${sessionId}_flaggedQuestions`,
  );

  const currentTime = new Date().toISOString();

  // First update gamification status to COMPLETED
  try {
    const gamificationUpdate: Partial<Gamification> = {
      status: "COMPLETED",
      endTime: currentTime,
      elapsedTime: currentTime
    };

    await updateGamificationWithRedis(sessionId, gamificationUpdate);
    await updateGamification(sessionId, gamificationUpdate);
    await syncGamificationData(sessionId);
  } catch (error) {
    console.error("Error updating gamification status:", error);
    // Continue with exam completion even if gamification update fails
  }

  const newSession: any = {
    status: "COMPLETED",
    end_time: currentTime,
    answers: storedSelectedOptions,
    flagged_questions: storedFlaggedQuestions,
  };

  const response = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/exam-sessions/${sessionId}`,
    {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
      body: JSON.stringify(newSession),
    },
  );

  if (!response.ok) {
    const errorText = await response.text();
    console.error("Failed to update exam session. Response text:", errorText);
    throw new Error("Failed to update exam session.");
  }

  const data = await response.json();

  try {
    // Pass the examType to insertExamScores
    await insertExamScores(sessionId, examType);
  } catch (error) {
    console.error("Error inserting exam score:", error);
  }

  // Clean up Redis keys after successful completion
  try {
    const redis = await getRedisWriteClient();
    await redis.del([
      `${sessionId}_selectedOptions`,
      `${sessionId}_flaggedQuestions`,
      `${sessionId}_gamification`,
      `${sessionId}_gamification_queue`,
      `${sessionId}_failed_gamification_updates`
    ]);
  } catch (error) {
    console.error("Error cleaning up Redis keys:", error);
  }

  return data.data.session_id;
}

// Helper function to sync all gamification data
export async function syncGamificationData(sessionId: string): Promise<void> {
  try {
    // Process queued updates
    const queueKey = `${sessionId}_gamification_queue`;
    const queue = await retrieveFromRedis(queueKey);
    if (queue && queue.length > 0) {
      for (const item of queue) {
        await updateGamification(sessionId, item.data);
      }
      await storeToRedis(queueKey, []);
    }

    // Process failed updates
    const failedKey = `${sessionId}_failed_gamification_updates`;
    const failedUpdates = await retrieveFromRedis(failedKey);
    if (failedUpdates && failedUpdates.length > 0) {
      for (const item of failedUpdates) {
        await updateGamification(sessionId, item.data);
      }
      await storeToRedis(failedKey, []);
    }
  } catch (error) {
    console.error("Error syncing gamification data:", error);
    throw error;
  }
}

// Types for exam configuration
interface SubjectInfo {
  id: string;
  name: string;
  key: string;
}

interface ExamType {
  id: string;
  name: string;
  subjects: SubjectInfo[];
}


export async function insertExamScores(sessionId: string, examType: string = ""): Promise<string> {
  const userId = await getUserId();

  if (!userId) throw new Error("User ID is undefined or null.");
  
  let examConfig: ExamType;
  
  try {
    // First, try to get the exam configuration from the API
    const configResponse = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/exam-config/${encodeURIComponent(examType.toUpperCase())}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );
    
    if (!configResponse.ok) {
      throw new Error(`Failed to get exam configuration: ${configResponse.statusText}`);
    }
    
    const configData = await configResponse.json();
    examConfig = configData.config;

    if (!examConfig) {
      throw new Error("Exam configuration is null or undefined");
    }
    
    console.log("Exam configuration:", examConfig);
  } catch (error) {
    console.error("Error fetching exam configuration:", error);
    throw new Error("Failed to fetch exam configuration. Cannot calculate scores.");
  }

  // Continue with the rest of your function using the resolved examConfig
  const storedSelectedOptions = await retrieveFromRedis(`${sessionId}_selectedOptions`);
  
  if (!storedSelectedOptions) {
    throw new Error("No selected options found for this session.");
  }
  
  const parsedSelectedOptions = JSON.parse(storedSelectedOptions);
  
  const questionData: any = await fetchExamQuestionsBySessionId(sessionId);
  const parsedData: any[] = JSON.parse(questionData.data[0].data);

  const totalQuestions = parsedData.length;
  let correctAnswers = 0;
  
  // Initialize counters for each subject
  const subjectTotals: Record<string, number> = {};
  const subjectCorrect: Record<string, number> = {};
  
  // Initialize all subject counters to zero
  examConfig.subjects.forEach((subject: any) => {
    subjectTotals[subject.key] = 0;
    subjectCorrect[subject.key] = 0;
  });

  // Map to translate subject names to keys
  const subjectNameToKey: Record<string, string> = {};
  examConfig.subjects.forEach((subject: any) => {
    subjectNameToKey[subject.name.toLowerCase()] = subject.key;
    // Also map the ID to the key for robustness
    subjectNameToKey[subject.id.toLowerCase()] = subject.key;
  });

  // Process each question
  parsedData.forEach((question) => {
    const questionId = question.id;
    const selectedOptionId = parsedSelectedOptions[questionId];
    
    const correctOption = question.options.values.find(
      (option: any) => option.is_correct
    );
    
    // Find the subject metadata
    const subjectMetadata = question.metadata.find(
      (meta: { name: string; value: string }) => meta.name === "subject"
    );
    
    if (!subjectMetadata) {
      console.warn(`No subject metadata found for question ${questionId}`);
      return; // Skip if no subject metadata
    }
    
    const subjectName: string = subjectMetadata.value;
    const subjectKey = subjectNameToKey[subjectName.toLowerCase()];
    
    if (!subjectKey) {
      console.warn(`Unknown subject: ${subjectName} for exam type: ${examConfig.id}`);
      return; // Skip if subject not found in configuration
    }
    
    // Increment total for this subject
    subjectTotals[subjectKey]++;
    
    // Check if answer is correct
    if (
      selectedOptionId &&
      correctOption &&
      selectedOptionId === correctOption.id
    ) {
      correctAnswers++;
      subjectCorrect[subjectKey]++;
    }
  });

  // Calculate overall score
  const score = (correctAnswers / totalQuestions) * 100;
  
  // Calculate subject scores
  const metadataScores: Record<string, any> = {
    exam_type: examConfig.id,
  };
  
  // Add subject-specific scores
  examConfig.subjects.forEach((subject: any) => {
    const total = subjectTotals[subject.key];
    const correct = subjectCorrect[subject.key];
    const subjectScore = total > 0 ? (correct / total) * 100 : 0;
    
    metadataScores[`${subject.key}`] = correct;
    metadataScores[`${subject.key}_total`] = total;
    metadataScores[`${subject.key}_score`] = subjectScore;
  });

  console.log("Metadata scores:", metadataScores);
  console.log("Overall score:", score);

  const scoreInsert = {
    session_id: sessionId,
    total_questions: totalQuestions,
    correct_answers: correctAnswers,
    score: score,
    metadata_scores: JSON.stringify(metadataScores)
  };

  console.log("Inserting score:", scoreInsert);

  const scoreResponse = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/exam-scores`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
      body: JSON.stringify(scoreInsert),
    }
  );

  if (!scoreResponse.ok) {
    const errorText = await scoreResponse.text();
    console.error("Failed to insert exam score. Response text:", errorText);
    throw new Error("Failed to insert exam score.");
  }

  const scoreData = await scoreResponse.json();
  return scoreData.data.session_id;
}

export async function fetchExamSessionBySessionId(
  sessionId: string,
): Promise<ExamSession> {
  const response = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/exam-sessions/${sessionId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
    },
  );

  if (!response.ok) {
    const errorText = await response.text();

    console.error("Failed to fetch exam session. Response text:", errorText);
    throw new Error("Failed to fetch exam session.");
  }

  const result = await response.json();

  return result.data;
}


export async function checkFeedbackRecords(): Promise<number> {
  try {
    const session = await auth();

    if (!session || !session.user?.email) {
      throw new Error("User is not authenticated or email is missing");
    }

    const email = session.user.email;

    if (!process.env.BACKEND_BASE_URL) {
      throw new Error("BACKEND_BASE_URL is not defined");
    }

    if (!process.env.BACKEND_API_KEY) {
      throw new Error("BACKEND_API_KEY is not defined");
    }

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/surveys/check-email/feedback?email=${encodeURIComponent(email)}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      },
    );

    if (!response.ok) {
      throw new Error(
        `Failed to check email records: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();

    return data.count;
  } catch (error) {
    console.error("Error checking email records:", error);
    throw error;
  }
}


// USER DEMOGRAPHICS

// config.ts
const isProduction = process.env.NODE_ENV === "production";

const REGION_SERVICE_URL = isProduction
  ? "http://region-service.default.svc.cluster.local"
  : "http://localhost:8080";

interface DemographicsData {
  id: number;
  nama: string;
}

export async function fetchRegionData(
  endpoint: string,
  params: Record<string, string> = {},
): Promise<DemographicsData[]> {
  const url = new URL(`${REGION_SERVICE_URL}${endpoint}?page=1&limit=10000000`);

  const response = await fetch(url.toString());

  if (!response.ok) {
    throw new Error("Network response was not ok");
  }

  return response.json();
}

export async function fetchProvinces(): Promise<DemographicsData[]> {
  return fetchRegionData("/provinsi");
}

export async function fetchCities(
  provinceId: number,
): Promise<DemographicsData[]> {
  return fetchRegionData(`/provinsi/${provinceId}/kota`);
}

export async function fetchDistricts(
  cityId: number,
): Promise<DemographicsData[]> {
  return fetchRegionData(`/kota/${cityId}/kecamatan`);
}

export async function fetchVillages(
  districtId: number,
): Promise<DemographicsData[]> {
  return fetchRegionData(`/kecamatan/${districtId}/kelurahan`);
}

export async function fetchAllCities(): Promise<DemographicsData[]> {
  return fetchRegionData("/kota");
}

export async function fetchAllDistricts(): Promise<DemographicsData[]> {
  return fetchRegionData("/kecamatan");
}

export async function fetchAllVillages(): Promise<DemographicsData[]> {
  return fetchRegionData("/kelurahan");
}

export async function fetchProvinceDetails(
  provinceId: string,
): Promise<DemographicsData> {
  return fetchRegionData(`/provinsi/${provinceId}`).then((data) => data[0]);
}

export async function fetchCityDetails(
  cityId: string,
): Promise<DemographicsData> {
  return fetchRegionData(`/kota/${cityId}`).then((data) => data[0]);
}

export async function fetchDistrictDetails(
  districtId: string,
): Promise<DemographicsData> {
  return fetchRegionData(`/kecamatan/${districtId}`).then((data) => data[0]);
}

export async function fetchVillageDetails(
  villageId: string,
): Promise<DemographicsData> {
  return fetchRegionData(`/kelurahan/${villageId}`).then((data) => data[0]);
}

const educationLevels: EducationLevel[] = [
  { id: 5, name: "SD" },
  { id: 10, name: "SLTP" },
  { id: 15, name: "SLTA" },
  { id: 17, name: "SMK/SLTA Kejuruan" },
  { id: 18, name: "SLTA Keguruan" },
  { id: 20, name: "Diploma I" },
  { id: 25, name: "Diploma II" },
  { id: 30, name: "Diploma III/Sarjana Muda" },
  { id: 35, name: "Diploma IV" },
  { id: 40, name: "S-1/Sarjana" },
  { id: 45, name: "S-2" },
  { id: 50, name: "S-3/Doktor" },
];

export async function getEducationLevels(): Promise<EducationLevel[]> {
  const cacheKey = "education_levels";
  const cachedLevels = await retrieveFromRedis(cacheKey);

  if (cachedLevels) {
    return cachedLevels;
  }

  await storeToRedis(cacheKey, educationLevels);

  return educationLevels;
}


// SSCBKN API FOOOOOORRRRR

export interface Formation {
  formasi_id: string;
  ins_nm: string;
  jp_nama: string;
  formasi_nm: string;
  jabatan_nm: string;
  lokasi_nm: string;
  jumlah_formasi: number;
  disable: number;
  gaji_min: string;
  gaji_max: string;
  jumlah_ms: number;
}

interface ApiResponse<T> {
  status: number;
  error: boolean;
  message: string;
  data: {
    meta: {
      total: number;
    };
    page: {
      total: number;
    };
    data: T[];
  };
}

type ApiError = {
  message: string;
  code: 'NO_FORMATIONS' | 'API_ERROR' | 'NETWORK_ERROR';
}

// Utility function to handle API errors
function handleApiError(response: Response): void {
  if (!response.ok) {
    throw {
      message: `API responded with status ${response.status}`,
      code: 'API_ERROR'
    } as ApiError;
  }
}

// Function to check if response has data
function hasData<T>(response: ApiResponse<T>): boolean {
  return response.data.meta.total > 0 && Array.isArray(response.data.data) && response.data.data.length > 0;
}

// Cache keys
const CACHE_KEYS = {
  programStudies: (educationLevelId: number, searchTerm: string = "") => 
    `program_studies:${educationLevelId}:${searchTerm}`,
  formations: (programStudyCode: number) => 
    `formations:${programStudyCode}`,
  formationExists: (programStudyCode: number) => 
    `formation_exists:${programStudyCode}`,
};

// Cache durations (in seconds)
const CACHE_DURATION = {
  PROGRAM_STUDIES: 21600, // 6 hours
  FORMATIONS: 21600,      // 6 hours
  FORMATION_EXISTS: 21600 // 6 hours
};

// Function to fetch program studies with Redis caching
export async function fetchProgramStudies(
  educationLevelId: number,
  searchTerm: string = "",
): Promise<ProgramStudy[]> {
  const cacheKey = CACHE_KEYS.programStudies(educationLevelId, searchTerm);
  
  try {
    // Try to get from cache first
    const cachedData = await retrieveFromRedis(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // If not in cache, fetch from API
    const response = await fetch(
      `https://api-sscasn.bkn.go.id/2024/referensi/pendidikan?tingkat=${educationLevelId}&nama=${searchTerm}&limit=2500`,
      {
        headers: {
          "Host": "api-sscasn.bkn.go.id",
          "Referer": "https://sscasn.bkn.go.id/",
          "Origin": "https://sscasn.bkn.go.id",
          "User-Agent": "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.110 Mobile Safari/537.36"
        }
      }
    );

    handleApiError(response);
    const data: ApiResponse<ProgramStudy> = await response.json();
    const programStudies = data.data.data;

    // Store in cache
    await storeToRedis(cacheKey, programStudies);

    return programStudies;
  } catch (error) {
    console.error("Error fetching program studies:", error);
    throw error;
  }
}

// Function to fetch formations by program study code with Redis caching
export async function fetchFormations(
  programStudyCode: number,
  throwOnEmpty: boolean = false
): Promise<Formation[]> {
  const cacheKey = CACHE_KEYS.formations(programStudyCode);

  try {
    // Try to get from cache first
    const cachedData = await retrieveFromRedis(cacheKey);
    if (cachedData) {
      if (cachedData.length === 0 && throwOnEmpty) {
        throw {
          message: `No formations found for program study code: ${programStudyCode}`,
          code: 'NO_FORMATIONS'
        } as ApiError;
      }
      return cachedData;
    }

    // If not in cache, fetch from API
    const response = await fetch(
      `https://api-sscasn.bkn.go.id/2024/portal/spf?kode_ref_pend=${programStudyCode}&offset=0&pengadaan_kd=2`,
      {
        headers: {
          "accept": "application/json, text/plain, */*",
          "user-agent": "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.110 Mobile Safari/537.36",
          "host": "api-sscasn.bkn.go.id",
          "origin": "https://sscasn.bkn.go.id",
          "referer": "https://sscasn.bkn.go.id/"
        }
      }
    );

    handleApiError(response);
    const data: ApiResponse<Formation> = await response.json();

    if (!hasData(data) && throwOnEmpty) {
      throw {
        message: `No formations found for program study code: ${programStudyCode}`,
        code: 'NO_FORMATIONS'
      } as ApiError;
    }

    const formations = data.data.data;

    // Store in cache
    await storeToRedis(cacheKey, formations);

    return formations;
  } catch (error) {
    console.error("Error fetching formations:", error);
    throw error;
  }
}

// Function to check if formations exist with Redis caching
export async function checkFormationExists(programStudyCode: number): Promise<boolean> {
  const cacheKey = CACHE_KEYS.formationExists(programStudyCode);

  try {
    // Try to get from cache first
    const cachedResult = await retrieveFromRedis(cacheKey);
    if (cachedResult !== null) {
      return cachedResult;
    }

    // If not in cache, check formations
    const formations = await fetchFormations(programStudyCode);
    const exists = formations.length > 0;

    // Store result in cache
    await storeToRedis(cacheKey, exists);

    return exists;
  } catch (error) {
    if ((error as ApiError).code === 'NO_FORMATIONS') {
      return false;
    }
    throw error;
  }
}

// Function to fetch all available formations with Redis caching
export async function fetchAllAvailableFormations(
  programStudyCodes: number[]
): Promise<Map<string, Formation[]>> {
  const results = new Map<string, Formation[]>();
  
  await Promise.all(
    programStudyCodes.map(async (code) => {
      try {
        const formations = await fetchFormations(code);
        if (formations.length > 0) {
          results.set(code.toString(), formations);
        }
      } catch (error) {
        console.warn(`Failed to fetch formations for code ${code}:`, error);
      }
    })
  );

  return results;
}
// Function to fetch all available formations with Redis caching
export async function searchUniversities(query: string, limit: number = 10) {
  try {
    // Use the existing university search endpoint
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/universities/search?name_contains=${encodeURIComponent(query)}&limit=${limit}`, 
      { 
        cache: 'no-store', // Ensure fresh results
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      },
    );

    if (!response.ok) {
      throw new Error('Failed to fetch universities');
    }

    const universities = await response.json();
    return universities;
  } catch (error) {
    console.error('Error searching universities:', error);
    return [];
  }
}

// Define the interface for form data 
export interface UserDemographicsFormData {
  // ID fields
  provinceId: number;
  cityId: number;
  districtId: number;
  villageId: number;
  educationLevelId: number;
  programStudyId: number;
  
  // Name fields
  provinceName: string;
  cityName: string;
  districtName: string;
  villageName: string;
  educationLevelName: string;
  programStudyName: string;
  
  // Other fields
  birthDate: string | null;
  lastOccupation: string;
  interests: string[];
  mainPurpose: string;
  gender: string;
  phoneNumber: string;
  preferredStudyMethods: string[];
  weeklyStudyTime: string;
  primaryDevices: string[];
  learningStyle: string;
  studyBudget: number;
  targetJabatan: string;
  targetInstitution: string;
  targetScore: number;
  learningChallenges: string[];
  targetUniversity: string;
  targetMajor: string;
}

// Instead of exporting the array directly, create a function that returns it
export async function getRequiredUserDemographicsFields(): Promise<(keyof UserDemographicsFormData)[]> {
  return [
    // We still require the ID fields as they're used for relationships
    "provinceId",
    "cityId",
    "districtId",
    "villageId",
    "birthDate",
    "educationLevelId",
    "programStudyId",
    
    // Name fields are now required too
    "provinceName",
    "cityName",
    "districtName",
    "villageName",
    "educationLevelName",
    "programStudyName",
    
    // Other required fields
    "lastOccupation",
    "interests",
    "mainPurpose",
    "gender",
    "preferredStudyMethods",
    "weeklyStudyTime",
    "primaryDevices",
    "learningStyle",
    "studyBudget",
    "targetScore",
    "learningChallenges"
  ];
}

// Update the validation function to use the function instead of direct array
export async function validateUserDemographicsForm(
  formData: UserDemographicsFormData
): Promise<{ [key: string]: string }> {
  const errors: { [key: string]: string } = {};

  if (!formData) return { form: "Please fill out the form" };

  const requiredFields = await getRequiredUserDemographicsFields();
  
  requiredFields.forEach((field) => {
    const value = formData[field];
    if (Array.isArray(value)) {
      if (value.length === 0) {
        errors[field] = `${field} is required`;
      }
    } else if (value === null || value === undefined || value === "") {
      errors[field] = `${field} is required`;
    }
  });

  return errors;
}

export async function submitUserDemographics(
  formData: UserDemographicsFormData,
): Promise<{ success: boolean; message: string; data?: any }> {
  const maxRetries = 3;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      const session = await auth();

      if (!session || !session.user?.email) {
        throw new Error("User is not authenticated or email is missing");
      }

      // Ensure numeric fields are properly converted to integers
      const dataToSubmit = {
        ...formData,
        email: session.user.email,
        studyBudget: parseInt(formData.studyBudget.toString(), 10),
        targetScore: formData.targetScore ? parseInt(formData.targetScore.toString(), 10) : 0,
      };

      // Validate numeric fields
      if (isNaN(dataToSubmit.studyBudget)) {
        throw new Error("Invalid study budget value");
      }

      if (isNaN(dataToSubmit.targetScore)) {
        // Set to 0 if invalid or not provided
        dataToSubmit.targetScore = 0;
      }

      console.log("Data to submit:", JSON.stringify(dataToSubmit, null, 2));

      if (!process.env.BACKEND_BASE_URL) {
        throw new Error("BACKEND_BASE_URL is not defined");
      }

      if (!process.env.BACKEND_API_KEY) {
        throw new Error("BACKEND_API_KEY is not defined");
      }

      const response = await fetch(
        `${process.env.BACKEND_BASE_URL}/v0/surveys`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Api-Key": process.env.BACKEND_API_KEY as string,
          },
          body: JSON.stringify(dataToSubmit),
        },
      );

      if (!response.ok) {
        const errorBody = await response.text();

        console.error(
          "Response not OK. Status:",
          response.status,
          "Body:",
          errorBody,
        );
        throw new Error(
          `Failed to submit user demographics: ${response.status} ${response.statusText}`,
        );
      }

      const data = await response.json();

      console.log("User demographics submitted successfully", data);

      return {
        success: true,
        message: "User demographics submitted successfully",
        data: data,
      };
    } catch (error) {
      console.error(
        `Error submitting user demographics (attempt ${retries + 1}):`,
        error,
      );
      if (error instanceof Error) {
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }

      retries++;
      if (retries < maxRetries) {
        console.log(`Retrying... (${maxRetries - retries} attempts left)`);
        await new Promise((resolve) => setTimeout(resolve, 1000 * retries));
      } else {
        return {
          success: false,
          message: "Failed to submit user demographics after multiple attempts",
        };
      }
    }
  }

  return {
    success: false,
    message: "Unexpected error occurred",
  };
}