"use client";

import React, { useState, ChangeEvent } from "react";
import {
  Card,
  Button,
  Input,
  Textarea,
  Spinner,
  Modal,
  Radio,
  RadioGroup,
  ModalFooter,
  ModalHeader,
  ModalContent,
  ModalBody,
  useDisclosure,
} from "@heroui/react";

interface Question {
  question: string;
  options: string[];
  correctAnswer: number;
  explanation: string;
}

const EditableQuestionGenerator: React.FC = () => {
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [fileName, setFileName] = useState<string>("");
  const [isPreviewOpen, setIsPreviewOpen] = useState<boolean>(false);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showExplanation, setShowExplanation] = useState<boolean>(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const handleFileUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];

    if (file) {
      setFileName(file.name);
      setIsGenerating(true);
      // Simulate file processing and question generation
      setTimeout(() => {
        const generatedQuestions: Question[] = [
          {
            question:
              "Dalam rangka memperingati Hari Kartini, kegiatan apa yang dapat dilakukan untuk memperkuat nilai-nilai yang diperjuangkan oleh R.A. Kartini, khususnya dalam konteks pemberdayaan perempuan saat ini?",
            options: [
              "Mengadakan lomba desain busana tradisional untuk perempuan.",
              "Membuat program televisi tentang perempuan inspiratif di Indonesia.",
              "Memberikan penghargaan kepada perempuan yang telah berkontribusi dalam masyarakat.",
              "Mengadakan bazar produk buatan perempuan.",
              "Menyelenggarakan seminar tentang peran perempuan dalam pembangunan nasional.",
            ],
            correctAnswer: 4,
            explanation:
              "Seminar tentang peran perempuan dalam pembangunan nasional dapat mengedukasi masyarakat tentang pentingnya kontribusi perempuan, sejalan dengan semangat R.A. Kartini dalam memajukan kesetaraan gender dan pemberdayaan perempuan.",
          },
          {
            question:
              "Dalam upaya mempertahankan nilai-nilai nasionalisme dan patriotisme yang diperjuangkan oleh Dr. Soetomo, program apa yang bisa dikembangkan untuk menginspirasi pemuda Indonesia saat ini?",
            options: [
              "Menyelenggarakan lomba debat dan pidato bertema nasionalisme.",
              "Membuat kursus online tentang sejarah nasionalisme Indonesia.",
              "Mengadakan program magang di lembaga pemerintah untuk pemuda",
              "Membangun jaringan pemuda nasional untuk proyek sosial dan kewarganegaraan.",
              "Mengadakan festival film yang menampilkan tema perjuangan kemerdekaan Indonesia.",
            ],
            correctAnswer: 3,
            explanation:
              "Membangun jaringan pemuda nasional untuk terlibat dalam proyek sosial dan kewarganegaraan dapat menginspirasi pemuda untuk berpartisipasi aktif dalam pembangunan bangsa, sesuai dengan semangat nasionalisme dan patriotisme yang diperjuangkan oleh Dr. Soetomo.",
          },
          {
            question:
              "Sebagai seorang ASN, bagaimana kamu dapat menunjukkan integritas dan nasionalisme dalam menghadapi tawaran suap yang berkaitan dengan pengadaan barang dan jasa pemerintah?",
            options: [
              "Melaporkan tawaran tersebut kepada atasan atau lembaga anti-korupsi.",
              "Menerima tawaran untuk keuntungan pribadi.",
              "Mengabaikan tawaran tersebut tanpa melaporkan.",
              "Menegosiasikan untuk mendapatkan bagian yang lebih besar.",
              "Membuat kebijakan internal yang lebih ketat terhadap suap.",
            ],
            correctAnswer: 0,
            explanation:
              "Melaporkan tawaran suap menunjukkan integritas dan komitmen tinggi terhadap nilai-nilai nasionalisme dan tanggung jawab sebagai ASN. Ini menguatkan sistem pemerintahan yang bersih dan akuntabel, serta menunjukkan dedikasi terhadap kesejahteraan masyarakat dan negara.",
          },
          {
            question:
              "Dalam situasi di mana terdapat tekanan untuk membuat keputusan yang menguntungkan kelompok tertentu, bagaimana seorang ASN harus bertindak untuk menunjukkan nasionalisme dan cinta tanah air?",
            options: [
              "Mengambil keputusan yang menguntungkan kelompok tertentu.",
              "Berkonsultasi dengan atasan dan mempertimbangkan kepentingan umum.",
              "Menghindari pengambilan keputusan.",
              "Memberikan keuntungan yang sama kepada semua kelompok.",
              "Menunda keputusan sampai tekanan berkurang.",
            ],
            correctAnswer: 1,
            explanation:
              "Berkonsultasi dengan atasan dan memprioritaskan kepentingan umum menunjukkan komitmen terhadap nasionalisme dan integritas. Keputusan harus berdasarkan prinsip keadilan dan kepentingan masyarakat luas, bukan kelompok tertentu.",
          },
          {
            question:
              "Sebagai ASN, bagaimana kamu dapat memanfaatkan posisi kamu untuk mendorong tanggung jawab sosial dan cinta tanah air di lingkungan kerja kamu?",
            options: [
              "Mengorganisir kegiatan sukarela di masyarakat.",
              "Menekankan pentingnya etika kerja dan tanggung jawab.",
              "Mengabaikan pelanggaran kecil untuk menjaga moral tim.",
              "Memfokuskan hanya pada tugas dan tanggung jawab resmi.",
              "Mengadakan pertemuan rutin yang hanya membahas kinerja kerja",
            ],
            correctAnswer: 0,
            explanation:
              "Mengorganisir kegiatan sukarela mendorong partisipasi aktif dalam masalah sosial dan lingkungan, menunjukkan cinta tanah air melalui aksi nyata. Ini juga menciptakan lingkungan kerja yang berorientasi pada komunitas dan tanggung jawab sosial.",
          },
        ];

        setQuestions(generatedQuestions);
        setIsGenerating(false);
      }, 2000);
    }
  };

  const handleQuestionChange = (
    index: number,
    field: keyof Question,
    value: string,
  ) => {
    const updatedQuestions = [...questions];

    updatedQuestions[index][field] = value as never;
    setQuestions(updatedQuestions);
  };

  const handleOptionChange = (
    questionIndex: number,
    optionIndex: number,
    value: string,
  ) => {
    const updatedQuestions = [...questions];

    updatedQuestions[questionIndex].options[optionIndex] = value;
    setQuestions(updatedQuestions);
  };

  const handleCorrectAnswerChange = (questionIndex: number, value: string) => {
    const updatedQuestions = [...questions];

    updatedQuestions[questionIndex].correctAnswer = parseInt(value, 10) - 1;
    setQuestions(updatedQuestions);
  };

  const handlePreviewClick = () => {
    setCurrentQuestionIndex(0);
    setSelectedAnswer(null);
    setShowExplanation(false);
    onOpen();
  };
  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      setSelectedAnswer(null);
      setShowExplanation(false);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
      setSelectedAnswer(null);
      setShowExplanation(false);
    }
  };

  const handleAnswerSelect = (index: number) => {
    setSelectedAnswer(index);
  };

  const handleCheckAnswer = () => {
    setShowExplanation(true);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br p-4 flex items-center justify-center">
      <Card className="w-full max-w-4xl p-6 shadow-xl backdrop-blur-sm bg-white/30">
        <h2 className="text-3xl font-bold text-center mb-6 text-gradient bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-blue-500">
          Terang AI - Editable Question Generator
        </h2>
        <div className="space-y-6">
          <div className="flex items-center justify-center">
            <input
              className="hidden"
              id="file-upload"
              type="file"
              onChange={handleFileUpload}
            />
            <Button
              as="label"
              className="bg-gradient-to-r from-purple-500 to-blue-500 text-white font-semibold py-2 px-6 rounded-full shadow-md hover:shadow-lg transition duration-300 ease-in-out transform hover:-translate-y-1 hover:scale-105"
              htmlFor="file-upload"
            >
              Import Textbook
            </Button>
            {fileName && (
              <span className="text-sm text-gray-600 font-medium">
                {fileName}
              </span>
            )}
          </div>

          {isGenerating && (
            <div className="flex justify-center items-center space-x-2">
              <Spinner color="secondary" size="sm" />
              <span className="text-purple-600 font-semibold">
                Generating questions...
              </span>
            </div>
          )}

          {questions.map((q, qIndex) => (
            <Card
              key={qIndex}
              className="p-6 space-y-4 shadow-md hover:shadow-lg transition-shadow duration-300 bg-white/50"
            >
              <Textarea
                className="w-full p-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Question"
                value={q.question}
                onChange={(e) =>
                  handleQuestionChange(qIndex, "question", e.target.value)
                }
              />
              {q.options.map((option, oIndex) => (
                <Input
                  key={oIndex}
                  className="w-full p-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder={`Option ${oIndex + 1}`}
                  value={option}
                  onChange={(e) =>
                    handleOptionChange(qIndex, oIndex, e.target.value)
                  }
                />
              ))}
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-700">
                  Correct Answer:
                </span>
                <Input
                  className="w-16 p-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  max={5}
                  min={1}
                  type="number"
                  value={(q.correctAnswer + 1).toString()}
                  onChange={(e) =>
                    handleCorrectAnswerChange(qIndex, e.target.value)
                  }
                />
              </div>
              <Textarea
                className="w-full p-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Explanation"
                value={q.explanation}
                onChange={(e) =>
                  handleQuestionChange(qIndex, "explanation", e.target.value)
                }
              />
            </Card>
          ))}
          <div className="flex justify-center">
            <Button
              className="mt-6 bg-gradient-to-r from-green-500 to-blue-500 text-white font-semibold py-2 px-6 rounded-full shadow-md hover:shadow-lg transition duration-300 ease-in-out transform hover:-translate-y-1 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={questions.length === 0}
              onClick={handlePreviewClick}
            >
              Preview Quiz
            </Button>
          </div>
        </div>
      </Card>
      <Modal
        isOpen={isOpen}
        scrollBehavior="inside"
        size="2xl"
        onClose={onClose}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                Quiz Preview
              </ModalHeader>
              <ModalBody>
                {questions.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-2xl font-bold text-gray-800">
                      Question {currentQuestionIndex + 1} of {questions.length}
                    </h3>
                    <p className="text-lg text-gray-700">
                      {questions[currentQuestionIndex].question}
                    </p>
                    <RadioGroup
                      value={
                        selectedAnswer !== null
                          ? selectedAnswer.toString()
                          : undefined
                      }
                      onValueChange={(value) =>
                        handleAnswerSelect(parseInt(value))
                      }
                    >
                      {questions[currentQuestionIndex].options.map(
                        (option, index) => (
                          <Radio key={index} value={index.toString()}>
                            {option}
                          </Radio>
                        ),
                      )}
                    </RadioGroup>
                    {!showExplanation && (
                      <Button
                        color="primary"
                        disabled={selectedAnswer === null}
                        onClick={handleCheckAnswer}
                      >
                        Check Answer
                      </Button>
                    )}
                    {showExplanation && (
                      <div
                        className={`p-4 rounded-md ${
                          selectedAnswer ===
                          questions[currentQuestionIndex].correctAnswer
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        <p className="font-bold">
                          {selectedAnswer ===
                          questions[currentQuestionIndex].correctAnswer
                            ? "Correct!"
                            : "Incorrect. The correct answer is: " +
                              questions[currentQuestionIndex].options[
                                questions[currentQuestionIndex].correctAnswer
                              ]}
                        </p>
                        <p className="mt-2">
                          {questions[currentQuestionIndex].explanation}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </ModalBody>
              <ModalFooter>
                <Button
                  color="default"
                  disabled={currentQuestionIndex === 0}
                  onClick={handlePreviousQuestion}
                >
                  Previous
                </Button>
                <Button
                  color="primary"
                  disabled={currentQuestionIndex === questions.length - 1}
                  onClick={handleNextQuestion}
                >
                  Next
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  );
};

export default EditableQuestionGenerator;
