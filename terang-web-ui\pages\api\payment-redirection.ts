import type { NextApiRequest, NextApiResponse } from 'next';
import { updatePaymentStatus } from '@/app/lib/actions/available-exams/actions';

const BACKEND_URL = process.env.BACKEND_BASE_URL;
const BACKEND_API_KEY = process.env.BACKEND_API_KEY;

if (!BACKEND_URL || !BACKEND_API_KEY) {
  console.error('BACKEND_BASE_URL or BACKEND_API_KEY is not set in the environment variables');
  process.exit(1);
}

interface OrderDetails {
  invoiceId: string;
  paymentId: string;
  paymentMethod?: string;
  transactionId?: string;
  transactionStatus?: string;
  transactionTime?: string;
  grossAmount?: string;
  // Add other relevant fields from the payment status response if needed
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
    console.log(req.query)
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method Not Allowed' });
  }

  const { order_id } = req.query;

  if (!order_id || typeof order_id !== 'string') {
    return res.status(400).json({ message: 'Invalid order_id' });
  }

  try {
    const orderDetails = await fetchOrderDetails(order_id);
    console.log(orderDetails);

    await updatePaymentStatus(
      order_id,
      orderDetails.invoiceId,
      orderDetails.paymentId,
      "PAID",
      "COMPLETED",
      "PAID",
      orderDetails.paymentMethod || "",
      orderDetails.transactionId || order_id,
      orderDetails.transactionTime as string,
    );

    res.redirect(302, '/available-exams');
  } catch (error) {
    console.error('Error updating payment status:', error);
    res.redirect(500, '/available-exams');
  }
}

async function fetchOrderDetails(orderId: string): Promise<OrderDetails> {
  // Fetch order details from your backend
  const response = await fetch(`${BACKEND_URL}/v0/payments/details?orderId=${orderId}`, {
    headers: {
      'x-api-key': BACKEND_API_KEY as string,
    },
  });
  
  if (!response.ok) {
    throw new Error(`Failed to fetch order details: ${response.statusText}`);
  }

  const data = await response.json();

  // Fetch payment status from Midtrans
  const paymentStatus = await checkPaymentStatus(orderId);
  console.log(paymentStatus)

  return {
    invoiceId: data.order?.invoiceId || '',
    paymentId: data.order?.paymentId || '',
    paymentMethod: paymentStatus.payment_type + '_' + paymentStatus.issuer + '_' + paymentStatus.acquirer,
    transactionId: paymentStatus.transaction_id,
    transactionStatus: paymentStatus.transaction_status,
    transactionTime: paymentStatus.transaction_time,
    grossAmount: paymentStatus.gross_amount,
    // Include other fields as needed
  };
}

async function checkPaymentStatus(orderId: string): Promise<any> {
  const response = await fetch(`${process.env.BASE_URL}/api/check-payment-status?orderId=${orderId}`, {
    headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch payment status: ${response.statusText}`);
  }

  const data = await response.json();
  return data;
}
