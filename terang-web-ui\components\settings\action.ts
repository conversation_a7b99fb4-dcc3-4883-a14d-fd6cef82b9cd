"use server";

import { revalidatePath } from "next/cache";
import { getUserId, clearUserSessionCookie } from "@/app/lib/actions/account/actions";
import { setUserSessionCookieAction } from "@/app/lib/actions/account/actions";

export interface UpdateProfileRequest {
  firstName: string;
  lastName: string;
  phone_number?: string | null;  // Changed from phoneNumber to match form
  picture?: string;
}

export interface User {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  picture?: string;
}

export interface UpdateProfileResponse {
  success: boolean;
  data?: {
    user: User;
    message: string;
  };
  error?: string;
}

export interface GetUserResponse {
    success: boolean;
    data?: {
      user: User;
    };
    error?: string;
}

export interface UploadProfilePictureResponse {
  success: boolean;
  data?: {
    picture_url: string;
    message: string;
  };
  error?: string;
}

/**
 * Uploads a profile picture to the server
 * @param file The file to upload
 * @returns Promise containing the response with the uploaded image URL or error
 */
export async function uploadProfilePicture(
  formData: FormData
): Promise<UploadProfilePictureResponse> {
  try {
    const userId = await getUserId();
    
    if (!userId) {
      throw new Error('User ID not found');
    }

    if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
      throw new Error('Missing required environment variables');
    }

    // Check if file exists in FormData
    const file = formData.get('file') as File;
    if (!file) {
      throw new Error('No file selected');
    }

    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!validTypes.includes(file.type)) {
      throw new Error('Invalid file type. Only JPG and PNG are supported.');
    }

    // Validate file size (max 1MB)
    const maxSize = 1 * 1024 * 1024; // 1MB in bytes
    if (file.size > maxSize) {
      throw new Error('File size exceeds 1MB limit');
    }

    const response = await fetch(`${process.env.BACKEND_BASE_URL}/v1/users/${userId}/profile-picture`, {
      method: 'POST',
      headers: {
        'X-API-KEY': process.env.BACKEND_API_KEY
      },
      body: formData // Send the FormData directly
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.error || `Failed to upload profile picture: ${response.status} ${response.statusText}`
      );
    }

    const result = await response.json();
    
    // Update session cache with the new picture URL
    try {
      const userResponse = await fetch(`${process.env.BACKEND_BASE_URL}/v1/users/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': process.env.BACKEND_API_KEY
        },
        cache: 'no-store'
      });
      
      if (userResponse.ok) {
        const userData = await userResponse.json();
        if (userData.data && userData.data.email) {
          // First clear the existing user session cache
          await clearUserSessionCookie(userData.data.email);
          
          // Update the user data with the new picture URL
          const updatedUserData = {
            ...userData.data,
            picture: result.data.picture_url,
            // Ensure name is set correctly
            name: userData.data.first_name && userData.data.last_name 
              ? `${userData.data.first_name} ${userData.data.last_name}`
              : userData.data.name || "User"
          };
          
          console.log('Updating session cache with picture URL');
          
          // Update the session cache with the new user data including picture
          await setUserSessionCookieAction(userData.data.email, updatedUserData);
        }
      }
    } catch (error) {
      console.error('Failed to update session cache with new picture:', error);
      // Continue even if cache update fails
    }
    
    // Revalidate the profile page to show updated data
    revalidatePath('/settings');
    
    return {
      success: true,
      data: {
        picture_url: result.data.picture_url,
        message: 'Profile picture uploaded successfully'
      }
    };
  } catch (error) {
    console.error('Profile picture upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

/**
 * Updates user profile using server action
 * @param data Profile data to update
 * @returns Promise containing the response with updated user data or error
 */
export async function updateProfile(
  formData: FormData | UpdateProfileRequest
): Promise<UpdateProfileResponse> {
  try {
    const userId = await getUserId();
    
    if (!userId) {
      throw new Error('User ID not found');
    }

    if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
      throw new Error('Missing required environment variables');
    }

    // Handle both FormData and direct object submission
    const data: UpdateProfileRequest = formData instanceof FormData
      ? {
          firstName: formData.get('firstName') as string,
          lastName: formData.get('lastName') as string,
          phone_number: formData.get('phone') as string,
          picture: formData.get('picture') as string || undefined
        }
      : formData;

    // Validate required fields
    if (!data.firstName || !data.lastName) {
      throw new Error('Missing required fields');
    }

    // Validate Indonesian phone number format
    const phoneRegex = /^\+62[1-9][\d\s-]{8,}$/;
    // If phone number is provided (not null/undefined/empty), validate it
    if (data.phone_number && !phoneRegex.test(data.phone_number.replace(/\s/g, ''))) {
      throw new Error('Invalid Indonesian phone number format');
    }

    const response = await fetch(`${process.env.BACKEND_BASE_URL}/v1/users/${userId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': process.env.BACKEND_API_KEY
      },
      body: JSON.stringify({
        first_name: data.firstName,
        last_name: data.lastName,
        phone_number: data.phone_number && data.phone_number.replace(/\s/g, ''), // Remove spaces from phone number
        picture: data.picture,
      })
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.message || `Failed to update profile: ${response.status} ${response.statusText}`
      );
    }

    const result = await response.json();
    
    // Get the user's email to update the session cache
    try {
      const userResponse = await fetch(`${process.env.BACKEND_BASE_URL}/v1/users/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': process.env.BACKEND_API_KEY
        },
        cache: 'no-store'
      });
      
      if (userResponse.ok) {
        const userData = await userResponse.json();
        if (userData.data && userData.data.email) {
          // First clear the existing user session cache
          await clearUserSessionCookie(userData.data.email);
          
          // Create updated user data with the new values
          const updatedUserData = {
            ...userData.data,
            first_name: data.firstName,
            last_name: data.lastName,
            phone_number: data.phone_number ? data.phone_number.replace(/\s/g, '') : null,
            // Add a name field by combining first and last name
            name: `${data.firstName} ${data.lastName}`
          };
          
          console.log('Updating session cache with data:', {
            email: updatedUserData.email,
            first_name: updatedUserData.first_name,
            last_name: updatedUserData.last_name,
            name: updatedUserData.name
          });
          
          // Update the session cache with the new user data
          await setUserSessionCookieAction(userData.data.email, updatedUserData);
        }
      }
    } catch (error) {
      console.error('Failed to update session cache:', error);
      // Continue even if cache update fails
    }
    
    // Revalidate the profile page to show updated data
    revalidatePath('/settings');
    
    return {
      success: true,
      data: {
        user: result.data,
        message: 'Profile updated successfully'
      }
    };
  } catch (error) {
    console.error('Profile update error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

/**
 * Fetches user data for a specific user ID using server action
 * @param targetUserId Optional user ID to fetch. If not provided, uses the current user's ID
 * @returns Promise containing the response with user data or error
 */
export async function getUserData(targetUserId?: string): Promise<GetUserResponse> {
    try {
      // If no target ID provided, get the current user's ID
      const userId = targetUserId || await getUserId();
      
      if (!userId) {
        throw new Error('User ID not found');
      }
  
      if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
        throw new Error('Missing required environment variables');
      }
  
      const response = await fetch(`${process.env.BACKEND_BASE_URL}/v1/users/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-API-KEY': process.env.BACKEND_API_KEY
        },
        // Use cache: 'no-store' to always fetch fresh data, or
        // next: { revalidate: 60 } to cache for 60 seconds
        cache: 'no-store'
      });
  
      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(
          errorData?.message || `Failed to fetch user data: ${response.status} ${response.statusText}`
        );
      }
  
      const result = await response.json();

      console.log(result)
      
      return {
        success: true,
        data: {
          user: result.data
        }
      };
    } catch (error) {
      console.error('User data fetch error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unknown error occurred'
      };
    }
  }
  