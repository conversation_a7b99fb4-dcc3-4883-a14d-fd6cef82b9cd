import React, { useEffect, useRef } from "react";
import styled, { keyframes } from "styled-components";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

import Vector from "./Icons/Vector";

// Ensure ScrollTrigger is registered
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface ThemeProps {
  theme: {
    text: string;
  }
}

const VectorContainer = styled.div`
  position: absolute;
  top: 0.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 100%;
  overflow: hidden;
  will-change: transform;

  svg {
    display: inline-block;
    width: 100%;
    height: 100%;
  }

  @media (max-width: 48em) {
    left: 1rem;
  }
`;

const Bounce = keyframes`
  from { transform: translateX(-50%) scale(0.5); }
  to { transform: translateX(-50%) scale(1); }
`;

const Ball = styled.div<ThemeProps>`
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background-color: ${(props) => props.theme.text};
  animation: ${Bounce} 0.5s linear infinite alternate;
  will-change: transform;
  
  @media (max-width: 48em) {
    left: 1rem;
  }
`;

const DrawSvg: React.FC = () => {
  const ref = useRef<HTMLDivElement>(null);
  const ballRef = useRef<HTMLDivElement>(null);
  const animationInitialized = useRef<boolean>(false);

  // Using useEffect instead of useLayoutEffect to ensure client-side only execution
  useEffect(() => {
    // Safety check for SSR
    if (typeof window === 'undefined') return;

    // Make sure we don't initialize multiple animations
    if (animationInitialized.current) return;
    animationInitialized.current = true;

    // Add a small delay to ensure DOM is fully rendered
    const timer = setTimeout(() => {
      const element = ref.current;
      
      // Use querySelector instead of getElementsByClassName for more reliability
      const svg = document.querySelector('.svg-path') as SVGPathElement | null;

      if (!element || !svg) {
        console.warn('DrawSvg: SVG path or container element not found');
        return;
      }

      // Get the total length of the path
      const length = svg.getTotalLength();

      // Set initial styles
      svg.style.strokeDasharray = length.toString();
      svg.style.strokeDashoffset = length.toString();

      // Create the ScrollTrigger
      const scrollTrigger = ScrollTrigger.create({
        trigger: element,
        start: "top center",
        end: "bottom bottom",
        onUpdate: (self) => {
          const draw = length * self.progress;
          svg.style.strokeDashoffset = (length - draw).toString();
        },
        onToggle: (self) => {
          if (ballRef.current) {
            ballRef.current.style.display = self.isActive ? "none" : "inline-block";
          }
        },
        fastScrollEnd: true,
      });

      // Return cleanup function
      return () => {
        scrollTrigger.kill();
      };
    }, 500); // Wait 500ms for everything to be ready

    // Cleanup
    return () => {
      clearTimeout(timer);
      
      // Kill all ScrollTrigger instances to prevent leaks
      if (typeof ScrollTrigger !== 'undefined') {
        ScrollTrigger.getAll().forEach(trigger => trigger.kill());
      }
    };
  }, []); // Empty dependency array ensures this runs once

  return (
    <>
      <Ball ref={ballRef} />
      <VectorContainer ref={ref}>
        <Vector />
      </VectorContainer>
    </>
  );
};

// Export without memo to avoid potential stale closures
export default DrawSvg;