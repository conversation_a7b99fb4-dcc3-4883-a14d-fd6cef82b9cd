"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useRef } from "react";
import Link from "next/link";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { usePathname } from "next/navigation";
import { Button, Input, Checkbox } from "@heroui/react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { z } from "zod";
import ReCAPTCHA from "react-google-recaptcha";
import { signIn } from "next-auth/react";

import AuthSocialButtons from "./AuthSocialButtons";
import { registerUser } from "@/app/lib/actions/auth/actions";
import FingerprintLoading from "@/public/images/icons/FingerprintLoading.gif";
import VerifiedLoading from "@/public/images/icons/VerifiedLoading.gif";
import {
  validateAndTrackReferralCode,
  registerDaerah3TForUser
} from '@/app/(dashboard)/referral/actions';
import {
  getReferralCodeFrom<PERSON>ookie,
  setReferralCode<PERSON>ookie,
  deleteReferralCodeCookie
} from './cookies-actions';

// Types for form values and errors
interface FormValues {
  firstname: string;
  lastname: string;
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  emailConsent: boolean;
  termsAgreed: boolean;
  referralCode: string;
}

interface FormErrors {
  [key: string]: string | undefined;
}

interface DiscountValidation {
  isValid: boolean;
  discountPercentage?: number;
  expiresAt?: string;
  errorMessage?: string;
}

export const Register = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const redirectParam = searchParams?.get('redirect') || null;
  
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [doneDispatch, setDoneDispatch] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState<boolean>(false);
  const [redirectPath, setRedirectPath] = useState<string | null>(null);
  
  // Referral code states
  const [referralValidation, setReferralValidation] = useState<DiscountValidation | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [cookieReferralCode, setCookieReferralCode] = useState<string | null>(null);
  const [hasManualInput, setHasManualInput] = useState(false);
  const [showValidateButton, setShowValidateButton] = useState(false);
  const [hideReferralInput, setHideReferralInput] = useState(false);
  const [showReferralInput, setShowReferralInput] = useState(false);
  const [askedAboutReferral, setAskedAboutReferral] = useState(false);
  
  // reCAPTCHA
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);
  const recaptchaRef = useRef<ReCAPTCHA>(null);
  const RecaptchasiteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
  
  // Form values state
  const [formValues, setFormValues] = useState<FormValues>({
    firstname: "",
    lastname: "",
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    emailConsent: false,
    termsAgreed: false,
    referralCode: ""
  });
  
  // Form errors state
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  
  const showNotification = (
    message: string,
    type: "success" | "info" | "error" | "warning"
  ) => {
    toast[type](message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  // Check for redirect path and handle initial referral code
  useEffect(() => {
    // Check for redirect path in localStorage
    const savedPath = localStorage.getItem('auth_redirect_uri');
    
    // If the saved path includes 'events', hide the referral input
    if (savedPath && savedPath.includes('events')) {
      setRedirectPath(savedPath);
      setHideReferralInput(true);
    } else if (redirectParam) {
      setRedirectPath(redirectParam);
    }
    
    // Process the initial referral code
    handleInitialReferralCode();
  }, [redirectParam]);
  
  // Handle initial referral code check
  useEffect(() => {
    handleInitialReferralCode();
  }, []);

  const handleInitialReferralCode = async () => {
    try {
      const result = await getReferralCodeFromCookie();
      if (result.success && result.value) {
        console.log('Found referral code:', result.value);
        setCookieReferralCode(result.value);
        setFormValues(prev => ({
          ...prev,
          referralCode: result.value || '' 
        }));
        
        // When we find a referral code in the cookie, automatically show the refcode input
        // and set the "asked about referral" flag to true
        setShowReferralInput(true);
        setAskedAboutReferral(true);
        
        await handleValidateReferral(result.value, false);
      }
    } catch (error) {
      console.error('Error handling initial referral code:', error);
      showNotification('Failed to load referral code', 'error');
    }
  };

  const handleValidateReferral = async (code: string, isManual: boolean = true) => {
    if (!code) return;
    
    setIsValidating(true);
    
    try {
      const validation = await validateAndTrackReferralCode(code, 'REGISTRATION');
      
      setReferralValidation(validation);
      
      if (validation.isValid) {
        // Set the cookie if validation is successful
        if (isManual) {
          const cookieResult = await setReferralCodeCookie(code);
          if (cookieResult.success) {
            setCookieReferralCode(code);
          }
          showNotification("Kode referral valid!", "success");
          setShowValidateButton(false); // Hide button after successful validation
        }
      } else {
        if (isManual) {
          setFormValues(prev => ({
            ...prev,
            referralCode: ''
          }));
          showNotification(validation.errorMessage || "Kode referral tidak valid", "error");
          // Clear cookie if validation fails
          deleteReferralCodeCookie();
          setCookieReferralCode(null);
        }
      }
    } catch (error) {
      console.error("Error validating referral code:", error);
      setReferralValidation({
        isValid: false,
        errorMessage: "Gagal memvalidasi kode referral"
      });
      if (isManual) {
        showNotification("Gagal memvalidasi kode referral", "error");
      }
    } finally {
      setIsValidating(false);
    }
  };

  const handleReferralInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    
    setHasManualInput(true);
    setShowValidateButton(!!value); // Show button only when there's input
    setFormValues(prev => ({
      ...prev,
      referralCode: value
    }));
    
    // Clear previous validation state when user starts typing
    setReferralValidation(null);
    
    // If there was a cookie referral code, clear it when user starts typing
    if (cookieReferralCode) {
      deleteReferralCodeCookie();
      setCookieReferralCode(null);
    }
  };

  // Zod validation schemas
  const FormSchema = z.object({
    firstname: z.string().min(1, "First name cannot be empty!"),
    lastname: z.string().min(1, "Last name cannot be empty!"),
    email: z.string().min(1, "Email cannot be empty!").email("Please enter a valid email address"),
    username: z
      .string()
      .min(1, "Username cannot be empty!")
      .min(6, "Username must be at least 6 characters long")
      .regex(
        /^[a-zA-Z]+(?=.*\d)[a-zA-Z\d]*$/,
        "Username must start with a letter and contain at least one number"
      )
      .transform((val) => val.toLowerCase()),
  });

  const PasswordSchema = z.object({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters long")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/\d/, "Password must contain at least one digit")
      .regex(
        /[^a-zA-Z0-9]/,
        "Password must contain at least one special character"
      ),
  });

  const ConfirmPasswordSchema = z
    .object({
      confirmPassword: z.string().min(1, "Please confirm your password"),
    })
    .refine((data) => data.confirmPassword === formValues.password, {
      message: "Passwords don't match",
      path: ["confirmPassword"],
    });

  const ConsentSchema = z.object({
    emailConsent: z.boolean().refine(val => val === true, {
      message: "You must agree to receive transactional emails to create an account"
    }),
    termsAgreed: z.boolean().refine(val => val === true, {
      message: "You must agree to the Terms and Conditions and Privacy Policy"
    })
  });

  // Handle reCAPTCHA change
  const handleRecaptchaChange = (token: string | null) => {
    setRecaptchaToken(token);
  };

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    
    setFormValues((prev) => ({
      ...prev,
      [name]: value,
    }));
    
    // Clear error when typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    
    setFormValues((prev) => ({
      ...prev,
      [name]: checked,
    }));
    
    // Clear error when checking
    if (checked && formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  // Validate a specific field
  const validateField = (name: string, value: string | boolean) => {
    try {
      if (name === "password") {
        PasswordSchema.parse({ password: value });
      } else if (name === "confirmPassword") {
        ConfirmPasswordSchema.parse({
          confirmPassword: value,
        });
      } else if (name === "emailConsent" || name === "termsAgreed") {
        ConsentSchema.shape[name].parse(value);
      } else {
        // For other fields, validate against the main schema
        FormSchema.shape[name as keyof typeof FormSchema.shape]?.parse(value);
      }
      // Clear error if validation passes
      setFormErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessage = error.errors[0]?.message || "Validation error";
        setFormErrors((prev) => ({
          ...prev,
          [name]: errorMessage,
        }));
        return false;
      }
      return false;
    }
  };

  // Generate avatar URL based on first and last name
  const getAvatarUrl = (firstname: string, lastname: string) => {
    const username = `${firstname}+${lastname}`.trim();
    if (!username) return "";
    return `https://avatar.iran.liara.run/username?username=${encodeURIComponent(username)}`;
  };

  // Redirect after successful registration
  useEffect(() => {
    if (doneDispatch) {
      const handleRedirect = async () => {
        const savedPath = localStorage.getItem('auth_redirect_uri');
        console.log("Saved path:", savedPath);
        
        if (savedPath && savedPath.includes('events') && savedPath.includes('gratis')) {
          console.log("Path includes both 'events' and 'gratis'");
          
          if (formValues.email) {
            console.log("Attempting to register user for Daerah 3T:", formValues.email);
            showNotification("Registering you for free program...", "info");
            
            try {
              // Use the route directly instead of the server function
              const response = await fetch('/api/daerah-3t-register', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  email: formValues.email
                }),
              });
              
              console.log("Response status:", response.status);
              
              if (response.ok) {
                const result = await response.json();
                console.log("Registration successful:", result);
                showNotification("Successfully registered for free program!", "success");
              } else {
                const errorText = await response.text();
                console.error("Failed to register. Server response:", errorText);
              }
            } catch (error) {
              console.error("Exception during registration:", error);
            }
          } else {
            console.log("No valid email, skipping registration");
          }
          
          // Redirect regardless of registration outcome
          router.push(savedPath);
        } 
        else if (savedPath && savedPath.includes('events')) {
          console.log("Regular events path, redirecting without registration");
          router.push(savedPath);
        } 
        else if (savedPath && (savedPath.includes('register') || savedPath?.includes('login'))){
          console.log("auth paths redirecting to dashboard");
          router.push("/dashboard");
        }
        else {
          console.log("No special path, redirecting to dashboard");
          router.push("/dashboard");
        }
      };
      
      // Execute the async function
      handleRedirect();
    }
  }, [doneDispatch, router, formValues.email, showNotification]);

  // Handle form submission
  const handleRegister = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Validate all fields first
    let isValid = true;
    
    // Check checkbox fields
    if (!formValues.emailConsent) {
      setFormErrors(prev => ({
        ...prev,
        emailConsent: "You must agree to receive transactional emails to create an account"
      }));
      isValid = false;
    }
    
    if (!formValues.termsAgreed) {
      setFormErrors(prev => ({
        ...prev,
        termsAgreed: "You must agree to the Terms and Conditions and Privacy Policy"
      }));
      isValid = false;
    }
    
    // Validate all other fields
    Object.keys(formValues).forEach(key => {
      // Skip referralCode and checkbox fields (already validated)
      if (key !== 'referralCode' && key !== 'emailConsent' && key !== 'termsAgreed') {
        const valid = validateField(key, formValues[key as keyof FormValues]);
        if (!valid) isValid = false;
      }
    });
    
    // If validation failed, stop here
    if (!isValid) {
      setIsLoading(false);
      return;
    }
    
    // Validate referral code before submission if one is present
    const referralToUse = formValues.referralCode;
    if (referralToUse && !referralValidation?.isValid) {
      await handleValidateReferral(referralToUse);
      if (!referralValidation?.isValid) {
        setIsLoading(false);
        return;
      }
    }
    
    try {
      // Use reCAPTCHA if available
      if (recaptchaRef.current) {
        recaptchaRef.current.reset();
        const token = await recaptchaRef.current.executeAsync?.();
        
        if (!token) {
          showNotification("CAPTCHA verification failed. Please try again.", "error");
          setIsLoading(false);
          return;
        }
        
        // Map formValues to FormData for API call
        const formData = new FormData();
        
        // Add form fields directly
        formData.append("firstname", formValues.firstname);
        formData.append("lastname", formValues.lastname);
        formData.append("email", formValues.email);
        formData.append("username", formValues.username);
        formData.append("password", formValues.password);
        formData.append("cpassword", formValues.confirmPassword); // Note the field name difference
        formData.append("referralCode", formValues.referralCode);
        formData.append("emailConsent", formValues.emailConsent ? 'true' : 'false');
        formData.append("termsAgreed", formValues.termsAgreed ? 'true' : 'false');
        formData.append("recaptchaToken", token);
        
        // Add picture URL based on firstname and lastname
        const pictureUrl = getAvatarUrl(formValues.firstname, formValues.lastname);
        formData.append("picture", pictureUrl);

        // Add the redirect path to the form data if it exists
        if (redirectPath) {
          formData.append("redirectPath", redirectPath);
        }
        
        console.log("Submitting registration with data:", {
          firstname: formValues.firstname,
          lastname: formValues.lastname,
          email: formValues.email,
          username: formValues.username,
          picture: pictureUrl,
          referralCode: formValues.referralCode
        });
        
        // Call the registerUser function with formData
        const result = await registerUser(formData);
        
        if (result && result.error) {
          const postgresError = result.error.message.error;
          
          if (postgresError && postgresError.includes('duplicate key value violates unique constraint "users_username_key"')) {
            showNotification('Username already exists. Please choose a different username.', 'error');
          } else {
            showNotification("Registration failed: " + (postgresError || "Unknown error"), 'error');
          }
          setIsLoading(false);
        } else if (result && result.result) {
          console.log("Registration successful! Logging in user automatically...");
          showNotification("Registration successful! Logging you in...", "success");
          
          // Client-side sign-in using the credentials
          if (result.credentials) {
            try {
              const signInResult = await signIn("credentials", {
                username: result.credentials.username,
                password: result.credentials.password,
                redirect: false
              });
              
              if (signInResult?.error) {
                console.error("Auto-login failed:", signInResult.error);
                showNotification("Registration successful, but automatic login failed. Please log in manually.", "warning");
              } else {
                console.log("Auto-login successful!");
                showNotification("You're now logged in!", "success");
              }
            } catch (loginError) {
              console.error("Auto-login error:", loginError);
              showNotification("Registration successful, but automatic login failed. Please log in manually.", "warning");
            }
          }
          
          // Continue with redirect regardless of the auto-login result
          setDoneDispatch(true);
        } else {
          console.log(result);
          throw new Error("Unexpected response from server");
        }
      } else {
        setIsLoading(false);
        showNotification("reCAPTCHA not loaded. Please refresh the page and try again.", "error");
      }
    } catch (error) {
      console.error("Registration error:", error);
      showNotification("An unexpected error occurred. Please try again later.", "error");
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center w-full max-w-md mx-auto">
      <div className="text-center text-[25px] font-bold mb-6">Register</div>

      <div className="w-full mb-6">
        <AuthSocialButtons title="Register with" />
      </div>

      <div className="flex items-center gap-3 w-full mb-6">
        <div className="h-[1px] flex-grow bg-gray-300"></div>
        <span className="text-sm text-gray-500 bg-white px-2">
          Or continue with email
        </span>
        <div className="h-[1px] flex-grow bg-gray-300"></div>
      </div>

      <form onSubmit={handleRegister} className="w-full space-y-4">
        <div className="flex flex-col w-full gap-4">
          <div className="space-y-4">
            {/* Firstname and lastname fields */}
            <div className="grid grid-cols-2 gap-4">
              <Input
                name="firstname"
                type="text"
                label="First Name"
                value={formValues.firstname}
                onChange={handleInputChange}
                isDisabled={isLoading}
                errorMessage={formErrors.firstname}
                isInvalid={!!formErrors.firstname}
                variant="bordered"
                isRequired
              />
              
              <Input
                name="lastname"
                type="text"
                label="Last Name"
                value={formValues.lastname}
                onChange={handleInputChange}
                isDisabled={isLoading}
                errorMessage={formErrors.lastname}
                isInvalid={!!formErrors.lastname}
                variant="bordered"
                isRequired
              />
            </div>

            <Input
              name="username"
              type="text"
              label="Username"
              value={formValues.username}
              onChange={handleInputChange}
              isDisabled={isLoading}
              errorMessage={formErrors.username}
              isInvalid={!!formErrors.username}
              variant="bordered"
              isRequired
            />

            <Input
              name="email"
              type="email"
              label="Email"
              value={formValues.email}
              onChange={handleInputChange}
              isDisabled={isLoading}
              errorMessage={formErrors.email}
              isInvalid={!!formErrors.email}
              variant="bordered"
              isRequired
            />

            <div className="relative">
              <Input
                name="password"
                type={showPassword ? "text" : "password"}
                label="Password"
                value={formValues.password}
                onChange={handleInputChange}
                isDisabled={isLoading}
                errorMessage={formErrors.password}
                isInvalid={!!formErrors.password}
                variant="bordered"
                isRequired
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-600"
                tabIndex={-1}
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? "🙈" : "👁️"}
              </button>
            </div>

            <div className="relative">
              <Input
                name="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                label="Confirm Password"
                value={formValues.confirmPassword}
                onChange={handleInputChange}
                isDisabled={isLoading}
                errorMessage={formErrors.confirmPassword}
                isInvalid={!!formErrors.confirmPassword}
                variant="bordered"
                isRequired
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-600"
                tabIndex={-1}
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? "🙈" : "👁️"}
              </button>
            </div>
            
            {/* Preview of avatar based on name */}
            {(formValues.firstname || formValues.lastname) && (
              <div className="flex items-center gap-3 p-2 bg-gray-50 rounded-lg">
                <img 
                  src={getAvatarUrl(formValues.firstname, formValues.lastname)} 
                  alt="Profile avatar preview" 
                  className="w-12 h-12 rounded-full object-cover"
                />
                <div className="text-sm text-gray-600">
                  <p>Your profile picture will be generated based on your name</p>
                </div>
              </div>
            )}
            
            {/* Referral Code Section */}
            {!hideReferralInput && !askedAboutReferral ? (
              <div className="flex flex-col mb-3">
                <p className="mb-2">Do you have a referral code?</p>
                <div className="flex gap-2">
                  <Button 
                    size="sm"
                    onPress={() => {
                      setShowReferralInput(true);
                      setAskedAboutReferral(true);
                    }}
                  >
                    Yes, I have a code
                  </Button>
                  <Button 
                    size="sm"
                    variant="ghost"
                    onPress={() => {
                      setShowReferralInput(false);
                      setAskedAboutReferral(true);
                      // Clear any existing referral code
                      setFormValues(prev => ({
                        ...prev,
                        referralCode: ""
                      }));
                      // Clear cookie if exists
                      if (cookieReferralCode) {
                        deleteReferralCodeCookie();
                        setCookieReferralCode(null);
                      }
                    }}
                  >
                    No, continue without code
                  </Button>
                </div>
              </div>
            ) : !hideReferralInput && showReferralInput ? (
              <div className="flex flex-col mb-3">
                <div className="flex gap-2">
                  <Input
                    id="referralCode"
                    name="referralCode"
                    label="Referral Code"
                    value={formValues.referralCode}
                    variant="bordered"
                    onChange={handleReferralInputChange}
                    isInvalid={referralValidation?.isValid === false}
                    isDisabled={isValidating}
                  />
                  {showValidateButton && hasManualInput && (
                    <Button 
                      className="mt-1"
                      size="lg"
                      isDisabled={isValidating}
                      onPress={() => handleValidateReferral(formValues.referralCode)}
                    >
                      {isValidating ? "Validating..." : "Terapkan"}
                    </Button>
                  )}
                </div>
                {isValidating && (
                  <p className="text-gray-500 text-sm mt-1">
                    Validating...
                  </p>
                )}
                {referralValidation?.isValid && (
                  <p className="text-green-500 text-sm mt-1">
                    ✓ Valid referral code, you will get +1000 AI Credits <br />
                    (2000 initial + 1000 referral bonus = 3000 Total credits)
                  </p>
                )}
                {referralValidation?.errorMessage && (
                  <p className="text-red-500 text-sm mt-1">
                    {referralValidation.errorMessage}
                  </p>
                )}
                {cookieReferralCode && !hasManualInput && (
                  <p className="text-gray-500 text-sm mt-1">
                    Using referral code from your previous session
                  </p>
                )}
                <Button 
                  size="sm" 
                  variant="ghost" 
                  className="self-start mt-2"
                  onPress={() => {
                    setShowReferralInput(false);
                    setFormValues(prev => ({
                      ...prev,
                      referralCode: ""
                    }));
                    if (cookieReferralCode) {
                      deleteReferralCodeCookie();
                      setCookieReferralCode(null);
                    }
                    setReferralValidation(null);
                  }}
                >
                  Skip referral code
                </Button>
              </div>
            ) : (
              // Even when hidden, we still include the value as a hidden input for form submission
              <div style={{ display: 'none' }}>
                <Input
                  id="referralCode"
                  name="referralCode"
                  value={formValues.referralCode}
                  variant="bordered"
                />
              </div>
            )}
            
            <div className="flex flex-col gap-1">
              <div className="flex items-start gap-2">
                <input
                  id="emailConsent"
                  name="emailConsent"
                  type="checkbox"
                  className="mt-1"
                  checked={formValues.emailConsent}
                  onChange={handleCheckboxChange}
                />
                <label htmlFor="emailConsent" className="text-sm text-gray-700">
                  I agree to receive transactional emails, including account notifications, service updates, and security alerts. I can opt-out anytime in account settings.
                </label>
              </div>
              {formErrors.emailConsent && (
                <p className="text-red-500 text-xs">{formErrors.emailConsent}</p>
              )}
            </div>
            
            <div className="flex flex-col gap-1">
              <div className="flex items-start gap-2">
                <input
                  id="termsAgreed"
                  name="termsAgreed"
                  type="checkbox"
                  className="mt-1"
                  checked={formValues.termsAgreed}
                  onChange={handleCheckboxChange}
                />
                <label htmlFor="termsAgreed" className="text-sm text-gray-700">
                  I agree to the <a href="https://terang.ai/terms-and-conditions" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Terms and Conditions</a> and <a href="https://terang.ai/privacy-policy" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Privacy Policy</a>
                </label>
              </div>
              {formErrors.termsAgreed && (
                <p className="text-red-500 text-xs">{formErrors.termsAgreed}</p>
              )}
            </div>
          </div>

          {/* reCAPTCHA (hidden) */}
          <ReCAPTCHA
            ref={recaptchaRef}
            sitekey={RecaptchasiteKey as string}
            size="invisible"
            onChange={handleRecaptchaChange}
          />

          {/* Submit Button */}
          <Button
            className={`w-full py-2 text-white font-bold ${
              isLoading ? "bg-gray-400 cursor-not-allowed" : "bg-black"
            }`}
            radius="full"
            size="lg"
            type="submit"
          >
            {isLoading ? (
              <Image alt="" height={40} src={FingerprintLoading} width={40} />
            ) : doneDispatch ? (
              <span className="inline-flex items-center">
                <Image alt="" height={40} src={VerifiedLoading} width={40} />
                Redirecting...
              </span>
            ) : (
              "Register"
            )}
          </Button>
        </div>
      </form>

      <div className="font-light text-slate-400 mt-4 text-sm">
        Already have an account?{" "}
        <Link
          href="/login"
          className="inline-block font-bold text-primary hover:underline px-2 py-1 -mx-2 touch-manipulation"
        >
          Login here
        </Link>
      </div>

      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
};

export default Register;