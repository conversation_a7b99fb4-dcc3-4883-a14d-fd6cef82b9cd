import React from "react";
import ReactMarkdown from "react-markdown";
import remarkMath from "remark-math";
import rehypeKatex from "rehype-katex";
import 'katex/dist/katex.min.css';
import parse from 'html-react-parser';
import DOMPurify from 'dompurify';
import { AdaptiveImage } from "./adaptive-image";

interface ContentType {
  type: string;
  content: string;
}

/**
 * Utility function to check if text contains LaTeX patterns
 */
export const containsLaTeX = (text: string): boolean => {
  if (!text) return false;
  
  // Preprocess the text to normalize LaTeX in numbered lists
  const preprocessed = text.replace(/(\d+\.\s+[^\n]*?)(\s*\n\s*)(\\[\[\(])/g, 
    (match, listPrefix, newlines, latexStart) => {
      return `${listPrefix}${newlines}${latexStart}`;
    });
    
  // Extended pattern list for LaTeX detection
  const latexPatterns = [
    /\$\$[\s\S]+?\$\$/,                                  // Display math $$ ... $$
    /(?<![a-zA-Z0-9])\$[^\$]+?\$(?![a-zA-Z0-9])/,       // Inline math $ ... $ (not within a word)
    /\\\[\s*[\s\S]*?\s*\\\]/,                           // Display math \[ ... \] with more flexible whitespace
    /\\\([\s\S]*?\\\)/,                                 // Inline math \( ... \) with ANY content (fixed pattern)
    /\\begin\{[^{}]+\}[\s\S]*?\\end\{[^{}]+\}/,          // Environment blocks like \begin{align*}...\end{align*}
    /\\frac\{[^{}]+\}\{[^{}]+\}/,                        // Fractions
    /\\begin\{/,                                         // Begin of environments
    /\\end\{/,                                           // End of environments
    /\\left/,                                            // Left delimiters
    /\\right/,                                           // Right delimiters
    /\\times/,                                           // Times operator
    /\\div/,                                             // Division operator
    /\\cdot/,                                            // Dot operator
    /(?<![a-zA-Z0-9])\\[a-zA-Z]+\{/,                     // General LaTeX commands (not within a word)
    /\\sqrt(\[[^\]]+\])?\{[^}]+\}/,                       // Matches \sqrt and \sqrt[n]{}
    /- \\[a-zA-Z]+/,                                     // Dashed list items with LaTeX commands
    /- \\[\[\(]/,                                         // Dashed list items with LaTeX delimiters
    /- .+?:\s*\n\s*<br\/>\s*\n\s*\\[\[\(]/,              // List items with LaTeX after break
    /\\\(.*?\\\)/                                         // Simplified pattern for \( ... \) - catches more cases
  ];
  
  // Add a specific pattern for numbered lists with LaTeX
  latexPatterns.push(/\d+\.\s+.*?\n\s*\\[\[\(]/);  // Numbered list items followed by LaTeX
  
  return latexPatterns.some(pattern => pattern.test(preprocessed));
};

/**
 * FIXED: Improved function to process align environments
 */
export const processAlignEnvironments = (text: string): string => {
  if (!text) return '';
  
  let processed = text;
  let replacementMade = false;
  
  // Case 1: Handle \[ \begin{align*}...\end{align*} \] - FIXED: Convert to align (not align*)
  processed = processed.replace(
    /\\\[\s*\\begin\{align\*\}([\s\S]*?)\\end\{align\*\}\s*\\\]/g,
    (_, content) => {
      replacementMade = true;
      // Use align instead of align*
      return `$$
\\begin{align}${content}\\end{align}
$$`;
    }
  );
  
  if (!replacementMade) {
    // Case 2: Handle \[ \begin{align}...\end{align} \]
    processed = processed.replace(
      /\\\[\s*\\begin\{align\}([\s\S]*?)\\end\{align\}\s*\\\]/g,
      (_, content) => {
        replacementMade = true;
        return `$$
\\begin{align}${content}\\end{align}
$$`;
      }
    );
  }
  
  if (!replacementMade) {
    // Case 3: Handle $ \begin{align*}...\end{align*} $ - FIXED: Convert to $$ with align
    processed = processed.replace(
      /\$\s*\\begin\{align\*\}([\s\S]*?)\\end\{align\*\}\s*\$/g,
      (_, content) => {
        replacementMade = true;
        return `$
\\begin{align}${content}\\end{align}
$`;
      }
    );
  }
  
  if (!replacementMade) {
  // Case 4: Handle $ \begin{align}...\end{align} $
  processed = processed.replace(
    /\$\s*\\begin\{align\}([\s\S]*?)\\end\{align\}\s*\$/g, 
    (_, content) => {
      replacementMade = true;
      return `$
\\begin{align}${content}\\end{align}
$`;
    }
  );
  }

  if (!replacementMade) {
  // Case 5: Handle regular \[ ... \] LaTeX blocks - convert to $$ ... $$
  processed = processed.replace(
    /\$\$([\s\S]*?)\$\$/g,
    (match, content) => {
      console.log('Found $$ content:', content);
      return `
$
${content}
$`;
    }
  );
}
  return processed;
};

/**
 * Doubles all backslashes in the input text
 * @param text The input text to process
 * @returns Text with all backslashes doubled
 */
export const polarizeBackslashes = (text: string): string => {
  if (!text) return '';
  
  // Replace any sequence of backslashes with double its count
  return text.replace(/\\+/g, match => {
    // Double the number of backslashes in the match
    return match.repeat(2);
  });
};

/**
 * Process LaTeX content - improved version with better environment handling
 */
export const processLaTeX = (text: string): string => {
  if (!text) return '';
  
  // Preserve markdown lists during processing
  const listPreserved = text.replace(/(^|\n)- /g, "$1\u{1F4A5}"); // Temporary marker for bullets

  // Existing processing logic
  let processed = listPreserved;

  processed = processed.replace(
    /(\s*-\s+.*?)(\n\s*<br\/>\s*\n\s*\\[\[\(])/g, 
    (match, listItem, latexStart) => {
      return `${listItem}\n\n${latexStart}`;
    }
  );
  
  // Tracking objects for placeholders
  const placeholders = {
    html: {} as Record<string, string>,
    latex: {} as Record<string, string>,
    htmlCount: 0,
    latexCount: 0
  };
  
  // Helper function to create HTML placeholders
  const createHtmlPlaceholder = (match: string): string => {
    const placeholder = `__HTML_PLACEHOLDER_${placeholders.htmlCount}__`;
    placeholders.html[placeholder] = match;
    placeholders.htmlCount++;
    return placeholder;
  };
  
  // Helper function to create LaTeX placeholders
  const createLatexPlaceholder = (match: string): string => {
    const placeholder = `__LATEX_PLACEHOLDER_${placeholders.latexCount}__`;
    placeholders.latex[placeholder] = match;
    placeholders.latexCount++;
    return placeholder;
  };
  
  // Convert HTML <br/> tags to Markdown line breaks 
  processed = processed.replace(/<br\s*\/?>/gi, '\n\n');
  
  // PHASE 1: First protect properly formatted LaTeX that is already there
  
  // 1.1: Protect display math delimited by $$ ... $$
  processed = processed.replace(/(\$\$[\s\S]*?\$\$)/g, match => 
    createLatexPlaceholder(match)
  );
  
  // 1.2: Protect inline math delimited by $ ... $ but not within words (improved pattern)
  processed = processed.replace(/(?<![a-zA-Z0-9])\$([^\$]+?)\$(?![a-zA-Z0-9])/g, match => 
    createLatexPlaceholder(match)
  );
  
  // PHASE 2: Protect HTML tags to avoid processing them as LaTeX
  
  // 2.1: Protect HTML tags
  processed = processed.replace(/<[^>]+>/g, match => 
    createHtmlPlaceholder(match)
  );
  
  // PHASE 3: Process remaining LaTeX notation
  
  // 3.1: Process display math \[ ... \] - Convert to $$
  processed = processed.replace(/\\\[\s*([\s\S]*?)\s*\\\]/g, (_, content) => {
    // Preserve line breaks within display math
    return `$$${content.trim()}$$`;
  });
  
  // 3.2: FIXED pattern for inline math \( ... \) - This now handles nested parentheses properly
  processed = processed.replace(/\\\(([\s\S]*?)\\\)/g, (_, content) => {
    return `$${content.trim()}$`;
  });
  
  // PHASE 4: Process standalone LaTeX commands
  
  // 4.1: Handle mixed fractions like 3\frac{1}{2}
  processed = processed.replace(/(\d+)\\frac\{([^{}]+)\}\{([^{}]+)\}/g, (match, number, numerator, denominator) => {
    if (!isInMathDelimiters(processed, match, processed.indexOf(match))) {
      return `$${number}\\frac{${numerator}}{${denominator}}$`;
    }
    return match;
  });
  
  // 4.2: Handle standalone fractions \frac{}{} not inside math delimiters
  processed = processed.replace(/(?<!\$)\\frac\{([^{}]+)\}\{([^{}]+)\}(?!\$)/g, (_, numerator, denominator) => {
    if (!isInMathDelimiters(processed, `\\frac{${numerator}}{${denominator}}`, processed.indexOf(`\\frac{${numerator}}{${denominator}}`))) {
      return `$\\frac{${numerator}}{${denominator}}$`;
    }
    return `\\frac{${numerator}}{${denominator}}`;
  });
  
  // 4.3: Process LaTeX operators
  const operators = ['\\div', '\\times', '\\cdot', '\\pm', '\\mp'];
  const operatorPattern = new RegExp(`(${operators.join('|')})`, 'g');
  
  processed = processed.replace(operatorPattern, (match) => {
    if (!isInMathDelimiters(processed, match, processed.indexOf(match))) {
      return `$${match}$`;
    }
    return match;
  });
  
  // PHASE 5: Restore placeholders
  
  // 5.1: Restore LaTeX placeholders
  Object.entries(placeholders.latex).forEach(([placeholder, original]) => {
    processed = processed.replace(placeholder, original);
  });
  
  // 5.2: Restore HTML placeholders
  Object.entries(placeholders.html).forEach(([placeholder, original]) => {
    processed = processed.replace(placeholder, original);
  });

  // 5.3: Process align environments
  processed = processAlignEnvironments(processed);

  // PHASE 6: Fix randomly inserted dollar signs within words 
  // This can happen with patterns like dite$mp$uh
  processed = processed.replace(/(\w+)\$(\w+)\$(\w+)/g, "$1$2$3");

  // PHASE 7: Add proper Markdown line breaks
  // Replace single newlines with double spaces + newline to preserve line breaks in Markdown
  processed = processed.replace(/([^\n])\n(?!\n)/g, '$1  \n');

  // Restore markdown lists after processing
  processed = processed.replace(/\u{1F4A5}/gu, `• `);

  // Final cleanup: Fix cases where $ ... $ might have been converted incorrectly for display math
  // Look for single line display math that should be $$ ... $$
  processed = processed.replace(/\$\s+(\\\w+[\s\S]*?)\s+\$/g, (match, content) => {
    // If the content contains display math commands like \text, convert to $$
    if (/\\text/.test(content)) {
      return `$$${content.trim()}$$`;
    }
    return match;
  });
  
  return processed;
};

/**
 * Helper function to check if a substring is already inside math delimiters
 */
function isInMathDelimiters(text: string, substr: string, startPos: number): boolean {
  if (startPos < 0) return false;
  
  // Extract text before the substring
  const textBefore = text.substring(0, startPos);
  
  // Count $ and $$ before our substring
  const singleDollarsBefore = (textBefore.match(/\$/g) || []).length;
  const doubleDollarsBefore = (textBefore.match(/\$\$/g) || []).length;
  
  // Calculate if we're inside math delimiters
  // If odd number of single $ and not inside $$, we're in math mode
  const insideSingleDollars = (singleDollarsBefore - (doubleDollarsBefore * 2)) % 2 === 1;
  
  // If odd number of $$, we're in display math mode
  const insideDoubleDollars = doubleDollarsBefore % 2 === 1;
  
  return insideSingleDollars || insideDoubleDollars;
}

/**
 * Component to render LaTeX content
 */
export const MathComponent: React.FC<{ math: string, display?: boolean }> = ({ math, display = false }) => {
  const processedMath = display ? `$$${math}$$` : `$${math}$`;
  
  return (
    <ReactMarkdown
      remarkPlugins={[remarkMath]}
      rehypePlugins={[rehypeKatex]}
    >
      {processedMath}
    </ReactMarkdown>
  );
};

/**
 * Improved content renderer that efficiently handles different content types
 */
export const renderEnhancedContent = (content: ContentType): React.ReactElement | null => {
  switch (content.type) {
    case "text": {
      // First apply our improved align environment processor
      let processedContent = processAlignEnvironments(content.content);
      
      // Check if content contains HTML tags
      if (/<[a-z][\s\S]*>/i.test(processedContent)) {
        if (containsLaTeX(processedContent)) {
          // For mixed HTML and LaTeX content
          return <MixedHtmlLatexRenderer content={processedContent} />;
        }
        return <HTMLRenderer htmlContent={processedContent} />;
      }
      
      // Then check if content contains LaTeX
      if (containsLaTeX(processedContent)) {
        // Process LaTeX content
        processedContent = processLaTeX(processedContent);
        
        // Check if it's exclusively LaTeX content with delimiters
        if (processedContent.trim().startsWith('$$') && processedContent.trim().endsWith('$$')) {
          const mathContent = processedContent.trim().substring(2, processedContent.trim().length - 2);
          return <MathComponent math={mathContent} display={true} />;
        }
        
        if (processedContent.trim().startsWith('$') && processedContent.trim().endsWith('$') && 
            !processedContent.trim().startsWith('$$') && !processedContent.trim().endsWith('$$')) {
          const mathContent = processedContent.trim().substring(1, processedContent.trim().length - 1);
          return <MathComponent math={mathContent} display={false} />;
        }
        
        // For mixed content with LaTeX, use ReactMarkdown
        const cleanContent = processedContent
        .replace(/\n/g, '\n\n')
        .replace(/- (.+?):\n<br\/>/g, '• $1:\n\n')  // Handle bulleted titles with proper spacing
        
        return (
          <ReactMarkdown
            remarkPlugins={[remarkMath]}
            rehypePlugins={[rehypeKatex]}
            components={{
              p: ({ children }) => <div className="mb-2">{children}</div>,
            }}
          >
            {cleanContent}
          </ReactMarkdown>
        );
      }
      
      // For plain text without HTML or LaTeX, just return as is
      return <span>{content.content}</span>;
    }
    
    case "media":
      return <AdaptiveImage src={content.content} alt="Question media" />;

    default:
      return null;
  }
};

/**
 * Component for handling mixed HTML and LaTeX content
 */
/**
 * Improved component for handling mixed HTML and LaTeX content
 * This version has better handling of LaTeX content within HTML list elements
 */
export const MixedHtmlLatexRenderer: React.FC<{ content: string }> = ({ content }) => {
  // First apply our improved align environment processor
  const processedAlignments = processAlignEnvironments(content);
  
  // Create a more sophisticated parser to handle nested HTML structures
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = DOMPurify.sanitize(processedAlignments, {
    ADD_ATTR: ['target'],
    ALLOWED_TAGS: ['br', 'p', 'div', 'span', 'a', 'b', 'i', 'strong', 'em', 'img', 'ul', 'ol', 'li']
  });
  
  // Function to recursively process nodes
  const processNode = (node: Node): React.ReactNode => {
    // Handle text nodes
    if (node.nodeType === Node.TEXT_NODE) {
      const textContent = node.textContent || '';
      
      // Check if text contains LaTeX
      if (containsLaTeX(textContent)) {
        const processedLaTeX = processLaTeX(textContent);
        
        return (
          <ReactMarkdown
            remarkPlugins={[remarkMath]}
            rehypePlugins={[rehypeKatex]}
          >
            {processedLaTeX}
          </ReactMarkdown>
        );
      }
      
      return textContent;
    }
    
    // Handle element nodes
    if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;
      const tagName = element.tagName.toLowerCase();
      
      // Special handling for list elements
      if (tagName === 'ul' || tagName === 'ol') {
        const children = Array.from(element.childNodes).map((child, i) => {
          // For li elements, we need to wrap them properly
          if ((child as Element).tagName?.toLowerCase() === 'li') {
            return (
              <li key={i}>
                {Array.from(child.childNodes).map((liChild, j) => (
                  <React.Fragment key={j}>{processNode(liChild)}</React.Fragment>
                ))}
              </li>
            );
          }
          return <React.Fragment key={i}>{processNode(child)}</React.Fragment>;
        });
        
        return tagName === 'ul' ? <ul>{children}</ul> : <ol>{children}</ol>;
      }
      
      // Handle BR tags
      if (tagName === 'br') {
        return <br />;
      }
      
      // For other elements, process their children
      const children = Array.from(element.childNodes).map((child, i) => (
        <React.Fragment key={i}>{processNode(child)}</React.Fragment>
      ));
      
      // Return appropriate elements based on tag name
      switch (tagName) {
        case 'p': return <p>{children}</p>;
        case 'div': return <div>{children}</div>;
        case 'span': return <span>{children}</span>;
        case 'a': 
          return <a href={element.getAttribute('href') || '#'} target={element.getAttribute('target') || '_self'}>{children}</a>;
        case 'b':
        case 'strong': return <strong>{children}</strong>;
        case 'i':
        case 'em': return <em>{children}</em>;
        case 'img': 
          return <img src={element.getAttribute('src') || ''} alt={element.getAttribute('alt') || ''} />;
        default: return <span>{children}</span>;
      }
    }
    
    // Handle other node types
    return null;
  };
  
  // Process the entire content
  return (
    <React.Fragment>
      {Array.from(tempDiv.childNodes).map((node, index) => (
        <React.Fragment key={index}>
          {processNode(node)}
        </React.Fragment>
      ))}
    </React.Fragment>
  );
};

/**
 * HTML renderer that can safely handle embedded LaTeX and HTML tags
 */
export const HTMLRenderer: React.FC<{ htmlContent: string }> = ({ htmlContent }) => {
  // Check for LaTeX in the HTML content
  if (containsLaTeX(htmlContent)) {
    return <MixedHtmlLatexRenderer content={htmlContent} />;
  }
  
  // For pure HTML without LaTeX
  const purifyConfig = {
    ADD_ATTR: ['target'],
    ALLOWED_TAGS: ['br', 'p', 'div', 'span', 'a', 'b', 'i', 'strong', 'em', 'img', 'ul', 'ol', 'li']
  };
  
  const sanitizedContent = DOMPurify.sanitize(htmlContent, purifyConfig);
  
  return <div className="html-content">{parse(sanitizedContent)}</div>;
};

/**
 * Preprocessor for question content - now with improved alignment handling
 */
export const preprocessQuestionContent = (text: string): string => {
  return containsLaTeX(text) ? processLaTeX(processAlignEnvironments(text)) : text;
};

interface LatexPlaceholder {
  placeholder: string;
  original: string;
}
/**
 * Improved function to handle content with both HTML and LaTeX in TypeScript
 * This version safely handles comparison operators like < and > in LaTeX
 */
export const processHtmlAndLaTeX = (content: string): React.ReactElement => {
  // Step 1: Extract and protect LaTeX blocks before HTML parsing
  const latexBlocks: LatexPlaceholder[] = [];
  let processedContent = content;
  
  // Extract display math blocks ($$...$$)
  const displayMathRegex = /(\$\$[\s\S]*?\$\$)/g;
  processedContent = processedContent.replace(displayMathRegex, (match) => {
    const placeholder = `__LATEX_DISPLAY_${latexBlocks.length}__`;
    latexBlocks.push({ placeholder, original: match });
    return placeholder;
  });
  
  // Extract inline math blocks ($...$)
  const inlineMathRegex = /(?<!\$)(\$[^\$]+?\$)(?!\$)/g;
  processedContent = processedContent.replace(inlineMathRegex, (match) => {
    const placeholder = `__LATEX_INLINE_${latexBlocks.length}__`;
    latexBlocks.push({ placeholder, original: match });
    return placeholder;
  });
  
  // Step 2: Split content by HTML tags
  const htmlParts: string[] = [];
  const htmlTagRegex = /(<[^>]+>)/g;
  
  let lastIndex = 0;
  let match;
  
  while ((match = htmlTagRegex.exec(processedContent)) !== null) {
    if (match.index > lastIndex) {
      htmlParts.push(processedContent.substring(lastIndex, match.index));
    }
    htmlParts.push(match[0]);
    lastIndex = match.index + match[0].length;
  }
  
  if (lastIndex < processedContent.length) {
    htmlParts.push(processedContent.substring(lastIndex));
  }
  
  // Step 3: Process each part, with special handling for LaTeX
  return (
    <React.Fragment>
      {htmlParts.map((part, index) => {
        // Handle HTML tags directly
        if (part.startsWith('<') && part.endsWith('>')) {
          if (part.match(/<br\s*\/?>/i)) {
            return <br key={index} />;
          }
          return (
            <span 
              key={index}
              dangerouslySetInnerHTML={{ 
                __html: DOMPurify.sanitize(part, {
                  ADD_ATTR: ['target'],
                  ALLOWED_TAGS: ['br', 'p', 'div', 'span', 'a', 'b', 'i', 'strong', 'em', 'img', 'ul', 'ol', 'li']
                })
              }}
            />
          );
        }
        
        // Process LaTeX placeholders in this part
        let restoredContent = part;
        
        // Check if this part contains any LaTeX placeholders
        const hasLatexPlaceholder = latexBlocks.some(
          block => restoredContent.includes(block.placeholder)
        );
        
        // Always restore placeholders regardless of other conditions
        if (hasLatexPlaceholder) {
          // Restore all placeholders in this part
          for (const { placeholder, original } of latexBlocks) {
            if (restoredContent.includes(placeholder)) {
              // Process the LaTeX before restoration to fix < and >
              const processedLatex = processLatexOperators(original);
              restoredContent = restoredContent.replace(
                new RegExp(placeholder, 'g'), // Use global replacement to catch all instances
                processedLatex
              );
            }
          }
          
          console.log(restoredContent)
          // Now check if the restored content contains actual LaTeX markers
          if (containsLaTeX(restoredContent)) {
            // Render with ReactMarkdown for LaTeX support
            return (
              <ReactMarkdown
                key={index}
                remarkPlugins={[remarkMath]}
                rehypePlugins={[rehypeKatex]}
              >
                {processLaTeX(restoredContent)}
              </ReactMarkdown>
            );
          }
        }else if(containsLaTeX(restoredContent)){
                    // Restore all placeholders in this part
                    for (const { placeholder, original } of latexBlocks) {
                      if (restoredContent.includes(placeholder)) {
                        // Process the LaTeX before restoration to fix < and >
                        const processedLatex = processLatexOperators(original);
                        restoredContent = restoredContent.replace(
                          new RegExp(placeholder, 'g'), // Use global replacement to catch all instances
                          processedLatex
                        );
                      }
                    }
                    
                    console.log(restoredContent)
                    // Now check if the restored content contains actual LaTeX markers
                    if (containsLaTeX(restoredContent)) {
                      // Render with ReactMarkdown for LaTeX support
                      return (
                        <ReactMarkdown
                          key={index}
                          remarkPlugins={[remarkMath]}
                          rehypePlugins={[rehypeKatex]}
                        >
                          {processLaTeX(restoredContent)}
                        </ReactMarkdown>
                      );
                    }
        }
        
        // For regular text parts without LaTeX
        return <span key={index}>{restoredContent}</span>;
      })}
    </React.Fragment>
  );
};

/**
 * Helper function to process LaTeX operators properly
 * Converts < and > to \lt and \gt for proper rendering
 */
export const processLatexOperators = (latex: string): string => {
  // Don't process if it's not a LaTeX string
  if (!latex.includes('$')) return latex;
  
  // Extract the LaTeX content based on whether it's display math or inline
  let content: string;
  let prefix: string;
  let suffix: string;
  
  if (latex.startsWith('$$') && latex.endsWith('$$')) {
    content = latex.substring(2, latex.length - 2);
    prefix = '$$';
    suffix = '$$';
  } else if (latex.startsWith('$') && latex.endsWith('$')) {
    content = latex.substring(1, latex.length - 1);
    prefix = '$';
    suffix = '$';
  } else {
    // Not a recognized LaTeX format, return as is
    return latex;
  }
  
  // Handle < and > within math content by replacing them with LaTeX commands
  // Only replace < and > that are used as operators (with spaces around them)
  const processedContent = content
    // Replace standalone < (with spaces around it)
    .replace(/(\s)<(\s)/g, '$1\\lt $2')
    // Replace < followed by = (less than or equal)
    .replace(/(\s)<=(\s)/g, '$1\\leq $2')
    // Replace standalone > (with spaces around it)
    .replace(/(\s)>(\s)/g, '$1\\gt $2')
    // Replace > followed by = (greater than or equal)
    .replace(/(\s)>=(\s)/g, '$1\\geq $2');
  
  // Return the processed LaTeX with delimiters
  return `${prefix}${processedContent}${suffix}`;
};

export default {
  containsLaTeX,
  processLaTeX,
  processAlignEnvironments,
  MathComponent,
  renderEnhancedContent,
  HTMLRenderer,
  MixedHtmlLatexRenderer,
  processHtmlAndLaTeX,
  preprocessQuestionContent
};
