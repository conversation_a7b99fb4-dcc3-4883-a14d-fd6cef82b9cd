"use server";

import { auth } from "@/auth";

export async function getPurchasedExams(examType: string = 'EXAM'): Promise<boolean | undefined | any> {
  const session = await auth();

  try {
    if (session && session.user) {
      // Added examType=EXAM as default parameter
      const url = `${process.env.BACKEND_BASE_URL}/v2/available-exams/purchased/${session.user.email}?pageSize=20000&page=1&examType=${examType}`;

      const requestOptions = {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      };

      const response = await fetch(url, requestOptions);

      if (!response.ok) {
        if (response.status === 400) {
          console.log(
            `RESPONSE CODE: ${response.status}, User is not registered`,
          );

          // Redirect to register page
          // redirect('/auth/register'); // this will throw error
          return false;
        } else {
          const errorText = await response.text();

          throw new Error(
            `Network response was not ok: ${response.status} - ${response.statusText}. Details: $`,
          );
        }
      } else {
        // If the response is successful (status 200)
        const data = await response.json();

        // console.log(
        //   `User already exists, this check is passed, proceed to the page`,
        // );

        // Proceed with your logic for a successful response
        // For example, you might want to return some user data
        return data.data;
      }
    }
  } catch (error) {
    console.error(`There is an error occurred: ${JSON.stringify(error)}`);
    throw error;
  }
}

export async function getExamsTaken(examType: string = 'EXAM'): Promise<boolean | undefined | any> {
  const session = await auth();

  try {
    if (session && session.user) {
      // Added examType=EXAM as default parameter
      const url = `${process.env.BACKEND_BASE_URL}/v2/available-exams/taken/${session.user.email}?pageSize=20000&page=1&examType=${examType}`;

      const requestOptions = {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      };

      const response = await fetch(url, requestOptions);

      if (!response.ok) {
        if (response.status === 400) {
          console.log(
            `RESPONSE CODE: ${response.status}, User is not registered`,
          );

          // Redirect to register page
          // redirect('/auth/register'); // this will throw error
          return false;
        } else {
          const errorText = await response.text();

          throw new Error(
            `Network response was not ok: ${response.status} - ${response.statusText}. Details: $`,
          );
        }
      } else {
        // If the response is successful (status 200)
        const data = await response.json();
        console.log(data)

        // console.log(
        //   `User already exists, this check is passed, proceed to the page`,
        // );

        // Proceed with your logic for a successful response
        // For example, you might want to return some user data
        return data.data;
      }
    }
  } catch (error) {
    console.error(`There is an error occurred: ${JSON.stringify(error)}`);
    throw error;
  }
}

// Frontend: app/lib/actions/my-exams-and-trials/actions.ts

interface PaginationParams {
  page: number;
  limit: number;
  subject?: string;
  type?: string;
  category_id?: string;
}


interface Exam {
  id: string;
  name: string;
  subname: string;
  description: string;
  baselinePrice: number;
  visibility: string;
  duration: string;
  type: string;
  subject: string;
  categories: Array<{
    id: string;
    name: string;
  }>;
}

interface PaginatedResponse {
  data: {
    [key: string]: Exam[];
  };
  pagination: any;
}

interface ApiResponse {
  data: {
    [key: string]: Exam[];
  };
  pagination: any;
}


export async function getAvailablePractices(
  params: Partial<PaginationParams> = {}
): Promise<PaginatedResponse> {
  const session = await auth();

  try {
    if (session && session.user) {
      // Build query parameters with the added category_id parameter
      const queryParams = new URLSearchParams();
      
      // Add required parameters
      queryParams.append('page', (params.page || 1).toString());
      queryParams.append('limit', (params.limit || 10).toString());
      
      // Add optional parameters if they exist
      if (params.subject) {
        queryParams.append('subject', params.subject);
      }
      
      if (params.type) {
        queryParams.append('type', params.type);
      }
      
      // Add category_id if provided
      if (params.category_id) {
        queryParams.append('category_id', params.category_id);
      }

      const url = `${process.env.BACKEND_BASE_URL}/v0/available-exams/practice?${queryParams}`;

      const requestOptions = {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      };

      const response = await fetch(url, requestOptions);

      if (!response.ok) {
        if (response.status === 400) {
          console.log(`RESPONSE CODE: ${response.status}, User is not registered`);
          return {
            data: {},
            pagination: {
              currentPage: 1,
              totalPages: 0,
              perSubjectLimit: 10,
              subjectCounts: [],
              totalExams: 0
            }
          };
        } else {
          const errorText = await response.text();
          throw new Error(
            `Network response was not ok: ${response.status} - ${response.statusText}. Details: ${errorText}`
          );
        }
      }

      const data: ApiResponse = await response.json();
      return data;
    }

    // Return empty response if no session
    return {
      data: {},
      pagination: {
        currentPage: 1,
        totalPages: 0,
        perSubjectLimit: 10,
        subjectCounts: [],
        totalExams: 0
      }
    };
  } catch (error) {
    console.error(`There is an error occurred: ${JSON.stringify(error)}`);
    throw error;
  }
}

interface ExamQuestion {
  id: string;
  examId: string;
  question: string;
  correctAnswer: string;
  explanation: string;
  options: {
    id: string;
    text: string;
    isCorrect: boolean;
  }[];
}

export interface QuestionAnswer {
  id: string;
  exam_id: string;
  data: string;
}

export async function getQuestionAnswers(examId: string, type?: string): Promise<QuestionAnswer[] | undefined> {
  const session = await auth();

  const fetchType = type ?? "LIMIT"; // Set default to "FULL" if type is undefined LIMIT | FULL

  try {
    if (!session?.user?.email) {
      throw new Error("User not authenticated");
    }

    const url = `${process.env.BACKEND_BASE_URL}/v1/exam-question-hints/${examId}?pageSize=1&page=1&fetchType=${fetchType}`;

    const requestOptions = {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
    };

    const response = await fetch(url, requestOptions);

    if (!response.ok) {
      if (response.status === 404) {
        console.log(`Exam ID ${examId} not found`);
        return undefined;
      }

      if (response.status === 403) {
        console.log(`User ${session.user.email} not authorized to access this exam`);
        return undefined;
      }

      if (response.status === 400) {
        console.log(`User ${session.user.email} has not purchased this exam`);
        return undefined;
      }

      const errorText = await response.text();
      throw new Error(
        `Network response was not ok: ${response.status} - ${response.statusText}. Details: ${errorText}`
      );
    }

    const data: any = await response.json();
    console.log(data.data)
    
    console.log(
      `Successfully retrieved ${data.data.length} questions for exam ${examId}`
    );

    return data.data;
  } catch (error) {
    console.error(`Error fetching exam questions: ${error instanceof Error ? error.message : JSON.stringify(error)}`);
    throw error;
  }
}