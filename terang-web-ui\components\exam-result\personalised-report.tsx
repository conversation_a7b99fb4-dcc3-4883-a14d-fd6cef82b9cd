"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Divider,
} from "@heroui/react";
import dynamic from "next/dynamic";
import { motion, Variants } from "framer-motion";
import { ApexOptions } from "apexcharts";

const Chart = dynamic(() => import("react-apexcharts"), { ssr: false });

const LaporanKinerjaUjianmu = () => {
  const [chartData, setChartData] = useState<{
    options: ApexOptions;
    series: ApexAxisChartSeries;
  }>({
    options: {
      chart: {
        id: "kinerja-ujianmu",
        toolbar: {
          show: false,
        },
        animations: {
          enabled: true,
          speed: 800,
          animateGradually: {
            enabled: true,
            delay: 150,
          },
          dynamicAnimation: {
            enabled: true,
            speed: 350,
          },
        },
      },
      xaxis: {
        categories: [
          "S1",
          "S2",
          "S3",
          "S4",
          "S5",
          "S6",
          "S7",
          "S8",
          "S9",
          "S10",
        ],
        title: {
          text: "Nomor Soal",
          style: {
            fontSize: "14px",
            fontWeight: 600,
          },
        },
      },
      yaxis: {
        title: {
          text: "Wak<PERSON> (detik)",
          style: {
            fontSize: "14px",
            fontWeight: 600,
          },
        },
      },
      colors: ["#7928CA"],
      plotOptions: {
        bar: {
          borderRadius: 10,
          columnWidth: "60%",
          dataLabels: {
            position: "top",
          },
        },
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val + " dtk";
        },
        offsetY: -20,
        style: {
          fontSize: "12px",
          colors: ["#304758"],
        },
      },
      grid: {
        borderColor: "#f1f1f1",
      },
      fill: {
        type: "gradient",
        gradient: {
          shade: "light",
          type: "vertical",
          shadeIntensity: 0.25,
          gradientToColors: undefined,
          inverseColors: true,
          opacityFrom: 0.85,
          opacityTo: 0.85,
          stops: [50, 0, 100],
        },
      },
    },
    series: [
      {
        name: "Waktu Kamu",
        data: [45, 30, 60, 80, 20, 55, 40, 75, 35, 50],
      },
    ],
  });

  const [report, setReport] = useState("");
  const [comparisonReport, setComparisonReport] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setReport(
        "Hei, kamu sudah kerja keras banget nih! 👏 \n\nKelihatannya soal nomor 4 tentang sinonim kata 'gelap' agak bikin pusing ya? Kamu butuh 80 detik buat nyeleseinnya. Tapi jangan khawatir, namanya juga belajar! Ingat, 'suram' itu sinonim yang tepat untuk 'gelap'.\n\nDi sisi lain, wuih... kamu keren banget di soal nomor 5 tentang sinonim 'kuat'! Cepet bener nyeleseinnya, cuma 20 detik. Kayaknya kamu udah paham banget kalau 'kokoh' itu sinonimnya 'kuat' ya? 🚀\n\nSoal nomor 8 tentang sinonim 'indah' juga butuh waktu agak lama nih, 75 detik. Mungkin kamu ragu-ragu antara 'cantik' dan pilihan lainnya? Inget ya, 'cantik' itu sinonim yang pas buat 'indah'.\n\nKalau mau ningkatin nilai, coba deh fokus ke materi sinonim yang masih bikin bingung, kayak 'gelap' dan 'indah'. Trus, jangan lupa pertahanin kehebatan kamu di materi yang udah dikuasai, kayak 'kuat' ya!\n\nInget, setiap kesulitan itu kesempatan buat jadi lebih pinter. Tetep semangat ya! 💪😊",
      );

      setComparisonReport(
        "Nih, kabar baik buat kamu! 🌟 Dari 1000 orang yang ikutan ujian sinonim ini, kamu masuk ke 30% teratas lho! Keren kan?\n\nKenapa bisa gitu? Soalnya kamu ngerjain soal-soal dengan kecepatan yang oke, terutama di soal nomor 5 tentang sinonim 'kuat'. Itu nunjukin kamu ngerti banget sama materi sinonim untuk kata-kata yang berhubungan dengan kekuatan.\n\nTapi inget ya, yang penting bukan cuma cepet, tapi juga bener. Jadi, pas belajar sinonim, fokus buat paham maknanya, bukan cuma hafalin doang. Misalnya, coba pahami kenapa 'suram' itu sinonim 'gelap', atau kenapa 'cantik' bisa jadi sinonim 'indah'.\n\nTetep pertahanin cara belajar kamu yang udah bagus, tapi jangan lupa buat ningkatin di bagian yang masih kurang, kayak sinonim untuk kata-kata yang berhubungan dengan penampilan atau suasana. Kamu udah di jalur yang bener kok! Lanjutkan! 🚀😄",
      );

      setIsLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const containerVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10,
        when: "beforeChildren",
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { type: "spring", stiffness: 100, damping: 10 },
    },
  };

  return (
    <motion.div animate="visible" initial="hidden" variants={containerVariants}>
      <Card className="max-w-[800px] mx-auto my-8">
        <CardHeader className="flex gap-3">
          <motion.div className="flex flex-col" variants={itemVariants}>
            <h2 className="text-xl font-bold">Hasil Ujian Sinonimmu</h2>
            <p className="text-small text-default-500">
              Yuk, Lihat Berapa Lama Kamu Ngerjain Tiap Soal Sinonim!
            </p>
          </motion.div>
        </CardHeader>
        <Divider />
        <CardBody>
          <motion.div variants={itemVariants}>
            <Chart
              height={350}
              options={chartData.options}
              series={chartData.series}
              type="bar"
            />
          </motion.div>
        </CardBody>
        <Divider />
        <CardFooter>
          <motion.div className="w-full" variants={itemVariants}>
            <h3 className="text-lg font-semibold mb-2">Kata-kata Buat Kamu</h3>
            {isLoading ? (
              <motion.div
                animate={{ opacity: 1 }}
                className="h-20 bg-gray-200 rounded animate-pulse"
                initial={{ opacity: 0 }}
                transition={{ duration: 0.5 }}
              />
            ) : (
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                initial={{ opacity: 0, y: 20 }}
                transition={{ type: "spring", stiffness: 100, damping: 10 }}
              >
                <p className="text-sm text-gray-600 whitespace-pre-line mb-4">
                  {report}
                </p>
                <h3 className="text-lg font-semibold mb-2">
                  Gimana Skor Kamu Dibanding yang Lain?
                </h3>
                <p className="text-sm text-gray-600 whitespace-pre-line">
                  {comparisonReport}
                </p>
              </motion.div>
            )}
          </motion.div>
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default LaporanKinerjaUjianmu;
