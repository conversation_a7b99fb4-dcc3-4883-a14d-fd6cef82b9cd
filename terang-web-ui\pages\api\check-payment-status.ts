import { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { orderId } = req.query;

  if (!orderId) {
    return res.status(400).json({ error: 'Order ID is required' });
  }

  const isProduction = process.env.NODE_ENV === 'production';
  const baseUrl = isProduction
    ? 'https://api.midtrans.com'
    : 'https://api.sandbox.midtrans.com';

  const serverKey = isProduction
    ? process.env.MIDTRANS_SNAP_PRODUCTION
    : process.env.MIDTRANS_SNAP_SANDBOX;

  try {
    const response = await fetch(`${baseUrl}/v2/${orderId}/status`, {
      headers: {
        'Accept': 'application/json',
        'Authorization': `Basic ${serverKey}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    console.log(response)

    const data = await response.json();
    res.status(200).json(data);
  } catch (error) {
    console.error('Error checking payment status:', error);
    res.status(500).json({ error: 'Failed to check payment status' });
  }
}