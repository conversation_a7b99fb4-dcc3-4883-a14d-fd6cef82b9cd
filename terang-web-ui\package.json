{"name": "terang-web-ui", "version": "0.0.3", "private": true, "scripts": {"dev:win": "next dev --turbopack --port 80", "dev": "NODE_ENV=development next dev --turbopack --port 80", "build": "NODE_ENV=production next build && if [ $(uname) = 'Darwin' ]; then grep -rl '15.1.6' build/static/chunks | xargs sed -i '' 's/15.1.6//g'; else grep -rl '15.1.6' build/static/chunks | xargs sed -i 's/15.1.6//g'; fi", "build:win": "set NODE_ENV=production && next build && powershell -Command \"Get-ChildItem -Path build/static/chunks -Recurse | Select-String -Pattern '15.1.6' -List | ForEach-Object { (Get-Content $_.Path) -replace '15.1.6','''' | Set-Content $_.Path }\"", "start": "NODE_ENV=production next start", "lint": "eslint . --ext .ts,.tsx -c .eslintrc.json --fix"}, "dependencies": {"@fontsource/akaya-telivigala": "^5.1.0", "@fontsource/nunito": "^5.1.0", "@fontsource/sora": "^5.1.0", "@hello-pangea/dnd": "18.0.1", "@heroui/breadcrumbs": "2.2.11", "@heroui/card": "2.2.14", "@heroui/divider": "2.2.10", "@heroui/input": "^2.4.15", "@heroui/react": "2.7.4", "@heroui/spacer": "2.2.11", "@heroui/system": "^2.4.11", "@heroui/theme": "2.4.17", "@livekit/components-react": "^2.9.8", "@livekit/components-styles": "^1.1.6", "@lottiefiles/dotlottie-react": "^0.13.0", "@lottiefiles/react-lottie-player": "^3.5.4", "@microsoft/tiktokenizer": "^1.0.9", "@next/third-parties": "15.1.6", "@react-aria/ssr": "^3.9.7", "@react-aria/visually-hidden": "^3.8.19", "@react-email/components": "^0.0.33", "@reduxjs/toolkit": "^2.6.1", "@types/react-datepicker": "^7.0.0", "@types/react-google-recaptcha": "^2.1.9", "@vercel/analytics": "^1.3.1", "@vercel/speed-insights": "^1.0.12", "apexcharts": "^4.1.0", "chart.js": "^4.4.6", "clsx": "2.1.1", "cookies-next": "^5.1.0", "d3": "^7.9.0", "dompurify": "^3.1.7", "eventsource-parser": "^3.0.0", "form-data": "^4.0.0", "formik": "^2.4.6", "framer-motion": "^12.0.0", "gsap": "^3.12.5", "html-react-parser": "^5.2.2", "hugeicons-react": "^0.3.0", "install": "^0.13.0", "intl-messageformat": "^10.5.0", "katex": "^0.16.21", "livekit-server-sdk": "^2.13.0", "lucide-react": "^0.462.0", "mailgun.js": "^10.2.1", "motion": "^12.0.0", "next": "15.1.6", "next-auth": "^5.0.0-beta.26", "next-redux-wrapper": "^8.1.0", "next-sitemap": "^4.2.3", "next-themes": "^0.2.1", "npm": "^10.9.0", "posthog-js": "^1.194.1", "react": "19.0.0", "react-apexcharts": "^1.6.0", "react-chartjs-2": "^5.2.0", "react-confetti": "^6.1.0", "react-datepicker": "^7.5.0", "react-dom": "19.0.0", "react-google-recaptcha": "^3.1.0", "react-intersection-observer": "^9.13.1", "react-markdown": "^9.0.1", "react-redux": "^9.2.0", "react-syntax-highlighter": "^15.6.1", "react-toastify": "^10.0.5", "react-use": "^17.5.1", "react-virtuoso": "^4.12.3", "reactflow": "^11.11.4", "redis": "^4.7.0", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sharp": "^0.33.5", "sonner": "^1.7.2", "styled-components": "^6.1.13", "swiper": "^11.1.14", "ua-parser-js": "^2.0.0-rc.1", "yup": "^1.4.0", "zod": "^3.23.8"}, "devDependencies": {"@types/dompurify": "^3.0.5", "@types/katex": "^0.16.7", "@types/node": "20.5.7", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "7.2.0", "@typescript-eslint/parser": "7.2.0", "autoprefixer": "10.4.19", "eslint": "^8.57.0", "eslint-config-next": "15.1.6", "eslint-config-prettier": "^8.2.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.23.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-unused-imports": "^3.2.0", "postcss": "8.4.38", "tailwind-variants": "0.1.20", "tailwindcss": "3.4.3", "typescript": "5.0.4"}, "overrides": {"@types/react": "19.0.8", "@types/react-dom": "19.0.3"}}