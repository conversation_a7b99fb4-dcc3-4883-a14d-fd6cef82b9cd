"use server";

import { z } from "zod";
import { auth } from "@/auth";
import { sendEmail } from "@/app/lib/emails/mailgun";
import * as React from "react";

// Import all email templates with their prop types
import TeacherInvite, { type TeacherInviteProps } from "@/app/lib/emails/templates/TeacherInvite";
import StudentInvite, { type StudentInviteProps } from "@/app/lib/emails/templates/StudentInvite";
import StudyReminder, { type StudyReminderProps } from "@/app/lib/emails/templates/StudyReminder";
import TeacherMessage, { type TeacherMessageProps } from "@/app/lib/emails/templates/TeacherMessage";
import StudentMessage, { type StudentMessageProps } from "@/app/lib/emails/templates/StudentMessage";
import Announcement, { type AnnouncementProps } from "@/app/lib/emails/templates/StudentAnnouncement";
import SubscriptionActivation, { type SubscriptionActivationProps } from "@/app/lib/emails/templates/SubscriptionActivation";

// Type definitions for responses
type EmailResponse = {
  success: boolean;
  result?: any;
  errors?: Record<string, string[]>;
  message?: string;
};

// Validation schemas
const teacherInviteSchema = z.object({
  teacherName: z.string().min(1, "Teacher name is required"),
  institutionName: z.string().min(1, "Institution name is required"),
  email: z.string().email("Invalid email address"),
});

const studentInviteSchema = z.object({
  studentName: z.string().min(1, "Student name is required"),
  className: z.string().min(1, "Class name is required"),
  teacherName: z.string().min(1, "Teacher name is required"),
  email: z.string().email("Invalid email address"),
});

const studyReminderSchema = z.object({
  studentName: z.string().min(1, "Student name is required"),
  subjectName: z.string().min(1, "Subject name is required"),
  email: z.string().email("Invalid email address"),
  nextTopic: z.string().min(1, "Next topic is required"),
  timeRemaining: z.string().optional(),
});

const teacherMessageSchema = z.object({
  studentName: z.string().min(1, "Student name is required"),
  teacherName: z.string().min(1, "Teacher name is required"),
  messagePreview: z.string().min(1, "Message preview is required"),
  className: z.string().min(1, "Class name is required"),
  email: z.string().email("Invalid email address"),
});

const studentMessageSchema = z.object({
  studentName: z.string().min(1, "Student name is required"),
  teacherName: z.string().min(1, "Teacher name is required"),
  messagePreview: z.string().min(1, "Message preview is required"),
  className: z.string().min(1, "Class name is required"),
  email: z.string().email("Invalid email address"),
});

const subscriptionSchema = z.object({
  managerName: z.string().min(1, "manager name is required"),
  institutionName: z.string().min(1, "school name is required"),
  email: z.string().email("Invalid email address"),
  planName: z.string().min(1, "Plan name is required"),
  expiryDate: z.string().min(1, "Expiry date is required"),
});

const announcementSchema = z.object({
  className: z.string(),
  teacherName: z.string(),
  email: z.string().email(),
});

// Helper function to render React components to HTML string
const getData = async (element: React.ReactElement): Promise<string> => {
  const ReactDOMServer = (await import("react-dom/server")).default;
  return ReactDOMServer.renderToString(element);
};

// Helper function to generate mock invite tokens
async function generateInviteToken(email: string, type: 'teacher' | 'student'): Promise<string> {
  // Create a mock token using timestamp and hash
  const timestamp = Date.now();
  const emailHash = Buffer.from(email).toString('base64').slice(0, 8);
  const randomStr = Math.random().toString(36).substring(7);
  const mockToken = `${type}-${emailHash}-${randomStr}-${timestamp}`;
  
  // Simulate API delay (500ms)
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return mockToken;
}

// Helper function to simulate email sending in development
async function mockSendEmail(from: string, to: string, subject: string, html: string): Promise<any> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('Email sent:', {
    from,
    to,
    subject,
    htmlLength: html.length,
  });
  
  return {
    success: true,
    messageId: `mock-${Date.now()}-${Math.random().toString(36).substring(7)}`,
  };
}

// Use mock or real email sending based on environment
const emailSender = sendEmail;

// Send teacher invitation email
export async function sendTeacherInvitation(formData: FormData): Promise<EmailResponse> {
  try {
    const validatedFields = teacherInviteSchema.safeParse({
      teacherName: formData.get("teacherName"),
      institutionName: formData.get("institutionName"),
      email: formData.get("email"),
    });

    if (!validatedFields.success) {
      return {
        success: false,
        errors: validatedFields.error.flatten().fieldErrors,
        message: "Invalid fields in teacher invitation.",
      };
    }

    const { teacherName, institutionName, email } = validatedFields.data;
    const inviteLink = await generateInviteToken(email, "teacher");

    const props: TeacherInviteProps = {
      teacherName,
      institutionName,
      inviteLink: `${process.env.NEXT_PUBLIC_APP_URL}/invite/teacher/${inviteLink}`,
    };

    const from = "Terang AI <<EMAIL>>";
    const to = email;
    const subject = "Join Terang AI as a Teacher";
    
    const emailHtml = await getData(React.createElement(TeacherInvite, props));
    const result = await emailSender(from, to, subject, emailHtml);

    return { success: true, result };
  } catch (error) {
    console.error("Error sending teacher invitation:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to send teacher invitation email.",
    };
  }
}

// Send student invitation email
export async function sendStudentInvitation(formData: FormData): Promise<EmailResponse> {
  try {
    const validatedFields = studentInviteSchema.safeParse({
      studentName: formData.get("studentName"),
      className: formData.get("className"),
      teacherName: formData.get("teacherName"),
      email: formData.get("email"),
    });

    if (!validatedFields.success) {
      return {
        success: false,
        errors: validatedFields.error.flatten().fieldErrors,
        message: "Invalid fields in student invitation.",
      };
    }

    const { studentName, className, teacherName, email } = validatedFields.data;
    const inviteLink = await generateInviteToken(email, "student");

    const props: StudentInviteProps = {
      studentName,
      className,
      teacherName,
      inviteLink: `${process.env.NEXT_PUBLIC_APP_URL}/invite/student/${inviteLink}`,
    };

    const from = "Terang AI <<EMAIL>>";
    const to = email;
    const subject = `Join ${className} on Terang AI`;
    
    const emailHtml = await getData(React.createElement(StudentInvite, props));
    const result = await emailSender(from, to, subject, emailHtml);

    return { success: true, result };
  } catch (error) {
    console.error("Error sending student invitation:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to send student invitation email.",
    };
  }
}

export async function sendAnnouncement(formData: FormData): Promise<EmailResponse> {
  try {
    const validatedFields = announcementSchema.safeParse({
      className: formData.get("className"),
      teacherName: formData.get("teacherName"),
      email: formData.get("email"),
    });

    if (!validatedFields.success) {
      return {
        success: false,
        errors: validatedFields.error.flatten().fieldErrors,
        message: "Invalid fields in student invitation.",
      };
    }

    const { className, teacherName, email } = validatedFields.data;

    const props: StudentInviteProps = {
      className,
      teacherName,
    };

    const from = "Terang AI <<EMAIL>>";
    const to = email;
    const subject = `Pengumuman ${className} on Terang AI`;
    
    const emailHtml = await getData(React.createElement(Announcement, props));
    const result = await emailSender(from, to, subject, emailHtml);

    return { success: true, result };
  } catch (error) {
    console.error("Error sending student invitation:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to send student invitation email.",
    };
  }
}

// Send study reminder email
export async function sendStudyReminder(formData: FormData): Promise<EmailResponse> {
  try {
    const validatedFields = studyReminderSchema.safeParse({
      studentName: formData.get("studentName"),
      subjectName: formData.get("subjectName"),
      email: formData.get("email"),
      nextTopic: formData.get("nextTopic"),
      timeRemaining: formData.get("timeRemaining"),
    });

    if (!validatedFields.success) {
      return {
        success: false,
        errors: validatedFields.error.flatten().fieldErrors,
        message: "Invalid fields in study reminder.",
      };
    }

    const { studentName, subjectName, email, nextTopic, timeRemaining } = validatedFields.data;

    const props: StudyReminderProps = {
      studentName,
      subjectName,
      nextTopic,
      timeRemaining,
      studyPlanLink: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/study-plan`,
    };

    const from = "Terang AI <<EMAIL>>";
    const to = email;
    const subject = `Time to study ${subjectName} - Your Terang AI reminder`;
    
    const emailHtml = await getData(React.createElement(StudyReminder, props));
    const result = await emailSender(from, to, subject, emailHtml);

    return { success: true, result };
  } catch (error) {
    console.error("Error sending study reminder:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to send study reminder email.",
    };
  }
}

// Send teacher message notification
export async function sendTeacherMessageNotification(formData: FormData): Promise<EmailResponse> {
  try {
    const validatedFields = teacherMessageSchema.safeParse({
      studentName: formData.get("studentName"),
      teacherName: formData.get("teacherName"),
      messagePreview: formData.get("messagePreview"),
      className: formData.get("className"),
      email: formData.get("email"),
    });

    if (!validatedFields.success) {
      return {
        success: false,
        errors: validatedFields.error.flatten().fieldErrors,
        message: "Invalid fields in message notification.",
      };
    }

    const { studentName, teacherName, messagePreview, className, email } = validatedFields.data;

    const props: TeacherMessageProps = {
      studentName,
      teacherName,
      messagePreview,
      className,
      messageLink: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/messages`,
    };

    const from = "Terang AI <<EMAIL>>";
    const to = email;
    const subject = `New message from ${teacherName}`;
    
    const emailHtml = await getData(React.createElement(TeacherMessage, props));
    const result = await emailSender(from, to, subject, emailHtml);

    return { success: true, result };
  } catch (error) {
    console.error("Error sending message notification:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to send message notification email.",
    };
  }
}
// Send teacher message notification
export async function sendStudentMessageNotification(formData: FormData): Promise<EmailResponse> {
  try {
    const validatedFields = studentMessageSchema.safeParse({
      studentName: formData.get("studentName"),
      teacherName: formData.get("teacherName"),
      messagePreview: formData.get("messagePreview"),
      className: formData.get("className"),
      email: formData.get("email"),
    });

    if (!validatedFields.success) {
      return {
        success: false,
        errors: validatedFields.error.flatten().fieldErrors,
        message: "Invalid fields in message notification.",
      };
    }

    const { studentName, teacherName, messagePreview, className, email } = validatedFields.data;

    const props: StudentMessageProps = {
      studentName,
      teacherName,
      messagePreview,
      className,
      messageLink: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/messages`,
    };

    const from = "Terang AI <<EMAIL>>";
    const to = email;
    const subject = `New message from ${studentName}`;
    
    const emailHtml = await getData(React.createElement(StudentMessage, props));
    const result = await emailSender(from, to, subject, emailHtml);

    return { success: true, result };
  } catch (error) {
    console.error("Error sending message notification:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to send message notification email.",
    };
  }
}

// Send subscription activation email
export async function sendSubscriptionActivation(formData: FormData): Promise<EmailResponse> {
  try {
    const validatedFields = subscriptionSchema.safeParse({
      institutionName: formData.get("institutionName"),
      managerName: formData.get("managerName"),
      email: formData.get("email"),
      planName: formData.get("planName"),
      expiryDate: formData.get("expiryDate"),
    });

    if (!validatedFields.success) {
      return {
        success: false,
        errors: validatedFields.error.flatten().fieldErrors,
        message: "Invalid fields in subscription activation.",
      };
    }

    const { institutionName, managerName, email, planName, expiryDate } = validatedFields.data;

    const props: SubscriptionActivationProps = {
      institutionName: institutionName,
      managerName: managerName,
      planName: planName as "Basic" | "Professional" | "Enterprise", 
      expiryDate,
      dashboardLink: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`,
    };

    const from = "Terang AI <<EMAIL>>";
    const to = email;
    const subject = `Welcome to Terang AI ${planName}!`;
    
    const emailHtml = await getData(React.createElement(SubscriptionActivation, props));
    const result = await emailSender(from, to, subject, emailHtml);

    return { success: true, result };
  } catch (error) {
    console.error("Error sending subscription activation:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to send subscription activation email.",
    };
  }
}

// Helper function to get plan features
function getPlanFeatures(planName: string): string[] {
  const planFeatures: Record<string, string[]> = {
    "Basic": [
      "Limited practice tests",
      "Basic study recommendations",
      "Basic analytics",
      "Email support",
    ],
    "Premium": [
      "Unlimited practice tests",
      "AI-powered study recommendations",
      "Advanced performance analytics",
      "Priority support",
      "Personalized study plans",
    ],
  };

  return planFeatures[planName.toLowerCase()] || planFeatures["Basic"];
}