import React, { useState, ChangeEvent, FormEvent } from 'react';
import "@fontsource/sora";
import AnimatedPattern from '../AnimatedPattern';
import { Button, Input, Textarea } from "@heroui/react";
import { sendContactForm } from '../../actions/email';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import { useLanguage } from '@/app/language-wrapper'; // Adjust import path according to your project structure

interface FormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

const ContactHero: React.FC = () => {
  const { t } = useLanguage();
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    const toastId = toast.loading(t('sending_message'), {
      duration: Infinity,
    });
    
    const form = new FormData();
    form.append("name", formData.name);
    form.append("email", formData.email);
    form.append("subject", formData.subject);
    form.append("message", formData.message);
  
    try {
      const response = await sendContactForm(form);
      toast.dismiss(toastId);
      
      if (response.success) {
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: ''
        });
        
        toast.success(t('message_sent_success'), {
          description: response.message,
        });
        
        if (response.remainingAttempts !== undefined) {
          toast.info(
            t('remaining_messages').replace('{count}', response.remainingAttempts.toString())
              .replace('{s}', response.remainingAttempts === 1 ? '' : 's'),
            { duration: 5000 }
          );
        }
      } else {
        toast.error(t('message_send_failed'), {
          description: response.message || t('try_again_later'),
        });
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.dismiss(toastId);
      toast.error(t('error_occurred'), {
        description: t('try_again_later'),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="bg-gray-50 relative" id="contact">
      <div className="absolute inset-0 overflow-hidden">
        <AnimatedPattern />
      </div>
      
      <div className="relative max-w-7xl mx-auto px-10 py-16 flex flex-col lg:flex-row items-center justify-between gap-12">
        {/* Left Column - Contact Information */}
        <div className="w-full lg:w-1/2 space-y-6">
          <h1 className="text-4xl md:text-5xl font-bold leading-tight font-sora text-gray-900">
            {t('get_in_touch')}
          </h1>
          <p className="text-xl text-gray-600">
            {t('contact_description')}
          </p>
          
          {/* Contact Information */}
          <div className="space-y-4 pt-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{t('email')}</h3>
              <p className="text-gray-600"><EMAIL></p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{t('phone')}</h3>
              <p className="text-gray-600">+62 857 5578 3673</p>
            </div>
          </div>
        </div>

        {/* Right Column - Contact Form */}
        <div className="w-full lg:w-1/2">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg p-8 border border-gray-100">
            <h2 className="text-2xl font-semibold text-gray-900 mb-2 font-sora">
              {t('send_us_message')}
            </h2>
            <p className="text-gray-600 mb-6">
              {t('fill_form_below')}
            </p>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor='name' className="block text-gray-700 text-sm mb-1 font-sora">{t('name')}</label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder={t('your_name')}
                    required
                    disabled={isSubmitting}
                    variant="bordered"
                    size="lg"
                    radius="lg"
                    classNames={{
                      input: [
                        "bg-transparent",
                        "text-gray-900",
                        "placeholder:text-gray-500"
                      ],
                      innerWrapper: "bg-transparent",
                      inputWrapper: [
                        "bg-white/50",
                        "hover:bg-white/60",
                        "group-data-[focused=true]:bg-white/70",
                        "border-gray-200",
                        "hover:border-gray-300",
                        "group-data-[focused=true]:border-blue-500"
                      ]
                    }}
                  />
                </div>
                <div>
                  <label htmlFor='email' className="block text-gray-700 text-sm mb-1 font-sora">{t('email')}</label>
                  <Input
                    id="email"
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    placeholder={t('your_email')}
                    required
                    disabled={isSubmitting}
                    variant="bordered"
                    size="lg"
                    radius="lg"
                    classNames={{
                      input: [
                        "bg-transparent",
                        "text-gray-900",
                        "placeholder:text-gray-500"
                      ],
                      innerWrapper: "bg-transparent",
                      inputWrapper: [
                        "bg-white/50",
                        "hover:bg-white/60",
                        "group-data-[focused=true]:bg-white/70",
                        "border-gray-200",
                        "hover:border-gray-300",
                        "group-data-[focused=true]:border-blue-500"
                      ]
                    }}
                  />
                </div>
              </div>

              <div>
                <label htmlFor="subject" className="block text-gray-700 text-sm mb-1 font-sora">{t('subject')}</label>
                <Input
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  placeholder={t('subject_placeholder')}
                  required
                  disabled={isSubmitting}
                  variant="bordered"
                  size="lg"
                  radius="lg"
                  classNames={{
                    input: [
                      "bg-transparent",
                      "text-gray-900",
                      "placeholder:text-gray-500"
                    ],
                    innerWrapper: "bg-transparent",
                    inputWrapper: [
                      "bg-white/50",
                      "hover:bg-white/60",
                      "group-data-[focused=true]:bg-white/70",
                      "border-gray-200",
                      "hover:border-gray-300",
                      "group-data-[focused=true]:border-blue-500"
                    ]
                  }}
                />
              </div>

              <div>
                <label htmlFor='message' className="block text-gray-700 text-sm mb-1 font-sora">{t('message')}</label>
                <Textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder={t('message_placeholder')}
                  required
                  disabled={isSubmitting}
                  variant="bordered"
                  size="lg"
                  radius="lg"
                  rows={4}
                  classNames={{
                    input: [
                      "bg-transparent",
                      "text-gray-900",
                      "placeholder:text-gray-500"
                    ],
                    innerWrapper: "bg-transparent",
                    inputWrapper: [
                      "bg-white/50",
                      "hover:bg-white/60",
                      "group-data-[focused=true]:bg-white/70",
                      "border-gray-200",
                      "hover:border-gray-300",
                      "group-data-[focused=true]:border-blue-500"
                    ]
                  }}
                />
              </div>

              <Button
                type="submit"
                color="primary"
                size="lg"
                radius="lg"
                disabled={isSubmitting}
                className="w-full bg-blue-600 hover:bg-blue-700 transition-all duration-200 transform active:scale-95 disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    {t('sending')}
                  </span>
                ) : (
                  t('send_message')
                )}
              </Button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactHero;