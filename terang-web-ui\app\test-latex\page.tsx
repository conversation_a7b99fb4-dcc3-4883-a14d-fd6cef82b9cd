"use client";
import React, { useState, useEffect, Fragment } from 'react';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import ReactMarkdown from 'react-markdown';
import 'katex/dist/katex.min.css';
import DOMPurify from 'dompurify';
import { containsLaTeX, processLaTeX, processHtmlAndLaTeX } from "@/components/shared/latex-handler"; 

// Define types to match your application
interface ContentType {
  type: string;
  content: string;
}

const TextAreaTest = () => {
  const [text, setText] = useState(`Setiap suku bertambah sebesar \\(3\\frac{1}{2}\\). Secara berurutan dapat dituliskan:
\\[ 
\\begin{align}
13 + 3\\frac{1}{2} & = 16\\frac{1}{2} \\\\
16\\frac{1}{2} + 3\\frac{1}{2} & = 20 \\\\
20 + 3\\frac{1}{2} & = 23\\frac{1}{2} \\\\
23\\frac{1}{2} + 3\\frac{1}{2} & = 27 \\\\
27 + 3\\frac{1}{2} & = 30\\frac{1}{2}
\\end{align}
\\] 
Dengan demikian, suku berikutnya adalah \\( 30\\frac{1}{2} \\).

Coba juga dengan: \\begin{align}x^2 + y^2 = z^2\\end{align}
`);
  
  const [processedText, setProcessedText] = useState('');
  
  // Process text whenever it changes
  useEffect(() => {
    if (containsLaTeX(text)) {
      const processed = processLaTeX(text);
      setProcessedText(processed);
    } else {
      setProcessedText(text);
    }
  }, [text]);

  // Render content function adapted for our test case
  const renderContent = (content: string) => {
    // Create a content object matching your application structure
    const contentObj: ContentType = {
      type: "text",
      content: content
    };

    console.log(contentObj.content)
    
    // Now use the logic from your application
    if (contentObj.type === "text") {
      if (/<[a-z][\s\S]*>/i.test(contentObj.content) && containsLaTeX(contentObj.content)) {
        console.log(`${contentObj.content} masuk html latex`)
        return processHtmlAndLaTeX(contentObj.content);
      }
      
      if (contentObj.content.includes('\\[') && contentObj.content.includes('\\]')) {
        console.log(`${contentObj.content} masuk traditional`)
        return (
          <ReactMarkdown
            remarkPlugins={[remarkMath]}
            rehypePlugins={[rehypeKatex]}
          >
            {processLaTeX(contentObj.content)}
          </ReactMarkdown>
        );
      }
      
      if (/<[a-z][\s\S]*>/i.test(contentObj.content)) {
        console.log(`${contentObj.content} masuk tes`)
        return (
          <div
            dangerouslySetInnerHTML={{
              __html: DOMPurify.sanitize(contentObj.content, {
                ADD_ATTR: ['target'],
                ALLOWED_TAGS: ['br', 'p', 'div', 'span', 'a', 'b', 'i', 'strong', 'em', 'img', 'ul', 'ol', 'li']
              }),
            }}
          />
        );
      }
      
      if (containsLaTeX(contentObj.content)) {
        console.log(`${contentObj.content} masuk modern`)
        console.log((contentObj.content))
        return (
          <ReactMarkdown
            remarkPlugins={[remarkMath]}
            rehypePlugins={[rehypeKatex]}
          >
            {processLaTeX(contentObj.content)}
          </ReactMarkdown>
        );
      }
      
      return <span>{contentObj.content}</span>;
    }
    
    return null;
  };

  return (
    <div className="flex flex-col gap-6 w-full max-w-4xl">
      <div className="flex flex-col gap-2">
        <label htmlFor="textarea" className="text-lg font-medium">Edit LaTeX Content:</label>
        <textarea
          id="textarea"
          className="w-full h-64 p-4 border border-gray-300 rounded-lg font-mono text-sm"
          value={text}
          onChange={(e) => setText(e.target.value)}
        />
        <div className="text-sm text-gray-600">
          Type LaTeX with traditional delimiters like \(x^2\) or \[E=mc^2\] and they will be converted properly.
        </div>
      </div>
      
      <div className="flex flex-col gap-2">
        <h2 className="text-lg font-medium">Processed Text (after conversion):</h2>
        <div className="p-4 bg-gray-100 rounded-lg border border-gray-200 font-mono text-sm whitespace-pre-wrap overflow-auto max-h-64">
          {processedText}
        </div>
      </div>
      
      <div className="flex flex-col gap-2">
        <h2 className="text-lg font-medium">Rendered Output:</h2>
        <div className="p-4 bg-gray-50 rounded-lg border border-gray-200 overflow-auto">
          {renderContent(text)}
        </div>
      </div>
    </div>
  );
};

export default TextAreaTest;