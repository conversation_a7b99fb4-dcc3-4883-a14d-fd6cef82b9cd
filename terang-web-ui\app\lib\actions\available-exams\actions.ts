"use server";

import { getUserId } from "../account/actions";

import { auth } from "@/auth";
import { AvailableExamsType } from "@/components/types";

import { createClient } from "redis";
import { headers } from 'next/headers'

const createWriteClient = () => createClient({
  url: process.env.REDIS_URL_WRITE as string,
});

const createReadClient = () => createClient({
  url: process.env.REDIS_URL_READ as string,
});

export async function storeToRedis(key: string, value: any) {
  const redis = createWriteClient();

  try {
    await redis.connect();
    await redis.set(key, JSON.stringify(value), {
      EX: 21600, // Set expiration to 6 hours (21600 seconds)
    });
  } catch (error) {
    console.error("Failed to store data in Redis:", error);
  } finally {
    await redis.disconnect();
  }
}

export async function retrieveFromRedis(key: string): Promise<any | null> {
  const redis = createReadClient();

  try {
    await redis.connect();
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error("Failed to retrieve data from Redis:", error);
    return null;
  } finally {
    await redis.disconnect();
  }
}

interface PaymentInfo {
  bundleId?: string;
  orderId: string;
  examId: string;
  invoiceId: string;
  paymentId: string;
  refCode?: string;
}

export async function storePaymentInfo(paymentInfo: PaymentInfo) {
  const userId = await getUserId();
  
  // If this is a bundle payment, store it with a different key
  if (paymentInfo.bundleId) {
    const key = `bundle_payment_info:${userId}`;
    await storeToRedis(key, paymentInfo);
  } else {
    const key = `payment_info:${userId}`;
    await storeToRedis(key, paymentInfo);
  }
}

export async function getPaymentInfo(type: 'bundle' | 'exam' = 'exam'): Promise<PaymentInfo | null> {
  const userId = await getUserId();
  
  // If looking for a bundle payment or any payment
  if (type === 'bundle') {
    const bundleKey = `bundle_payment_info:${userId}`;
    const bundlePaymentInfo = await retrieveFromRedis(bundleKey);
    
    if (bundlePaymentInfo) {
      return bundlePaymentInfo;
    }
  }
  
  // If looking for a normal exam payment or any payment
  if (type === 'exam') {
    const examKey = `payment_info:${userId}`;
    return await retrieveFromRedis(examKey);
  }
  
  return null;
}

export async function removePaymentInfo(type: 'bundle' | 'exam' | 'any' = 'any') {
  const userId = await getUserId();
  
  if (type === 'bundle' || type === 'any') {
    const bundleKey = `bundle_payment_info:${userId}`;
    await storeToRedis(bundleKey, null);
  }
  
  if (type === 'exam' || type === 'any') {
    const examKey = `payment_info:${userId}`;
    await storeToRedis(examKey, null);
  }
}

export async function storePaymentToken(orderId: string, token: string) {
  const key = `payment_token:${orderId}`;
  await storeToRedis(key, token);
}

export async function getPaymentToken(orderId: string): Promise<string | null> {
  const key = `payment_token:${orderId}`;
  return await retrieveFromRedis(key);
}


interface FetchOptions {
  method?: string;
  body?: any;
}

interface UserSession {
  email: string;
  firstName: string;
  lastName: string;
}

async function fetchFromBackend<T>(
  url: string,
  options: FetchOptions = {},
): Promise<T> {
  const requestOptions: RequestInit = {
    method: options.method || "GET",
    headers: {
      "Content-Type": "application/json",
      "X-Api-Key": process.env.BACKEND_API_KEY as string,
    },
    body: options.body ? JSON.stringify(options.body) : undefined,
  };

  const response = await fetch(url, requestOptions);

  if (!response.ok) {
    const errorText = await response.text();

    throw new Error(
      `Network response was not ok: ${response.status} - ${response.statusText}. Details: ${errorText}`,
    );
  }

  return response.json();
}

async function getSessionUserDetails(): Promise<UserSession | null | any> {
  const session = await auth();

  if (session && session.user) {
    return {
      id: session.user.id,
      email: session.user.email,
      firstName: session.user.firstname || "First Name", // Default if not available
      lastName: session.user.lastname || "Last Name", // Default if not available
    };
  }

  return null;
}

export async function getAvailableExams(type?: string): Promise<any | boolean> {
  const userSession = await getSessionUserDetails();

  if (!userSession) return;

  const examType = type ?? "EXAM"; // Set default to "EXAM" if type is undefined
  console.log(examType)

  try {
    const url = `${process.env.BACKEND_BASE_URL}/v2/available-exams/${userSession.email}?pageSize=2000&page=1&examType=${examType}`;
    const data = await fetchFromBackend<any>(url);

    return data.data;
  } catch (error: any) {
    if (error.status === 400 || (error.message && error.message.includes("400"))) {
      // console.log(`RESPONSE CODE: 400, User is not registered`);
      return false;
    }
    console.error(`There is an error occurred: ${JSON.stringify(error)}`);
    throw error;
  }
}

export async function getExamBundles(): Promise<any | boolean> {
  try {
    const userSession = await getSessionUserDetails();
    
    if (!userSession || !userSession.email) {
      console.log("User session or email not found");
      return false;
    }
    
    console.log("Fetching bundles with email:", userSession.email);
    const url = `${process.env.BACKEND_BASE_URL}/v0/exam-bundles?email=${encodeURIComponent(userSession.email)}`;
    const data = await fetchFromBackend<any>(url);

    return data;
  } catch (error: any) {
    if (error.status === 400 || (error.message && error.message.includes("400"))) {
      // console.log(`RESPONSE CODE: 400, User is not registered`);
      return false;
    }
    console.error(`There is an error occurred: ${JSON.stringify(error)}`);
    throw error;
  }
}

export async function getExamBundlesPurchased(): Promise<any | boolean> {
  try {
    const userId = await getUserId()
    const url = `${process.env.BACKEND_BASE_URL}/v0/exam-bundles/purchased-bundles/${userId}`;
    const data = await fetchFromBackend<any>(url);

    return data;
  } catch (error: any) {
    if (error.status === 400 || (error.message && error.message.includes("400"))) {
      // console.log(`RESPONSE CODE: 400, User is not registered`);
      return false;
    }
    console.error(`There is an error occurred: ${JSON.stringify(error)}`);
    throw error;
  }
}

interface BundleExam {
  id: string;
  bundleId: string;
  examId: string;
}

interface BundleDetails {
  bundle: any; // Use your Bundle type here
  exams: BundleExam[];
}

export async function getBundleDetails(bundleId: string): Promise<{ bundle: any; examCount: number } | null> {
  try {
    const userSession = await getSessionUserDetails();
    
    if (!userSession || !userSession.email) {
      console.log("User session or email not found");
      // Continue without email, but free access won't be checked
    }
    
    // Add email parameter to check for daerah 3T access
    const url = `${process.env.BACKEND_BASE_URL}/v0/exam-bundles/${bundleId}${userSession?.email ? `?email=${encodeURIComponent(userSession.email)}` : ''}`;
    
    console.log(`Fetching bundle details with URL: ${url}`);
    
    const data: BundleDetails = await fetchFromBackend<any>(url);
    
    // Log if the bundle has free access for debugging
    if (data.bundle && data.bundle.isFreeAccess) {
      console.log(`Bundle ${bundleId} has free access for user ${userSession?.email}`);
    }
    
    // Return the bundle and the exam count
    return {
      bundle: data.bundle,
      examCount: data.exams.length
    };
  } catch (error) {
    console.error('Error fetching bundle details:', error);
    return null;
  }
}

export async function getBundleDetailsPurchased(bundleId: string): Promise<{ bundle: any; examCount: number } | null> {
  try {
    const userId = await getUserId()
    const url = `${process.env.BACKEND_BASE_URL}/v0/exam-bundles/purchased-bundles/${userId}/${bundleId}`;

    const data: BundleDetails = await fetchFromBackend<any>(url);
    // Return the bundle and the exam count
    return {
      bundle: data.bundle,
      examCount: data.exams.length
    };
  } catch (error) {
    console.error('Error fetching bundle details:', error);
    return null;
  }
}

export async function orderExams(
  exam: AvailableExamsType, 
  referralCode?: string, 
  isBundle: boolean = false,
  bundleId?: string
): Promise<any> {
  const userSession = await getSessionUserDetails();
  if (!userSession) return;

  const userId = await getUserId();
  const userEmail = userSession.email;

  let orderId: string | null = null;
  let invoiceId: string | null = null;
  let paymentId: string | null = null;

  try {
    // New Step: Check if exam is free via daerah 3T program
    let isFreeViaDaerah3T = false;
    let finalAmount = 0;
    let originalAmount = 0;
    let discountAmount = 0;
    
    if (!isBundle) {
      // Only check for individual exams, not bundles
      const daerah3TUrl = `${process.env.BACKEND_BASE_URL}/v1/available-exams/daerah3t/${userEmail}/${exam.id}`;
      try {
        const daerah3TResponse = await fetchFromBackend<any>(daerah3TUrl, { method: "GET" });
        
        if (daerah3TResponse && daerah3TResponse.freeAccess === true) {
          isFreeViaDaerah3T = true;
          console.log(`Exam ${exam.id} is free via daerah 3T program for user ${userEmail}`);
          // If it's free via daerah 3T, it will have baseline_price = 0
          originalAmount = 0;
          finalAmount = 0;
        }
      } catch (error) {
        // If the API returns an error (e.g., 403 Forbidden), the user doesn't have free access
        console.log(`User doesn't have daerah 3T free access to exam ${exam.id}`);
        // Continue with normal flow - don't throw the error
      }
    }

    // Step 1: Generate Order - different for exam vs bundle
    let orderResponse;
    if (isBundle && bundleId) {
      // Generate order with bundleId
      orderResponse = await generateOrder(exam.id, bundleId);
    } else {
      // Regular exam order
      orderResponse = await generateOrder(exam.id);
    }

    if (!orderResponse.data || !orderResponse.data.id)
      throw new Error("Failed to generate order.");
    orderId = orderResponse.data.id as string;

    // Step 2: Generate Invoice
    const invoiceResponse = await generateInvoice(orderId);
    if (!invoiceResponse.data || !invoiceResponse.data.id)
      throw new Error("Failed to generate invoice.");
    invoiceId = invoiceResponse.data.id;

    // Step pre-3: Get price information if not already determined
    if (!isFreeViaDaerah3T) {
      if (isBundle && bundleId) {
        // For bundles, get bundle details to determine the price
        const bundleDetails = await getBundleDetails(bundleId);
        if (!bundleDetails || !bundleDetails.bundle) {
          throw new Error("Failed to fetch bundle details.");
        }
        
        originalAmount = bundleDetails.bundle.price;
        finalAmount = originalAmount;
        
        // Apply bundle discount if available
        if (bundleDetails.bundle.discountPercentage && 
            bundleDetails.bundle.discountPercentage.Valid) {
          const discountPct = bundleDetails.bundle.discountPercentage.Int32;
          discountAmount = finalAmount * (discountPct / 100);
          finalAmount = finalAmount - discountAmount;
        }
      } else {
        // For regular exams, use the exam price from backend (not client data)
        const AEurl = `${process.env.BACKEND_BASE_URL}/v1/available-exams/${exam.id}`;
        const availableExamResponse = await fetchFromBackend<any>(AEurl, { method: "GET" });
        
        if (!availableExamResponse.data) {
          throw new Error("Failed to fetch exam price information.");
        }
        
        originalAmount = availableExamResponse.data.baseline_price;
        finalAmount = originalAmount;
      }
    }

    // Step 3: If item is free (either by price or daerah 3T), mark as completed and return
    if (finalAmount === 0 || isFreeViaDaerah3T) {
      const url = `${process.env.BACKEND_BASE_URL}/v1/orders/${orderId}`;
      await fetchFromBackend<any>(url, {
        method: "PUT",
        body: { status: "COMPLETED" }
      });
      
      let successMessage = isBundle 
        ? "Bundle acquired successfully" 
        : "Exam ticket acquired successfully";
      
      if (isFreeViaDaerah3T) {
        successMessage += " (free via Daerah 3T program)";
      }
      
      return { 
        ok: true, 
        message: successMessage,
        isFreeViaDaerah3T
      };
    }

    // Step 4: Create pending payment record with original amount first
    if (invoiceId) {
      const paymentResponse = await createPendingPayment(
        invoiceId,
        finalAmount
      );
      paymentId = paymentResponse.data.id;
    } else {
      throw new Error("Invoice ID is null when creating pending payment");
    }

    // Step 5: If referral code exists, validate and redeem it
    if (referralCode && paymentId) {
      // First validate the referral code
      const validateUrl = `${process.env.BACKEND_BASE_URL}/v0/referral/validate/${referralCode}?type=PURCHASE&userId=${userId}`;
      const validation = await fetchFromBackend<any>(validateUrl);
      
      if (validation.isValid) {
        // Calculate the discount based on referral configuration on the ORIGINAL amount
        const refDiscountAmount = originalAmount * (validation.refereeRewardAmount / 100);
        discountAmount += refDiscountAmount;
        finalAmount = originalAmount - discountAmount; // Recalculate final amount with all discounts
      } else {
        throw new Error("Failed to validate referral code");
      }
    }

    console.log(finalAmount)
    // Step 6: Process Payment and get Snap Token with final amount
    const midtransResponse = await processTokenMidtrans(
      orderId,
      finalAmount, // Use discounted amount for Midtrans
      userSession
    );

    return {
      ok: true,
      token: midtransResponse.token,
      orderId,
      examId: exam.id,
      bundleId: isBundle ? bundleId : undefined,
      invoiceId,
      paymentId,
      originalAmount: originalAmount,
      discountAmount,
      finalAmount,
      refCode: referralCode,
      isFreeViaDaerah3T
    };

  } catch (error) {
    console.error(`An error occurred:`, error);

    // Rollback: Delete payment, invoice, and order if they exist
    if (paymentId) {
      try {
        const deletePaymentUrl = `${process.env.BACKEND_BASE_URL}/v1/payments/${paymentId}`;
        await fetchFromBackend<any>(deletePaymentUrl, { method: "DELETE" });
        console.log(`Payment ${paymentId} deleted for rollback.`);
      } catch (rollbackPaymentError) {
        console.error(
          `Failed to rollback payment ${paymentId}:`,
          rollbackPaymentError
        );
      }
    }

    if (invoiceId) {
      try {
        const deleteInvoiceUrl = `${process.env.BACKEND_BASE_URL}/v1/invoices/${invoiceId}`;
        await fetchFromBackend<any>(deleteInvoiceUrl, { method: "DELETE" });
        console.log(`Invoice ${invoiceId} deleted for rollback.`);
      } catch (rollbackInvoiceError) {
        console.error(
          `Failed to rollback invoice ${invoiceId}:`,
          rollbackInvoiceError
        );
      }
    }

    if (orderId) {
      try {
        const deleteOrderUrl = `${process.env.BACKEND_BASE_URL}/v1/orders/${orderId}`;
        await fetchFromBackend<any>(deleteOrderUrl, { method: "DELETE" });
        console.log(`Order ${orderId} deleted for rollback.`);
      } catch (rollbackOrderError) {
        console.error(
          `Failed to rollback order ${orderId}:`,
          rollbackOrderError
        );
      }
    }

    throw error;
  }
}

export interface RedeemDiscountResult {
  success: boolean;
  message: string;
  error?: string;
}

export async function finalRedeemDiscount(
  paymentId: string, 
  referralCode: string
): Promise<RedeemDiscountResult> {
  try {
    // Input validation
    if (!paymentId || !referralCode) {
      return {
        success: false,
        message: "Missing required parameters",
        error: "Payment ID and referral code are required"
      };
    }

    const userId = await getUserId();
    if (!userId) {
      return {
        success: false,
        message: "User not found",
        error: "Unable to identify user"
      };
    }

    // Get validation first
    const validateUrl = `${process.env.BACKEND_BASE_URL}/v0/referral/validate/${referralCode}?type=PURCHASE&userId=${userId}`;
    const validation = await fetchFromBackend<any>(validateUrl);

    if (!validation.isValid) {
      return {
        success: false,
        message: "Invalid referral code",
        error: validation.errorMessage || "The referral code is invalid or expired"
      };
    }

    // Redeem the referral code with proper payload structure
    const redeemUrl = `${process.env.BACKEND_BASE_URL}/v0/referral/redeem/${referralCode}`;
    
    // For PURCHASE type, only send refereeId, type, and paymentId
    const payload = {
      refereeId: userId,
      type: "PURCHASE",
      paymentId
    };
    
    const response = await fetchFromBackend<any>(redeemUrl, {
      method: "POST",
      body: payload
    });

    // Check if the response has error field
    if (response.error) {
      return {
        success: false,
        message: "Failed to redeem referral code",
        error: response.error
      };
    }

    return {
      success: true,
      message: "Referral code redeemed successfully"
    };

  } catch (error: any) {
    console.error("Error in finalRedeemDiscount:", error);
    
    // Extract error message from response if available
    const errorMessage = error.message || "An unexpected error occurred";
    
    if (errorMessage.includes("400")) {
      return {
        success: false,
        message: "Invalid request",
        error: "Please check your referral code and try again"
      };
    }

    if (errorMessage.includes("404")) {
      return {
        success: false,
        message: "Referral code not found",
        error: "The referral code is invalid"
      };
    }

    if (errorMessage.includes("already used")) {
      return {
        success: false,
        message: "Referral code already used",
        error: "This referral code has already been redeemed"
      };
    }

    return {
      success: false,
      message: "Failed to process referral code",
      error: errorMessage
    };
  }
}

interface ValidationResponse {
  isValid: boolean;
  referrerId?: string;
  expiresAt?: string;
  refereeRewardAmount?: number;
  errorMessage?: string;
}

export async function validateReferralCode(code: string): Promise<ValidationResponse> {
  if (!code) {
    return {
      isValid: false,
      errorMessage: "Kode referral tidak boleh kosong"
    };
  }

  const userId = await getUserId()

  try {
    const validateUrl = `${process.env.BACKEND_BASE_URL}/v0/referral/validate/${code}?type=PURCHASE&userId=${userId}`;
    const response = await fetchFromBackend<any>(validateUrl);

    // If validation is successful, return the validation details
    if (response.isValid) {
      return {
        isValid: true,
        referrerId: response.referrerId,
        expiresAt: response.expiresAt,
        refereeRewardAmount: response.refereeRewardAmount
      };
    }

    // If validation fails, return error details
    return {
      isValid: false,
      errorMessage: response.errorMessage || "Kode referral tidak valid"
    };

  } catch (error: any) {
    console.error("Error validating referral code:", error);
    
    // Handle specific error cases
    if (error.message?.includes("404")) {
      return {
        isValid: false,
        errorMessage: "Kode referral tidak ditemukan"
      };
    }

    if (error.message?.includes("already used")) {
      return {
        isValid: false,
        errorMessage: "Kode referral sudah digunakan"
      };
    }

    // Default error response
    return {
      isValid: false,
      errorMessage: "Gagal memvalidasi kode referral. Silakan coba lagi."
    };
  }
}

async function trackVisit(code: string, type: 'CLICK' | 'IMPRESSION') {
  try {
      const headersList = await headers();
      // Extract tracking data from headers
      const trackingData = {
          type,
          ipAddress: headersList.get('x-forwarded-for') || headersList.get('x-real-ip'),
          userAgent: headersList.get('user-agent'),
          refererUrl: headersList.get('referer')
      };

      await fetch(`${process.env.BACKEND_BASE_URL}/v0/referral/track/${code}`, {
          method: 'POST',
          headers: {
              'Content-Type': 'application/json',
              "X-Api-Key": process.env.BACKEND_API_KEY as string,
          },
          body: JSON.stringify(trackingData),
      });
  } catch (error) {
      // Log but don't throw - tracking shouldn't break the main flow
      console.error('Error tracking visit:', error);
  }
}

export async function validateAndTrackReferralCode(code: string) {
  try {
      // Track the impression first
      await trackVisit(code, 'IMPRESSION');
      
      // Then validate the code
      const validationResult = await validateReferralCode(code);
      
      return validationResult;
  } catch (error) {
      // If validation fails, we still want to keep the impression tracking
      // So we only re-throw the validation error
      console.error('Error in validateAndTrackReferralCode:', error);
      throw error;
  }
}

async function createPendingPayment(invoiceId: string, amount: number): Promise<any> {
  const url = `${process.env.BACKEND_BASE_URL}/v1/payments`;
  const payload = {
    invoice_id: invoiceId,
    payment_method: "",
    amount: amount,
    status: "PENDING"
  };

  return fetchFromBackend<any>(url, { method: "POST", body: payload });
}

export async function updatePaymentStatus(
  orderId: string, 
  invoiceId: string, 
  paymentId: string, 
  invoiceStatus: string, 
  orderStatus: string, 
  paymentStatus: string,
  paymentMethod: string,
  transactionId: string,
  transactionTime: string
): Promise<void> {
  console.log(paymentMethod);

  console.log(transactionTime)
  // Format the transaction time
  const formattedTransactionTime = formatTransactionTime(transactionTime);
  console.log(formattedTransactionTime)

  // Update Invoice
  const invoiceUrl = `${process.env.BACKEND_BASE_URL}/v1/invoices/${invoiceId}`;
  const invoicePayload = {
    status: invoiceStatus
  };
  await fetchFromBackend<any>(invoiceUrl, { method: "PUT", body: invoicePayload });

  // Update Order
  const orderUrl = `${process.env.BACKEND_BASE_URL}/v1/orders/${orderId}`;
  const orderPayload = {
    status: orderStatus
  };
  await fetchFromBackend<any>(orderUrl, { method: "PUT", body: orderPayload });

  // Update Payment
  const paymentUrl = `${process.env.BACKEND_BASE_URL}/v1/payments/${paymentId}`;
  const paymentPayload = {
    status: paymentStatus,
    payment_method: paymentMethod,
    transaction_id: transactionId,
    payment_start_date: formattedTransactionTime,
    payment_end_date: formattedTransactionTime // Assuming the end date is the same as the transaction time for successful payments
  };
  await fetchFromBackend<any>(paymentUrl, { method: "PUT", body: paymentPayload });
  
  // If payment is successful, check for REGISTRATION referrals to update
  if (paymentStatus === "PAID") {
    try {
      // Check if the user has an unredeemed REGISTRATION referral that needs to be updated
      const userId = await getUserId();
      if (userId) {
        // Call the new endpoint to update referral redeemability
        try {
          const url = `${process.env.BACKEND_BASE_URL}/v0/referral/update-redeemability`;
          await fetchFromBackend<any>(url, {
            method: "POST",
            body: {
              refereeId: userId,
              paymentId: paymentId
            }
          });
          console.log("Successfully updated referral redeemability");
        } catch (error: any) {
          // Don't fail the payment flow if this fails - just log the error
          // This is expected to fail if user doesn't have any REGISTRATION referrals
          if (error.message?.includes("404") || error.message?.includes("No unredeemed registration referral")) {
            console.log("No unredeemed registration referral found - this is normal");
          } else if (error.message?.includes("minimum required amount")) {
            console.log("Payment amount too low for referral redeemability");
          } else {
            console.error("Error updating referral redeemability:", error);
          }
        }
      }
    } catch (error) {
      // Just log errors with referral operations - don't fail the payment flow
      console.error("Error with referral operations:", error);
    }
  }
}

function formatTransactionTime(transactionTime: string): string {
  // Check if the input already has a timezone offset
  if (transactionTime.includes('+')) {
    // Parse the date string with the timezone offset
    const date = new Date(transactionTime);

    // Ensure the date is valid
    if (isNaN(date.getTime())) {
      throw new Error('Invalid transactionTime format');
    }

    // Convert to UTC and return in ISO 8601 format
    return date.toISOString();
  } else {
    // If no timezone offset, assume it's local time and proceed as before
    const dateTimeString = transactionTime.replace(' ', 'T') + 'Z';
    const date = new Date(dateTimeString);

    if (isNaN(date.getTime())) {
      throw new Error('Invalid transactionTime format');
    }

    return date.toISOString();
  }
}

async function generateOrder(examId: string, bundleId?: string): Promise<any> {
  const userSession = await getSessionUserDetails();

  const userId = await getUserId()

  console.log(userId)
  
  if (!userSession) throw new Error("User session not found.");
  
  const url = `${process.env.BACKEND_BASE_URL}/v1/orders`;
  const payload: any = {
    client_id: userId || null,
    client_email: userSession.email, // Use email as a fallback
    exam_id: examId,
    quantity: 1,
    status: "PENDING",
    bundle_id: null
  };

  if (bundleId) {
    payload.bundle_id = bundleId;
  }

  return fetchFromBackend<any>(url, { method: "POST", body: payload });
}

export async function generateInvoice(orderId: string): Promise<any> {
  const userId = await getUserId();

  if (!userId) throw new Error("User ID is undefined or null.");

  const url = `${process.env.BACKEND_BASE_URL}/v1/invoices`;
  const date = new Date();
  const formattedDate = `${date.getFullYear()}${(date.getMonth() + 1).toString().padStart(2, "0")}${date.getDate().toString().padStart(2, "0")}`;
  const shortenedUUID = orderId.substring(0, 8).toUpperCase();
  const invoiceNumber = `${formattedDate}-${shortenedUUID}`.toUpperCase();

  const payload = {
    invoice_number: invoiceNumber,
    order_id: orderId,
    status: "PENDING",
  };

  return fetchFromBackend<any>(url, { method: "POST", body: payload });
}

interface TransactionDetails {
  order_id: string;
  gross_amount: number;
}

interface CustomerDetails {
  first_name: string;
  last_name: string;
  email: string;
}

interface PaymentRequestBody {
  transaction_details: TransactionDetails;
  credit_card: { secure: boolean };
  customer_details: CustomerDetails;
}

export async function processTokenMidtrans(
  orderId: string,
  amount: number,
  userSession: { firstName: string; lastName: string; email: string }
): Promise<any> {
  const snapUrl =
    process.env.NODE_ENV === "production"
      ? "https://app.midtrans.com/snap/v1/transactions"
      : "https://app.sandbox.midtrans.com/snap/v1/transactions";

  const secret =
    process.env.NODE_ENV === "production"
      ? process.env.MIDTRANS_SNAP_PRODUCTION
      : process.env.MIDTRANS_SNAP_SANDBOX;

  if (!secret) {
    throw new Error("Midtrans secret key is not set");
  }
  // Remove or comment out the console.log to avoid logging sensitive information
  // console.log(secret);

  const paymentBody: PaymentRequestBody = {
    transaction_details: {
      order_id: orderId,
      gross_amount: amount,
    },
    credit_card: {
      secure: true,
    },
    customer_details: {
      first_name: userSession.firstName,
      last_name: userSession.lastName,
      email: userSession.email,
    },
  };

  try {
    const response = await fetch(snapUrl, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
        Authorization: `Basic ${secret}`,
      },
      body: JSON.stringify(paymentBody),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Midtrans API Error:", errorData);
      throw new Error(JSON.stringify(errorData));
    }

    const paymentData = await response.json();
    console.log("Midtrans payment successful:", paymentData);

    return paymentData;
  } catch (error) {
    console.error("Midtrans payment error:", error);
    throw error;
  }
}
