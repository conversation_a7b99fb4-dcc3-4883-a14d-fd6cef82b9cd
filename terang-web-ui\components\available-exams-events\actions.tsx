"use server";

import { redirect } from "next/navigation";
import { createClient } from "redis";

import { ExamSession } from "../types";
import { EducationLevel, ProgramStudy } from "../types";

import { getUserId } from "@/app/lib/actions/account/actions";
import { auth } from "@/auth";
import { UserDemographicsFormData } from "@/components/shared/actions"

interface ExamQuestionData {
  passing_grade: any;
  number: number;
  question: string;
  media: { url: string; mime_type: string }[];
  options: {
    shuffle: boolean;
    values: { order: number; value: string; is_correct: boolean }[];
  };
  hints: { content: string; image_url: string }[];
  explanation: { content: string };
  insight: { content: string }[]; // Assuming each insight has a content string
}

interface ExamQuestion {
  id: string;
  exam_id: string;
  data: ExamQuestionData[];
  passing_grade: string;
}

// Placeholder for fetching all exam questions and answers including hints
export async function fetchExamQuestionsBySessionId(
  sessionId: string,
): Promise<ExamQuestion> {
  // Replace this with the actual API call to your backend
  const response = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/exam-question-hints/sessions/${sessionId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
    },
  );

  if (!response.ok) {
    const errorText = await response.text();

    console.error("Failed to fetch exam questions. Response text:", errorText);
    throw new Error("Failed to fetch exam questions.");
  }

  const result = await response.json();

  // Check if 'result.data' is a string and parse it only if needed
  const examQuestions = result;

  return examQuestions;
}

// Placeholder for fetching all exam questions and answers including hints
export async function fetchExamQuestionsBySessionIdBySubject(
  sessionId: string, subject: string
): Promise<ExamQuestion> {

  const dataToSubmit = {
    subject: subject,
  };
  // Replace this with the actual API call to your backend
  const response = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/exam-question-hints/sessions/${sessionId}/subject`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
      body: JSON.stringify(dataToSubmit),
    },
  );

  if (!response.ok) {
    const errorText = await response.text();

    console.error("Failed to fetch exam questions. Response text:", errorText);
    throw new Error("Failed to fetch exam questions.");
  }

  const result = await response.json();

  // Check if 'result.data' is a string and parse it only if needed
  const examQuestions = result;

  return examQuestions;
}

// Placeholder for inserting an exam session and retrieving the session ID
async function insertExamSession(
  examId: string,
  userId: string | boolean | null,
  type: string,
): Promise<string> {
  const currentTime = new Date().toISOString();

  const newSession: any = {
    user_id: userId,
    exam_id: examId,
    type: type, // Set the type of session as needed
    status: "ACTIVE", // Set the initial status
    start_time: currentTime,
    end_time: null,
    answers: "[{}]", // Initialize with an empty object for answers
    flagged_questions: "[{}]", // Initialize with an empty object for answers
  };

  // Replace this with the actual API call to your backend
  const response = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/exam-sessions`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
      body: JSON.stringify(newSession),
    },
  );

  if (!response.ok) {
    // Print the response text for debugging
    const errorText = await response.text();

    console.error("Failed to create exam session. Response text:", errorText);
    throw new Error("Failed to create exam session.");
  }

  const data = await response.json();

  console.log(data);

  return data.data.session_id;
}

export async function fetchLastExamSession(
  examId: string
): Promise<ExamSession | null> {
  const userId = await getUserId();
  const response = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/exam-sessions/last/${examId}/${userId}/exam`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
    },
  );

  if (!response.ok) {
    if (response.status === 404) {
      return null; // No session found
    }
    const errorText = await response.text();

    console.error(
      "Failed to fetch last exam session. Response text:",
      errorText,
    );
    throw new Error("Failed to fetch last exam session.");
  }

  const data = await response.json();

  return data.data;
}
interface AvailableExam {
  id: string;
  name: string;
  subname: string;
  description: string;
  baseline_price: number;
  visibility: string;
  duration: string;
  created_at: string;
  modified_at: string;
  user_id: string;
}

async function fetchAvailableExams(
  examId: string,
): Promise<AvailableExam | null> {
  const response = await fetch(
    `${process.env.BACKEND_BASE_URL}/v1/available-exams/${examId}`,
    {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
    },
  );

  if (!response.ok) {
    if (response.status === 404) {
      return null; // No session found
    }
    const errorText = await response.text();

    console.error(
      "Failed to fetch last exam session. Response text:",
      errorText,
    );
    throw new Error("Failed to fetch last exam session.");
  }

  const data = await response.json();

  return data.data;
}
function parseDuration(durationString: string): number {
  const [hours, minutes, seconds] = durationString.split(":").map(Number);

  return hours * 3600 + minutes * 60 + seconds;
}

export async function examSession(data: FormData) {
  try {
    const examId = data.get("id") as string;

    if (!examId) {
      throw new Error("Exam ID is required.");
    }

    const userId = await getUserId();

    if (!userId) throw new Error("User ID is undefined or null.");

    let sessionId: string;

    // Fetch the available exam details
    const availableExam = await fetchAvailableExams(examId);

    if (!availableExam) {
      throw new Error("Exam not found.");
    }

    const allowedDuration = parseDuration(availableExam.duration as string);

    // Check if there's an existing exam session
    const lastSession = await fetchLastExamSession(examId);

    console.log(lastSession);

    if (lastSession && lastSession.status === "ACTIVE") {
      // Parse the session duration
      const sessionDuration = parseDuration(lastSession.elapsed_duration);

      if (sessionDuration <= allowedDuration) {
        // If there's an active session and it hasn't exceeded the allowed duration, use that
        sessionId = lastSession.session_id;
      } else {
        // If the active session has exceeded the allowed duration, create a new one
        console.log("last session is exceeded the allowed duration");
        sessionId = await insertExamSession(examId, userId, "EXAM");
        await createGamification(sessionId)
      }
    } else {
      // If no session exists or the last session is COMPLETED or ABANDONED, create a new one
      console.log("last session is completed/abandoned");
      sessionId = await insertExamSession(examId, userId, "EXAM");
      await createGamification(sessionId)
    }

    // Redirect to the exam page with the session ID
    redirect(`/exam/${sessionId}`);
  } catch (error) {
    if ((error as any)?.digest?.startsWith("NEXT_REDIRECT")) {
      // This is expected, rethrow
      throw error;
    }
  
    console.error("Error in examSession:", {
      error,
      stack: (error as Error).stack,
      examId: data.get("id")
    });
    
    // Immediately redirect on error without delay
    redirect("/error?message=Failed to create or retrieve exam session");
  }
}


export async function examTrialSession(data: FormData) {
  console.log("dummy")
  return "dummy"
}

const createWriteClient = () => createClient({
  url: process.env.REDIS_URL_WRITE as string,
});

const createReadClient = () => createClient({
  url: process.env.REDIS_URL_READ as string,
});

export async function storeToRedis(key: string, value: any) {
  const redis = createWriteClient();

  try {
    await redis.connect();
    await redis.set(key, JSON.stringify(value), {
      EX: 21600, // Set expiration to 6 hours (21600 seconds)
    });
  } catch (error) {
    console.error("Failed to store data in Redis:", error);
  } finally {
    await redis.disconnect();
  }
}

export async function retrieveFromRedis(key: string): Promise<any | null> {
  const redis = createReadClient();

  try {
    await redis.connect();
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error("Failed to retrieve data from Redis:", error);
    return null;
  } finally {
    await redis.disconnect();
  }
}

// config.ts
const isProduction = process.env.NODE_ENV === "production";

const REGION_SERVICE_URL = isProduction
  ? "http://region-service.default.svc.cluster.local"
  : "http://localhost:8080";

interface DemographicsData {
  id: string;
  nama: string;
}

export async function fetchRegionData(
  endpoint: string,
  params: Record<string, string> = {},
): Promise<DemographicsData[]> {
  const url = new URL(`${REGION_SERVICE_URL}${endpoint}?page=1&limit=10000000`);

  const response = await fetch(url.toString());

  if (!response.ok) {
    throw new Error("Network response was not ok");
  }

  return response.json();
}

export async function fetchProvinces(): Promise<DemographicsData[]> {
  return fetchRegionData("/provinsi");
}

export async function fetchCities(
  provinceId: string,
): Promise<DemographicsData[]> {
  return fetchRegionData(`/provinsi/${provinceId}/kota`);
}

export async function fetchDistricts(
  cityId: string,
): Promise<DemographicsData[]> {
  return fetchRegionData(`/kota/${cityId}/kecamatan`);
}

export async function fetchVillages(
  districtId: string,
): Promise<DemographicsData[]> {
  return fetchRegionData(`/kecamatan/${districtId}/kelurahan`);
}

export async function fetchAllCities(): Promise<DemographicsData[]> {
  return fetchRegionData("/kota");
}

export async function fetchAllDistricts(): Promise<DemographicsData[]> {
  return fetchRegionData("/kecamatan");
}

export async function fetchAllVillages(): Promise<DemographicsData[]> {
  return fetchRegionData("/kelurahan");
}

export async function fetchProvinceDetails(
  provinceId: string,
): Promise<DemographicsData> {
  return fetchRegionData(`/provinsi/${provinceId}`).then((data) => data[0]);
}

export async function fetchCityDetails(
  cityId: string,
): Promise<DemographicsData> {
  return fetchRegionData(`/kota/${cityId}`).then((data) => data[0]);
}

export async function fetchDistrictDetails(
  districtId: string,
): Promise<DemographicsData> {
  return fetchRegionData(`/kecamatan/${districtId}`).then((data) => data[0]);
}

export async function fetchVillageDetails(
  villageId: string,
): Promise<DemographicsData> {
  return fetchRegionData(`/kelurahan/${villageId}`).then((data) => data[0]);
}

const educationLevels: EducationLevel[] = [
  { id: 5, name: "SD" },
  { id: 10, name: "SLTP" },
  { id: 15, name: "SLTA" },
  { id: 17, name: "SMK/SLTA Kejuruan" },
  { id: 18, name: "SLTA Keguruan" },
  { id: 20, name: "Diploma I" },
  { id: 25, name: "Diploma II" },
  { id: 30, name: "Diploma III/Sarjana Muda" },
  { id: 35, name: "Diploma IV" },
  { id: 40, name: "S-1/Sarjana" },
  { id: 45, name: "S-2" },
  { id: 50, name: "S-3/Doktor" },
];

export async function getEducationLevels(): Promise<EducationLevel[]> {
  const cacheKey = "education_levels";
  const cachedLevels = await retrieveFromRedis(cacheKey);

  if (cachedLevels) {
    return cachedLevels;
  }

  await storeToRedis(cacheKey, educationLevels);

  return educationLevels;
}


// SSCBKN API FOOOOOORRRRR

export interface Formation {
  formasi_id: string;
  ins_nm: string;
  jp_nama: string;
  formasi_nm: string;
  jabatan_nm: string;
  lokasi_nm: string;
  jumlah_formasi: number;
  disable: number;
  gaji_min: string;
  gaji_max: string;
  jumlah_ms: number;
}

interface ApiResponse<T> {
  status: number;
  error: boolean;
  message: string;
  data: {
    meta: {
      total: number;
    };
    page: {
      total: number;
    };
    data: T[];
  };
}

type ApiError = {
  message: string;
  code: 'NO_FORMATIONS' | 'API_ERROR' | 'NETWORK_ERROR';
}

// Utility function to handle API errors
function handleApiError(response: Response): void {
  if (!response.ok) {
    throw {
      message: `API responded with status ${response.status}`,
      code: 'API_ERROR'
    } as ApiError;
  }
}

// Function to check if response has data
function hasData<T>(response: ApiResponse<T>): boolean {
  return response.data.meta.total > 0 && Array.isArray(response.data.data) && response.data.data.length > 0;
}

// Cache keys
const CACHE_KEYS = {
  programStudies: (educationLevelId: string, searchTerm: string = "") => 
    `program_studies:${educationLevelId}:${searchTerm}`,
  formations: (programStudyCode: string) => 
    `formations:${programStudyCode}`,
  formationExists: (programStudyCode: string) => 
    `formation_exists:${programStudyCode}`,
};

// Cache durations (in seconds)
const CACHE_DURATION = {
  PROGRAM_STUDIES: 21600, // 6 hours
  FORMATIONS: 21600,      // 6 hours
  FORMATION_EXISTS: 21600 // 6 hours
};

// Function to fetch program studies with Redis caching
export async function fetchProgramStudies(
  educationLevelId: string,
  searchTerm: string = "",
): Promise<ProgramStudy[]> {
  const cacheKey = CACHE_KEYS.programStudies(educationLevelId, searchTerm);
  
  try {
    // Try to get from cache first
    const cachedData = await retrieveFromRedis(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // If not in cache, fetch from API
    const response = await fetch(
      `https://api-sscasn.bkn.go.id/2024/referensi/pendidikan?tingkat=${educationLevelId}&nama=${searchTerm}&limit=2500`,
      {
        headers: {
          "Host": "api-sscasn.bkn.go.id",
          "Referer": "https://sscasn.bkn.go.id/",
          "Origin": "https://sscasn.bkn.go.id",
          "User-Agent": "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.110 Mobile Safari/537.36"
        }
      }
    );

    handleApiError(response);
    const data: ApiResponse<ProgramStudy> = await response.json();
    const programStudies = data.data.data;

    // Store in cache
    await storeToRedis(cacheKey, programStudies);

    return programStudies;
  } catch (error) {
    console.error("Error fetching program studies:", error);
    throw error;
  }
}

// Function to fetch formations by program study code with Redis caching
export async function fetchFormations(
  programStudyCode: string,
  throwOnEmpty: boolean = false
): Promise<Formation[]> {
  const cacheKey = CACHE_KEYS.formations(programStudyCode);

  try {
    // Try to get from cache first
    const cachedData = await retrieveFromRedis(cacheKey);
    if (cachedData) {
      if (cachedData.length === 0 && throwOnEmpty) {
        throw {
          message: `No formations found for program study code: ${programStudyCode}`,
          code: 'NO_FORMATIONS'
        } as ApiError;
      }
      return cachedData;
    }

    // If not in cache, fetch from API
    const response = await fetch(
      `https://api-sscasn.bkn.go.id/2024/portal/spf?kode_ref_pend=${programStudyCode}&offset=0&pengadaan_kd=2`,
      {
        headers: {
          "accept": "application/json, text/plain, */*",
          "user-agent": "Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.110 Mobile Safari/537.36",
          "host": "api-sscasn.bkn.go.id",
          "origin": "https://sscasn.bkn.go.id",
          "referer": "https://sscasn.bkn.go.id/"
        }
      }
    );

    handleApiError(response);
    const data: ApiResponse<Formation> = await response.json();

    if (!hasData(data) && throwOnEmpty) {
      throw {
        message: `No formations found for program study code: ${programStudyCode}`,
        code: 'NO_FORMATIONS'
      } as ApiError;
    }

    const formations = data.data.data;

    // Store in cache
    await storeToRedis(cacheKey, formations);

    return formations;
  } catch (error) {
    console.error("Error fetching formations:", error);
    throw error;
  }
}

// Function to check if formations exist with Redis caching
export async function checkFormationExists(programStudyCode: string): Promise<boolean> {
  const cacheKey = CACHE_KEYS.formationExists(programStudyCode);

  try {
    // Try to get from cache first
    const cachedResult = await retrieveFromRedis(cacheKey);
    if (cachedResult !== null) {
      return cachedResult;
    }

    // If not in cache, check formations
    const formations = await fetchFormations(programStudyCode);
    const exists = formations.length > 0;

    // Store result in cache
    await storeToRedis(cacheKey, exists);

    return exists;
  } catch (error) {
    if ((error as ApiError).code === 'NO_FORMATIONS') {
      return false;
    }
    throw error;
  }
}

// Function to fetch all available formations with Redis caching
export async function fetchAllAvailableFormations(
  programStudyCodes: string[]
): Promise<Map<string, Formation[]>> {
  const results = new Map<string, Formation[]>();
  
  await Promise.all(
    programStudyCodes.map(async (code) => {
      try {
        const formations = await fetchFormations(code);
        if (formations.length > 0) {
          results.set(code, formations);
        }
      } catch (error) {
        console.warn(`Failed to fetch formations for code ${code}:`, error);
      }
    })
  );

  return results;
}

export async function submitUserDemographics(
  formData: UserDemographicsFormData,
): Promise<{ success: boolean; message: string; data?: any }> {
  const maxRetries = 3;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      const session = await auth();

      if (!session || !session.user?.email) {
        throw new Error("User is not authenticated or email is missing");
      }

      // Ensure studyBudget is an integer
      const dataToSubmit = {
        ...formData,
        email: session.user.email,
        studyBudget: parseInt(formData.studyBudget.toString(), 10), // Ensure integer conversion
      };

      // Validate studyBudget
      if (isNaN(dataToSubmit.studyBudget)) {
        throw new Error("Invalid study budget value");
      }

      console.log("Data to submit:", JSON.stringify(dataToSubmit, null, 2));

      if (!process.env.BACKEND_BASE_URL) {
        throw new Error("BACKEND_BASE_URL is not defined");
      }

      if (!process.env.BACKEND_API_KEY) {
        throw new Error("BACKEND_API_KEY is not defined");
      }

      const response = await fetch(
        `${process.env.BACKEND_BASE_URL}/v0/surveys`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Api-Key": process.env.BACKEND_API_KEY as string,
          },
          body: JSON.stringify(dataToSubmit),
        },
      );

      if (!response.ok) {
        const errorBody = await response.text();

        console.error(
          "Response not OK. Status:",
          response.status,
          "Body:",
          errorBody,
        );
        throw new Error(
          `Failed to submit user demographics: ${response.status} ${response.statusText}`,
        );
      }

      const data = await response.json();

      console.log("User demographics submitted successfully", data);

      return {
        success: true,
        message: "User demographics submitted successfully",
        data: data,
      };
    } catch (error) {
      console.error(
        `Error submitting user demographics (attempt ${retries + 1}):`,
        error,
      );
      if (error instanceof Error) {
        console.error("Error message:", error.message);
        console.error("Error stack:", error.stack);
      }

      retries++;
      if (retries < maxRetries) {
        console.log(`Retrying... (${maxRetries - retries} attempts left)`);
        await new Promise((resolve) => setTimeout(resolve, 1000 * retries)); // Exponential backoff
      } else {
        return {
          success: false,
          message: "Failed to submit user demographics after multiple attempts",
        };
      }
    }
  }

  return {
    success: false,
    message: "Unexpected error occurred",
  };
}

export async function checkEmailRecords(): Promise<number> {
  try {
    const session = await auth();

    if (!session || !session.user?.email) {
      throw new Error("User is not authenticated or email is missing");
    }

    const email = session.user.email;

    if (!process.env.BACKEND_BASE_URL) {
      throw new Error("BACKEND_BASE_URL is not defined");
    }

    if (!process.env.BACKEND_API_KEY) {
      throw new Error("BACKEND_API_KEY is not defined");
    }

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/surveys/check-email?email=${encodeURIComponent(email)}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      },
    );

    if (!response.ok) {
      throw new Error(
        `Failed to check email records: ${response.status} ${response.statusText}`,
      );
    }

    const data = await response.json();

    return data.count;
  } catch (error) {
    console.error("Error checking email records:", error);
    throw error;
  }
}


// Add an interface for Gamification
interface Gamification {
  id: string;
  examSessionId: string;
  currentLives: number;
  hintsRemaining: { count: string };
  status: string;
  streakCount: number;
  highestStreak: number;
  elapsedTime: string; // HH:MM:SS
  totalPauseTime: string; // HH:MM:SS
  // Add other fields as necessary
}

// Function to create gamification (already exists)
async function createGamification(sessionId: string): Promise<void> {
  try {
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/gamification`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
        body: JSON.stringify({
          examSessionId: sessionId,
        }),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Failed to create gamification. Response text:", errorText);

      // Handle specific error responses if needed
      if (response.status === 400) {
        throw new Error("Invalid request to create gamification.");
      } else if (response.status === 500) {
        throw new Error("Server error when creating gamification.");
      } else {
        throw new Error(`Unexpected error: ${response.statusText}`);
      }
    }

    const data = await response.json();
    console.log("Gamification created successfully:", data);
  } catch (error) {
    console.error("Error creating gamification:", error);
    throw error;
  }
}