// app/lib/actions/analytics/actions.ts
"use server";
import {auth} from '@/auth';

export interface AnalyticsEvent {
  id: string;                    // Added this
  userEmail?: string;
  eventType: string;
  eventTimestamp: string;        // Added this
  sessionId?: string;
  durationSeconds?: number;
  path?: string;
  deviceInfo?: Record<string, any>;
  locationInfo?: Record<string, any>;
  eventMetadata?: Record<string, any>;
  status?: string;
}

export interface AnalyticsSummary {
  totalEvents: number;
  uniqueUsers: number;
  uniqueSessions: number;
  avgDuration: number;
  errorCount: number;
}

interface BaseResponse {
  success: boolean;
  error?: string;
}

export interface TrackEventResponse extends BaseResponse {
  data?: {
    event: AnalyticsEvent;
    message: string;
  };
}

export interface GetSummaryResponse extends BaseResponse {
  data?: {
    summary: AnalyticsSummary;
  };
}

export interface GetEventsResponse extends BaseResponse {
  data?: {
    events: AnalyticsEvent[];
  };
}

/**
 * Gets analytics summary using server action
 * @param timeframe Time period for summary (24h, 7d, 30d)
 * @param eventType Optional event type filter
 * @returns Promise containing the response with summary data or error
 */
export async function getAnalyticsSummary(
  timeframe: string = "24h",
  eventType?: string
): Promise<GetSummaryResponse> {
  try {
    if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
      throw new Error("Missing required environment variables");
    }

    const params = new URLSearchParams();
    if (timeframe) params.append("timeframe", timeframe);
    if (eventType) params.append("eventType", eventType);

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/analytics/summary?${params.toString()}`,
      {
        headers: {
          "Content-Type": "application/json",
          "X-API-KEY": process.env.BACKEND_API_KEY,
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.message ||
          `Failed to fetch summary: ${response.status} ${response.statusText}`
      );
    }

    const result = await response.json();

    return {
      success: true,
      data: {
        summary: result.summary,
      },
    };
  } catch (error) {
    console.error("Analytics summary fetch error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}

/**
 * Gets user's analytics events using server action
 * @param userEmail User email to fetch events for
 * @param options Optional parameters (limit, offset, eventType)
 * @returns Promise containing the response with events data or error
 */
export async function getUserEvents(
  userEmail: string,
  options: {
    limit?: number;
    offset?: number;
    eventType?: string;
  } = {}
): Promise<GetEventsResponse> {
  try {
    if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
      throw new Error("Missing required environment variables");
    }

    const params = new URLSearchParams();
    if (options.limit) params.append("limit", options.limit.toString());
    if (options.offset) params.append("offset", options.offset.toString());
    if (options.eventType) params.append("eventType", options.eventType);

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/users/${userEmail}/analytics?${params.toString()}`,
      {
        headers: {
          "Content-Type": "application/json",
          "X-API-KEY": process.env.BACKEND_API_KEY,
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.message ||
          `Failed to fetch events: ${response.status} ${response.statusText}`
      );
    }

    const result = await response.json();

    return {
      success: true,
      data: {
        events: result.events,
      },
    };
  } catch (error) {
    console.error("User events fetch error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}

export interface GetAllEventsOptions {
  limit?: number;
  offset?: number;
  eventType?: string;
}

/**
 * Gets all analytics events using server action
 * @param options Optional parameters (limit, offset, eventType)
 * @returns Promise containing the response with events data or error
 */
export async function getAllEvents(
  options: GetAllEventsOptions = {}
): Promise<GetEventsResponse> {
  try {
    if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
      throw new Error("Missing required environment variables");
    }

    const params = new URLSearchParams();
    if (options.limit) params.append("limit", options.limit.toString());
    if (options.offset) params.append("offset", options.offset.toString());
    if (options.eventType) params.append("eventType", options.eventType);

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/analytics?${params.toString()}`,
      {
        headers: {
          "Content-Type": "application/json",
          "X-API-KEY": process.env.BACKEND_API_KEY,
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.message ||
          `Failed to fetch events: ${response.status} ${response.statusText}`
      );
    }

    const result = await response.json();

    return {
      success: true,
      data: {
        events: result.events,
      },
    };
  } catch (error) {
    console.error("Events fetch error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}

// app/lib/actions/analytics/actions.ts

// Add these new interfaces
export interface UserActivityMetrics {
  daily: number;
  weekly: number;
  monthly: number;
  yearToDate: number;
  activeUsers: {
    date: string;
    count: number;
  }[];
  retentionRate: number;
  growthRate: number;
}

export interface GetUserActivityResponse extends BaseResponse {
  data?: {
    metrics: UserActivityMetrics;
  };
}

// Add this new function
export async function getUserActivity(): Promise<GetUserActivityResponse> {
  try {
    if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
      throw new Error("Missing required environment variables");
    }

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/analytics/user-activity`,
      {
        headers: {
          "Content-Type": "application/json",
          "X-API-KEY": process.env.BACKEND_API_KEY,
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => null);
      throw new Error(
        errorData?.message ||
          `Failed to fetch user activity: ${response.status} ${response.statusText}`
      );
    }

    const result = await response.json();

    return {
      success: true,
      data: {
        metrics: result.metrics,
      },
    };
  } catch (error) {
    console.error("User activity fetch error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}

/**
 * Tracks an analytics event using server action
 * @param data Event data to track
 * @returns Promise containing the response with tracked event data or error
 */
export async function trackEvent(
  formData: FormData | AnalyticsEvent
): Promise<TrackEventResponse> {
try {
  if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
  throw new Error("Missing required environment variables");
  }

  const session = await auth();

  const currentTimestamp = new Date().toISOString();

  // Handle both FormData and direct object submission
  const data: AnalyticsEvent = formData instanceof FormData 
  ? {
      id: formData.get("id") as string || crypto.randomUUID(),
      userEmail: session?.user.email,
      eventType: formData.get("eventType") as string,
      eventTimestamp: formData.get("eventTimestamp") as string || currentTimestamp,
      sessionId: formData.get("sessionId") as string,
      durationSeconds: Number(formData.get("durationSeconds")),
      path: formData.get("path") as string,
      deviceInfo: JSON.parse(formData.get("deviceInfo") as string),
      locationInfo: JSON.parse(formData.get("locationInfo") as string),
      eventMetadata: JSON.parse(formData.get("eventMetadata") as string),
      status: formData.get("status") as string,
      }
  : formData; // Reference the input parameter instead of the not-yet-defined data

  // Validate required fields
  if (!data.userEmail || !data.eventType) {
  throw new Error("Missing required fields");
  }

  const response = await fetch(`${process.env.BACKEND_BASE_URL}/v0/analytics`, {
  method: "POST",
  headers: {
      "Content-Type": "application/json",
      "X-API-KEY": process.env.BACKEND_API_KEY,
  },
  body: JSON.stringify({
      ...data,
      deviceInfo: JSON.stringify(data.deviceInfo),
      locationInfo: JSON.stringify(data.locationInfo),
      eventMetadata: JSON.stringify(data.eventMetadata),
  }),
  });

  if (!response.ok) {
  const errorData = await response.json().catch(() => null);
  throw new Error(
      errorData?.message ||
      `Failed to track event: ${response.status} ${response.statusText}`
  );
  }

  const result = await response.json();
  
  return {
  success: true,
  data: {
      event: result.event,
      message: "Event tracked successfully",
  },
  };
} catch (error) {
  console.error("Event tracking error:", error);
  return {
  success: false,
  error: error instanceof Error ? error.message : "An unknown error occurred",
  };
}
}