
    // actions.ts
    "use server";

  export interface Gamification {
    id: string;
    examSessionId: string;
    currentLives: number;
    hintsRemaining: { count: string };
    status: string;
    streakCount: number;
    highestStreak: number;
    elapsedTime: string;
    totalPauseTime: string;
    question_times?: { [key: string]: number };
    endTime?: string;
    startTime?: string;
    createdAt?: string;
    modifiedAt?: string;
  }
  
  import { createClient, RedisClientType, RedisDefaultModules, RedisFunctions, RedisScripts } from "redis";
  type RedisClient = RedisClientType<RedisDefaultModules & RedisFunctions & RedisScripts>;
  
  let redisWriteClient: RedisClient | null = null;
  let redisReadClient: RedisClient | null = null;
  
  export async function getRedisWriteClient(): Promise<RedisClient> {
    if (!redisWriteClient || !redisWriteClient.isOpen) {
      redisWriteClient = createClient({
        url: process.env.REDIS_URL_WRITE,
      }) as RedisClient;
      await redisWriteClient.connect();
    }
    return redisWriteClient;
  }
  
  export async function getRedisReadClient(): Promise<RedisClient> {
    if (!redisReadClient || !redisReadClient.isOpen) {
      redisReadClient = createClient({
        url: process.env.REDIS_URL_READ,
      }) as RedisClient;
      await redisReadClient.connect();
    }
    return redisReadClient;
  }
  
  export async function storeToRedis(key: string, value: unknown): Promise<void> {
    const redis = await getRedisWriteClient();
    await redis.set(key, JSON.stringify(value), {
      EX: 21600, // 6 hours
    });
  }
  
  export async function retrieveFromRedis<T>(key: string): Promise<T | null> {
    const redis = await getRedisReadClient();
    const data = await redis.get(key);
    return data ? JSON.parse(data) as T : null;
  }
  
  export async function updateGamification(
    sessionId: string,
    updateData: Partial<Gamification>
  ): Promise<void> {
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/gamification/session/${sessionId}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
        body: JSON.stringify(updateData),
      }
    );
  
    if (!response.ok) {
      throw new Error(`Failed to update gamification: ${response.statusText}`);
    }
  }
  
  export async function updateGamificationWithRedis(
    sessionId: string,
    updateData: Partial<Gamification>
  ): Promise<void> {
    try {
      // Update Redis
      const existingData = await retrieveFromRedis<Gamification>(`${sessionId}_gamification`);
      const updatedGamification = {
        ...existingData,
        ...updateData,
        question_times: {
          ...(existingData?.question_times || {}),
          ...(updateData.question_times || {})
        }
      };
      
      await storeToRedis(`${sessionId}_gamification`, updatedGamification);
      
      // Update Backend
      await updateGamification(sessionId, updatedGamification);
    } catch (error) {
      console.error("Error updating gamification:", error);
      throw error;
    }
  }
  
  export async function fetchGamificationState(sessionId: string): Promise<Gamification> {
    const redisData = await retrieveFromRedis<Gamification>(`${sessionId}_gamification`);
    if (redisData) return redisData;
  
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/gamification/session/${sessionId}`,
      {
        headers: {
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );
  
    if (!response.ok) {
      throw new Error(`Failed to fetch gamification: ${response.statusText}`);
    }
  
    const data = await response.json();
    await storeToRedis(`${sessionId}_gamification`, data);
    return data;
  }
  
  export async function handleAnswerSubmission(
    sessionId: string,
    isCorrect: boolean,
    currentGamification: Gamification
  ): Promise<Gamification> {
    if (!isCorrect) {
      return handleIncorrectAnswer(sessionId, currentGamification);
    }
  
    const newStreakCount = currentGamification.streakCount + 1;
    const newHighestStreak = Math.max(currentGamification.highestStreak, newStreakCount);
    
    const updateData: Partial<Gamification> = {
      streakCount: newStreakCount,
      highestStreak: newHighestStreak,
    };
  
    await updateGamificationWithRedis(sessionId, updateData);
  
    return {
      ...currentGamification,
      ...updateData,
    };
  }
  
  export async function handleIncorrectAnswer(
    sessionId: string,
    currentGamification: Gamification
  ): Promise<Gamification> {
    const newLives = Math.max(0, currentGamification.currentLives - 1);
    const updateData: Partial<Gamification> = {
      currentLives: newLives,
      streakCount: 0,
    };
  
    await updateGamificationWithRedis(sessionId, updateData);
  
    return {
      ...currentGamification,
      ...updateData,
    };
  }
  
  export async function isGameOver(gamification: Gamification): Promise<boolean> {
    return gamification.currentLives <= 0;
  }