"use client";

import { useState, useCallback } from "react";

/**
 * Custom hook for managing login modal state
 * Uses React's built-in state instead of Zustand
 */
const useLoginModal = () => {
  const [isOpen, setIsOpen] = useState(false);
  
  const onOpen = useCallback(() => {
    setIsOpen(true);
  }, []);
  
  const onClose = useCallback(() => {
    setIsOpen(false);
  }, []);
  
  return {
    isOpen,
    onOpen,
    onClose
  };
};

export default useLoginModal;