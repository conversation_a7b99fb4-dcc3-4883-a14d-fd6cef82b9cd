import React, { Suspense } from "react";
import dynamic from 'next/dynamic';
import styled, { keyframes } from "styled-components";
import Loading from "../Loading";
import Image from "next/image";

const DotLottie = dynamic(() => import("@/components/shared/dotlottie-animation"), {
  loading: () => <Loading />,
  ssr: true
});

const TypeWriterText = dynamic(() => import("../TypeWriterText"), {
  loading: () => <Loading />,
  ssr: true
});

const TrustIndicator = dynamic(() => import("../TrustIndicator"),{
  loading: () => <Loading />,
  ssr: true
})

const Section = styled.section`
  min-height: ${(props) => `calc(100vh - ${props.theme.navHeight})`};
  width: 100%;
  position: relative;
  background-color: ${(props) => props.theme.body};
  overflow: hidden;
`;

const Container = styled.div`
  width: min(75%, 1200px);
  min-height: 80vh;
  margin: 0 auto;
  display: flex;
  flex-direction: column; /* Changed to column */
  justify-content: center;
  align-items: center;
  position: relative;

  @media (max-width: 64em) {
    width: 85%;
  }
  @media (max-width: 48em) {
    width: 100%;
  }
`;

// New wrapper for the two boxes
const ContentWrapper = styled.div`
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;

  @media (max-width: 48em) {
    flex-direction: column-reverse;
    & > *:first-child {
      width: 100%;
      margin-top: 4rem;
    }
  }
`;

const Box = styled.div`
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  
  @media (max-width: 64em) {
    margin-top: 0px;
  }
`;

const LottieWrapper = styled.div`
  height: clamp(250px, 50vw, 480px);
  position: relative;
  z-index: 5;
  
  @media (max-width: 64em) {
    margin-top: 20px;
    width: clamp(300px, 50vw, 480px);
    height: clamp(250px, 50vw, 480px);
  }
`;

const BackedBySection = styled.div`
  text-align: center;
  margin-top: -5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  position: relative;
  z-index: 5;
  
  @media (max-width: 64em) {
    margin-top: 0rem;
    line-height: 0.5;
    margin-bottom: 2rem;
  }
`;

const LogosContainer = styled.div`
  display: flex;
  gap: 2rem;
  justify-content: center;
  align-items: center;

  img {
    max-width: 150px;
    height: auto;
  }

  @media (max-width: 64em) {
    flex-direction: column;
    gap: 0.8rem;
  }
`;

// Animation Background Styled Components
const AnimationBackground = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
`;

// Floating animations using styled-components keyframes
const floatAnimation1 = keyframes`
  0% { transform: translate(0, 0) scale(1); }
  25% { transform: translate(5px, -5px) scale(1.05); }
  50% { transform: translate(0, 0) scale(1); }
  75% { transform: translate(-5px, 5px) scale(0.95); }
  100% { transform: translate(0, 0) scale(1); }
`;

const floatAnimation2 = keyframes`
  0% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(-5px, 5px) rotate(5deg); }
  50% { transform: translate(0, 0) rotate(0deg); }
  75% { transform: translate(5px, -5px) rotate(-5deg); }
  100% { transform: translate(0, 0) rotate(0deg); }
`;

const floatAnimation3 = keyframes`
  0% { transform: translate(0, 0); }
  33% { transform: translate(5px, 10px); }
  66% { transform: translate(-10px, -5px); }
  100% { transform: translate(0, 0); }
`;

// Styled components for the shapes with animations baked in
const Circle1 = styled.div`
  position: absolute;
  border-radius: 50%;
  opacity: 0.15;
  animation: ${floatAnimation1} 15s ease-in-out infinite;
`;

const Circle2 = styled.div`
  position: absolute;
  border-radius: 50%;
  opacity: 0.12;
  animation: ${floatAnimation2} 18s ease-in-out infinite;
`;

const Square = styled.div`
  position: absolute;
  border-radius: 10px;
  opacity: 0.1;
  transform: rotate(45deg);
  animation: ${floatAnimation3} 20s ease-in-out infinite;
`;

const Triangle = styled.div`
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0.08;
  animation: ${floatAnimation2} 17s ease-in-out infinite;
`;

// SVG Animation Component with responsive sizing
const SVGShapes = styled.div`
  position: absolute;
  opacity: 0.5;
  animation-duration: 15s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  z-index: 1;
  
  &.float1 {
    animation-name: ${floatAnimation1};
  }
  &.float2 {
    animation-name: ${floatAnimation2};
  }
  &.float3 {
    animation-name: ${floatAnimation3};
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  /* Only apply these styles on mobile */
  @media (max-width: 48em) {
    &.ielts-svg {
      width: 60px !important;
      top: 2.5% !important;
      left: 65% !important;
    }
    &.toefl-svg {
      width: 60px !important;
      top: 22% !important;
      left: 72% !important;
    }
    &.lpdp-svg {
      width: 80px !important;
      top: 30% !important;
      left: 25% !important;
    }
    &.jlpt-svg {
      width: 80px !important;
      top: 25% !important;
      left: 10% !important;
    }
    &.sscasn-svg {
      width: 85px !important;
      top: 30% !important;
      left: 58% !important;
    }
    &.utbk-svg {
      width: 90px !important;
      top: 2.5% !important;
      left: 22% !important;
    }
  }
`;

// Background Shapes Component with desktop and mobile layouts
const BackgroundShapes = () => {
  // S3 bucket path for the SVGs
  const baseUrl = "https://cdn.terang.ai/landingpage-assets/exams/";
  
  // Array of SVG files positioned to the right with larger sizes
  const svgFiles = [
    // Right side of DotLottie (more emphasis here)
    { file: "ielts.svg", width: "130px", top: "18%", left: "80%", animClass: "float1", className: "ielts-svg" },
    { file: "toefl.svg", width: "120px", top: "50%", left: "80%", animClass: "float2", delay: "-3s", className: "toefl-svg" },
    { file: "lpdp.svg", width: "140px", top: "20%", left: "50%", animClass: "float1", delay: "-5s", className: "lpdp-svg" },
    
    // Around the character
    { file: "jlpt.svg", width: "200px", top: "50%", left: "50%", animClass: "float3", delay: "-2s", className: "jlpt-svg" },
    { file: "sscasn.svg", width: "135px", top: "40%", left: "85%", animClass: "float2", delay: "-4s", className: "sscasn-svg" },
    
    // Top
    { file: "utbk-snbt.svg", width: "130px", top: "17%", left: "65%", animClass: "float3", delay: "-1s", className: "utbk-svg" },
  ];
  
  return (
    <AnimationBackground>
      {/* SVGs from S3 positioned to the right */}
      {svgFiles.map((svg, index) => (
        <SVGShapes 
          key={index}
          className={`${svg.animClass} ${svg.className}`}
          style={{
            top: svg.top,
            left: svg.left,
            width: svg.width,
            animationDelay: svg.delay || "0s",
          }}
        >
          <img src={`${baseUrl}${svg.file}`} alt={svg.file.split('.')[0]} />
        </SVGShapes>
      ))}
      
      {/* Basic shapes with desktop positioning */}
      <Circle1 
        style={{
          top: '10%',
          left: '72%',
          width: '150px',
          height: '150px',
          backgroundColor: 'rgb(100, 100, 255)',
          opacity: 0.05,
        }}
        className="shape circle1"
      />
      <Circle2
        style={{
          top: '55%',
          left: '75%',
          width: '130px',
          height: '130px',
          backgroundColor: 'rgb(255, 100, 100)',
          opacity: 0.07,
          animationDelay: '-6s',
        }}
        className="shape circle2"
      />
      <Square
        style={{
          top: '55%',
          left: '50%',
          width: '80px',
          height: '80px',
          backgroundColor: 'rgb(255, 100, 255)',
          opacity: 0.07,
          animationDelay: '-4s',
        }}
        className="shape square"
      />
      <Triangle
        style={{
          top: '10%',
          left: '50%',
          borderLeft: '40px solid transparent',
          borderRight: '40px solid transparent',
          borderBottom: '80px solid rgb(100, 255, 255)',
          opacity: 0.15,
          animationDelay: '-2s',
        }}
        className="shape triangle"
      />
    </AnimationBackground>
  );
};

// Add simple styles for mobile shape adjustments
const GlobalStyle = styled.div`
  /* Mobile adjustments for shapes */
  @media (max-width: 48em) {
    .shape.circle1 {
      width: 100px !important;
      height: 100px !important;
      top: 15% !important;
      left: 60% !important;
    }
    .shape.circle2 {
      width: 90px !important;
      height: 90px !important;
      top: 60% !important;
      left: 65% !important;
    }
    .shape.square {
      width: 60px !important;
      height: 60px !important;
      top: 50% !important;
      left: 70% !important;
    }
    .shape.triangle {
      top: 25% !important;
      left: 45% !important;
      border-left: 25px solid transparent !important;
      border-right: 25px solid transparent !important;
      border-bottom: 50px solid rgb(100, 255, 255) !important;
    }
  }
`;

const TrustWrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-self: stretch;

  @media (max-width: 48em) {
    position: absolute; /* Position absolutely on mobile */
    top: 0; /* Stick to top */
    left: 0; /* Align to left */
    z-index: 10; /* Keep above other elements */
    width: 100%; /* Full width */
    padding: 1rem; /* Add some padding */
  }
`;


const Home = () => {
  return (
    <GlobalStyle>
      <Section id="home">
        <BackgroundShapes />

        <Container>
          <Suspense fallback={<Loading />}>
            <TrustWrapper>
              <TrustIndicator />
            </TrustWrapper>
          </Suspense>

          <ContentWrapper>
            <Box>
              <Suspense fallback={<Loading />}>
                <TypeWriterText />
              </Suspense>
            </Box>
            <Box>
              <Suspense fallback={<Loading />}>
                <LottieWrapper>
                  <DotLottie src="https://cdn.terang.ai/dotlotties/ai-people.lottie" width="100%" height="100%" devicePixelRatio={1.5} />
                </LottieWrapper>
              </Suspense>
            </Box>
          </ContentWrapper>
        </Container>
        
        <BackedBySection>
          <p>Partnered & backed by</p>
          <LogosContainer>
            <Image src="https://cdn.terang.ai/landingpage-assets/google-for-startups.webp" alt="Google for Startups" width={150} height={50} />
            <Image src="https://cdn.terang.ai/landingpage-assets/nvidia-inception-program-badge-rgb-for-screen.webp" alt="NVIDIA Inception Program" width={150} height={50} />
            {/* <Image src="https://cdn.terang.ai/landingpage-assets/microsoft-founders-hub.webp" alt="Microsoft for startup founders hub" width={150} height={50} /> */}
            <Image src="https://cdn.terang.ai/landingpage-assets/beasiswa-unggulan.webp" alt="Beasiswa Unggulan" width={100} height={100} />
            <Image src="https://cdn.terang.ai/landingpage-assets/matagaruda-papua.webp" alt="Mata Garuda Papua" width={80} height={80} />
          </LogosContainer>
        </BackedBySection>
      </Section>
    </GlobalStyle>
  );
};

export default Home;