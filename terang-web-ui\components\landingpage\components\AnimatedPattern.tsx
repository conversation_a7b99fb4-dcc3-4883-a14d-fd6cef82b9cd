import React from 'react';

const AnimatedPattern = () => {
  return (
    <div className="absolute inset-0 overflow-hidden">
      <svg className="h-full w-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
        <defs>
          <pattern id="circles" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
            <g>
              <animateTransform
                attributeName="transform"
                type="translate"
                values="0,0; 2,-2; 0,0; -2,2; 0,0"
                dur="8s"
                repeatCount="indefinite"
              />
              
              <circle cx="2" cy="2" r="1" fill="#E5E7EB" opacity="0.5">
                <animate
                  attributeName="opacity"
                  values="0.5;0.3;0.5"
                  dur="4s"
                  repeatCount="indefinite"
                />
              </circle>
              
              <circle cx="10" cy="10" r="1.5" fill="#E5E7EB" opacity="0.5">
                <animate
                  attributeName="opacity"
                  values="0.5;0.3;0.5"
                  dur="4s"
                  begin="1s"
                  repeatCount="indefinite"
                />
              </circle>
              
              <circle cx="18" cy="18" r="1" fill="#E5E7EB" opacity="0.5">
                <animate
                  attributeName="opacity"
                  values="0.5;0.3;0.5"
                  dur="4s"
                  begin="2s"
                  repeatCount="indefinite"
                />
              </circle>

              <path d="M0 0L20 20M20 0L0 20" stroke="#E5E7EB" strokeWidth="0.5" opacity="0.3">
                <animate
                  attributeName="opacity"
                  values="0.3;0.1;0.3"
                  dur="6s"
                  repeatCount="indefinite"
                />
              </path>
            </g>
          </pattern>

          {/* Create a second pattern that's offset for seamless tiling */}
          <pattern id="shiftedCircles" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
            <rect width="40" height="40" fill="url(#circles)" />
            <rect width="40" height="40" fill="url(#circles)" transform="translate(20, 20)">
              <animateTransform
                attributeName="transform"
                type="translate"
                values="20,20; 22,18; 20,20; 18,22; 20,20"
                dur="8s"
                repeatCount="indefinite"
              />
            </rect>
          </pattern>
        </defs>

        {/* Main background with shifting effect */}
        <rect width="100" height="100" fill="url(#shiftedCircles)">
          <animate
            attributeName="opacity"
            values="1;0.8;1"
            dur="8s"
            repeatCount="indefinite"
          />
        </rect>
      </svg>
    </div>
  );
};

export default AnimatedPattern;