"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardBody } from "@heroui/react";
import { But<PERSON> } from "@heroui/button";
import { Progress } from "@heroui/progress";
import { Dropdown, DropdownTrigger, DropdownMenu, DropdownItem } from "@heroui/dropdown";
import { 
  ChevronLeft, 
  ChevronRight,
  Rotate3D,
  BookOpen,
  Shuffle,
  RefreshCw,
  ChevronDown,
  Star,
  Filter,
  Award,
  Brain
} from 'lucide-react';
import FlashcardComponent from './flashcard-component';

interface Flashcard {
  id: string;
  front: {
    type: 'text' | 'image';
    content: string;
    title?: string;
  };
  back: {
    type: 'text' | 'image';
    content: string;
    explanation?: string;
    examples?: string[];
    mnemonics?: string;
  };
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface FlashcardDeck {
  id: string;
  title: string;
  description: string;
  cards: Flashcard[];
}

interface CardProgress {
    id: string;
    understanding: 'mastered' | 'reviewing' | 'learning' | 'struggling';
    lastReviewed: Date;
    repetitions: number;
  }
  

const enhancedBiologyDecks: FlashcardDeck[] = [
    {
      id: "cell-biology",
      title: "Cell Biology Fundamentals",
      description: "Master cellular structures, functions, and processes",
      cards: [
        {
          id: "cell-1",
          front: {
            type: "text",
            title: "Cell Membrane",
            content: "What is the primary function of the cell membrane and its components?"
          },
          back: {
            type: "text",
            content: "Controls what enters and exits the cell through selective permeability",
            explanation: "The cell membrane is a phospholipid bilayer with embedded proteins that regulates transport through various mechanisms: passive diffusion, facilitated diffusion, and active transport.",
            examples: [
              "Glucose transport through GLUT proteins",
              "Sodium-potassium pump for nerve signals",
              "Cholesterol maintaining membrane fluidity"
            ],
            mnemonics: "FLIP: Fluid mosaic, Lipid bilayer, Intrinsic proteins, Peripheral proteins"
          },
          category: "cell-structure",
          difficulty: "medium"
        },
        {
          id: "cell-2",
          front: {
            type: "text",
            title: "Mitochondria",
            content: "Describe the structure and function of mitochondria"
          },
          back: {
            type: "text",
            content: "Powerhouse of the cell, produces energy through cellular respiration",
            explanation: "Mitochondria have a double membrane structure with inner folds called cristae. They contain their own DNA and produce ATP through the electron transport chain.",
            examples: [
              "Present in high numbers in muscle cells",
              "Contains own ribosomes and DNA",
              "Can divide independently of cell division"
            ],
            mnemonics: "MITO: Makes Inside The Organism (energy)"
          },
          category: "organelles",
          difficulty: "easy"
        },
        {
          id: "mol-1",
          front: {
            type: "text",
            title: "DNA Structure",
            content: "Describe the structure of DNA and its components"
          },
          back: {
            type: "text",
            content: "Double helix composed of nucleotides with complementary base pairing",
            explanation: "DNA consists of two antiparallel strands of nucleotides connected by hydrogen bonds between complementary base pairs (A-T, G-C), forming a double helix structure.",
            examples: [
              "Adenine pairs with Thymine (2 H bonds)",
              "Guanine pairs with Cytosine (3 H bonds)",
              "Sugar-phosphate backbone on outside"
            ],
            mnemonics: "PAGS: Purines (A,G) pair with their Specific pyrimidines"
          },
          category: "molecular-biology",
          difficulty: "medium"
        }
      ]
    },
    {
      id: "molecular-biology",
      title: "Molecular Biology",
      description: "Explore DNA, RNA, and protein synthesis",
      cards: [
        {
          id: "mol-2",
          front: {
            type: "text",
            title: "RNA Types",
            content: "What are the main types of RNA and their functions?"
          },
          back: {
            type: "text",
            content: "mRNA, tRNA, and rRNA each play distinct roles in protein synthesis",
            explanation: "Different RNA types work together in protein synthesis: mRNA carries genetic information, tRNA transfers amino acids, and rRNA forms part of ribosomes.",
            examples: [
              "mRNA: Messenger for genetic code",
              "tRNA: Amino acid carrier",
              "rRNA: Ribosome structure"
            ],
            mnemonics: "MRT: Message, Read, Transform (into protein)"
          },
          category: "molecular-biology",
          difficulty: "medium"
        },
        {
          id: "mol-3",
          front: {
            type: "text",
            title: "Transcription",
            content: "Explain the process of transcription and its stages"
          },
          back: {
            type: "text",
            content: "Process of creating RNA copy from DNA template",
            explanation: "Transcription occurs in three stages: initiation, elongation, and termination. RNA polymerase creates mRNA using DNA as a template.",
            examples: [
              "Initiation at promoter sequence",
              "RNA polymerase action",
              "Addition of poly-A tail"
            ],
            mnemonics: "IET: Initiate, Elongate, Terminate"
          },
          category: "gene-expression",
          difficulty: "hard"
        },
        {
          id: "mol-3",
          front: {
            type: "text",
            title: "Translation",
            content: "Describe the process of protein synthesis"
          },
          back: {
            type: "text",
            content: "Process of creating proteins from mRNA instructions",
            explanation: "Translation involves ribosomes reading mRNA codons and building protein chains using tRNA molecules carrying specific amino acids.",
            examples: [
              "Start codon AUG",
              "Peptide bond formation",
              "Stop codons UAA, UAG, UGA"
            ],
            mnemonics: "START: Synthesis Through Amino acid Reading Translation"
          },
          category: "gene-expression",
          difficulty: "hard"
        }
      ]
    },
    {
      id: "metabolism",
      title: "Cellular Metabolism",
      description: "Learn about energy production and cellular processes",
      cards: [
        {
          id: "met-2",
          front: {
            type: "text",
            title: "Photosynthesis",
            content: "What are the light-dependent and light-independent reactions?"
          },
          back: {
            type: "text",
            content: "Two-stage process converting light energy to chemical energy",
            explanation: "Light-dependent reactions produce ATP and NADPH, while light-independent reactions (Calvin cycle) use these products to produce glucose.",
            examples: [
              "Photosystem I and II",
              "Carbon fixation in Calvin cycle",
              "Production of glucose"
            ],
            mnemonics: "LIGHT: Light In Generates High-energy Transport"
          },
          category: "energy-production",
          difficulty: "hard"
        }
      ]
    },
    {
      id: "genetics",
      title: "Genetics & Inheritance",
      description: "Study inheritance patterns and genetic variation",
      cards: [
        {
          id: "gen-1",
          front: {
            type: "text",
            title: "Chromosomal Theory",
            content: "Explain the chromosomal theory of inheritance"
          },
          back: {
            type: "text",
            content: "Genes are located on chromosomes that segregate during meiosis",
            explanation: "The chromosomal theory explains how genetic material is passed through generations via chromosomes during gamete formation and fertilization.",
            examples: [
              "X-linked inheritance patterns",
              "Chromosome segregation in meiosis",
              "Morgan's fruit fly experiments"
            ],
            mnemonics: "CHAIN: Chromosomes Hold And INherit genes"
          },
          category: "inheritance",
          difficulty: "hard"
        },
        {
          id: "gen-2",
          front: {
            type: "text",
            title: "Gene Expression",
            content: "What factors influence gene expression?"
          },
          back: {
            type: "text",
            content: "Environmental and genetic factors regulate gene expression",
            explanation: "Gene expression is controlled by various factors including transcription factors, epigenetic modifications, and environmental conditions.",
            examples: [
              "DNA methylation",
              "Histone modifications",
              "Environmental stress response"
            ],
            mnemonics: "GENE: Genetic and Environmental NEtwork"
          },
          category: "molecular-genetics",
          difficulty: "hard"
        },
        {
          id: "gen-3",
          front: {
            type: "text",
            title: "Genetic Mutations",
            content: "Describe different types of mutations and their effects"
          },
          back: {
            type: "text",
            content: "Changes in DNA sequence that can affect protein function",
            explanation: "Mutations can be point mutations, insertions, deletions, or chromosomal changes, each with different potential effects on phenotype.",
            examples: [
              "Silent mutations",
              "Frameshift mutations",
              "Chromosome inversions"
            ],
            mnemonics: "SPID: Substitution, Point, Insertion, Deletion"
          },
          category: "molecular-genetics",
          difficulty: "medium"
        },
        {
          id: "gen-4",
          front: {
            type: "text",
            title: "Population Genetics",
            content: "What is Hardy-Weinberg equilibrium?"
          },
          back: {
            type: "text",
            content: "Mathematical model predicting allele frequencies in populations",
            explanation: "Hardy-Weinberg equilibrium describes genetic equilibrium in populations without evolutionary forces, requiring specific conditions.",
            examples: [
              "p² + 2pq + q² = 1",
              "Random mating",
              "No selection pressure"
            ],
            mnemonics: "HARM: Hardy And Random Mating"
          },
          category: "population-genetics",
          difficulty: "hard"
        },
        {
          id: "gen-5",
          front: {
            type: "text",
            title: "Genetic Inheritance Patterns",
            content: "Compare different inheritance patterns"
          },
          back: {
            type: "text",
            content: "Various modes of inheritance determine trait transmission",
            explanation: "Inheritance can be autosomal dominant, autosomal recessive, X-linked, or show incomplete dominance or codominance.",
            examples: [
              "Autosomal dominant: Huntington's",
              "Autosomal recessive: Cystic fibrosis",
              "X-linked: Color blindness"
            ],
            mnemonics: "BASIC: Both Autosomal and Sex-linked Inheritance Combinations"
          },
          category: "inheritance",
          difficulty: "medium"
        },
        {
          id: "gen-6",
          front: {
            type: "text",
            title: "Genetic Linkage",
            content: "What is genetic linkage and crossing over?"
          },
          back: {
            type: "text",
            content: "Genes on same chromosome tend to be inherited together",
            explanation: "Linked genes are inherited together unless separated by crossing over during meiosis, affecting recombination frequencies.",
            examples: [
              "Linkage maps",
              "Recombination frequency",
              "Crossing over events"
            ],
            mnemonics: "LINK: Loci In Nearby Kinship"
          },
          category: "inheritance",
          difficulty: "hard"
        },
        {
          id: "gen-7",
          front: {
            type: "text",
            title: "Gene Regulation",
            content: "How is gene expression regulated in prokaryotes?"
          },
          back: {
            type: "text",
            content: "Operons control groups of related genes",
            explanation: "Prokaryotic gene regulation often involves operons, where multiple genes are controlled by a single promoter and regulatory elements.",
            examples: [
              "Lac operon",
              "Trp operon",
              "Operator sequences"
            ],
            mnemonics: "STOP: Sequence That Operates Proteins"
          },
          category: "molecular-genetics",
          difficulty: "hard"
        },
        {
          id: "gen-8",
          front: {
            type: "text",
            title: "Epigenetics",
            content: "What are epigenetic modifications?"
          },
          back: {
            type: "text",
            content: "Heritable changes in gene expression without DNA sequence changes",
            explanation: "Epigenetic modifications include DNA methylation and histone modifications that affect gene expression without altering DNA sequence.",
            examples: [
              "DNA methylation",
              "Histone acetylation",
              "Chromatin remodeling"
            ],
            mnemonics: "MODE: Modification Of DNA Expression"
          },
          category: "molecular-genetics",
          difficulty: "medium"
        },
        {
          id: "gen-9",
          front: {
            type: "text",
            title: "Genetic Engineering",
            content: "What are common genetic engineering techniques?"
          },
          back: {
            type: "text",
            content: "Methods to modify DNA and gene expression",
            explanation: "Various techniques allow scientists to modify genes and their expression, including CRISPR, restriction enzymes, and PCR.",
            examples: [
              "CRISPR-Cas9 editing",
              "Restriction digestion",
              "Gene cloning"
            ],
            mnemonics: "EDIT: Enzyme Directed Insertion Techniques"
          },
          category: "biotechnology",
          difficulty: "hard"
        },
        {
          id: "gen-10",
          front: {
            type: "text",
            title: "Evolution Mechanisms",
            content: "Describe mechanisms of evolution"
          },
          back: {
            type: "text",
            content: "Processes that drive genetic change in populations",
            explanation: "Evolution occurs through natural selection, genetic drift, gene flow, and mutation, leading to changes in allele frequencies.",
            examples: [
              "Natural selection",
              "Genetic drift",
              "Gene flow"
            ],
            mnemonics: "MDGS: Mutation, Drift, Gene flow, Selection"
          },
          category: "evolution",
          difficulty: "medium"
        },
        {
          id: "gen-1",
          front: {
            type: "text",
            title: "Mendel's Laws",
            content: "State and explain Mendel's three laws of inheritance"
          },
          back: {
            type: "text",
            content: "Law of Segregation, Independent Assortment, and Dominance",
            explanation: "These laws describe how traits are passed from parents to offspring through separate alleles, independent inheritance of different traits, and dominant/recessive relationships.",
            examples: [
              "Segregation in gamete formation",
              "Independent assortment in dihybrid cross",
              "Complete dominance in pea plants"
            ],
            mnemonics: "SID: Segregation, Independence, Dominance"
          },
          category: "inheritance",
          difficulty: "medium"
        }
      ]
    },
    {
      id: "ecology",
      title: "Ecology & Environment",
      description: "Understand species interactions and ecosystems",
      cards: [
        {
          id: "eco-1",
          front: {
            type: "text",
            title: "Ecological Relationships",
            content: "Define and give examples of different ecological relationships"
          },
          back: {
            type: "text",
            content: "Various interactions between species in an ecosystem",
            explanation: "Ecological relationships include predation, competition, mutualism, commensalism, and parasitism, each affecting species survival differently.",
            examples: [
              "Predator-prey: Lion and zebra",
              "Mutualism: Clownfish and anemone",
              "Parasitism: Tapeworm in host"
            ],
            mnemonics: "PCMCP: Predation, Competition, Mutualism, Commensalism, Parasitism"
          },
          category: "species-interactions",
          difficulty: "medium"
        }
      ]
    }
  ];

// Color system for theming
const colorThemes = [
  { name: 'blue', hue: 210, saturation: 70, lightness: 45 },
  { name: 'purple', hue: 270, saturation: 65, lightness: 45 },
  { name: 'pink', hue: 330, saturation: 65, lightness: 45 },
  { name: 'orange', hue: 30, saturation: 70, lightness: 45 },
  { name: 'teal', hue: 180, saturation: 65, lightness: 45 }
];

const useCardColors = (deckId: string) => {
  return useMemo(() => {
    // Generate a consistent index based on the deckId
    const themeIndex = Math.abs(deckId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % colorThemes.length;
    const theme = colorThemes[themeIndex];
    
    // Generate the color variations
    const mainColor = `hsl(${theme.hue}, ${theme.saturation}%, ${theme.lightness}%)`;
    const darkerColor = `hsl(${theme.hue}, ${theme.saturation}%, ${theme.lightness - 10}%)`;
    const lighterColor = `hsl(${theme.hue}, ${theme.saturation}%, ${theme.lightness + 5}%)`;

    return {
      frontGradient: `linear-gradient(135deg, ${mainColor}, ${darkerColor})`,
      backGradient: `linear-gradient(135deg, ${darkerColor}, hsl(${theme.hue}, ${theme.saturation}%, ${theme.lightness - 15}%))`,
      accentColor: lighterColor,
      background: `from-[${mainColor}] to-[${darkerColor}]`
    };
  }, [deckId]);
}

const EnhancedFlashcardPage = () => {
    const [currentDeckIndex, setCurrentDeckIndex] = useState(0);
    const [currentCardIndex, setCurrentCardIndex] = useState(0);
    const [isFlipped, setIsFlipped] = useState(false);
    const [studyProgress, setStudyProgress] = useState(0);
    const [markedCards, setMarkedCards] = useState<Set<string>>(new Set());
    const [filterDifficulty, setFilterDifficulty] = useState<'all' | 'easy' | 'medium' | 'hard'>('all');
    const [cardProgress, setCardProgress] = useState<Record<string, CardProgress>>({});
  
    // Understanding level descriptions with visual cues
    const understandingLevels = {
      mastered: {
        label: "Mastered",
        description: "I can explain this confidently",
        color: "bg-green-500",
        icon: <Award className="w-4 h-4" />,
        interval: 7
      },
      reviewing: {
        label: "Reviewing",
        description: "I understand but need practice",
        color: "bg-blue-500",
        icon: <Brain className="w-4 h-4" />,
        interval: 3
      },
      learning: {
        label: "Learning",
        description: "I'm getting familiar with this",
        color: "bg-yellow-500",
        icon: <BookOpen className="w-4 h-4" />,
        interval: 1
      },
      struggling: {
        label: "Need Help",
        description: "I need to focus on this more",
        color: "bg-red-500",
        icon: <RefreshCw className="w-4 h-4" />,
        interval: 0
      }
    };
  
    const currentDeck = enhancedBiologyDecks[currentDeckIndex];
    const filteredCards = currentDeck.cards.filter(card => 
      filterDifficulty === 'all' || card.difficulty === filterDifficulty
    );
    const currentCard = filteredCards[currentCardIndex];
    const totalCards = filteredCards.length;
    const colors = useCardColors(currentDeck.id);
  
    // Handle self-assessment of understanding
    const handleAssessUnderstanding = (level: keyof typeof understandingLevels) => {
      const newProgress: CardProgress = {
        id: currentCard.id,
        understanding: level,
        lastReviewed: new Date(),
        repetitions: (cardProgress[currentCard.id]?.repetitions || 0) + 1
      };
      
      setCardProgress(prev => ({
        ...prev,
        [currentCard.id]: newProgress
      }));
  
      // Move to next card after brief delay
      setTimeout(() => {
        if (currentCardIndex < totalCards - 1) {
          handleNextCard();
        }
      }, 500);
    };
  
    // Progress tracking
    useEffect(() => {
      const progress = ((currentCardIndex + 1) / totalCards) * 100;
      setStudyProgress(progress);
    }, [currentCardIndex, totalCards]);
  
    // Navigation handlers
    const handleNextCard = () => {
      if (currentCardIndex < totalCards - 1) {
        setCurrentCardIndex(prev => prev + 1);
        setIsFlipped(false);
      }
    };
  
    const handlePreviousCard = () => {
      if (currentCardIndex > 0) {
        setCurrentCardIndex(prev => prev - 1);
        setIsFlipped(false);
      }
    };
  
    const handleDeckChange = (index: number) => {
      setCurrentDeckIndex(index);
      setCurrentCardIndex(0);
      setIsFlipped(false);
      setStudyProgress(0);
    };
  
    const handleShuffle = () => {
      const randomIndex = Math.floor(Math.random() * totalCards);
      setCurrentCardIndex(randomIndex);
      setIsFlipped(false);
    };
  
    const handleRestart = () => {
      setCurrentCardIndex(0);
      setIsFlipped(false);
      setStudyProgress(0);
    };
  
    // Marking cards as favorite
    const handleMarkCard = (e: React.MouseEvent) => {
      e.stopPropagation();
      setMarkedCards(prev => {
        const newSet = new Set(prev);
        if (newSet.has(currentCard.id)) {
          newSet.delete(currentCard.id);
        } else {
          newSet.add(currentCard.id);
        }
        return newSet;
      });
    };
  
    // Learning stats component
    const LearningStats = () => (
      <div className="bg-white p-4 rounded-lg shadow-sm">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-semibold">Learning Progress</h3>
          <div className="text-sm text-gray-500">
            Total Cards: {totalCards}
          </div>
        </div>
        <div className="grid gap-3">
          {Object.entries(understandingLevels).map(([level, info]) => {
            const count = Object.values(cardProgress).filter(
              p => p.understanding === level
            ).length;
            const percentage = (count / totalCards) * 100;
            
            return (
              <div key={level} className="flex items-center gap-2">
                <div className={`${info.color} p-2 rounded-full`}>
                  {info.icon}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium">{info.label}</div>
                  <Progress 
                    value={percentage} 
                    className="h-2"
                    classNames={{
                      indicator: info.color
                    }}
                  />
                </div>
                <div className="text-sm text-gray-500">
                  {count} cards
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  
    // Card UI
    const renderCard = () => (
        <FlashcardComponent
        currentCard={currentCard}
        isFlipped={isFlipped}
        onFlip={() => setIsFlipped(!isFlipped)}
        onMark={handleMarkCard}
        markedCards={markedCards}
        colors={colors}
        cardProgress={cardProgress}
        handleAssessUnderstanding={handleAssessUnderstanding}
        understandingLevels={understandingLevels}
      />
    );
  
    return (
      <div className="min-h-screen p-8 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Main Content */}
            <div className="md:col-span-3 space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between bg-white p-4 rounded-lg shadow-sm">
                <div className="flex items-center space-x-3">
                  <BookOpen className="w-8 h-8" style={{ color: colors.accentColor }} />
                  <Dropdown>
                    <DropdownTrigger>
                      <Button
                        variant="light"
                        endContent={<ChevronDown className="w-4 h-4" />}
                      >
                        <span className="text-xl font-bold">{currentDeck.title}</span>
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu 
                      aria-label="Select deck"
                      onAction={(key) => handleDeckChange(Number(key))}
                    >
                      {enhancedBiologyDecks.map((deck, index) => (
                        <DropdownItem key={index}>
                          <div className="font-semibold">{deck.title}</div>
                          <div className="text-sm text-gray-500">{deck.description}</div>
                        </DropdownItem>
                      ))}
                    </DropdownMenu>
                  </Dropdown>
                </div>
                
                <Dropdown>
                  <DropdownTrigger>
                    <Button
                      variant="light"
                      startContent={<Filter className="w-4 h-4" />}
                    >
                      Difficulty
                    </Button>
                  </DropdownTrigger>
                  <DropdownMenu
                    aria-label="Filter difficulty"
                    onAction={(key) => setFilterDifficulty(key as any)}
                  >
                    <DropdownItem key="all">All Levels</DropdownItem>
                    <DropdownItem key="easy">Easy</DropdownItem>
                    <DropdownItem key="medium">Medium</DropdownItem>
                    <DropdownItem key="hard">Hard</DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </div>
  
              {/* Progress */}
              <div className="bg-white p-4 rounded-lg shadow-sm space-y-2">
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Progress</span>
                  <span>{currentCardIndex + 1} of {totalCards}</span>
                </div>
                <Progress
                  value={studyProgress}
                  className="w-full"
                  classNames={{
                    indicator: `bg-gradient-to-r ${colors.background}`,
                  }}
                />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Category: {currentCard.category}</span>
                  <span>Difficulty: {currentCard.difficulty}</span>
                </div>
              </div>
  
              {/* Flashcard */}
              {renderCard()}
  
              {/* Controls */}
              <div className="flex justify-between items-center bg-white p-4 rounded-lg shadow-sm">
                <Button
                  isIconOnly
                  variant="light"
                  onPress={handlePreviousCard}
                  isDisabled={currentCardIndex === 0}
                >
                  <ChevronLeft className="w-6 h-6" />
                </Button>
  
                <div className="flex space-x-2">
                  <Button
                    variant="flat"
                    onPress={handleShuffle}
                    startContent={<Shuffle className="w-4 h-4" />}
                  >
                    Shuffle
                  </Button>
                  <Button
                    variant="flat"
                    onPress={handleRestart}
                    startContent={<RefreshCw className="w-4 h-4" />}
                  >
                    Restart
                  </Button>
                </div>
  
                <Button
                  isIconOnly
                  variant="light"
                  onPress={handleNextCard}
                  isDisabled={currentCardIndex === totalCards - 1}
                >
                  <ChevronRight className="w-6 h-6" />
                </Button>
              </div>
            </div>
  
            {/* Learning Progress Sidebar */}
            <div className="md:col-span-1 space-y-6">
              <LearningStats />
              
              {/* Today's Study Stats */}
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <h3 className="font-semibold mb-3">Today&apos;s Progress</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Cards Reviewed</span>
                    <span className="font-medium">
                      {Object.values(cardProgress).filter(
                        p => new Date(p.lastReviewed).toDateString() === new Date().toDateString()
                      ).length}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Mastered Today</span>
                    <span className="font-medium">
                      {Object.values(cardProgress).filter(
                        p => 
                          p.understanding === 'mastered' && 
                          new Date(p.lastReviewed).toDateString() === new Date().toDateString()
                      ).length}
                    </span>
                  </div>
                </div>
              </div>
  
              {/* Study Streak */}
              <div className="bg-white p-4 rounded-lg shadow-sm">
                <h3 className="font-semibold mb-3">Study Streak</h3>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-500">
                    {Math.floor(Object.keys(cardProgress).length / 5)} {/* Placeholder streak calculation */}
                  </div>
                  <div className="text-sm text-gray-500">days</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };
  
  export default EnhancedFlashcardPage;