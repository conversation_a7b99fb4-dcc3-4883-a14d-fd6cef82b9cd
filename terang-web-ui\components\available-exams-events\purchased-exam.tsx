"use client";

import React, { useState, lazy, Suspense, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import {
  <PERSON><PERSON>,
  Spinner,
  Modal,
  ModalContent,
  ModalBody,
  useDisclosure
} from "@heroui/react";

import { AvailableExamsType } from "../types";

// Lazy load the full component implementation
const PurchasedExamFullImpl = lazy(() => import('./purchased-exam-impl'));

interface Props {
  examData: AvailableExamsType;
}

// Lightweight placeholder component
export const PurchasedExam: React.FC<Props> = ({ examData }) => {
  const pathname = usePathname();
  const router = useRouter();
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [isTrialMode, setIsTrialMode] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  
  // Load the implementation when opened without needing a second click
  useEffect(() => {
    if (isTrialMode !== null) {
      onOpen();
      setIsTrialMode(null);
    }
  }, [isTrialMode, onOpen]);

  const isMyExamsPath = pathname === "/my-exams";
  const isMyTrialsPath = pathname?.startsWith("/my-trials");
  const isAvailableExamsPath = pathname === "/available-exams";

  // Simple placeholder buttons until user interacts
  const renderPlaceholderButtons = () => {
    return (
      <div className="grid grid-rows-1 lg:grid-cols-1 gap-4 w-full">
        <div className="flex flex-row items-stretch gap-2 lg:gap-2">
          {/* Trial Button */}
          {(isMyTrialsPath || isAvailableExamsPath) && (
            <Button
              className="w-full text-sm lg:text-md px-5 py-3 lg:px-12 lg:py-6 bg-secondary-100 text-secondary-700 hover:bg-secondary-200 shadow-md"
              variant="shadow"
              disabled={isLoading}
              onPress={() => {
                if (isAvailableExamsPath) {
                  router.push('/my-trials/subjects');
                } else {
                  setIsLoading(true);
                  setIsTrialMode(true);
                }
              }}
            >
              {isLoading && isTrialMode === true ? (
                <Spinner size="sm" />
              ) : isAvailableExamsPath ? (
                "Ke Latihan"
              ) : (
                "Mulai Latihan"
              )}
            </Button>
          )}

          {/* Main Exam Button */}
          {(isMyExamsPath || isAvailableExamsPath) && (
            <>
              {isMyTrialsPath && <div className="hidden lg:block border-r border-gray-200 h-full" />}
              <Button
                className="w-full text-sm lg:text-md px-5 py-3 lg:px-12 lg:py-6"
                color="primary"
                variant="shadow"
                disabled={isLoading}
                onPress={() => {
                  if (isAvailableExamsPath) {
                    router.push('/my-exams');
                  } else {
                    setIsLoading(true);
                    setIsTrialMode(false);
                  }
                }}
              >
                {isLoading && isTrialMode === false ? (
                  <Spinner color="white" size="sm" />
                ) : isAvailableExamsPath ? (
                  "Ke Ujian"
                ) : (
                  "Mulai Ujian"
                )}
              </Button>
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      {renderPlaceholderButtons()}
      
      <Modal 
        isOpen={isOpen} 
        onOpenChange={() => {
          onOpenChange();
          setIsLoading(false);
        }}
      >
        <Suspense fallback={
          <ModalContent>
            <ModalBody>
              <div className="flex justify-center py-8">
                <Spinner size="lg" label="Loading..." />
              </div>
            </ModalBody>
          </ModalContent>
        }>
          {isOpen && (
            <PurchasedExamFullImpl 
              examData={examData} 
              initialTrialMode={isTrialMode === true} 
              onClose={() => {
                onOpenChange();
                setIsLoading(false);
              }}
            />
          )}
        </Suspense>
      </Modal>
    </>
  );
};

export default PurchasedExam;