"use server";

import { revalidateTag } from "next/cache";

const API_URL = process.env.BACKEND_BASE_URL;
const API_KEY = process.env.BACKEND_API_KEY;

if (!API_URL || !API_KEY) {
  throw new Error("Missing BACKEND_BASE_URL or BACKEND_API_KEY environment variables");
}

/**
 * Fetches a mindmap from the backend by session ID.
 * @param sessionId The ID of the interview session.
 * @returns The mindmap data if found, otherwise null.
 */
export const getMindmapBySessionId = async (sessionId: string) => {
  try {
    const response = await fetch(`${API_URL}/v1/mindmaps/${sessionId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'X-Api-Key': API_KEY,
      },
      next: { tags: [`mindmap-${sessionId}`] },
    });

    if (response.status === 404) {
      console.log(`No mindmap found for session ${sessionId}. A new one will be created.`);
      return null;
    }

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API error: ${response.status} - ${errorData.message || 'Failed to fetch mindmap'}`);
    }

    const result = await response.json();
    return result.data;
  } catch (error) {
    console.error("Error fetching mindmap:", error);
    // In case of network or other errors, proceed as if not found
    return null;
  }
};

/**
 * Creates or updates a mindmap in the backend.
 * @param sessionId The ID of the interview session.
 * @param nodes The array of nodes in the mindmap.
 * @param edges The array of edges in the mindmap.
 * @returns The saved mindmap data.
 */
export const saveMindmap = async (sessionId: string, nodes: any[], edges: any[]) => {
  try {
    const response = await fetch(`${API_URL}/v1/mindmaps`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Api-Key': API_KEY,
      },
      body: JSON.stringify({
        session_id: sessionId,
        nodes: nodes, // Pass the object/array directly
        edges: edges, // Pass the object/array directly
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`API error: ${response.status} - ${errorData.message || 'Failed to save mindmap'}`);
    }
    
    // Revalidate the cache for this mindmap
    revalidateTag(`mindmap-${sessionId}`);

    const result = await response.json();
    console.log("Mindmap saved successfully:", result.message);
    return result.data;
  } catch (error) {
    console.error("Error saving mindmap:", error);
    throw error; // Re-throw to be handled by the caller
  }
};