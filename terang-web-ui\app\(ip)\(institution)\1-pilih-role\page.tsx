"use client";

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON> } from "@heroui/react";
import { X } from "lucide-react";

interface Role {
  id: 'institution-admin' | 'teacher' | 'student';
  title: string;
  description: string;
  icon: string;
}

const roles: Role[] = [
  {
    id: "institution-admin",
    title: "Institution Admin",
    description: "Manage institution settings, users, and oversee overall analytics",
    icon: "🏫"
  },
  {
    id: "teacher",
    title: "Teacher",
    description: "Create classes, manage content, and track student progress",
    icon: "👩‍🏫"
  },
  {
    id: "student",
    title: "Student",
    description: "Access learning materials, take tests, and track your progress",
    icon: "👨‍🎓"
  }
];

export default function RoleSelectionPage() {
  const [isOpen, setIsOpen] = useState<boolean>(true);
  const [selectedRole, setSelectedRole] = useState<Role['id'] | null>(null);

  const handleRoleSelect = (roleId: Role['id']): void => {
    setSelectedRole(roleId);
    // Handle role selection - you might want to redirect or update application state here
    setIsOpen(false);
  };

  return (
    <Modal 
      isOpen={isOpen} 
      onClose={() => selectedRole && setIsOpen(false)}
      size="2xl"
      isDismissable={!!selectedRole}
      radius="lg"
      classNames={{
        base: "max-w-3xl",
        closeButton: "hidden"
      }}
    >
      <ModalContent>
        <div className="p-8">
          {/* Header with close button */}
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold">Select Your Role</h2>
            {selectedRole && (
              <Button
                isIconOnly
                variant="light"
                onClick={() => setIsOpen(false)}
                className="text-gray-500"
              >
                <X size={24} />
              </Button>
            )}
          </div>

          {/* Role cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {roles.map((role) => (
              <button
                key={role.id}
                onClick={() => handleRoleSelect(role.id)}
                className="bg-gray-50 rounded-xl p-6 text-center hover:bg-gray-100 transition-colors duration-200 flex flex-col items-center"
              >
                <span className="text-5xl mb-4">{role.icon}</span>
                <h3 className="text-xl font-semibold mb-3">{role.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {role.description}
                </p>
              </button>
            ))}
          </div>
        </div>
      </ModalContent>
    </Modal>
  );
}