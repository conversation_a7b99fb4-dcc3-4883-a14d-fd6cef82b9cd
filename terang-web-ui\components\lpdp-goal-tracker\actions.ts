"use server";

import { auth } from "@/auth";

export interface LpdpGoalTrackerFormData {
  name: string;
  email: string;
  verbalReasoning: number;
  quantitativeReasoning: number;
  problemSolving: number;
  passedLpdpTbs: boolean;
  feltHelped: boolean;
  helpfulnessRating: number;
  mostHelpfulAspect: string;
  improvementSuggestions: string;
  contactConsent: boolean;
  phoneNumber: string;
}

export interface FormErrors {
  [key: string]: string;
}

export async function validateLpdpGoalTrackerForm(
  formData: LpdpGoalTrackerFormData
): Promise<FormErrors> {
  const errors: FormErrors = {};

  // Validate required fields
  if (!formData.name) errors.name = "Nama harus diisi";
  if (!formData.email) errors.email = "Email harus diisi";

  // Validate numeric fields - make sure they are defined and valid
  if (formData.verbalReasoning === undefined || formData.verbalReasoning === null)
    errors.verbalReasoning = "<PERSON>lai <PERSON>alaran Verbal harus diisi";
  else if (formData.verbalReasoning < 0)
    errors.verbalReasoning = "Nilai Penalaran Verbal tidak valid";

  if (formData.quantitativeReasoning === undefined || formData.quantitativeReasoning === null)
    errors.quantitativeReasoning = "Nilai Penalaran Kuantitatif harus diisi";
  else if (formData.quantitativeReasoning < 0)
    errors.quantitativeReasoning = "Nilai Penalaran Kuantitatif tidak valid";

  if (formData.problemSolving === undefined || formData.problemSolving === null)
    errors.problemSolving = "Nilai Pemecahan Masalah harus diisi";
  else if (formData.problemSolving < 0)
    errors.problemSolving = "Nilai Pemecahan Masalah tidak valid";

  // Validate helpfulness rating
  if (formData.helpfulnessRating === undefined || formData.helpfulnessRating === null)
    errors.helpfulnessRating = "Rating harus diisi";
  else if (formData.helpfulnessRating < 1 || formData.helpfulnessRating > 10)
    errors.helpfulnessRating = "Rating harus antara 1-10";

  // Validate phone number if contact consent is given
  if (formData.contactConsent && !formData.phoneNumber)
    errors.phoneNumber = "Nomor telepon harus diisi jika bersedia dihubungi";

  return errors;
}

export async function submitLpdpGoalTrackerForm(
  formData: LpdpGoalTrackerFormData
): Promise<{ success: boolean; message: string }> {
  try {
    // Try to get the session, but don't require it
    const session = await auth();

    // Use email from session if available, otherwise use the one from the form
    const userEmail = session?.user?.email || formData.email;

    // If no email is available at all, that's an error
    if (!userEmail) {
      throw new Error("Email is required to submit the form");
    }

    // Convert camelCase to snake_case for backend
    const dataToSubmit = {
      name: formData.name,
      email: userEmail,
      verbal_reasoning: formData.verbalReasoning,
      quantitative_reasoning: formData.quantitativeReasoning,
      problem_solving: formData.problemSolving,
      passed_lpdp_tbs: formData.passedLpdpTbs,
      felt_helped: formData.feltHelped,
      helpfulness_rating: formData.helpfulnessRating,
      most_helpful_aspect: formData.mostHelpfulAspect,
      improvement_suggestions: formData.improvementSuggestions,
      contact_consent: formData.contactConsent,
      phone_number: formData.phoneNumber,
    };

    console.log("[LPDP Goal Tracker] Submitting data:", dataToSubmit);

    if (!process.env.BACKEND_BASE_URL) {
      throw new Error("BACKEND_BASE_URL is not defined");
    }

    if (!process.env.BACKEND_API_KEY) {
      throw new Error("BACKEND_API_KEY is not defined");
    }

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/user-goal-tracker`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
        body: JSON.stringify(dataToSubmit),
      }
    );

    const result = await response.json();

    if (!response.ok) {
      return {
        success: false,
        message: result.message || "Failed to submit form",
      };
    }

    return {
      success: true,
      message: "Form submitted successfully",
    };
  } catch (error) {
    console.error("Error submitting form:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unknown error occurred",
    };
  }
}

export async function checkLpdpGoalTrackerSubmission(): Promise<boolean> {
  try {
    const session = await auth();

    // If user is not authenticated, we can't check submission status
    // But we'll return false instead of throwing an error
    if (!session || !session.user?.email) {
      console.log("[LPDP Goal Tracker] User not authenticated, assuming no previous submission");
      return false;
    }

    const email = session.user.email;

    if (!process.env.BACKEND_BASE_URL) {
      throw new Error("BACKEND_BASE_URL is not defined");
    }

    if (!process.env.BACKEND_API_KEY) {
      throw new Error("BACKEND_API_KEY is not defined");
    }

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/user-goal-tracker/check-submission?email=${encodeURIComponent(email)}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to check submission status");
    }

    const result = await response.json();
    return result.submitted || false;
  } catch (error) {
    console.error("Error checking submission:", error);
    return false;
  }
}


