"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Clock, AlertTriangle, Play, RotateCcw } from "lucide-react";

interface ResumeDialogProps {
  isOpen: boolean;
  onResume: () => void;
  onStartNew: () => void;
  onCancel: () => void;
  resumeInfo?: {
    questionsAsked: number;
    elapsedMinutes: number;
    remainingMinutes: number;
    isNearEnd: boolean;
    sessionId?: string;
  };
  interviewName?: string;
}

export function ResumeDialog({ 
  isOpen, 
  onResume, 
  onStartNew, 
  onCancel, 
  resumeInfo,
  interviewName
}: ResumeDialogProps) {
  const [isResuming, setIsResuming] = useState(false);
  const [isStartingNew, setIsStartingNew] = useState(false);

  if (!isOpen) return null;

  const formatTime = (minutes: number) => {
    const hrs = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    if (hrs > 0) {
      return `${hrs}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const handleResume = async () => {
    setIsResuming(true);
    try {
      await onResume();
    } finally {
      setIsResuming(false);
    }
  };

  const handleStartNew = async () => {
    setIsStartingNew(true);
    try {
      await onStartNew();
    } finally {
      setIsStartingNew(false);
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.3, ease: [0.09, 1.04, 0.245, 1.055] }}
          className="bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
        >
          {/* Header */}
          <div className="bg-blue-50 p-6 border-b border-blue-100">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <RotateCcw size={20} className="text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  Resume Previous Session?
                </h2>
                <p className="text-sm text-gray-600 mt-1">
                  {interviewName || "Interview session"} in progress
                </p>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <p className="text-gray-600 mb-6">
              We found a previous interview session. Would you like to continue where you left off or start fresh?
            </p>

            {resumeInfo && (
              <div className="bg-gray-50 rounded-lg p-4 mb-6 space-y-3">
                <h3 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                  <Clock size={16} />
                  Session Progress
                </h3>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="bg-white rounded-lg p-3">
                    <div className="text-gray-500 text-xs uppercase tracking-wide">Questions Asked</div>
                    <div className="text-lg font-semibold text-gray-900">{resumeInfo.questionsAsked}</div>
                  </div>
                  
                  <div className="bg-white rounded-lg p-3">
                    <div className="text-gray-500 text-xs uppercase tracking-wide">Time Elapsed</div>
                    <div className="text-lg font-semibold text-gray-900">{formatTime(resumeInfo.elapsedMinutes)}</div>
                  </div>
                  
                  <div className="bg-white rounded-lg p-3">
                    <div className="text-gray-500 text-xs uppercase tracking-wide">Time Remaining</div>
                    <div className="text-lg font-semibold text-gray-900">{formatTime(resumeInfo.remainingMinutes)}</div>
                  </div>
                  
                  <div className="bg-white rounded-lg p-3">
                    <div className="text-gray-500 text-xs uppercase tracking-wide">Status</div>
                    <div className={`text-sm font-medium ${
                      resumeInfo.isNearEnd ? 'text-orange-600' : 'text-green-600'
                    }`}>
                      {resumeInfo.isNearEnd ? 'Near End' : 'In Progress'}
                    </div>
                  </div>
                </div>

                {resumeInfo.isNearEnd && (
                  <div className="flex items-center gap-2 mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                    <AlertTriangle size={16} className="text-orange-600" />
                    <span className="text-sm text-orange-700">
                      Interview is approaching the time limit
                    </span>
                  </div>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col gap-3">
              <button
                onClick={handleResume}
                disabled={isResuming || isStartingNew}
                className="flex items-center justify-center gap-2 w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {isResuming ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Resuming...
                  </>
                ) : (
                  <>
                    <Play size={16} />
                    Continue Session
                  </>
                )}
              </button>
              
              <button
                onClick={handleStartNew}
                disabled={isResuming || isStartingNew}
                className="flex items-center justify-center gap-2 w-full bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
              >
                {isStartingNew ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Starting New...
                  </>
                ) : (
                  <>
                    <RotateCcw size={16} />
                    Start New Session
                  </>
                )}
              </button>
              
              <button
                onClick={onCancel}
                disabled={isResuming || isStartingNew}
                className="w-full py-2 px-4 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}