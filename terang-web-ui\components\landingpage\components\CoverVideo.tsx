import React from "react";
import styled from "styled-components";

const VideoContainer = styled.div`
  width: 100%;

  video {
    width: 100%;
    height: auto;
  }

  @media (max-width: 64em) {
    min-width: 40vh;
  }
`;

const CoverVideo: React.FC = () => {
  const videoSrc: string = "/landingpage-assets/Home Video.mp4";

  return (
    <VideoContainer>
      <video autoPlay loop muted src={videoSrc}>
        <source src={videoSrc} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    </VideoContainer>
  );
};

export default CoverVideo;
