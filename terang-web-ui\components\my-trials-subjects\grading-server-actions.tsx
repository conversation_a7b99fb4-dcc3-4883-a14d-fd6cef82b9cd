"use server";

import { createClient } from "redis";
import { ExamGradingResponse } from "./grading-types";

// Cache keys and durations
const CACHE_KEYS = {
  examGradingInfo: "exam_grading_info",
};

const CACHE_DURATION = {
  EXAM_GRADING_INFO: 3600, // 1 hour in seconds
};

// Redis client creation functions
const createWriteClient = () => createClient({
  url: process.env.REDIS_URL_WRITE as string,
});

const createReadClient = () => createClient({
  url: process.env.REDIS_URL_READ as string,
});

// Cache utility functions
async function storeToRedis(key: string, value: any, expirationSeconds = CACHE_DURATION.EXAM_GRADING_INFO) {
  const redis = createWriteClient();

  try {
    await redis.connect();
    await redis.set(key, JSON.stringify(value), {
      EX: expirationSeconds,
    });
  } catch (error) {
    console.error("Failed to store data in Redis:", error);
  } finally {
    await redis.disconnect();
  }
}

async function retrieveFromRedis(key: string): Promise<any | null> {
  const redis = createReadClient();

  try {
    await redis.connect();
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error("Failed to retrieve data from Redis:", error);
    return null;
  } finally {
    await redis.disconnect();
  }
}

// Main function to fetch exam grading information
export async function fetchExamGradingInfo(): Promise<ExamGradingResponse> {
  try {
    if (!process.env.BACKEND_BASE_URL) {
      throw new Error("BACKEND_BASE_URL is not defined");
    }

    if (!process.env.BACKEND_API_KEY) {
      throw new Error("BACKEND_API_KEY is not defined");
    }

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/exams/grading-info`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!response.ok) {
      console.error("Error fetching exam grading info:", response.status, response.statusText);
      throw new Error(`Failed to fetch exam grading info: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error in fetchExamGradingInfo:", error);
    // Return default structure with empty arrays if fetch fails
    return {
      grading_info: [],
      question_counts: []
    };
  }
}

// Function to invalidate the cache (useful after updates)
export async function invalidateExamGradingInfoCache(): Promise<void> {
  const redis = createWriteClient();

  try {
    await redis.connect();
    await redis.del(CACHE_KEYS.examGradingInfo);
  } catch (error) {
    console.error("Error invalidating exam grading info cache:", error);
  } finally {
    await redis.disconnect();
  }
}