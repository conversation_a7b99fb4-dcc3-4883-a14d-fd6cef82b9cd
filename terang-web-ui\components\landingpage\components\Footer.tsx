import React, { lazy, Suspense } from "react";
import styled from "styled-components";
import Image from "next/image";
import Link from "next/link";
import Logo from "./Logo";
import Facebook from "./Icons/Facebook";
import Instagram from "./Icons/Instagram";
import Twitter from "./Icons/Twitter";
import LinkedIn from "./Icons/LinkedIn";
import Loading from "./Loading";
import { useLanguage } from "@/app/language-wrapper"; // Adjust import path according to your project structure

const Banner = lazy(() => import("./Banner"));

const FooterWrapper = styled.footer`
  min-height: 100vh;
  width: 100%;
  background-color: ${(props) => props.theme.body};
  position: relative;
  color: ${(props) => props.theme.text};
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const Container = styled.div`
  width: 75%;
  margin: 2rem auto;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1px solid ${(props) => props.theme.text};

  @media (max-width: 48em) {
    width: 90%;
    flex-direction: column;
    align-items: center;
  }
`;

const Left = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  @media (max-width: 48em) {
    align-items: center;
  }
`;

const IconList = styled.nav`
  display: flex;
  align-items: center;
  margin: 1rem 0;

  a {
    padding-right: 1rem;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.2);
    }
  }
`;

const MenuItems = styled.nav`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 1rem;
  text-align: left;

  @media (max-width: 48em) {
    grid-template-columns: 1fr;
    text-align: center;
  }
`;

const MenuItem = styled(Link)`
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
`;

const Bottom = styled.div`
  width: 100%;
  padding: 3rem 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background-color: ${(props) => props.theme.body};

  a {
    text-decoration: underline;
    color: ${(props) => props.theme.text};
    
    &:hover {
      opacity: 0.8;
    }
  }

  @media (max-width: 48em) {
    width: 100%;
  }
`;

const CompanyInfo = styled.address`
  margin-top: 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
  font-style: normal;
  
  a {
    display: inline-block;
    margin: 0 0.25rem;
  }
`;

const PaymentIcons = styled.div`
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin: 1rem 0;

  .payment-icon {
    margin: 0.5rem;
    position: relative;
    width: 80px;
    height: 40px;
  }
`;

const paymentMethods = [
  { src: "https://cdn.terang.ai/images/payment-methods/bca.png", alt: "BCA" },
  { src: "https://cdn.terang.ai/images/payment-methods/qris.png", alt: "QRIS" },
  { src: "https://cdn.terang.ai/images/payment-methods/gopay.png", alt: "GoPay" },
  { src: "https://cdn.terang.ai/images/payment-methods/mandiri.png", alt: "Bank Mandiri" },
  { src: "https://cdn.terang.ai/images/payment-methods/permata.png", alt: "PermataBank" },
];

const socialLinks = [
  { href: "https://www.instagram.com/terang.ai/", aria: "Visit our Instagram page", Icon: Instagram },
  { href: "https://twitter.com/terang_ai", aria: "Visit our Twitter page", Icon: Twitter },
  { href: "https://www.linkedin.com/company/terang-ai/", aria: "Visit our LinkedIn page", Icon: LinkedIn },
];

// Menu items with translation keys
const getMenuItems = (t: (key: string) => string) => [
  { href: "/about", text: t('footer_about') },
  { href: "/career", text: t('footer_career') },
  { href: "/partnership", text: t('footer_partnership') },
  { href: "/blog", text: t('footer_blog') },
  { href: "/#faq", text: t('footer_faq') },
  { href: "/privacy-policy", text: t('footer_privacy_policy') },
  { href: "/terms-and-conditions", text: t('footer_terms') },
];

const Footer: React.FC = () => {
  const { t } = useLanguage();
  const menuItems = getMenuItems(t);
  
  return (
    <FooterWrapper>
      <Suspense fallback={<Loading />}>
        <Banner />
      </Suspense>

      <Container>
        <Left>
          <Logo />
          <IconList>
            {socialLinks.map(({ href, aria, Icon }) => (
              <Link key={href} href={href} aria-label={aria}>
                <Icon />
              </Link>
            ))}
          </IconList>
        </Left>

        <MenuItems>
          {menuItems.map(({ href, text }) => (
            <MenuItem key={href} href={href}>
              {text}
            </MenuItem>
          ))}
        </MenuItems>
      </Container>

      <Bottom>
        <CompanyInfo>
          <strong>PT. TERANG INOVASI INDONESIA</strong><br />
          {t('company_address')}
          <br />
          <strong>{t('phone_and_extension')}:</strong> <a href="tel:02150106260">02150106260</a> - <u><strong>2086</strong></u>
          <br />
          <strong>{t('founders')}:</strong> <a href="tel:+6285755783673">+6285755783673</a>
          <br />
          <strong>{t('email_address')}:</strong> <a href="mailto:<EMAIL>"><EMAIL></a>
        </CompanyInfo>
        <PaymentIcons>
          {paymentMethods.map((method, index) => (
            <div key={index} className="payment-icon">
              <Image
                src={method.src}
                alt={method.alt}
                fill
                sizes="80px"
                style={{ objectFit: 'contain' }}
                priority={index < 2}
              />
            </div>
          ))}
        </PaymentIcons>

        <p>&copy; {new Date().getFullYear()}. {t('all_rights_reserved')}</p>
      </Bottom>
    </FooterWrapper>
  );
};

export default Footer;