'use client';

import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';

type LanguageContextType = {
  language: string;
  setLanguage: (lang: string) => void;
  t: (key: string) => string;
};

// Define supported languages to avoid any type issues
type SupportedLanguage = 'en' | 'id';

// Define the full translation type to enable string indexing
interface TranslationsType {
  [language: string]: {
    [key: string]: string;
  };
}

// Define translations with proper typing and comprehensive coverage
const translations: TranslationsType = {
    en: {
      // Navigation translations
      'home': 'Home',
      'ujian': 'Exam',
      'roadmap': 'Roadmap',
      'showcase': 'Showcase',
      'team': 'Team',
      'faq': 'FAQ',
      'daftar_di_sini': 'Register Here',
      'settings': 'Settings',
      'feedback': 'Feedback',
      'logout': 'Logout',

      "trust_indicator_text": "Trusted by <span class=\"font-semibold text-gray-900\">5.3K+</span> learners in Indonesia",
      
      // TypeWriter text translations
      'cool_way': 'Cool way to practice',
      'lpdp_exam': 'LPDP Exam',
      'utbk_exam': 'UTBK Exam',
      'cpns_exam': 'CPNS Exam',
      'ask_explanation': 'Ask for any exam explanation you want, anything, anytime!',
      'start_now': 'Start now - it\'s FREE!',
      
      // Ticket component translations
      'choose': 'Choose',
      'exam_card': 'Exam Card',
      'start_free': 'START FREE',
      'collection': 'Collection of 50+ curated questions, improved by selected academics for a fun learning process.',
      'total_questions': 'Total Questions',
      'total_time': 'Total Time',
      'total_max_score': 'Total Maximum Score',
      'pass': 'Pass',
      'max': 'Max',
      'questions': 'questions',
      
      // LPDP exam translations
      'lpdp_card_title': 'LPDP Exam Card:',
      'verbal_reasoning': 'Verbal Reasoning',
      'quantitative_reasoning': 'Quantitative Reasoning',
      'problem_solving': 'Problem Solving',
      'personality': 'Personality',
      
      // LPDP tips
      'lpdp_verbal_title': '1. Verbal Reasoning',
      'lpdp_verbal_time': 'Time: 20-25 minutes',
      'lpdp_verbal_tips': '• Focus on understanding context and core text.<br />• Apply critical reading techniques to identify main ideas.<br />• Practice synonym, antonym, & analogy questions in our practice feature.<br />• Strengthen vocabulary through diverse readings.<br />• Evaluate answers by reflecting on text logic.',
      
      'lpdp_quant_title': '2. Quantitative Reasoning',
      'lpdp_quant_time': 'Time: 25-30 minutes',
      'lpdp_quant_tips': '• Master basic concepts of mathematics and statistics.<br />• Break complex problems into simple parts.<br />• Apply elimination method to filter answers.<br />• Practice concept application through routine problems.<br />• Manage time by solving easy problems first.',
      
      'lpdp_problem_title': '3. Problem Solving',
      'lpdp_problem_time': 'Time: 20-25 minutes',
      'lpdp_problem_tips': '• Clearly identify problems before starting.<br />• Arrange solution steps systematically.<br />• Train critical thinking with problem simulations.<br />• Feedback from mentors is also necessary for improvement.<br />• Test strategies periodically and evaluate practice results.',
      
      'lpdp_personality_title': '4. Personality',
      'lpdp_personality_time': 'Time: 15-20 minutes',
      'lpdp_personality_tips': '• Show authenticity and consistency in answers.<br />• Use honest personal narratives.<br />• Explain values relevant to LPDP.<br />• Avoid extreme answers; provide balanced explanations.<br />• Align responses with LPDP vision and mission.',
      
      // UTBK exam translations
      'utbk_card_title': 'UTBK Exam Card:',
      'literacy_test': 'Literacy Test',
      'math_reasoning': 'Mathematical Reasoning Test',
      'scholastic_test': 'Scholastic Potential Test',
      
      // UTBK tips
      'utbk_literacy_title': '1. Literacy Test',
      'utbk_literacy_time': 'Time: 45 minutes',
      'utbk_literacy_tips': '• Understand text structure with skimming & scanning techniques.<br />• Mark main ideas and make brief summaries.<br />• Improve vocabulary through diverse readings.<br />• Focus on context and purpose of each text.<br />• Evaluate practice results to find areas for improvement.',
      
      'utbk_math_title': '2. Mathematical Reasoning Test',
      'utbk_math_time': 'Time: 30 minutes',
      'utbk_math_tips': '• Master basic high school math concepts.<br />• Break problems into simpler parts.<br />• Use elimination to simplify choices.<br />• Practice application problems through exam simulations.<br />• Manage time with effective work strategies.',
      
      'utbk_scholastic_title': '3. Scholastic Potential Test',
      'utbk_scholastic_time': 'Time: 105 minutes',
      'utbk_scholastic_tips': '• Start with easy questions to collect points.<br />• Use elimination on challenging questions.<br />• Practice verbal, quantitative, & analytical reasoning alternately.<br />• Manage time carefully to answer all questions.<br />• Evaluate practice to hone strategy and speed.',
      
      // CPNS exam translations
      'cpns_card_title': 'CPNS Exam Card:',
      'tkp': 'TKP (Personal Characteristics Test)',
      'twk': 'TWK (National Insight Test)',
      'tiu': 'TIU (General Intelligence Test)',
      
      // CPNS tips
      'cpns_tkp_title': '1. TKP (Personal Characteristics Test)',
      'cpns_tkp_time': 'Time: 15-25 minutes',
      'cpns_tkp_tips': '• Start with this for maximum effectiveness.<br />• Target: Minimum 34 questions with highest score (5 points/question).<br />• Answer all 45 questions, do not miss any.<br />• Remember: TKP points are higher than TWK/TIU.<br />• Work quickly and confidently, choose answers showing integrity and service orientation.',
      
      'cpns_twk_title': '2. TWK (National Insight Test)',
      'cpns_twk_time': 'Time: 30-35 minutes',
      'cpns_twk_tips': '• Target: Minimum 13 questions answered correctly.<br />• Avoid long complicated questions.<br />• Skip difficult questions, prioritize easy ones.<br />• Focus on Pancasila, 1945 Constitution, & Indonesian history.<br />• Use mnemonics for important facts and update on latest national issues.',
      
      'cpns_tiu_title': '3. TIU (General Intelligence Test)',
      'cpns_tiu_time': 'Time: 40 minutes',
      'cpns_tiu_tips': '• Target: Minimum 16 questions answered correctly.<br />• Avoid complicated calculations.<br />• Prioritize questions that can be answered quickly.<br />• Use elimination for difficult questions.<br />• Practice logic, basic math, & verbal analogies; maintain speed.',
  
      // Roadmap section
      'structured_learning': 'Structured Learning',
      
      // Roadmap items
      'roadmap_item1_title': 'Kinesthetic Learning',
      'roadmap_item1_text': 'Learning becomes fun and easy. Cramming? No worries... With our interactive Flashcards, you can swipe & tap to practice questions anytime, anywhere.',
      
      'roadmap_item2_title': 'Choose Exam Card',
      'roadmap_item2_text': 'First select an exam card, our question models are already varied, as we leverage AI to make questions challenging, without compromising on desired quality.',
      
      'roadmap_item3_title': 'Practice First',
      'roadmap_item3_text': 'Before actually taking the exam simulation, you can practice first. Here you can ask the AI to provide hints that help you remember the material, which is very helpful for your learning process.',
      
      'roadmap_item4_title': 'Start Exam Simulation',
      'roadmap_item4_text': 'Tired of practicing? Directly experience the exam atmosphere and start the simulation. Remember, it can\'t be paused, so make sure you\'re mentally and physically prepared.',
      
      'roadmap_item5_title': 'Get Comprehensive Explanations',
      'roadmap_item5_text': 'Our explanations are comprehensive, and if you\'re not satisfied, you can ask our AI consultant or inquire in our WhatsApp channel.',
      
      // Showcase translations
      'how': 'How is',
      'different_from_others': 'Different From Others?',
      
      // Features translations
      'pembelajaran_dipersonalisasi': 'Personalized Learning',
      'pembelajaran_description': 'We invented AI that adapts learning materials to your needs and level of understanding.',
      
      'analisis_kinerja': 'Real-time Performance Analysis',
      'analisis_description': 'Get instant feedback on your progress and areas that need improvement.',
      
      'bank_soal': 'Complete Question Bank',
      'bank_soal_description': 'Access thousands of practice questions from various subjects and difficulty levels.',
      
      'simulasi_ujian': 'Integrated Exam Simulation',
      'simulasi_description': 'Test your readiness with simulations that closely resemble actual exams.',
      
      // VideoShowcase translations
      'features': 'Features',
      'featured': 'Featured',
      'see_how': 'See How',
      'terang_ai_works': 'Terang AI Works',
      'live_demo_experience': 'Live Demo Experience',
      'revolutionary_learning': 'Experience revolutionary learning with cutting-edge AI technology',
      'works': 'Works',
      
      // VideoFeature translations - AI Interview Features
      'voice_recognition': 'Real-time Voice Recognition',
      'natural_language_processing': 'Natural Language Processing',
      'realtime_speech_analysis': 'Speech Analysis',
      'instant_feedback_system': 'Instant Feedback System',
      'lpdp_interview_simulation': 'LPDP Interview Simulation',
      'lpdp_interview_description': 'Comprehensive LPDP interview simulation with AI Professor Terra. Get realistic interview experience with real-time feedback and in-depth analysis to prepare you for the actual interview.',
      'personal_background_motivation': '🎯 Personal Background & Motivation',
      'ai_professor_terra_interactive': '🤖 AI Professor Terra Interactive',
      'realtime_feedback_analysis': '📊 Real-time Feedback Analysis',
      'complete_simulation_45_60_minutes': '⏱️ 45-60 minutes complete simulation',
      'start_free_lpdp_interview': 'Start Free LPDP Interview',
      
      'natural_voice_interview': 'Natural Voice Interview',
      'voice_interview_description': 'Speak directly with AI like a real interviewer. Advanced voice recognition system that perfectly understands Indonesian language and provides natural, contextual responses.',
      'indonesian_voice_recognition': '🎙️ Indonesian Voice Recognition',
      'natural_conversation_flow': '🗣️ Natural Conversation Flow',
      'realtime_audio_analysis': '📈 Real-time Audio Analysis',
      'interactive_response_system': '🔄 Interactive Response System',
      'start_voice_interview': 'Start Voice Interview',
      
      'smart_performance_insights': 'Smart Performance Insights',
      'performance_insights_description': 'AI analyzes your answers and provides in-depth feedback with comprehensive performance scores and personalized learning recommendations to improve your interview skills.',
      'detailed_performance_score': '📊 Detailed Performance Score',
      'ai_generated_learning_path': '🧠 AI-Generated Learning Path',
      'improvement_recommendations': '📈 Improvement Recommendations',
      'curated_learning_resources': '📚 Curated Learning Resources',
      
      // VideoFeature translations - Exam Prep Features
      'gamifikasi_latihan': 'Gamified Practice',
      'gamified_practice_description': 'Fun learning system with gamification that makes practice questions feel like playing games. Collect points, unlock achievements, and level up while learning.',
      'interactive_game_based_learning': '🎮 Interactive Game-based Learning',
      'achievement_badge_system': '🏆 Achievement & Badge System',
      'progress_tracking': '📈 Progress Tracking',
      'instant_feedback': '⚡ Instant Feedback',
      'start_free_practice': 'Start Free Practice',
      
      'pembimbing_ai': 'AI Tutor',
      'ai_tutor_description': 'Personal AI tutor that provides in-depth explanations for every question. Get step-by-step explanations that are easy to understand and smart tips for solving questions from intelligent AI.',
      'personal_ai_tutor': '🤖 Personal AI Tutor',
      'step_by_step_explanations': '📝 Step-by-step Explanations',
      'smart_learning_tips': '💡 Smart Learning Tips',
      'personalized_study_path': '🎯 Personalized Study Path',
      'try_ai_tutor': 'Try AI Tutor',
      
      'analisis_performa': 'Performance Analysis',
      'analisis_performa_description': 'Get deep insights about your personal intelligence mapping to measure your strengths and areas for improvement through comprehensive AI-based analysis.',
      'comprehensive_analytics': '📊 Comprehensive Analytics',
      'weakness_identification': '🎯 Weakness Identification',
      'improvement_roadmap': '📈 Improvement Roadmap',
      'personalized_resources': '📚 Personalized Resources',
      'view_analytics': 'View Analytics',
      
      // Tab labels
      'ai_interview': 'AI Interview',
      'exam_prep': 'Exam Prep',
      
      // AI Interview translations
      'voice_ai_technology': 'Voice AI Technology',
      'with_professor_terra': 'with Professor Terra',
      'read_more': 'Read More',
      'live': 'LIVE',
      'professor_terra': 'Professor Terra',
      'ai_interview_specialist': 'AI Interview Specialist',
      'professor_terra_speaking': 'Professor Terra is speaking...',
      'listening_to_your_answer': 'Listening to your answer...',
      'ready_to_start_interview': 'Ready to start interview',
      'interview_session_restarting': 'Thank you for today\'s interview. Session will restart soon...',
      
      // AI Interview Questions (English)
      'ai_question_1': 'Good morning! I\'m Professor Terra. Let\'s start with introducing yourself.',
      'ai_question_2': 'Why are you interested in taking the LPDP scholarship and continuing your studies abroad?',
      'ai_question_3': 'Tell me about the most memorable leadership experience in your life.',
      'ai_question_4': 'How do you see the contribution you can make to Indonesia after completing your studies?',
      'ai_question_5': 'What is the biggest challenge you have faced and how did you overcome it?',
      'ai_question_6': 'What are your career plans after returning to Indonesia?',
      
      // FAQ translations
      'faq_progress_question': 'WHERE CAN I VIEW MY LEARNING PROGRESS?',
      'faq_progress_answer': 'After you complete materials or exam simulations, you can view your learning progress and simulation results in your account dashboard.',
      
      'faq_what_is_question': 'WHAT IS TERANG AI?',
      'faq_what_is_answer': 'Terang AI is a learning platform that uses artificial intelligence technology to help users prepare for LPDP, CPNS, and UTBK exams.',
      
      'faq_simulation_question': 'WHY ARE EXAM SIMULATIONS IMPORTANT?',
      'faq_simulation_answer': 'Exam simulations help you understand question formats, measure your abilities, and prepare mentally for the actual exam, increasing your chances of passing.',
      
      'faq_how_simulation_question': 'HOW DO I USE THE EXAM SIMULATION FEATURE?',
      'faq_how_simulation_answer': 'You can select an exam card from the dashboard, complete the simulation, and get explanations and scores immediately after finishing the exam.',
      
      'faq_materials_question': 'WHAT MATERIALS ARE AVAILABLE ON TERANG AI?',
      'faq_materials_answer': 'Terang AI provides materials for CPNS, LPDP, and UTBK exams, including practice questions and explanations.',
      
      'faq_ai_help_question': 'HOW DOES AI HELP MY LEARNING?',
      'faq_ai_help_answer': 'The AI in Terang AI acts as a virtual tutor that provides material recommendations, explains questions, and provides performance analysis based on your simulation results.',
      
      // Contact form translations
      'get_in_touch': 'Get in Touch',
      'contact_description': 'Have questions or want to learn more? We want to hear from you. Send us a message and we\'ll get back to you as soon as possible.',
      'email': 'Email',
      'phone': 'Phone',
      'send_us_message': 'Send us a message',
      'fill_form_below': 'Fill out the form below and we\'ll get in touch with you soon.',
      'name': 'Name',
      'your_name': 'Your name',
      'your_email': '<EMAIL>',
      'subject': 'Subject',
      'subject_placeholder': 'What is your message about?',
      'message': 'Message',
      'message_placeholder': 'Type your message here...',
      'send_message': 'Send Message',
      'sending': 'Sending...',
      'sending_message': 'Sending your message...',
      'message_sent_success': 'Message sent successfully!',
      'message_send_failed': 'Failed to send message',
      'try_again_later': 'Please try again later.',
      'error_occurred': 'An error occurred',
      'remaining_messages': 'You have {count} message{s} remaining this hour.',
      
      // Footer translations
      'footer_about': 'About',
      'footer_career': 'Careers',
      'footer_partnership': 'Partnership',
      'footer_blog': 'Blog',
      'footer_faq': 'FAQ',
      'footer_privacy_policy': 'Privacy Policy',
      'footer_terms': 'Terms and Conditions',
      'company_address': 'Indonesia Stock Exchange Building (IDX) Tower 1, SCBD Jl. Jendral Sudirman Kav 52-53, Kebayoran Baru, South Jakarta, DKI Jakarta, Indonesia 12190',
      'phone_and_extension': 'Phone & Extension',
      'founders': 'Founders',
      'email_address': 'E-mail Address',
      'all_rights_reserved': 'All rights reserved',
      
      'dashboard': 'Dashboard',
      'products': 'Products',
      'exam_preparation_ai': 'Exam Preparation AI',
      'exam_prep_description': 'AI-powered study companion for exam success',
      'ai_interview_description': 'Practice interviews with AI feedback',
      'beta': 'Beta',
    },
    id: {
      // Navigation translations
      'home': 'Beranda',
      'ujian': 'Ujian',
      'roadmap': 'Roadmap',
      'showcase': 'Showcase',
      'team': 'Tim',
      'faq': 'FAQ',
      'daftar_di_sini': 'Daftar Di sini',
      'settings': 'Pengaturan',
      'feedback': 'Umpan Balik',
      'logout': 'Keluar',

      "trust_indicator_text": "Digunakan oleh <span class=\"font-semibold text-gray-900\">5.3K+</span> pembelajar Indonesia",
      
      // TypeWriter text translations
      'cool_way': 'Cara keren buat latihan',
      'lpdp_exam': 'Soal LPDP',
      'utbk_exam': 'Soal UTBK',
      'cpns_exam': 'Soal CPNS',
      'ask_explanation': 'Bisa tanya pembahasan soal yang kamu mau, anything, kapanpun!',
      'start_now': 'Coba sekarang - GRATIS!',
      
      // Ticket component translations
      'choose': 'Pilih',
      'exam_card': 'Kartu Ujian',
      'start_free': 'MULAI GRATISAN',
      'collection': 'Koleksi 50+ soal terkurasi, diimprove oleh akademisi terpilih untuk proses belajar yang asik.',
      'total_questions': 'Total Soal',
      'total_time': 'Total Waktu',
      'total_max_score': 'Total Skor Maksimal',
      'pass': 'Pass',
      'max': 'Max',
      'questions': 'soal',
      
      // LPDP exam translations
      'lpdp_card_title': 'Kartu Ujian LPDP:',
      'verbal_reasoning': 'Penalaran Verbal',
      'quantitative_reasoning': 'Penalaran Kuantitatif',
      'problem_solving': 'Pemecahan Masalah',
      'personality': 'Kepribadian',
      
      // LPDP tips
      'lpdp_verbal_title': '1. Penalaran Verbal',
      'lpdp_verbal_time': 'Waktu: 20-25 menit',
      'lpdp_verbal_tips': '• Fokus pada pemahaman konteks dan inti teks.<br />• Terapin teknik membaca kritis untuk mengidentifikasi ide utama.<br />• Latihan soal sinonim, antonim, & analogi di fitur latihan kita.<br />• Perkuat kosakata melalui bacaan beragam.<br />• Evaluasi jawaban dengan merefleksikan logika teks.',
      
      'lpdp_quant_title': '2. Penalaran Kuantitatif',
      'lpdp_quant_time': 'Waktu: 25-30 menit',
      'lpdp_quant_tips': '• Kuasain konsep dasar matematika dan statistik.<br />• Pecah soal kompleks menjadi bagian sederhana.<br />• Terapin metode eliminasi untuk menyaring jawaban.<br />• Latih aplikasi konsep melalui soal rutin.<br />• Kelola waktu dengan menyelesaikan soal mudah terlebih dahulu.',
      
      'lpdp_problem_title': '3. Pemecahan Masalah',
      'lpdp_problem_time': 'Waktu: 20-25 menit',
      'lpdp_problem_tips': '• Identifikasi masalah secara jelas sebelum mulai.<br />• Susun langkah solusi secara sistematis.<br />• Latih pola pikir kritis dengan simulasi soal.<br />• Feedback dari mentor untuk perbaikan juga perlu.<br />• Uji strategi secara berkala dan evaluasi hasil latihan.',
      
      'lpdp_personality_title': '4. Kepribadian',
      'lpdp_personality_time': 'Waktu: 15-20 menit',
      'lpdp_personality_tips': '• Tampilin keaslian dan konsistensi dalam jawaban.<br />• Gunakan narasi personal yang jujur.<br />• Jelaskan nilai yang relevan dengan LPDP.<br />• Hindari jawaban ekstrem; berikan penjelasan seimbang.<br />• Sesuaikan respons dengan visi dan misi LPDP.',
      
      // UTBK exam translations
      'utbk_card_title': 'Kartu Ujian UTBK:',
      'literacy_test': 'Tes Literasi',
      'math_reasoning': 'Tes Penalaran Matematika',
      'scholastic_test': 'Tes Potensi Skolastik',
      
      // UTBK tips
      'utbk_literacy_title': '1. Tes Literasi',
      'utbk_literacy_time': 'Waktu: 45 menit',
      'utbk_literacy_tips': '• Pahami struktur teks dengan teknik skimming & scanning.<br />• Tandai ide pokok dan buat ringkasan singkat.<br />• Tingkatkan kosakata melalui bacaan beragam.<br />• Fokus pada konteks dan tujuan setiap teks.<br />• Evaluasi hasil latihan untuk menemukan area perbaikan.',
      
      'utbk_math_title': '2. Tes Penalaran Matematika',
      'utbk_math_time': 'Waktu: 30 menit',
      'utbk_math_tips': '• Kuasai konsep dasar matematika SMA.<br />• Pecah soal menjadi bagian yang lebih sederhana.<br />• Gunakan eliminasi untuk menyederhanakan pilihan.<br />• Latih soal aplikasi melalui simulasi ujian.<br />• Kelola waktu dengan strategi pengerjaan efektif.',
      
      'utbk_scholastic_title': '3. Tes Potensi Skolastik',
      'utbk_scholastic_time': 'Waktu: 105 menit',
      'utbk_scholastic_tips': '• Mulai dengan soal mudah untuk mengumpulkan poin.<br />• Gunakan eliminasi pada soal yang menantang.<br />• Latih penalaran verbal, kuantitatif, & analitis bergantian.<br />• Atur waktu secara cermat agar semua soal terjawab.<br />• Evaluasi latihan untuk mengasah strategi dan kecepatan.',
      
      // CPNS exam translations
      'cpns_card_title': 'Kartu Ujian CPNS:',
      'tkp': 'TKP (Tes Karakteristik Pribadi)',
      'twk': 'TWK (Tes Wawasan Kebangsaan)',
      'tiu': 'TIU (Tes Intelegensi Umum)',
      
      // CPNS tips
      'cpns_tkp_title': '1. TKP (Tes Karakteristik Pribadi)',
      'cpns_tkp_time': 'Waktu: 15-25 menit',
      'cpns_tkp_tips': '• Mulai dengan ini untuk efektivitas maksimal.<br />• Target: Minimal 34 soal dengan skor tertinggi (5 poin/soal).<br />• Jawab semua 45 soal, jangan ada yang terlewat.<br />• Ingat: Poin TKP lebih tinggi dari TWK/TIU.<br />• Kerjakan dengan cepat dan yakin, pilih jawaban yang menunjukkan integritas dan orientasi pelayanan.',
      
      'cpns_twk_title': '2. TWK (Tes Wawasan Kebangsaan)',
      'cpns_twk_time': 'Waktu: 30-35 menit',
      'cpns_twk_tips': '• Target: Minimal 13 soal terjawab benar.<br />• Hindari soal panjang yang rumit.<br />• Skip soal sulit, prioritaskan yang mudah.<br />• Fokus pada Pancasila, UUD 1945, & sejarah Indonesia.<br />• Gunakan mnemonik untuk fakta penting dan update isu nasional terkini.',
      
      'cpns_tiu_title': '3. TIU (Tes Intelegensi Umum)',
      'cpns_tiu_time': 'Waktu: 40 menit',
      'cpns_tiu_tips': '• Target: Minimal 16 soal terjawab benar.<br />• Hindari soal hitung-hitungan rumit.<br />• Prioritaskan soal yang bisa dijawab cepat.<br />• Gunakan eliminasi untuk soal sulit.<br />• Latih logika, matematika dasar, & analogi verbal; jaga kecepatan.',
  
      // Roadmap section
      'structured_learning': 'Belajar Terstruktur',
      
      // Roadmap items
      'roadmap_item1_title': 'Belajar Secara Kinestetik',
      'roadmap_item1_text': 'Belajar jadi seru dan gampang. Mau SKS? Santai... Pakai Flashcard interaktif kita, kamu bisa swipe & tap buat latihan soal kapan aja, di mana aja.',
      
      'roadmap_item2_title': 'Pilih Kartu Ujian',
      'roadmap_item2_text': 'Pilih kartu ujian dulu, model soal yang kita punya udah bervariasi, karena kita memanfaatkan AI buat bikin soal-soal jadi menantang, tanpa mengurangi kualitas soal yang diinginkan.',
      
      'roadmap_item3_title': 'Bisa Latihan Dulu',
      'roadmap_item3_text': 'Sebelum beneran ujian simulasi, kamu juga bisa latihan dulu, di sini kamu bisa minta AI buat kasih hint-hint yang ngebantu untuk mengingat materi, ini ngebantu banget buat proses belajar kamu.',
      
      'roadmap_item4_title': 'Mulai Ujian Simulasi',
      'roadmap_item4_text': 'Udah bosen latihan? Langsung coba suasana ujian dan mulai simulasinya. Ingat, nggak bisa dipause dan pastiin siap jiwa dan raga.',
      
      'roadmap_item5_title': 'Pembahasan Ditampilin',
      'roadmap_item5_text': 'Pembahasan yang kita tampilin udah komprehensif, kalo nggak puas, bisa tanya sama konsultan AI kita, bisa juga tanya ke channel WA kita.',
      
      // Showcase translations
      'how': 'Bagaimana',
      'different_from_others': 'Berbeda Dari Yang Lain?',
      
      // Features translations
      'pembelajaran_dipersonalisasi': 'Pembelajaran yang Dipersonalisasi',
      'pembelajaran_description': 'Kami menginvensi AI yang menyesuaikan materi belajar sesuai dengan kebutuhan dan tingkat pemahaman kamu.',
      
      'analisis_kinerja': 'Analisis Kinerja Real-time',
      'analisis_description': 'Dapatkan feedback instan tentang kemajuan kamu dan area yang perlu ditingkatkan.',
      
      'bank_soal': 'Bank Soal Terlengkap',
      'bank_soal_description': 'Akses ribuan soal latihan dari berbagai mata pelajaran dan tingkat kesulitan.',
      
      'simulasi_ujian': 'Simulasi Ujian Terintegrasi',
      'simulasi_description': 'Uji kesiapan kamu dengan simulasi ujian yang mirip dengan ujian sebenarnya.',
      
      // VideoShowcase translations
      'features': 'Fitur',
      'featured': 'Unggulan',
      'see_how': 'Lihat Bagaimana',
      'terang_ai_works': 'Terang AI Bekerja',
      'live_demo_experience': 'Live Demo Experience',
      'revolutionary_learning': 'Rasakan pengalaman belajar yang modern dengan teknologi AI terdepan',
      'works': 'Bekerja',
      
      // VideoFeature translations - AI Interview Features
      'voice_recognition': 'Pengenal Suara Real-time',
      'natural_language_processing': 'Proses Bahasa Natural',
      'realtime_speech_analysis': 'Speech Analysis',
      'instant_feedback_system': 'Sistem Feedback Instan',
      'lpdp_interview_simulation': 'Simulasi Wawancara LPDP',
      'lpdp_interview_description': 'Simulasi wawancara LPDP yang komprehensif dengan AI Professor Terra. Dapatkan pengalaman wawancara yang realistis dengan feedback real-time dan analisis mendalam untuk mempersiapkan kamu menghadapi wawancara sesungguhnya.',
      'personal_background_motivation': '🎯 Personal Background & Motivation',
      'ai_professor_terra_interactive': '🤖 AI Professor Terra Interactive',
      'realtime_feedback_analysis': '📊 Real-time Feedback Analysis',
      'complete_simulation_45_60_minutes': '⏱️ 45-60 menit simulasi lengkap',
      'start_free_lpdp_interview': 'Mulai LPDP Interview Gratis',
      
      'natural_voice_interview': 'Wawancara Suara Natural',
      'voice_interview_description': 'Berbicara langsung dengan AI seperti pewawancara sesungguhnya. Sistem voice recognition canggih memahami bahasa Indonesia dengan sempurna dan memberikan respons yang natural dan kontekstual.',
      'indonesian_voice_recognition': '🎙️ Voice Recognition Indonesia',
      'natural_conversation_flow': '🗣️ Natural Conversation Flow',
      'realtime_audio_analysis': '📈 Real-time Audio Analysis',
      'interactive_response_system': '🔄 Interactive Response System',
      'start_voice_interview': 'Mulai Voice Interview',
      
      'smart_performance_insights': 'Smart Performance Insights',
      'performance_insights_description': 'AI menganalisis jawaban dan memberikan feedback mendalam dengan skor performa yang komprehensif dan rekomendasi pembelajaran yang dipersonalisasi untuk meningkatkan kemampuan wawancaramu.',
      'detailed_performance_score': '📊 Detailed Performance Score',
      'ai_generated_learning_path': '🧠 AI-Generated Learning Path',
      'improvement_recommendations': '📈 Improvement Recommendations',
      'curated_learning_resources': '📚 Curated Learning Resources',
      
      // VideoFeature translations - Exam Prep Features
      'gamifikasi_latihan': 'Gamifikasi Latihan',
      'gamified_practice_description': 'Sistem pembelajaran yang menyenangkan dengan gamifikasi yang membuat latihan soal menjadi seperti bermain game. Kumpulkan poin, buka achievement, dan tingkatkan level kamu sambil belajar.',
      'interactive_game_based_learning': '🎮 Interactive Game-based Learning',
      'achievement_badge_system': '🏆 Achievement & Badge System',
      'progress_tracking': '📈 Progress Tracking',
      'instant_feedback': '⚡ Instant Feedback',
      'start_free_practice': 'Mulai Latihan Gratis',
      
      'pembimbing_ai': 'AI Tutor',
      'ai_tutor_description': 'AI tutor personal yang memberikan pembahasan mendalam untuk setiap soal. Dapatkan penjelasan step-by-step yang mudah dipahami dan tips strategi mengerjakan soal dari AI yang cerdas.',
      'personal_ai_tutor': '🤖 Personal AI Tutor',
      'step_by_step_explanations': '📝 Step-by-step Explanations',
      'smart_learning_tips': '💡 Smart Learning Tips',
      'personalized_study_path': '🎯 Personalized Study Path',
      'try_ai_tutor': 'Coba AI Tutor',
      
      'analisis_performa': 'Analisis Performa',
      'analisis_performa_description': 'Dapatkan insight mendalam tentang pemetaan kecerdasan personal untuk mengukur kekuatan dan area pengembangan kemampuanmu melalui analisis berbasis AI yang komprehensif.',
      'comprehensive_analytics': '📊 Comprehensive Analytics',
      'weakness_identification': '🎯 Weakness Identification',
      'improvement_roadmap': '📈 Improvement Roadmap',
      'personalized_resources': '📚 Personalized Resources',
      'view_analytics': 'Lihat Analytics',
      
      // Tab labels
      'ai_interview': 'AI Wawancara',
      'exam_prep': 'Persiapan Ujian',
      
      // AI Interview translations
      'voice_ai_technology': 'Teknologi Voice AI',
      'with_professor_terra': 'dengan Professor Terra',
      'read_more': 'Baca Selengkapnya',
      'live': 'LIVE',
      'professor_terra': 'Professor Terra',
      'ai_interview_specialist': 'AI Interview Specialist',
      'professor_terra_speaking': 'Professor Terra sedang berbicara...',
      'listening_to_your_answer': 'Mendengarkan jawaban Anda...',
      'ready_to_start_interview': 'Siap memulai wawancara',
      'interview_session_restarting': 'Terima kasih atas wawancara hari ini. Sesi akan segera dimulai ulang...',
      
      // AI Interview Questions (Indonesian)
      'ai_question_1': 'Selamat pagi! Saya Professor Terra. Mari kita mulai dengan perkenalan diri kamu.',
      'ai_question_2': 'Mengapa kamu tertarik untuk mengambil beasiswa LPDP dan melanjutkan studi ke luar negeri?',
      'ai_question_3': 'Ceritakan tentang pengalaman kepemimpinan yang paling berkesan dalam hidup kamu.',
      'ai_question_4': 'Bagaimana kamu melihat kontribusi yang bisa diberikan untuk Indonesia setelah menyelesaikan studi?',
      'ai_question_5': 'Apa tantangan terbesar yang kamu hadapi dan bagaimana cara mengatasinya?',
      'ai_question_6': 'Bagaimana rencana karier kamu setelah kembali ke Indonesia?',
      
      // FAQ translations
      'faq_progress_question': 'DI MANA SAYA DAPAT MELIHAT PROGRES BELAJAR SAYA?',
      'faq_progress_answer': 'Setelah Kamu menyelesaikan materi atau simulasi ujian, Kamu dapat melihat progres belajar dan hasil simulasi di dashboard akun Kamu.',
      
      'faq_what_is_question': 'APA ITU TERANG AI?',
      'faq_what_is_answer': 'Terang AI adalah platform pembelajaran yang menggunakan teknologi kecerdasan buatan untuk membantu pengguna mempersiapkan diri menghadapi ujian LPDP, CPNS, UTBK.',
      
      'faq_simulation_question': 'MENGAPA SIMULASI UJIAN PENTING?',
      'faq_simulation_answer': 'Simulasi ujian membantu Kamu memahami format soal, mengukur kemampuan, dan mempersiapkan mental dalam menghadapi ujian sesungguhnya, meningkatkan peluang Kamu untuk lulus.',
      
      'faq_how_simulation_question': 'BAGAIMANA CARA MENGGUNAKAN FITUR SIMULASI UJIAN?',
      'faq_how_simulation_answer': 'Kamu bisa memilih kartu ujian dari dashboard, menyelesaikan simulasi, dan mendapatkan pembahasan serta skor langsung setelah menyelesaikan ujian.',
      
      'faq_materials_question': 'APA SAJA MATERI YANG TERSEDIA DI TERANG AI?',
      'faq_materials_answer': 'Terang AI menyediakan materi untuk ujian CPNS, LPDP, UTBK, termasuk latihan soal, dan pembahasan.',
      
      'faq_ai_help_question': 'BAGAIMANA AI MEMBANTU PEMBELAJARAN SAYA?',
      'faq_ai_help_answer': 'AI di Terang AI berperan sebagai tutor virtual yang memberikan rekomendasi materi, menjelaskan soal, dan memberikan analisis kinerja berdasarkan hasil simulasi Kamu.',
      
      // Contact form translations
      'get_in_touch': 'Hubungi Kami',
      'contact_description': 'Punya pertanyaan atau ingin mempelajari lebih lanjut? Kami ingin mendengar darimu. Kirimkan pesan kepada kami dan kami akan segera menanggapinya.',
      'email': 'Email',
      'phone': 'Telepon',
      'send_us_message': 'Kirim pesan kepada kami',
      'fill_form_below': 'Isi formulir di bawah ini dan kami akan segera menghubungimu.',
      'name': 'Nama',
      'your_name': 'Nama kamu',
      'your_email': '<EMAIL>',
      'subject': 'Subjek',
      'subject_placeholder': 'Tentang apa pesanmu?',
      'message': 'Pesan',
      'message_placeholder': 'Ketik pesanmu di sini...',
      'send_message': 'Kirim Pesan',
      'sending': 'Mengirim...',
      'sending_message': 'Mengirim pesanmu...',
      'message_sent_success': 'Pesan terkirim dengan sukses!',
      'message_send_failed': 'Gagal mengirim pesan',
      'try_again_later': 'Silakan coba lagi nanti.',
      'error_occurred': 'Terjadi kesalahan',
      'remaining_messages': 'Kamu memiliki {count} pesan{s} tersisa di jam ini.',
      
      // Footer translations
      'footer_about': 'Tentang',
      'footer_career': 'Karir',
      'footer_partnership': 'Kerjasama',
      'footer_blog': 'Blog',
      'footer_faq': 'FAQ',
      'footer_privacy_policy': 'Kebijakan Privasi',
      'footer_terms': 'Syarat dan Ketentuan',
      'company_address': 'Gedung Bursa Efek Indonesia (IDX) Tower 1, SCBD Jl. Jendral Sudirman Kav 52-53, Kebayoran Baru, Jakarta Selatan, DKI Jakarta, Indonesia 12190',
      'phone_and_extension': 'No. Telp & No. extension office',
      'founders': 'Founders',
      'email_address': 'Alamat Email',
      'all_rights_reserved': 'Seluruh hak dilindungi',

      'dashboard': 'Dasbor',
      'products': 'Produk',
      'exam_preparation_ai': 'AI Persiapan Ujian',
      'exam_prep_description': 'Pendamping belajar bertenaga AI untuk sukses ujian',
      'ai_interview_description': 'Latihan wawancara dengan umpan balik AI',
      'beta': 'Beta'
    }
  };

// Create a context with a default value
const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

interface LanguageWrapperProps {
  children: ReactNode;
}

export const LanguageWrapper: React.FC<LanguageWrapperProps> = ({ children }) => {
  // Use useState with a function to initialize from localStorage safely (client-side only)
  const [language, setLanguage] = useState<string>('id');
  const [isClient, setIsClient] = useState(false);

  // Handle initial client-side load
  useEffect(() => {
    setIsClient(true);
    // Load saved language preference from localStorage on initial render
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'id')) {
      setLanguage(savedLanguage);
    } else {
      // Set default language if none is saved
      localStorage.setItem('language', 'id');
    }
    
    // Set the HTML lang attribute
    document.documentElement.lang = savedLanguage || 'id';
  }, []);

  // Update localStorage and HTML lang attribute when language changes
  useEffect(() => {
    if (isClient) {
      localStorage.setItem('language', language);
      document.documentElement.lang = language;
    }
  }, [language, isClient]);

  // Enhanced translation function with fallback handling
  const t = (key: string): string => {
    // Handle template strings with variables like {count}
    const handleTemplate = (template: string, variables?: Record<string, any>): string => {
      if (!variables) return template;
      
      let result = template;
      Object.keys(variables).forEach(varKey => {
        const regex = new RegExp(`\\{${varKey}\\}`, 'g');
        result = result.replace(regex, variables[varKey]);
      });
      return result;
    };

    // Use type assertion to help TypeScript understand the structure
    if (language && language in translations) {
      const translationSet = translations[language as keyof typeof translations];
      if (key in translationSet) {
        return translationSet[key];
      }
    }
    
    // Fallback to English if key exists there
    if (language !== 'en' && 'en' in translations) {
      const englishSet = translations['en'];
      if (key in englishSet) {
        return englishSet[key];
      }
    }
    
    // Return the key itself as final fallback
    return key;
  };

  // Enhanced language setter with validation
  const setLanguageWithValidation = (lang: string): void => {
    if (lang === 'en' || lang === 'id') {
      setLanguage(lang);
    } else {
      console.warn(`Unsupported language: ${lang}. Falling back to Indonesian.`);
      setLanguage('id');
    }
  };

  return (
    <LanguageContext.Provider value={{ 
      language, 
      setLanguage: setLanguageWithValidation, 
      t 
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageWrapper');
  }
  return context;
};