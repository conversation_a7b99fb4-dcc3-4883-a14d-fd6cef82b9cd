"use client";

import React, { useState, use<PERSON>emo, useEffect } from "react";
import {
  Card,
  CardBody,
  CardHeader,
  Checkbox,
  Button,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  Modal<PERSON>ooter,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Select,
  SelectItem,
  Selection,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Badge,
} from "@heroui/react";

interface Subject {
  value: string;
  label: string;
}

interface Task {
  nama: string;
  selesai: boolean;
}

interface ReminderChannels {
  email: boolean;
  whatsapp: boolean;
  googleKalender: boolean;
}

interface StudySchedule {
  jamPerMinggu: string;
  hariPerMinggu: string;
  durasiSesi: string;
}

interface Reminder {
  id: string;
  subject: string;
  channel: string;
  schedule: string;
}

const subjects: Subject[] = [
  { value: "cpns", label: "CPNS" },
  { value: "lpdp", label: "LPDP" },
  { value: "toefl", label: "TOEFL" },
  { value: "ielts", label: "IELTS" },
  { value: "gre", label: "GRE" },
];

const Email = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    color={"#000000"}
    fill={"none"}
    height={24}
    viewBox="0 0 24 24"
    width={24}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M14.795 1.66181C12.842 1.61273 10.908 1.61273 8.95499 1.6618L8.89682 1.66326C7.372 1.70155 6.14502 1.73235 5.1617 1.9036C4.1322 2.08289 3.29548 2.42677 2.58862 3.13632C1.88471 3.8429 1.54264 4.6676 1.36676 5.6804C1.19929 6.64478 1.17378 7.8418 1.14219 9.32465L1.14093 9.38339C1.11969 10.3797 1.11969 11.3703 1.14094 12.3666L1.14219 12.4254C1.17379 13.9082 1.19929 15.1052 1.36676 16.0696C1.54264 17.0824 1.88472 17.9071 2.58862 18.6137C3.29548 19.3232 4.1322 19.6671 5.1617 19.8464C6.14501 20.0176 7.37197 20.0485 8.89677 20.0867L8.95499 20.0882C9.3618 20.0984 9.76778 20.1065 10.1733 20.1125C10.6067 20.1189 10.8234 20.122 10.9148 19.9948C11.0063 19.8675 10.9285 19.6476 10.7731 19.2078C10.5153 18.4781 10.375 17.693 10.375 16.875C10.375 13.009 13.509 9.875 17.375 9.875C18.9069 9.875 20.3238 10.3671 21.4764 11.2018C22.0253 11.5993 22.2997 11.798 22.4635 11.7142C22.6272 11.6303 22.6264 11.3186 22.6248 10.6952C22.6236 10.2584 22.6184 9.82148 22.6091 9.3834L22.6078 9.32467C22.5762 7.84183 22.5507 6.64479 22.3832 5.68042C22.2074 4.66761 21.8653 3.84292 21.1614 3.13634C20.4545 2.42679 19.6178 2.08291 18.5883 1.90362C17.605 1.73237 16.378 1.70156 14.8532 1.66327L14.795 1.66181Z"
      fill="currentColor"
      opacity="0.4"
    />
    <path
      d="M6.01435 6.15137C6.29543 5.67597 6.90869 5.51843 7.3841 5.79951L10.3261 7.53894C11.1664 8.03578 11.5648 8.16031 11.8752 8.16031C12.1855 8.16031 12.5839 8.03578 13.4242 7.53894L16.3662 5.79951C16.8416 5.51843 17.4549 5.67597 17.736 6.15137C18.017 6.62678 17.8595 7.24004 17.3841 7.52112L14.4421 9.26055C13.5666 9.77819 12.7648 10.1603 11.8752 10.1603C10.9855 10.1603 10.1838 9.77819 9.30823 9.26055L6.36622 7.52112C5.89081 7.24004 5.73328 6.62678 6.01435 6.15137Z"
      fill="currentColor"
    />
    <path
      clipRule="evenodd"
      d="M17.375 13.375C15.442 13.375 13.875 14.942 13.875 16.875C13.875 18.808 15.442 20.375 17.375 20.375C17.9273 20.375 18.375 20.8227 18.375 21.375C18.375 21.9273 17.9273 22.375 17.375 22.375C14.3374 22.375 11.875 19.9126 11.875 16.875C11.875 13.8374 14.3374 11.375 17.375 11.375C20.4126 11.375 22.875 13.8374 22.875 16.875V17.375C22.875 18.7557 21.7557 19.875 20.375 19.875C19.6546 19.875 19.0053 19.5703 18.5491 19.0827C18.1991 19.2693 17.7994 19.375 17.375 19.375C15.9943 19.375 14.875 18.2557 14.875 16.875C14.875 15.4943 15.9943 14.375 17.375 14.375C18.7557 14.375 19.875 15.4943 19.875 16.875V17.375C19.875 17.6511 20.0989 17.875 20.375 17.875C20.6511 17.875 20.875 17.6511 20.875 17.375V16.875C20.875 14.942 19.308 13.375 17.375 13.375ZM17.875 16.875C17.875 16.5989 17.6511 16.375 17.375 16.375C17.0989 16.375 16.875 16.5989 16.875 16.875C16.875 17.1511 17.0989 17.375 17.375 17.375C17.6511 17.375 17.875 17.1511 17.875 16.875Z"
      fill="currentColor"
      fillRule="evenodd"
    />
  </svg>
);

const Chat = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    color={"#000000"}
    fill={"none"}
    height={24}
    viewBox="0 0 24 24"
    width={24}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      clipRule="evenodd"
      d="M18 1.25C15.3766 1.25 13.25 3.37665 13.25 6C13.25 8.62335 15.3766 10.75 18 10.75C20.6234 10.75 22.75 8.62335 22.75 6C22.75 3.37665 20.6234 1.25 18 1.25ZM19.5303 5.53033C19.8232 5.23744 19.8232 4.76256 19.5303 4.46967C19.2374 4.17678 18.7626 4.17678 18.4697 4.46967L17.4697 5.46967C17.1768 5.76256 17.1768 6.23744 17.4697 6.53033C17.7626 6.82322 18.2374 6.82322 18.5303 6.53033L19.5303 5.53033Z"
      fill="currentColor"
      fillRule="evenodd"
    />
    <path
      d="M12.5448 1.26307C13.0503 1.28737 13.303 1.29952 13.3803 1.46126C13.4575 1.623 13.289 1.85336 12.952 2.31409C12.1962 3.34747 11.75 4.62163 11.75 6C11.75 9.45178 14.5482 12.25 18 12.25C19.3844 12.25 20.6637 11.7999 21.6995 11.038C22.1751 10.6881 22.413 10.5132 22.577 10.5951C22.7411 10.677 22.7438 10.9373 22.7494 11.4581C22.7498 11.4943 22.75 11.5305 22.75 11.5667C22.75 17.295 17.9048 21.8834 12 21.8834C11.3041 21.8843 10.6103 21.8199 9.92698 21.6916C9.68979 21.647 9.53909 21.6189 9.42696 21.6036C9.34334 21.5907 9.25931 21.6219 9.22775 21.6391C9.11322 21.6935 8.96068 21.7744 8.72714 21.8986C7.29542 22.66 5.62504 22.93 4.01396 22.6303C3.75381 22.5819 3.5384 22.4 3.44713 22.1517C3.35586 21.9033 3.40224 21.6252 3.56917 21.4199C4.03697 20.8445 4.35863 20.1513 4.50088 19.4052C4.53937 19.2 4.45227 18.9213 4.18451 18.6494C2.36972 16.8065 1.25 14.3144 1.25 11.5667C1.25 5.83838 6.09523 1.25 12 1.25C12.1826 1.25 12.3643 1.25439 12.5448 1.26307Z"
      fill="currentColor"
      opacity="0.4"
    />
    <path
      clipRule="evenodd"
      d="M7 12C7 11.4477 7.44772 11 8 11H8.00897C8.56126 11 9.00897 11.4477 9.00897 12C9.00897 12.5523 8.56126 13 8.00897 13H8C7.44772 13 7 12.5523 7 12ZM10.9955 12C10.9955 11.4477 11.4432 11 11.9955 11H12.0045C12.5568 11 13.0045 11.4477 13.0045 12C13.0045 12.5523 12.5568 13 12.0045 13H11.9955C11.4432 13 10.9955 12.5523 10.9955 12Z"
      fill="currentColor"
      fillRule="evenodd"
    />
  </svg>
);

const Calendar = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    color={"#000000"}
    fill={"none"}
    height={24}
    viewBox="0 0 24 24"
    width={24}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M7 2C7 1.44772 6.55228 1 6 1C5.44772 1 5 1.44772 5 2V2.44885C5.38032 2.32821 5.78554 2.24208 6.21533 2.17961C6.46328 2.14357 6.72472 2.11476 7 2.09173V2Z"
      fill="currentColor"
    />
    <path
      d="M19 2.44885C18.6197 2.32821 18.2145 2.24208 17.7847 2.17961C17.5367 2.14357 17.2753 2.11476 17 2.09173V2C17 1.44772 17.4477 1 18 1C18.5523 1 19 1.44772 19 2V2.44885Z"
      fill="currentColor"
    />
    <path
      d="M13.0288 2H10.9712C9.02294 1.99997 7.45141 1.99994 6.21533 2.17961C4.92535 2.3671 3.8568 2.76781 3.01802 3.6746C2.18949 4.57031 1.83279 5.69272 1.66416 7.04866C1.49997 8.36894 1.49998 10.0541 1.5 12.1739V12.8261C1.49998 14.9459 1.49997 16.6311 1.66416 17.9513C1.83279 19.3073 2.18949 20.4297 3.01802 21.3254C3.8568 22.2322 4.92535 22.6329 6.21533 22.8204C7.45142 23.0001 9.02293 23 10.9712 23H11.05C11.6023 23 12.05 22.5523 12.05 22C12.05 21.4477 11.6023 21 11.05 21C9.00425 21 7.57858 20.9975 6.503 20.8412C5.4647 20.6903 4.89956 20.4142 4.48622 19.9673C4.06263 19.5094 3.79327 18.8656 3.64887 17.7045C3.50182 16.5221 3.5 14.9616 3.5 12.7568V12.2432C3.5 11.3942 3.50027 10.6407 3.509 9.96751C3.51487 9.51472 3.51781 9.28833 3.66385 9.14417C3.8099 9 4.03921 9 4.49783 9H19.5021C19.9608 9 20.1901 9 20.3361 9.14416C20.4822 9.28832 20.4851 9.51471 20.491 9.96749C20.4989 10.5753 20.4999 11.2491 20.5 12.0001C20.5 12.5524 20.9478 13.0001 21.5001 13C22.0524 12.9999 22.5 12.5522 22.5 11.9999C22.4998 9.92553 22.4958 8.27198 22.3261 6.97211C22.1536 5.65047 21.7949 4.55343 20.982 3.6746C20.1432 2.76781 19.0747 2.3671 17.7847 2.17961C16.5486 1.99994 14.9771 1.99997 13.0288 2Z"
      fill="currentColor"
      opacity="0.4"
    />
    <path
      clipRule="evenodd"
      d="M17 13C17.5523 13 18 13.4477 18 14V17H21C21.5523 17 22 17.4477 22 18C22 18.5523 21.5523 19 21 19H18V22C18 22.5523 17.5523 23 17 23C16.4477 23 16 22.5523 16 22V19H13C12.4477 19 12 18.5523 12 18C12 17.4477 12.4477 17 13 17H16V14C16 13.4477 16.4477 13 17 13Z"
      fill="currentColor"
      fillRule="evenodd"
    />
  </svg>
);

const Home = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    color={"#000"}
    fill={"none"}
    height={24}
    viewBox="0 0 24 24"
    width={24}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M12 1.25C11.1339 1.25 10.3597 1.52688 9.52381 1.99594C8.7139 2.45043 7.78586 3.12145 6.61887 3.96524L5.10984 5.05632C4.1733 5.73346 3.42528 6.27429 2.86109 6.77487C2.27855 7.29173 1.84274 7.807 1.5663 8.45513C1.28925 9.10469 1.22225 9.77045 1.25941 10.5381C1.29528 11.2789 1.43264 12.1727 1.60393 13.2872L1.91918 15.3387C2.16256 16.9225 2.35635 18.1836 2.64105 19.1662C2.93544 20.1821 3.35016 20.9887 4.0914 21.6052C4.82957 22.2192 5.7089 22.4926 6.78306 22.6231C7.828 22.75 9.14615 22.75 10.8111 22.75H13.1889C14.8539 22.75 16.172 22.75 17.2169 22.6231C18.2911 22.4926 19.1704 22.2192 19.9086 21.6052C20.6499 20.9887 21.0646 20.1821 21.359 19.1662C21.6437 18.1837 21.8374 16.9225 22.0808 15.3387L22.3961 13.2871C22.5674 12.1726 22.7047 11.2789 22.7406 10.5381C22.7778 9.77045 22.7108 9.10469 22.4337 8.45513C22.1573 7.807 21.7215 7.29173 21.1389 6.77487C20.5747 6.2743 19.8267 5.73347 18.8902 5.05633L17.3811 3.96525C16.2142 3.12146 15.2861 2.45043 14.4762 1.99594C13.6403 1.52688 12.8661 1.25 12 1.25Z"
      fill="currentColor"
      opacity="0.4"
    />
    <path
      clipRule="evenodd"
      d="M9 18C9 17.4477 9.44772 17 10 17L14 17C14.5523 17 15 17.4477 15 18C15 18.5523 14.5523 19 14 19L10 19C9.44772 19 9 18.5523 9 18Z"
      fill="currentColor"
      fillRule="evenodd"
    />
  </svg>
);
const Search = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    color={"#000"}
    fill={"none"}
    height={24}
    viewBox="0 0 24 24"
    width={24}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M17.5 17.5L22 22"
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="1.5"
    />
    <path
      d="M20 11C20 6.02944 15.9706 2 11 2C6.02944 2 2 6.02944 2 11C2 15.9706 6.02944 20 11 20C15.9706 20 20 15.9706 20 11Z"
      fill="currentColor"
      opacity="0.4"
    />
    <path
      d="M20 11C20 6.02944 15.9706 2 11 2C6.02944 2 2 6.02944 2 11C2 15.9706 6.02944 20 11 20C15.9706 20 20 15.9706 20 11Z"
      stroke="currentColor"
      strokeLinejoin="round"
      strokeWidth="1.5"
    />
  </svg>
);

const Bell = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    color={"#000"}
    fill={"none"}
    height={24}
    viewBox="0 0 24 24"
    width={24}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M6.91458 10.3577C8.6263 7.85593 12.1899 7.52473 14.3334 9.66817C16.4768 11.8116 16.1456 15.3752 13.6439 17.087L13.5246 17.1685C12.5817 17.8137 12.0179 18.8826 12.0179 20.0251V20.12C12.0179 21.5706 10.2641 22.2971 9.23834 21.2714L2.73017 14.7632C1.70443 13.7375 2.4309 11.9836 3.88151 11.9836H3.97648C5.11897 11.9836 6.18785 11.4198 6.83299 10.4769L6.91458 10.3577Z"
      fill="currentColor"
      opacity="0.4"
    />
    <path
      d="M20.8764 6.36077C21.9584 5.50653 22.0526 3.89942 21.0778 2.92459C20.103 1.94977 18.4959 2.04398 17.6416 3.12603L14.3877 7.24759C14.2571 7.41309 14.1918 7.49585 14.2047 7.58163C14.2177 7.66742 14.3089 7.73 14.4911 7.85515C14.8084 8.0731 15.1112 8.32375 15.3949 8.60751C15.6787 8.89124 15.9293 9.19398 16.1473 9.51129C16.2724 9.69352 16.335 9.78463 16.4208 9.79762C16.5066 9.81062 16.5893 9.74529 16.7548 9.61463L20.8764 6.36077Z"
      fill="currentColor"
    />
    <path
      d="M3.90775 20.0949C3.19352 19.3807 3.06269 18.304 3.51527 17.4569C3.57432 17.3464 3.72224 17.3327 3.81084 17.4213L6.58141 20.1918C6.67 20.2804 6.65625 20.4283 6.54574 20.4874C5.69871 20.94 4.62199 20.8091 3.90775 20.0949Z"
      fill="currentColor"
    />
    <path
      clipRule="evenodd"
      d="M20.1819 15.0168C20.725 15.1172 21.0839 15.6388 20.9836 16.1819C20.7651 17.3642 20.1937 18.4823 19.2676 19.3762C18.397 20.2165 17.3284 20.7474 16.2021 20.9796C15.6612 21.0911 15.1323 20.743 15.0208 20.2021C14.9093 19.6612 15.2574 19.1323 15.7983 19.0208C16.5782 18.86 17.2981 18.4975 17.8786 17.9372C18.4944 17.3429 18.8715 16.6053 19.0168 15.8185C19.1172 15.2754 19.6388 14.9165 20.1819 15.0168Z"
      fill="currentColor"
      fillRule="evenodd"
    />
    <path
      clipRule="evenodd"
      d="M8.97814 3.79131C9.09352 4.33141 8.74921 4.86277 8.20911 4.97814C7.42701 5.14521 6.70061 5.52256 6.11159 6.11159C5.52256 6.70061 5.14521 7.42701 4.97814 8.20911C4.86277 8.74921 4.3314 9.09352 3.79131 8.97814C3.25121 8.86277 2.9069 8.33141 3.02227 7.79131C3.26601 6.65025 3.82237 5.57238 4.69737 4.69737C5.57238 3.82237 6.65025 3.26601 7.79131 3.02227C8.33141 2.9069 8.86277 3.25121 8.97814 3.79131Z"
      fill="currentColor"
      fillRule="evenodd"
    />
  </svg>
);

const User = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    color={"#000"}
    fill={"none"}
    height={24}
    viewBox="0 0 24 24"
    width={24}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M17.8061 14.8372C17.9224 14.9064 18.0661 14.9875 18.2288 15.0793C18.9416 15.4814 20.0191 16.0893 20.7573 16.8118C21.2189 17.2637 21.6576 17.8592 21.7373 18.5888C21.8221 19.3646 21.4837 20.0927 20.8047 20.7396C19.6332 21.8556 18.2274 22.75 16.4091 22.75H7.59086C5.77255 22.75 4.36677 21.8556 3.19532 20.7396C2.5163 20.0927 2.17784 19.3646 2.26265 18.5888C2.34239 17.8592 2.78104 17.2637 3.24271 16.8118C3.98088 16.0893 5.05839 15.4814 5.7712 15.0793L5.77123 15.0792C5.93389 14.9875 6.07755 14.9064 6.19386 14.8372C9.74791 12.7209 14.2521 12.7209 17.8061 14.8372Z"
      fill="currentColor"
    />
    <path
      d="M6.75 6.5C6.75 3.6005 9.1005 1.25 12 1.25C14.8995 1.25 17.25 3.6005 17.25 6.5C17.25 9.39949 14.8995 11.75 12 11.75C9.1005 11.75 6.75 9.39949 6.75 6.5Z"
      fill="currentColor"
      opacity="0.4"
    />
  </svg>
);

const reminderChannels = [
  { id: "email" as keyof ReminderChannels, label: "Email", icon: Email },
  { id: "whatsapp" as keyof ReminderChannels, label: "WhatsApp", icon: Chat },
  {
    id: "googleKalender" as keyof ReminderChannels,
    label: "Google Kalender",
    icon: Calendar,
  },
];

const RencanaBelajarPengingat: React.FC = () => {
  const [tugas, setTugas] = useState<Task[]>([]);
  const [subjekTerpilih, setSubjekTerpilih] = useState<Selection>(new Set([]));
  const [saluranTerpilih, setSaluranTerpilih] = useState<ReminderChannels>({
    email: false,
    whatsapp: false,
    googleKalender: false,
  });
  const [modalTerbuka, setModalTerbuka] = useState<boolean>(false);
  const [jadwalBelajar, setJadwalBelajar] = useState<StudySchedule>({
    jamPerMinggu: "5",
    hariPerMinggu: "5",
    durasiSesi: "1",
  });
  const [reminders, setReminders] = useState<Reminder[]>([]);
  const [error, setError] = useState<string | null>(null);

  const selectedSubject = useMemo(() => {
    const selected = subjekTerpilih as Set<string>;

    return selected.size > 0 ? Array.from(selected)[0] : null;
  }, [subjekTerpilih]);

  const tambahTugas = () => {
    console.log(selectedSubject);
    if (selectedSubject) {
      setTugas([...tugas, { nama: selectedSubject, selesai: false }]);
    }
  };

  const toggleTugas = (index: number) => {
    const tugasUpdate = [...tugas];

    tugasUpdate[index].selesai = !tugasUpdate[index].selesai;
    setTugas(tugasUpdate);
  };

  const toggleSaluran = (saluran: keyof ReminderChannels) => {
    setSaluranTerpilih((prev) => ({
      ...prev,
      [saluran]: !prev[saluran],
    }));

    if (saluran === "googleKalender" && !saluranTerpilih.googleKalender) {
      setModalTerbuka(true);
    }
  };

  const handleJadwalBelajarChange =
    (key: keyof StudySchedule) => (e: React.ChangeEvent<HTMLSelectElement>) => {
      setJadwalBelajar((prev) => ({
        ...prev,
        [key]: e.target.value,
      }));
    };

  const tambahPengingat = () => {
    console.log("Attempting to add reminder");
    setError(null); // Reset error state
    if (!selectedSubject) {
      console.log(subjekTerpilih);
      setError("Mohon pilih subjek terlebih dahulu.");

      return;
    }

    const selectedChannels = Object.entries(saluranTerpilih)
      .filter(([_, value]) => value)
      .map(([key, _]) => key);

    if (selectedChannels.length === 0) {
      setError("Mohon pilih setidaknya satu saluran pengingat.");

      return;
    }

    try {
      const newReminder: Reminder = {
        id: Date.now().toString(),
        subject: selectedSubject,
        channel: selectedChannels.join(", "),
        schedule: `${jadwalBelajar.hariPerMinggu} hari/minggu, ${jadwalBelajar.jamPerMinggu} jam/minggu, ${jadwalBelajar.durasiSesi} jam/sesi`,
      };

      console.log(newReminder);

      setReminders((prevReminders) => [...prevReminders, newReminder]);

      // Reset selected subject and channels
      setSubjekTerpilih(new Set([]));
      setSaluranTerpilih({
        email: false,
        whatsapp: false,
        googleKalender: false,
      });

      console.log("Reminder added successfully");
    } catch (err) {
      console.error("Error adding reminder:", err);
      setError(
        "Terjadi kesalahan saat menambahkan pengingat. Silakan coba lagi.",
      );
    }
  };

  useEffect(() => {
    console.log(reminders);
  }, [reminders]);

  return (
    <div className="max-w-4xl mx-2 my-10">
      <Card>
        <CardHeader className="flex gap-3">
          <div className="flex flex-col">
            <p className="text-xl font-bold">Rencana Belajar Saya</p>
          </div>
        </CardHeader>
        <CardBody>
          <div className="flex gap-2 mb-4">
            <Dropdown>
              <DropdownTrigger>
                <Button className="bg-black text-white" variant="shadow">
                  {selectedSubject || "Pilih subjek"}
                </Button>
              </DropdownTrigger>
              <DropdownMenu
                disallowEmptySelection
                aria-label="Pilih Subjek"
                selectedKeys={subjekTerpilih}
                selectionMode="single"
                variant="flat"
                onSelectionChange={setSubjekTerpilih}
              >
                {subjects.map((subject) => (
                  <DropdownItem key={subject.value}>
                    {subject.label}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </Dropdown>
            <Button
              className="bg-gradient-to-r from-purple-500 to-blue-500 text-white"
              color="primary"
              onPress={tambahTugas}
            >
              Tambah Tugas
            </Button>
          </div>
          <div className="space-y-2">
            {tugas.map((tugas, index) => (
              <div key={index} className="flex items-center gap-2">
                <Checkbox
                  isSelected={tugas.selesai}
                  onValueChange={() => toggleTugas(index)}
                />
                <span
                  className={tugas.selesai ? "line-through text-gray-400" : ""}
                >
                  {tugas.nama}
                </span>
              </div>
            ))}
          </div>
          <div className="mt-4">
            <p className="text-md mb-2 font-bold">Saluran Pengingat</p>
            <div className="space-y-2">
              {reminderChannels.map((channel) => (
                <div key={channel.id} className="flex items-center gap-2">
                  <Checkbox
                    isSelected={saluranTerpilih[channel.id]}
                    onValueChange={() => toggleSaluran(channel.id)}
                  />
                  <span className="flex items-center gap-2">
                    <channel.icon />
                    {channel.label}
                  </span>
                </div>
              ))}
            </div>
          </div>
          <Button
            className="mt-4 w-full bg-gradient-to-r from-green-400 to-blue-500 text-white"
            color="primary"
            onPress={() => setModalTerbuka(true)}
          >
            Atur Pengingat
          </Button>
        </CardBody>
      </Card>

      <Card className="mt-8">
        <CardHeader>
          <h3 className="text-lg font-semibold">Daftar Pengingat</h3>
        </CardHeader>
        <CardBody>
          <Table aria-label="Daftar Pengingat">
            <TableHeader>
              <TableColumn>SUBJEK</TableColumn>
              <TableColumn>SALURAN</TableColumn>
              <TableColumn>JADWAL</TableColumn>
            </TableHeader>
            <TableBody>
              {reminders.map((reminder) => (
                <TableRow key={reminder.id}>
                  <TableCell>{reminder.subject}</TableCell>
                  <TableCell>{reminder.channel}</TableCell>
                  <TableCell>{reminder.schedule}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardBody>
      </Card>

      <Modal
        isOpen={modalTerbuka}
        placement="center"
        onClose={() => setModalTerbuka(false)}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                Atur Jadwal Belajar
              </ModalHeader>
              <ModalBody>
                <Select
                  label="Jam belajar per minggu"
                  placeholder="Pilih jam belajar"
                  value={jadwalBelajar.jamPerMinggu}
                  onChange={handleJadwalBelajarChange("jamPerMinggu")}
                >
                  {[5, 10, 15, 20, 25].map((jam) => (
                    <SelectItem
                      key={jam}
                      textValue={jam.toString()}
                    >
                      {jam} jam
                    </SelectItem>
                  ))}
                </Select>
                <Select
                  label="Hari belajar per minggu"
                  placeholder="Pilih hari belajar"
                  value={jadwalBelajar.hariPerMinggu}
                  onChange={handleJadwalBelajarChange("hariPerMinggu")}
                >
                  {[1, 2, 3, 4, 5, 6, 7].map((hari) => (
                    <SelectItem
                      key={hari}
                      textValue={hari.toString()}
                    >
                      {hari} hari
                    </SelectItem>
                  ))}
                </Select>
                <Select
                  label="Durasi sesi belajar"
                  placeholder="Pilih durasi sesi"
                  value={jadwalBelajar.durasiSesi}
                  onChange={handleJadwalBelajarChange("durasiSesi")}
                >
                  {[0.5, 1, 1.5, 2, 2.5, 3].map((durasi) => (
                    <SelectItem
                      key={durasi}
                      textValue={durasi.toString()}
                    >
                      {durasi} jam
                    </SelectItem>
                  ))}
                </Select>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="light" onPress={onClose}>
                  Batal
                </Button>
                <Button
                  className="bg-gradient-to-r from-cyan-500 to-blue-500 text-white"
                  onPress={() => {
                    tambahPengingat();
                    onClose();
                  }}
                >
                  Simpan
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
      <div className="fixed bottom-0 left-0 right-0 bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)] md:hidden z-50">
        <nav className="flex justify-around items-center h-16">
          <Button isIconOnly className="h-full" variant="light">
            <Home />
          </Button>
          <Button isIconOnly className="h-full" variant="light">
            <Search />
          </Button>
          <Button isIconOnly className="h-full relative" variant="light">
            <Badge
              className="absolute -top-1 -right-1"
              color="danger"
              content=""
              shape="circle"
              size="sm"
            >
              <Bell />
            </Badge>
          </Button>
          <Button isIconOnly className="h-full" variant="light">
            <User />
          </Button>
        </nav>
      </div>
    </div>
  );
};

export default RencanaBelajarPengingat;
