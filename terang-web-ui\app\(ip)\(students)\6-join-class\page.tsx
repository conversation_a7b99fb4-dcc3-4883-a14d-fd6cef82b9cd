"use client";

import React, { useState } from 'react';
import { <PERSON>, CardBody, CardHeader } from "@heroui/card";
import { <PERSON><PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";
import { Progress } from "@heroui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>ead<PERSON>, ModalBody } from "@heroui/modal";
import { Tabs, Tab } from "@heroui/tabs";
import MaterialSection from './material-section';
import { 
  BookOpen, 
  Key, 
  Lock, 
  FileText, 
  Users, 
  ChevronDown, 
  ChevronUp, 
  Play, 
  Book, 
  Search, 
  CheckCircle2, 
  Clock,
  Calendar,
  Download,
  ExternalLink,
  Mail,
  CheckCircle,
  XCircle
} from 'lucide-react';

// Types and Interfaces
interface Content {
  id: string;
  title: string;
  type: 'text' | 'video';
  content: string;
  createdAt: string;
}

interface Chapter {
  id: string;
  title: string;
  order: number;
  contents: Content[];
}

interface Material {
  id: string;
  title: string;
  description: string;
  status: string;
  createdAt: string;
  chapters: Chapter[];
}

interface ClassInvitation {
  id: string;
  classId: number;
  status: 'pending' | 'accepted' | 'declined';
  invitedBy: string;
  invitedAt: string;
}

interface ClassData {
  id: number;
  name: string;
  teacher: string;
  schedule: string;
  enrollmentCode: string;
  materials: Material[];
  thumbnail?: string;
  description?: string;
  startDate?: string;
  endDate?: string;
}

// Mock data with the provided structure
const mockMaterials: Material[] = [
  {
    id: "1",
    title: "Introduction to Biology",
    description: "Comprehensive guide to fundamental biological concepts",
    status: "published",
    createdAt: "2024-01-15",
    chapters: [
      {
        id: "ch1",
        title: "Chapter 1: Cell Biology",
        order: 1,
        contents: [
          {
            id: "text1",
            title: "What are Cells?",
            type: "text",
            content: "Cells are the fundamental units of life...",
            createdAt: "2024-01-15"
          },
          {
            id: "video1",
            title: "Introduction to Cell Structure",
            type: "video",
            content: "https://example.com/biology/cell-structure-intro",
            createdAt: "2024-01-16"
          }
        ]
      },
      {
        id: "ch2",
        title: "Chapter 2: Genetics",
        order: 2,
        contents: [
          {
            id: "text2",
            title: "Basics of Inheritance",
            type: "text",
            content: "Genetics is the scientific study of heredity...",
            createdAt: "2024-01-17"
          },
          {
            id: "video2",
            title: "DNA and Genetic Inheritance",
            type: "video",
            content: "https://example.com/biology/dna-inheritance",
            createdAt: "2024-01-18"
          }
        ]
      }
    ]
  }
];

const mockClasses: ClassData[] = [
  {
    id: 1,
    name: "Biology 101",
    teacher: "Dr. Sarah Smith",
    schedule: "Mon, Wed 10:00 AM",
    enrollmentCode: "BIO101-2024",
    description: "Introduction to fundamental biological concepts and principles",
    startDate: "2024-01-15",
    endDate: "2024-05-30",
    materials: mockMaterials
  },
  {
    id: 2,
    name: "Chemistry Fundamentals",
    teacher: "Prof. James Wilson",
    schedule: "Tue, Thu 2:00 PM",
    enrollmentCode: "CHEM101-2024",
    description: "Basic principles of chemistry and molecular interactions",
    startDate: "2024-01-15",
    endDate: "2024-05-30",
    materials: []
  }
];

const mockInvitations: ClassInvitation[] = [
    {
      id: "inv1",
      classId: 1,
      status: 'pending',
      invitedBy: "Dr. Sarah Smith",
      invitedAt: "2024-01-20"
    },
    {
      id: "inv2",
      classId: 2,
      status: 'pending',
      invitedBy: "Prof. James Wilson",
      invitedAt: "2024-01-21"
    }
  ];

const ClassEnrollmentPage: React.FC = () => {
    // State management
    const [selectedClass, setSelectedClass] = useState<ClassData | null>(null);
    const [enrollmentCode, setEnrollmentCode] = useState<string>("");
    const [showEnrollModal, setShowEnrollModal] = useState<boolean>(false);
    const [enrolledClasses, setEnrolledClasses] = useState<ClassData[]>([]);
    const [activeTab, setActiveTab] = useState<string>("available");
    const [error, setError] = useState<string>("");
    const [searchTerm, setSearchTerm] = useState<string>("");
    const [selectedMaterial, setSelectedMaterial] = useState<string | null>(null);
    const [invitations, setInvitations] = useState<ClassInvitation[]>(mockInvitations);
  
    // Handlers
    const handleEnrollment = () => {
      const classToEnroll = mockClasses.find(c => c.id === selectedClass?.id);
      if (classToEnroll?.enrollmentCode === enrollmentCode) {
        setEnrolledClasses(prev => [...prev, classToEnroll]);
        setShowEnrollModal(false);
        setError("");
        setEnrollmentCode("");
      } else {
        setError("Invalid enrollment code. Please try again.");
      }
    };
  
    const handleInvitationResponse = (invitationId: string, accept: boolean) => {
      const invitation = invitations.find(inv => inv.id === invitationId);
      if (!invitation) return;
  
      // Find the class from the invitation
      const classToEnroll = mockClasses.find(c => c.id === invitation.classId);
      if (!classToEnroll) return;
  
      if (accept) {
        // Add to enrolled classes
        setEnrolledClasses(prev => [...prev, classToEnroll]);
      }
  
      // Update invitation status
      setInvitations(prev => prev.map(inv => 
        inv.id === invitationId 
          ? { ...inv, status: accept ? 'accepted' : 'declined' }
          : inv
      ));
    };
  
    const handleTabChange = (key: string | number) => {
      setActiveTab(String(key));
    };
  
    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(e.target.value);
    };
  
    // Filtered classes based on search
    const filteredClasses = mockClasses.filter(c => 
      !enrolledClasses.find(ec => ec.id === c.id) &&
      (c.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
       c.teacher.toLowerCase().includes(searchTerm.toLowerCase()) ||
       c.description?.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  
    // Pending invitations
    const pendingInvitations = invitations.filter(inv => inv.status === 'pending');
  
    return (
      <div className="min-h-screen p-8 bg-gray-50">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <BookOpen className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold">Class Enrollment & Materials</h1>
                <p className="text-sm text-gray-600">Browse available classes or access your enrolled courses</p>
              </div>
            </div>
          </div>
  
          {/* Search Bar - Only show in available classes tab */}
          {activeTab === "available" && (
            <div className="relative">
              <Input
                placeholder="Search classes..."
                value={searchTerm}
                onChange={handleSearch}
                startContent={<Search className="w-4 h-4 text-gray-400" />}
                className="max-w-md"
              />
            </div>
          )}
  
          {/* Tabs */}
          <Tabs 
            selectedKey={activeTab} 
            onSelectionChange={handleTabChange}
            className="mb-6"
          >
            <Tab 
              key="invitations" 
              title={
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4" />
                  <span>Invitations</span>
                  {pendingInvitations.length > 0 && (
                    <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                      {pendingInvitations.length}
                    </span>
                  )}
                </div>
              }
            >
              <div className="grid gap-4 mt-4">
                {invitations.map((invitation) => {
                  const classData = mockClasses.find(c => c.id === invitation.classId);
                  if (!classData) return null;
  
                  return (
                    <Card key={invitation.id} className="hover:bg-gray-50/50 transition-colors">
                      <CardBody className="p-6">
                        <div className="flex justify-between items-start">
                          <div className="space-y-3">
                            <div>
                              <h3 className="text-lg font-semibold">{classData.name}</h3>
                              <p className="text-gray-600 mt-1">
                                Invited by {invitation.invitedBy} on {new Date(invitation.invitedAt).toLocaleDateString()}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <p className="text-gray-600 flex items-center">
                                <Users className="w-4 h-4 mr-2" />
                                Instructor: {classData.teacher}
                              </p>
                              <p className="text-gray-600 flex items-center">
                                <Clock className="w-4 h-4 mr-2" />
                                Schedule: {classData.schedule}
                              </p>
                            </div>
                          </div>
                          {invitation.status === 'pending' ? (
                            <div className="flex space-x-2">
                              <Button 
                                color="success"
                                onPress={() => handleInvitationResponse(invitation.id, true)}
                                className="self-start"
                                startContent={<CheckCircle className="w-4 h-4" />}
                              >
                                Accept
                              </Button>
                              <Button 
                                color="danger"
                                variant="light"
                                onPress={() => handleInvitationResponse(invitation.id, false)}
                                className="self-start"
                                startContent={<XCircle className="w-4 h-4" />}
                              >
                                Decline
                              </Button>
                            </div>
                          ) : (
                            <span className={`px-3 py-1 rounded-full text-sm ${
                              invitation.status === 'accepted' 
                                ? 'bg-green-100 text-green-700'
                                : 'bg-red-100 text-red-700'
                            }`}>
                              {invitation.status.charAt(0).toUpperCase() + invitation.status.slice(1)}
                            </span>
                          )}
                        </div>
                      </CardBody>
                    </Card>
                  );
                })}
                {invitations.length === 0 && (
                  <div className="text-center py-12 bg-gray-50/50 rounded-lg">
                    <Mail className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 text-lg">No invitations</p>
                    <p className="text-gray-500">You will see class invitations from teachers here</p>
                  </div>
                )}
              </div>
            </Tab>
          <Tab 
            key="available" 
            title={
              <div className="flex items-center space-x-2">
                <BookOpen className="w-4 h-4" />
                <span>Available Classes</span>
              </div>
            }
          >
            <div className="grid gap-4 mt-4">
              {filteredClasses.map((classItem) => (
                <Card key={classItem.id} className="hover:bg-gray-50/50 transition-colors">
                  <CardBody className="p-6">
                    <div className="flex justify-between items-start">
                      <div className="space-y-3">
                        <div>
                          <h3 className="text-lg font-semibold">{classItem.name}</h3>
                          <p className="text-gray-600 mt-1">{classItem.description}</p>
                        </div>
                        <div className="space-y-1">
                          <p className="text-gray-600 flex items-center">
                            <Users className="w-4 h-4 mr-2" />
                            Instructor: {classItem.teacher}
                          </p>
                          <p className="text-gray-600 flex items-center">
                            <Clock className="w-4 h-4 mr-2" />
                            Schedule: {classItem.schedule}
                          </p>
                          <p className="text-gray-600 flex items-center">
                            <Calendar className="w-4 h-4 mr-2" />
                            Duration: {classItem.startDate} - {classItem.endDate}
                          </p>
                        </div>
                      </div>
                      <Button 
                        color="primary"
                        onPress={() => {
                          setSelectedClass(classItem);
                          setShowEnrollModal(true);
                        }}
                        className="self-start"
                        startContent={<Lock className="w-4 h-4" />}
                      >
                        Enroll Now
                      </Button>
                    </div>
                  </CardBody>
                </Card>
              ))}
              {filteredClasses.length === 0 && (
                <div className="text-center py-12 bg-gray-50/50 rounded-lg">
                  <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 text-lg">No available classes found</p>
                  <p className="text-gray-500">Try adjusting your search criteria</p>
                </div>
              )}
            </div>
          </Tab>

          <Tab 
            key="enrolled" 
            title={
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>My Classes</span>
              </div>
            }
          >
            <div className="grid gap-6 mt-4">
              {enrolledClasses.map((classItem) => (
                <Card key={classItem.id}>
                  <CardHeader className="border-b">
                    <div className="flex justify-between items-center w-full">
                      <div>
                        <h3 className="text-lg font-semibold">{classItem.name}</h3>
                        <p className="text-gray-600">Instructor: {classItem.teacher}</p>
                      </div>
                      <Button 
                        color="primary" 
                        variant="light"
                        startContent={<Book className="w-4 h-4" />}
                      >
                        Course Materials
                      </Button>
                    </div>
                  </CardHeader>
                  <CardBody>
                    <div className="space-y-4">
                      {classItem.materials.map((material) => (
                        <MaterialSection 
                          key={material.id}
                          material={material}
                          isExpanded={selectedMaterial === material.id}
                          onToggle={() => setSelectedMaterial(
                            selectedMaterial === material.id ? null : material.id
                          )}
                        />
                      ))}
                      {classItem.materials.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                          No materials available yet
                        </div>
                      )}
                    </div>
                  </CardBody>
                </Card>
              ))}
              {enrolledClasses.length === 0 && (
                <div className="text-center py-12 bg-gray-50/50 rounded-lg">
                  <Lock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600 text-lg">You have not enrolled in any classes yet</p>
                  <p className="text-gray-500">Check the Available Classes tab to get started!</p>
                </div>
              )}
            </div>
          </Tab>
        </Tabs>

        {/* Enrollment Modal */}
        <Modal
          isOpen={showEnrollModal}
          onClose={() => {
            setShowEnrollModal(false);
            setError("");
            setEnrollmentCode("");
          }}
          size="lg"
        >
          <ModalContent>
            <ModalHeader>
              <div>
                <h3 className="text-xl font-semibold">Enroll in {selectedClass?.name}</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Enter the enrollment code provided by your instructor
                </p>
              </div>
            </ModalHeader>
            <ModalBody className="pb-6">
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                  <p className="text-gray-700 flex items-center">
                    <Users className="w-4 h-4 mr-2" />
                    Instructor: {selectedClass?.teacher}
                  </p>
                  <p className="text-gray-700 flex items-center">
                    <Clock className="w-4 h-4 mr-2" />
                    Schedule: {selectedClass?.schedule}
                  </p>
                </div>
                <Input
                  label="Enrollment Code"
                  placeholder="Enter code (e.g., BIO101-2024)"
                  value={enrollmentCode}
                  onChange={(e) => setEnrollmentCode(e.target.value)}
                  startContent={<Key className="w-4 h-4 text-gray-400" />}
                  isInvalid={!!error}
                  errorMessage={error}
                />
                <Button 
                  color="primary" 
                  className="w-full"
                  onPress={handleEnrollment}
                  size="lg"
                >
                  Complete Enrollment
                </Button>
              </div>
            </ModalBody>
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
};

export default ClassEnrollmentPage;