"use client";

import React, { useEffect, useState } from 'react';
import { Tooltip } from "@heroui/react";
import { Award01Icon, CheckmarkBadge01Icon, Medal01Icon, FireIcon, Timer01Icon, Rocket01Icon, StarIcon } from "hugeicons-react";
import { AvailableExam, fetchCompletionData } from './actions';

// TypeScript interface for completed exam data
interface CompletedExam {
  examId: string;
  examName: string;
  subject: string;
  completionCount: number;
  lastSessionId: string;
  lastEndTime: string;
  lastScore: number;
}

interface ExamCompletionIndicatorProps {
  examId: string;
  className?: string;
  alwaysShow?: boolean; // If true, will show status even if not completed
  onStartExam?: () => void; // Callback for starting an exam
  trialCompletionData: AvailableExam | null;
}

const ExamCompletionIndicator: React.FC<ExamCompletionIndicatorProps> = ({
  examId,
  className = "",
  alwaysShow = true,
  onStartExam,
  trialCompletionData
}) => {
  const [completionData, setCompletionData] = useState<CompletedExam | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Skip unnecessary async function and loading state
    if (examId && trialCompletionData) {
      const examCompletionData = Array.isArray(trialCompletionData) 
        ? trialCompletionData.find(exam => exam.examId === examId)
        : null;
      
      setCompletionData(examCompletionData || null);
      setLoading(false);
    } else {
      setCompletionData(null);
      setLoading(false);
    }
  }, [examId, trialCompletionData]);

  // If not always showing and no completion data or still loading, don't render
  if ((loading || !completionData) && !alwaysShow) {
    return null;
  }

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  // Show different UI based on completion status
  if (!completionData) {
    // Not completed yet - show gradient with text and icon
    return (
      <div className={`mt-2 ${className}`}>
        <div className="relative w-full">
          {/* Gradient background for not completed */}
          <div className="w-full h-12 bg-gradient-to-r from-purple-200 to-blue-200 rounded-md flex items-center justify-center">
            <Tooltip content="Selesaikan untuk mendapatkan lencana prestasi">
              <button 
                className="w-full h-full flex items-center justify-between px-3 cursor-pointer" 
                onClick={onStartExam}
              >
                <div className="flex items-center gap-2">
                  <Timer01Icon className="w-4 h-4 text-purple-600" />
                  <span className="text-sm font-medium text-purple-700">Belum Selesai</span>
                </div>
                <StarIcon className="w-4 h-4 text-blue-500" />
              </button>
            </Tooltip>
          </div>
        </div>
      </div>
    );
  }

  // Completed - show achievement badge
  const getCompletionBadge = () => {
    if (completionData.completionCount >= 5) {
      return {
        icon: <Medal01Icon className="w-4 h-4" />,
        color: "text-amber-500",
        bgColor: "bg-amber-100",
        label: "Master"
      };
    } else if (completionData.completionCount >= 3) {
      return {
        icon: <FireIcon className="w-4 h-4" />,
        color: "text-orange-500",
        bgColor: "bg-orange-100",
        label: "Pro"
      };
    } else {
      return {
        icon: <CheckmarkBadge01Icon className="w-4 h-4" />,
        color: "text-green-500",
        bgColor: "bg-green-100",
        label: "Selesai"
      };
    }
  };

  const badge = getCompletionBadge();

  return (
    <div className={`mt-2 ${className}`}>
      <div className="ribbon-container relative w-full">
        {/* Decorative element - gradient ribbon */}
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/20 to-amber-500/20 rounded-md h-full w-full" />
        
        <div className="flex items-center justify-between w-full p-2 rounded-md border border-gray-100">
          <Tooltip content={`Terakhir diselesaikan: ${formatDate(completionData.lastEndTime)}`}>
            <div className={`flex items-center gap-2 px-2 py-1 rounded-full ${badge.bgColor}`}>
              {badge.icon}
              <span className={`text-sm font-medium ${badge.color}`}>{badge.label}</span>
            </div>
          </Tooltip>

          <Tooltip content={`Telah diselesaikan ${completionData.completionCount} kali`}>
            <div className="flex items-center gap-1">
              <Award01Icon className="w-4 h-4 text-amber-500" />
              <span className="text-sm font-bold text-amber-600">{completionData.completionCount}x</span>
            </div>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};

export default ExamCompletionIndicator;