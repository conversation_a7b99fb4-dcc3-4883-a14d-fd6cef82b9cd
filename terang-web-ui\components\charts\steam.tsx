import React, { useState, useMemo } from 'react';
import Chart from 'react-apexcharts';
import { <PERSON>, CardBody, CardHeader } from "@heroui/react";
import { Select, SelectItem, Button } from "@heroui/react";
import { ApexOptions } from 'apexcharts';
import { CircleArrowLeftDoubleIcon, CircleArrowRightDoubleIcon } from './chevron';

interface ExamProgressData {
  sessionId: string;
  startTime: string;
  examType: string;
  subjectScores: {
    [key: string]: number; // Dynamic key-value pairs for different subjects
  };
}

interface PaginatedProgressChartProps {
  progressData: ExamProgressData[];
}

type DataRange = '10' | '20' | '50' | 'all';

const DATA_RANGES: Record<DataRange, number> = {
  '10': 10,
  '20': 20,
  '50': 50,
  'all': Number.POSITIVE_INFINITY
};

const PaginatedProgressChart: React.FC<PaginatedProgressChartProps> = ({
  progressData
}) => {
  const [dataRange, setDataRange] = useState<DataRange>('10');
  const [startIndex, setStartIndex] = useState(0);
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);

  // Find all available subjects from the data
  const availableSubjects = useMemo(() => {
    const subjects = new Set<string>();
    progressData.forEach(item => {
      Object.keys(item.subjectScores || {}).forEach(subject => {
        subjects.add(subject);
      });
    });
    return Array.from(subjects);
  }, [progressData]);

  // Initialize selected subjects with all available subjects if empty
  React.useEffect(() => {
    if (selectedSubjects.length === 0 && availableSubjects.length > 0) {
      setSelectedSubjects(availableSubjects);
    }
  }, [availableSubjects, selectedSubjects]);

  // Define type for processed item with string index signature
  type ProcessedDataItem = {
    date: string;
    examType: string;
    [key: string]: string | number; // Allow string indexing
  };

  // Process data without time filtering, just sort by date
  const processedData = useMemo(() => {
    return [...progressData]
      .map(item => {
        // Create base object with proper type
        const processedItem: ProcessedDataItem = {
          date: item.startTime,
          examType: item.examType
        };
        
        // Add all subject scores as properties
        Object.entries(item.subjectScores || {}).forEach(([key, value]) => {
          processedItem[key] = value;
        });
        
        return processedItem;
      })
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [progressData]);

  // Get number of items to display based on selected range
  const itemsToShow = useMemo(() => {
    return dataRange === 'all' ? processedData.length : DATA_RANGES[dataRange];
  }, [dataRange, processedData.length]);

  // Get current window of data
  const currentData = useMemo(() => {
    const end = Math.min(startIndex + itemsToShow, processedData.length);
    return processedData.slice(startIndex, end);
  }, [processedData, startIndex, itemsToShow]);

  // Generate series dynamically based on available subjects
  const series = useMemo(() => {
    return selectedSubjects.map(subject => {
      return {
        name: subject.replace('_',' ').toUpperCase(),
        data: [...currentData].reverse().map(item => {
          const value = item[subject];
          return typeof value === 'number' ? Number(value.toFixed(2)) : 0;
        }),
      };
    });
  }, [currentData, selectedSubjects]);

  const options: ApexOptions = {
    chart: {
      type: "area",
      animations: {
        enabled: true,
        speed: 300,
        animateGradually: {
          enabled: true,
          delay: 150
        },
        dynamicAnimation: {
          enabled: true,
          speed: 300
        }
      },
      sparkline: {
        enabled: false,
      },
      brush: {
        enabled: false,
      },
      id: "basic-bar",
      foreColor: "#666",
      stacked: false,
      toolbar: {
        show: false,
      },
    },
    xaxis: {
      categories: [...currentData].reverse().map(item => {
        const date = new Date(item.date);
        return date.toLocaleDateString('id-ID', { 
          day: '2-digit',
          month: 'short',
          hour: '2-digit',
          minute: '2-digit'
        });
      }),
      labels: {
        style: {
          colors: "#666",
        },
        rotate: -45,
      },
    },
    yaxis: {
      labels: {
        formatter: (value) => `${value.toFixed(2)}%`,
        style: {
          colors: "#666",
        },
      },
      min: 0,
      max: 100,
    },
    tooltip: {
      enabled: true,
      y: {
        formatter: (value) => `${value.toFixed(2)}%`,
      },
    },
    grid: {
      show: true,
      borderColor: "#f0f0f0",
      strokeDashArray: 0,
      position: "back",
    },
    stroke: {
      curve: "smooth",
      width: 2,
    },
    fill: {
      type: "gradient",
      gradient: {
        shadeIntensity: 1,
        opacityFrom: 0.7,
        opacityTo: 0.9,
        stops: [0, 90, 100],
      },
    },
    markers: {
      size: 4,
    },
    // Generate colors dynamically based on selected subjects
    colors: selectedSubjects.map((_, index) => {
      const predefinedColors = ['#3b82f6', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899'];
      return predefinedColors[index % predefinedColors.length];
    }),
    dataLabels: {
      enabled: false,
    },
    legend: {
      position: "top",
      horizontalAlign: "left",
    },
  };

  const handleSubjectToggle = (subject: string) => {
    setSelectedSubjects(prev => {
      if (prev.includes(subject)) {
        return prev.filter(s => s !== subject);
      } else {
        return [...prev, subject];
      }
    });
  };

  const handleDataRangeChange = (value: string) => {
    setDataRange(value as DataRange);
    setStartIndex(0);
  };

  const handleNext = () => {
    if (startIndex > 0) {
      setStartIndex(Math.max(0, startIndex - itemsToShow));
    }
  };

  const handlePrev = () => {
    if (startIndex + itemsToShow < processedData.length) {
      setStartIndex(startIndex + itemsToShow);
    }
  };

  const canGoNext = startIndex > 0;
  const canGoPrev = startIndex + itemsToShow < processedData.length;

  // Group by exam type for filtering
  const examTypes = useMemo(() => {
    const types = new Set<string>();
    progressData.forEach(item => {
      if (item.examType) {
        types.add(item.examType);
      }
    });
    return Array.from(types);
  }, [progressData]);

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-col md:flex-row justify-between items-center px-6 py-4 gap-4">
        <h4 className="text-lg font-semibold">Peningkatan Nilaimu</h4>
        <div className="flex flex-wrap gap-4 items-center">
          {/* Subject filter chips */}
          <div className="flex flex-wrap gap-2">
            {availableSubjects.map(subject => (
              <Button
                key={subject}
                size="sm"
                variant={selectedSubjects.includes(subject) ? "solid" : "bordered"}
                onPress={() => handleSubjectToggle(subject)}
                className="capitalize"
              >
                {typeof subject === 'string' ? subject.replace('_',' ').toUpperCase() : ''}
              </Button>
            ))}
          </div>
          
          {/* Data range selector */}
          <Select 
            label="Time Range" 
            size="sm"
            selectedKeys={[dataRange]}
            onChange={(e) => handleDataRangeChange(e.target.value)}
            className="w-32"
          >
            <SelectItem key="10">10 Records</SelectItem>
            <SelectItem key="20">20 Records</SelectItem>
            <SelectItem key="50">50 Records</SelectItem>
            <SelectItem key="all">All Records</SelectItem>
          </Select>
        </div>
      </CardHeader>
      <CardBody className="px-2 py-0">
        {progressData.length > 0 ? (
          <>
            <div className="w-full">
              <Chart 
                height={400} 
                options={options} 
                series={series} 
                type="area" 
              />
            </div>
            <div className="flex justify-center gap-2 mt-4 mb-2">
              <Button
                isIconOnly
                size="sm"
                variant="light"
                onPress={handlePrev}
                isDisabled={!canGoPrev}
              >
                <CircleArrowLeftDoubleIcon width={24} height={24} />
              </Button>
              <Button
                isIconOnly
                size="sm"
                variant="light"
                onPress={handleNext}
                isDisabled={!canGoNext}
              >
                <CircleArrowRightDoubleIcon width={24} height={24} />
              </Button>
            </div>
          </>
        ) : (
          <div className="flex justify-center items-center h-64">
            <p className="text-gray-500">No progress data available</p>
          </div>
        )}
      </CardBody>
    </Card>
  );
};

export default PaginatedProgressChart;