import { SVGProps } from 'react';

export const CircleArrowRightDoubleIcon = ({ 
  width = 24, 
  height = 24, 
  color = "#000000", 
  ...props 
}: SVGProps<SVGSVGElement>) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 24 24" 
    width={width} 
    height={height} 
    color={color} 
    fill="none" 
    {...props}
  >
    <path 
      opacity="0.4" 
      d="M1.25 12C1.25 6.06294 6.06294 1.25 12 1.25C17.9371 1.25 22.75 6.06294 22.75 12C22.75 17.9371 17.9371 22.75 12 22.75C6.06294 22.75 1.25 17.9371 1.25 12Z" 
      fill="currentColor" 
    />
    <path 
      d="M12.3656 8.28019C12.6506 7.94435 13.1519 7.90483 13.4852 8.19192L13.93 8.58851C14.193 8.82852 14.5461 9.15968 14.9013 9.52028C15.2531 9.87746 15.6229 10.2796 15.9102 10.6597C16.0533 10.8491 16.1894 11.0507 16.293 11.2516C16.3877 11.4353 16.5 11.702 16.5 12C16.5 12.2979 16.3877 12.5647 16.293 12.7483C16.1894 12.9493 16.0533 13.1508 15.9102 13.3403C15.6229 13.7204 15.2531 14.1225 14.9013 14.4797C14.5461 14.8403 14.193 15.1715 13.93 15.4115L13.4853 15.8081C13.1519 16.0952 12.6506 16.0557 12.3656 15.7198C12.0806 15.384 12.1199 14.879 12.4532 14.5919L12.8637 14.2257C13.1138 13.9974 13.4449 13.6867 13.7739 13.3527C14.1062 13.0153 14.4205 12.6701 14.6464 12.3712C14.7598 12.2211 14.8375 12.1002 14.8836 12.0109L14.8891 12L14.8836 11.9891C14.8375 11.8998 14.7598 11.7789 14.6464 11.6288C14.4205 11.3298 14.1062 10.9847 13.7739 10.6473C13.4449 10.3133 13.1138 10.0026 12.8637 9.77434L12.4533 9.40818C12.12 9.12108 12.0806 8.61601 12.3656 8.28019Z" 
      fill="currentColor" 
    />
    <path 
      d="M9.75484 8.58851C10.0179 8.82852 10.3709 9.15968 10.7261 9.52028C11.078 9.87746 11.4478 10.2796 11.735 10.6597C11.8782 10.8491 12.0143 11.0507 12.1179 11.2516C12.2126 11.4353 12.3249 11.702 12.3249 12C12.3249 12.2979 12.2126 12.5647 12.1179 12.7483C12.0143 12.9493 11.8782 13.1508 11.735 13.3403C11.4478 13.7204 11.078 14.1225 10.7262 14.4797C10.371 14.8403 10.0179 15.1715 9.75487 15.4115L9.31015 15.8081C9.07456 16.011 8.74333 16.0572 8.46184 15.9266C8.18034 15.7959 8.00002 15.5122 8.00002 15.2L8 8.80001C8 8.48778 8.18031 8.20408 8.46181 8.07343C8.7433 7.94277 9.07453 7.98903 9.31012 8.19192L9.75484 8.58851Z" 
      fill="currentColor" 
    />
  </svg>
);

export const CircleArrowLeftDoubleIcon = ({ 
  width = 24, 
  height = 24, 
  color = "#000000", 
  ...props 
}: SVGProps<SVGSVGElement>) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 24 24" 
    width={width} 
    height={height} 
    color={color} 
    fill="none" 
    {...props}
  >
    <path 
      opacity="0.4" 
      d="M22.75 12C22.75 6.06294 17.9371 1.25 12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12Z" 
      fill="currentColor" 
    />
    <path 
      d="M11.6344 8.28019C11.3494 7.94435 10.8481 7.90483 10.5148 8.19192L10.07 8.58851C9.80702 8.82852 9.45394 9.15968 9.09875 9.52028C8.74693 9.87746 8.37707 10.2796 8.08985 10.6597C7.94668 10.8491 7.81057 11.0507 7.70697 11.2516C7.6123 11.4353 7.5 11.702 7.5 12C7.5 12.2979 7.6123 12.5647 7.70697 12.7483C7.81057 12.9493 7.94668 13.1508 8.08985 13.3403C8.37707 13.7204 8.74692 14.1225 9.09873 14.4797C9.45392 14.8403 9.807 15.1715 10.07 15.4115L10.5147 15.8081C10.8481 16.0952 11.3494 16.0557 11.6344 15.7198C11.9194 15.384 11.8801 14.879 11.5468 14.5919L11.1363 14.2257C10.8862 13.9974 10.5551 13.6867 10.2261 13.3527C9.89379 13.0153 9.57949 12.6701 9.35359 12.3712C9.2402 12.2211 9.16251 12.1002 9.11645 12.0109L9.11092 12L9.11644 11.9891C9.1625 11.8998 9.2402 11.7789 9.35359 11.6288C9.57948 11.3298 9.89379 10.9847 10.2261 10.6473C10.5551 10.3133 10.8862 10.0026 11.1363 9.77434L11.5467 9.40818C11.88 9.12108 11.9194 8.61601 11.6344 8.28019Z" 
      fill="currentColor" 
    />
    <path 
      d="M14.2452 8.58851C13.9821 8.82852 13.6291 9.15968 13.2739 9.52028C12.922 9.87746 12.5522 10.2796 12.265 10.6597C12.1218 10.8491 11.9857 11.0507 11.8821 11.2516C11.7874 11.4353 11.6751 11.702 11.6751 12C11.6751 12.2979 11.7874 12.5647 11.8821 12.7483C11.9857 12.9493 12.1218 13.1508 12.265 13.3403C12.5522 13.7204 12.922 14.1225 13.2738 14.4797C13.629 14.8403 13.9821 15.1715 14.2451 15.4115L14.6899 15.8081C14.9254 16.011 15.2567 16.0572 15.5382 15.9266C15.8197 15.7959 16 15.5122 16 15.2L16 8.80001C16 8.48778 15.8197 8.20408 15.5382 8.07343C15.2567 7.94277 14.9255 7.98903 14.6899 8.19192L14.2452 8.58851Z" 
      fill="currentColor" 
    />
  </svg>
);