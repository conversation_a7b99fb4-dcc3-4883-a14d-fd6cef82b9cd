import {
  Body,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";

export interface AnnouncementProps {
  className?: string;
  teacherName?: string;
}

const logoUrl = `https://cdn.terang.ai/images/logo/logo-terang-ai-combined.png`;
const currentYear = new Date().getFullYear();

const Announcement: React.FC<AnnouncementProps> = ({
  className = "",
  teacherName = "",
}) => (
  <Html>
    <Head />
    <Preview>Pengumuman: Hari kamis minggu depan kelas pengganti</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={coverSection}>
          <Section style={imageSection}>
            <Img
              alt="Terang AI's Logo"
              src={logoUrl}
              style={{
                margin: "0 auto",
                display: "block",
                maxWidth: "250px",
                height: "auto",
              }}
            />
          </Section>
          <Section style={upperSection}>
            <Heading style={h1}>Hari kamis minggu depan kelas pengganti</Heading>
            
            <Section style={metadataSection}>
              <Text style={priorityBadge}>high priority</Text>
              <Text style={audienceBadge}>All Students</Text>
              <Text style={dateBadge}>25/12/2024</Text>
            </Section>

            <Text style={mainText}>
              Halo semua peserta didik, pada hari kamis minggu depan tidak diadakan kelas seperti biasanya, 
              melainkan hanya kelas pengganti materi yang belum selesai pada pekan lalu, dimohon untuk 
              disiapkan catatan. Terima kasih.
            </Text>

            <Section style={buttonContainer}>
              <Link href="https://terang.ai" style={button}>
                Buka di Terang AI
              </Link>
            </Section>
          </Section>
          <Hr />
          <Section style={lowerSection}>
            <Text style={cautionText}>
              Email ini dikirim karena Anda terdaftar di kelas {className}. Jika Anda tidak terdaftar di kelas ini, 
              silakan abaikan email ini atau hubungi guru Anda.
            </Text>
          </Section>
        </Section>
        <Text style={footerText}>
          This message was produced and distributed by Terang.ai, Inc., 
          Gedung Bursa Efek Indonesia (IDX) Tower 1, SCBD Jl. Jendral Sudirman Kav 52-53, 
          Kebayoran Baru, Jakarta Selatan, DKI Jakarta, Indonesia 12190 © {currentYear}, 
          All rights reserved.
          Terang.ai is a registered copyright of{" "}
          <Link href="https://terang.ai" style={link}>
            Terang.ai
          </Link>
        </Text>
      </Container>
    </Body>
  </Html>
);

export default Announcement;

// Base styles
const main = {
  backgroundColor: "#fff",
  color: "#212121",
};

const container = {
  padding: "20px",
  margin: "0 auto",
  backgroundColor: "#eee",
  maxWidth: "600px",
};

const text = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "16px",
  margin: "24px 0",
  lineHeight: "1.5",
};

// Component styles
const h1 = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "24px",
  fontWeight: "bold",
  marginBottom: "15px",
};

const link = {
  color: "#2754C5",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  textDecoration: "underline",
};

const metadataSection = {
  display: "flex",
  gap: "8px",
  flexWrap: "wrap" as const,
  marginBottom: "20px",
};

const baseBadge = {
  padding: "4px 12px",
  borderRadius: "16px",
  fontSize: "14px",
  margin: "0",
};

const priorityBadge = {
  ...baseBadge,
  backgroundColor: "#FFE4E4",
  color: "#FF4D4D",
};

const audienceBadge = {
  ...baseBadge,
  backgroundColor: "#F3F4F6",
  color: "#4B5563",
};

const dateBadge = {
  ...text,
  color: "#6B7280",
  margin: "4px 0 0 auto",
  fontSize: "14px",
};

const imageSection = {
  backgroundColor: "#252f3d",
  alignItems: "center",
  justifyContent: "center",
  padding: "20px 0",
  textAlign: "center" as const,
};

const coverSection = { backgroundColor: "#fff" };
const upperSection = { padding: "25px 35px" };
const lowerSection = { padding: "25px 35px" };

const buttonContainer = {
  marginTop: "32px",
  alignItems: "center",
  justifyContent: "center",
  textAlign: "center" as const,
};

const button = {
  alignItems: "center",
  justifyContent: "center",
  textAlign: "center" as const,
  background: "linear-gradient(135deg, #89CFF0, #0095ff)",
  border: "none",
  fontSize: "16px",
  lineHeight: "1.2",
  padding: "12px 24px",
  borderRadius: "8px",
  maxWidth: "200px",
  color: "#fff",
  cursor: "pointer",
  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
  textDecoration: "none",
};

const mainText = { 
  ...text, 
  marginBottom: "14px",
  whiteSpace: "pre-wrap" as const,
};

const cautionText = { 
  ...text, 
  margin: "0px",
  fontSize: "14px",
  color: "#6B7280",
};

const footerText = {
  ...text,
  fontSize: "12px",
  padding: "0 20px",
};