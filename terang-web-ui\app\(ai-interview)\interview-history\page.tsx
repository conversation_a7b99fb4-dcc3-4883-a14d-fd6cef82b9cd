"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { getUserSessions } from "./actions";
import { getOrTriggerGrading } from "../ai-interview-result/[interviewId]/[sessionId]/components/actions";
import { getScoreColorClass, getScoreBgClass } from "@/app/lib/mocks/interview-scores";

import {
  Clock,
  Calendar,
  Check,
  X,
  AlertTriangle,
  Info,
  ArrowRight,
  Repeat,
  RefreshCw,
  Award,
  BarChart3
} from "lucide-react";

interface ScoreData {
  overallScore: number;
  maxScore: number;
  recommendation: string;
  readinessLevel: number;
  categoryScores: Array<{
    category: string;
    score: number;
    maxScore: number;
  }>;
}

interface InterviewHistoryItem {
  sessionId: string;
  interviewId: string;
  startTime: number;
  duration: string;
  status: 'active' | 'completed' | 'force_finished' | 'cancelled' | 'disconnected_timeout' | 'disconnected_grace';
  interviewName?: string;
  interviewSubname?: string;
  categoryName?: string;
  interviewType?: string;
  endedAt: string | null;
  scoreData?: ScoreData;
}

export default function InterviewHistoryPage() {
  const [sessions, setSessions] = useState<InterviewHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSessions() {
      try {
        setIsLoading(true);
        const sessions = await getUserSessions();
        
        // Transform sessions into InterviewHistoryItem format
        // Transform sessions and fetch grading data for completed sessions
        if (sessions){
            const formattedSessions = await Promise.all(sessions.map(async session => {
                const isCompleted = session.status === 'completed' || session.status === 'force_finished';
                let gradingData = null;
                
                if (isCompleted) {
                    try {
                        gradingData = await getOrTriggerGrading(session.sessionId);
                    } catch (error) {
                        console.error(`Failed to fetch grading for session ${session.sessionId}:`, error);
                    }
                }
                
                // Get relevant categories based on interview type
                const getRelevantCategories = (interviewType: string): string[] => {
                    // Define interview type keywords and their corresponding categories
                    const categoryKeywords: Record<string, string[]> = {
                        "Complete LPDP Scholarship": [
                            "Personal Background and Motivation",
                            "Study Plans and University Choice",
                            "Future Contributions to Indonesia",
                            "Academic and Professional Qualifications",
                            "Leadership and Organizational Experience",
                            "Knowledge about Indonesia's Challenges"
                        ],
                        "Personal Background": [
                            "Personal Background and Motivation"
                        ],
                        "Study Plans": [
                            "Study Plans and University Choice"
                        ],
                        "Future Contributions": [
                            "Future Contributions to Indonesia"
                        ],
                        "Academic and Professional": [
                            "Academic and Professional Qualifications"
                        ],
                        "Leadership": [
                            "Leadership and Organizational Experience"
                        ],
                        "Knowledge about Indonesia": [
                            "Knowledge about Indonesia's Challenges"
                        ]
                    };
                    
                    // Find matching interview type by keyword
                    for (const [keyword, categories] of Object.entries(categoryKeywords)) {
                        if (interviewType?.includes(keyword)) {
                            return categories;
                        }
                    }
                    
                    // If no match found, return all categories for complete simulation
                    return categoryKeywords["Complete LPDP Scholarship"];
                };

                let adjustedOverallScore = gradingData ? gradingData.results.overallScore : 0;
                let filteredCategoryScores = gradingData ? gradingData.results.categoryScores : [];
                
                // Adjust overall score based on interview type if grading data exists
                // Use the interview subname from transcript if available, otherwise fall back to category
                if (gradingData && session.interviewSubname) {
                    const relevantCategories = getRelevantCategories(session.interviewSubname);
                    console.log(`History - Relevant categories for ${session.interviewSubname}:`, relevantCategories);
                    
                    // For specific interview types, filter and recalculate score
                    if (!session.interviewSubname.includes("Complete LPDP Scholarship")) {
                        // Filter categories using flexible matching
                        filteredCategoryScores = gradingData.results.categoryScores.filter(cs => {
                            return relevantCategories.some(relevantCategory => {
                                const categoryLower = cs.category.toLowerCase();
                                const relevantLower = relevantCategory.toLowerCase();
                                
                                return categoryLower.includes(relevantLower) || 
                                       relevantLower.includes(categoryLower) ||
                                       // Check for specific keywords
                                       (relevantCategory.includes("Personal") && categoryLower.includes("personal")) ||
                                       (relevantCategory.includes("Study") && categoryLower.includes("study")) ||
                                       (relevantCategory.includes("Future") && categoryLower.includes("contribution")) ||
                                       (relevantCategory.includes("Academic") && (categoryLower.includes("academic") || categoryLower.includes("qualification"))) ||
                                       (relevantCategory.includes("Leadership") && (categoryLower.includes("leadership") || categoryLower.includes("organization"))) ||
                                       (relevantCategory.includes("Knowledge") && categoryLower.includes("indonesia"));
                            });
                        });
                        
                        // If we have filtered categories, recalculate the overall score
                        if (filteredCategoryScores.length > 0) {
                            const totalScore = filteredCategoryScores.reduce((sum, cs) => sum + cs.score, 0);
                            const totalMaxScore = filteredCategoryScores.reduce((sum, cs) => sum + cs.maxScore, 0);
                            adjustedOverallScore = totalMaxScore > 0 ? Math.round((totalScore / totalMaxScore) * 10) : 0;
                        }
                    }
                }
                
                const scoreData = gradingData ? {
                    overallScore: adjustedOverallScore,
                    maxScore: 10,
                    recommendation: gradingData.results.interviewerAssessment.recommendation,
                    readinessLevel: gradingData.results.interviewerAssessment.readinessLevel,
                    categoryScores: filteredCategoryScores
                } : undefined;

                return {
                    sessionId: session.sessionId,
                    interviewId: session.interviewId,
                    startTime: new Date(session.startTime).getTime(),
                    duration: session.endTime ?
                    `${Math.round((new Date(session.endTime).getTime() - new Date(session.startTime).getTime()) / 60000)} min`
                    : 'In Progress',
                    status: session.status as InterviewHistoryItem['status'],
                    interviewName: session.interviewName,
                    interviewType: session.type,
                    categoryName: session.category,
                    endedAt: session.endTime,
                    interviewSubname: session.interviewSubname,
                    scoreData
                };
            }));
            setSessions(formattedSessions);
        }

      } catch (err) {
        console.error("Failed to fetch interview history:", err);
        if (err instanceof Error && err.message === 'Not authenticated') {
          setError("Please log in to view your interview history.");
        } else {
          setError("Failed to load your interview history. Please try again later.");
        }
      } finally {
        setIsLoading(false);
      }
    }

    fetchSessions();
  }, []);

  // Format date for display
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Format time for display
  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Get status badge style and icon
  const getStatusInfo = (status: string) => {
    switch (status) {
      case "completed":
        return {
          icon: <Check size={16} />,
          label: "Completed (Early)",
          bgColor: "bg-green-100",
          textColor: "text-green-800",
        };
      case "active":
        return {
          icon: <Clock size={16} />,
          label: "Active",
          bgColor: "bg-blue-100",
          textColor: "text-blue-800",
        };
      case "force_finished":
        return {
          icon: <Check size={16} />,
          label: "Completed (Forced End)",
          bgColor: "bg-green-100",
          textColor: "text-green-800",
        };
      case "cancelled":
        return {
          icon: <X size={16} />,
          label: "Cancelled",
          bgColor: "bg-gray-100",
          textColor: "text-gray-800",
        };
      case "disconnected_timeout":
        return {
          icon: <AlertTriangle size={16} />,
          label: "Disconnected",
          bgColor: "bg-red-100",
          textColor: "text-red-800",
        };
      case "disconnected_grace":
        return {
          icon: <RefreshCw size={16} />,
          label: "Reconnecting",
          bgColor: "bg-purple-100",
          textColor: "text-purple-800",
        };
      default:
        return {
          icon: <Info size={16} />,
          label: status,
          bgColor: "bg-gray-100",
          textColor: "text-gray-800",
        };
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <h2 className="mt-4 text-lg font-semibold text-gray-900">Loading your interview history...</h2>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-red-50 p-4 rounded-lg inline-block">
              <AlertTriangle className="h-10 w-10 text-red-500 mb-2 mx-auto" />
              <h2 className="text-lg font-semibold text-red-800">Error</h2>
              <p className="text-red-700">{error}</p>
              <button
                onClick={() => window.location.reload()}
                className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-10 pb-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Interview History</h1>
          <p className="text-gray-600 mt-1">View all your previous interview sessions</p>
        </div>

        {sessions.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm p-8 text-center">
            <div className="mx-auto w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-4">
              <Info className="h-8 w-8 text-blue-500" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900 mb-2">No Interview Sessions Found</h2>
            <p className="text-gray-600 mb-6">
              You haven&apos;t completed any interview sessions yet. Start an interview to practice!
            </p>
            <Link
              href="/available-interviews"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Browse Available Interviews
            </Link>
          </div>
        ) : (
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="grid grid-cols-1 divide-y divide-gray-200">
              {sessions.map((session) => {
                const statusInfo = getStatusInfo(session.status);
                const isCompleted = session.status === 'completed' || session.status === 'force_finished';
                
                return (
                  <div key={session.sessionId} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="md:flex md:items-start md:justify-between">
                      <div className="md:flex-1">
                        <div className="flex items-center gap-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {session.interviewName || "Unknown Interview"}
                          </h3>
                          <div className={`flex items-center gap-1 px-3 py-1 rounded-full ${statusInfo.bgColor} ${statusInfo.textColor}`}>
                            {statusInfo.icon}
                            <span className="text-sm font-medium">{statusInfo.label}</span>
                          </div>
                        </div>
                        
                        {session.interviewSubname && (
                          <p className="text-gray-600 mt-1">{session.interviewSubname}</p>
                        )}
                        
                        <div className="mt-2 flex flex-wrap items-center gap-2">
                          <div className="flex items-center gap-1 text-gray-500 text-sm">
                            <Calendar size={14} />
                            {formatDate(session.startTime)}
                          </div>
                          <div className="flex items-center gap-1 text-gray-500 text-sm">
                            <Clock size={14} />
                            {formatTime(session.startTime)}
                          </div>
                          <div className="flex items-center gap-1 text-gray-500 text-sm">
                            <Info size={14} />
                            Duration: {session.duration}
                          </div>
                          {session.categoryName && (
                            <span className="px-2 py-1 bg-blue-50 text-blue-700 text-xs rounded-full">
                              {session.categoryName}
                            </span>
                          )}
                          {session.interviewType && (
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              session.interviewType === 'full' 
                                ? 'bg-indigo-50 text-indigo-700' 
                                : 'bg-green-50 text-green-700'
                            }`}>
                              {session.interviewType === 'full' ? 'Full Interview' : 'Focused Session'}
                            </span>
                          )}
                        </div>
                        
                        {isCompleted && (
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            {session.scoreData ? (
                              <div>
                                <div className="flex items-center gap-2 mb-2">
                                  <BarChart3 className="text-blue-600" size={16} />
                                  <h4 className="font-medium text-gray-800">Performance Summary</h4>
                                </div>
                                <div className="flex flex-wrap items-center gap-3">
                                  <div className="flex items-center gap-2">
                                    <div className={`inline-flex items-center justify-center ${getScoreBgClass(session.scoreData.overallScore, session.scoreData.maxScore)} rounded-full w-10 h-10`}>
                                      <span className={`text-sm font-bold ${getScoreColorClass(session.scoreData.overallScore, session.scoreData.maxScore)}`}>
                                        {session.scoreData.overallScore.toFixed(1)}
                                      </span>
                                    </div>
                                    <span className="text-sm text-gray-600">Score (out of {session.scoreData.maxScore})</span>
                                  </div>
                                  
                                  <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-gray-100">
                                    <Award size={14} className="text-blue-600" />
                                    <span className="text-xs font-medium text-gray-700">
                                      {session.scoreData.recommendation}
                                    </span>
                                  </div>
                                  
                                  <div className="flex items-center gap-1 px-2 py-1 rounded-md bg-gray-100">
                                    <span className="text-xs font-medium text-gray-700">
                                      Readiness: {session.scoreData.readinessLevel}%
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ) : (
                              <div className="text-sm text-blue-600">
                                View the complete results for detailed scoring and feedback
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className="mt-4 md:mt-0 flex flex-col items-end gap-3">
                        {session.scoreData && (
                          <div className="flex items-center gap-2 text-gray-700">
                            <span className="text-sm">Top category:</span>
                            <span className="font-medium">
                              {session.scoreData.categoryScores.sort((a, b) => b.score - a.score)[0].category.split(' ')[0]}
                            </span>
                          </div>
                        )}
                        
                        {isCompleted && (
                          <Link
                            href={`/ai-interview-result/${session.interviewId}/${session.sessionId}`}
                            className="inline-flex items-center gap-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                          >
                            View Results
                            <ArrowRight size={16} />
                          </Link>
                        )}
                        
                        {session.status === 'active' && (
                          <Link
                            href={`/ai-interview/${session.interviewId}/${session.sessionId}`}
                            className="inline-flex items-center gap-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                          >
                            Resume Interview
                            <ArrowRight size={16} />
                          </Link>
                        )}
                        
                        {session.status === 'disconnected_grace' && (
                          <Link
                            href={`/ai-interview/${session.interviewId}/${session.sessionId}`}
                            className="inline-flex items-center gap-1 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                          >
                            Reconnect
                            <RefreshCw size={16} />
                          </Link>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        <div className="mt-8 text-center">
          <Link
            href="/available-interviews"
            className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 transition-colors"
          >
            Back to Available Interviews
          </Link>
        </div>
      </div>
    </div>
  );
}
