"use client";

import React, { useEffect, useState, useMemo } from "react";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/breadcrumbs";
import { Swiper, SwiperSlide } from "swiper/react";
import { FreeMode, Pagination, Mousewheel } from "swiper/modules";
import { toast, ToastContainer } from "react-toastify";
import { useRouter } from "next/navigation";

import { AvailableInterview } from "@/components/types";
import { InterviewCard } from "./interview-card";
import { getAvailableInterviews } from "@/app/lib/actions/available-interviews/actions";
import { getUserInterviewHistory } from "@/app/lib/actions/interview-history/actions";
import DotLottieAnimation from "../shared/dotlottie-animation";

import "react-toastify/dist/ReactToastify.css";
import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/pagination";

interface Props {
  initialData?: AvailableInterview[] | { data: AvailableInterview[] };
}

// Interface for our category groups
interface CategoryGroup {
  category_name: string;
  interviews: AvailableInterview[];
  newest_interview_date: Date;
}

// Constants for localStorage keys
const LOCAL_STORAGE_CATEGORY_KEY = 'activeAvailableInterviewCategory';

// Theme interface to properly type the bgColorStyle property
interface CategoryTheme {
  icon: string;
  bgColor: string;
  textColor: string;
  bgColorStyle?: React.CSSProperties;
}

const getCategoryTheme = (categoryName: string): CategoryTheme => {
  const themes = {
    LPDP: {
      icon: "🎓", 
      bgColor: "bg-gradient-to-r from-blue-500 to-indigo-600",
      textColor: "text-white"
    },
    CPNS: {
      icon: "🏛️", 
      bgColor: "bg-gradient-to-r from-teal-600 to-emerald-400",
      textColor: "text-white"
    },
    UTBK: {
      icon: "📚", 
      bgColorStyle: { background: "linear-gradient(to right, #15BBBB, #7fd8d8)" },
      bgColor: "", // Empty string as we'll use the style approach
      textColor: "text-white"
    },
    Uncategorized: {
      icon: "✨", 
      bgColor: "bg-gradient-to-r from-gray-400 to-gray-600",
      textColor: "text-white"
    }
  };

  // Return the theme for the category or the uncategorized theme if not found
  return themes[categoryName as keyof typeof themes] || themes.Uncategorized;
};

export const AvailableInterviews: React.FC<Props> = ({ initialData }) => {
  console.log(initialData)
  const router = useRouter();
  // State for interviews and UI
  const [interviews, setInterviews] = useState<AvailableInterview[]>([]);
  const [sessionCount, setSessionCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(!initialData);
  const [error, setError] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [refreshData, setRefreshData] = useState(false);

  // Handle initialData - could be array or object with data property
  useEffect(() => {
    if (initialData) {
      if (Array.isArray(initialData)) {
        setInterviews(initialData);
      } else if (initialData.data && Array.isArray(initialData.data)) {
        setInterviews(initialData.data);
      }
    }
  }, [initialData]);

  // Load interviews on mount or when refreshData changes
  useEffect(() => {
    const fetchInterviews = async () => {
      try {
        if (!initialData || refreshData) {
          setLoading(true);
          setError(null);
          
          const response = await getAvailableInterviews('INTERVIEW');
          
          if (response === false) {
            setError('You need to be logged in to view available interviews.');
          } else if (response && typeof response === 'object') {
            console.log(response)
            // Handle response that might be an array or an object with a data property
            if (Array.isArray(response)) {
              setInterviews(response);
            } else if (response.data && Array.isArray(response.data)) {
              setInterviews(response.data);
            } else {
              console.error('Unexpected response format:', response);
              setInterviews([]);
              setError('Unexpected response format');
            }
          } else {
            setInterviews([]);
          }
        }
      } catch (err: any) {
        console.error('Error fetching interviews:', err);
        setError(err.message || 'Failed to load interviews');
      } finally {
        setLoading(false);
        setRefreshData(false);
      }
    };

    // Fetch session count
    const fetchSessionCount = async () => {
      try {
        const historyData = await getUserInterviewHistory();
        setSessionCount(historyData.length);
      } catch (error) {
        console.error('Error fetching session count:', error);
      }
    };

    fetchInterviews();
    fetchSessionCount();
  }, [initialData, refreshData]);

  // Group interviews by category
  const categoryGroups = useMemo(() => {
    // Create a map to group interviews by category
    const groupMap = new Map<string, CategoryGroup>();
    
    // Make sure interviews is an array before using forEach
    if (!Array.isArray(interviews)) {
      console.error('interviews is not an array:', interviews);
      return [];
    }

    interviews.forEach((interview) => {
      // Handle null/undefined category by providing a default value
      const categoryName = interview.category_name || "Uncategorized";
      
      // Determine the date to use for "newest" calculation
      const interviewDate = new Date(interview.created_at || "");
      
      if (!groupMap.has(categoryName)) {
        // Initialize a new category group
        groupMap.set(categoryName, {
          category_name: categoryName,
          interviews: [interview],
          newest_interview_date: interviewDate,
        });
      } else {
        // Add to existing category and update newest date if applicable
        const group = groupMap.get(categoryName)!;
        group.interviews.push(interview);
        
        if (interviewDate > group.newest_interview_date) {
          group.newest_interview_date = interviewDate;
        }
      }
    });
    
    // Convert map to array and sort by newest_interview_date (newest first)
    return Array.from(groupMap.values()).sort((a, b) => 
      b.newest_interview_date.getTime() - a.newest_interview_date.getTime()
    );
  }, [interviews]);

  // Set default category or retrieve from localStorage
  useEffect(() => {
    if (categoryGroups.length > 0) {
      try {
        // Try to get cached category from localStorage
        const cachedCategory = localStorage.getItem(LOCAL_STORAGE_CATEGORY_KEY);
        
        // Check if cached category exists and is valid
        if (cachedCategory && categoryGroups.some(group => group.category_name === cachedCategory)) {
          setActiveCategory(cachedCategory);
        } else if (!activeCategory) {
          // If no valid cached category or active category, set first one
          setActiveCategory(categoryGroups[0].category_name);
        }
      } catch (error) {
        console.error("Error accessing localStorage:", error);
        // Fallback to default if localStorage fails
        if (!activeCategory) {
          setActiveCategory(categoryGroups[0].category_name);
        }
      }
    }
  }, [categoryGroups, activeCategory]);

  // Handle category change
  const handleCategoryChange = (categoryName: string) => {
    setActiveCategory(categoryName);
    
    // Save to localStorage
    try {
      localStorage.setItem(LOCAL_STORAGE_CATEGORY_KEY, categoryName);
    } catch (error) {
      console.error("Failed to save category to localStorage:", error);
    }
  };

  // Navigate to interview history
  const navigateToHistory = () => {
    router.push('/interview-history');
  };

  // Show notification
  const showNotification = (
    message: string,
    type: "success" | "info" | "error",
  ) => {
    toast[type](message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  // Configuration for Swiper that can be reused
  const swiperConfig = {
    breakpoints: {
      640: {
        slidesPerView: 1.5,
        slidesOffsetBefore: 20,
        slidesOffsetAfter: 20,
      },
      768: {
        slidesPerView: 2.1,
        slidesOffsetBefore: 20,
        slidesOffsetAfter: 20,
      },
      1024: {
        slidesPerView: 3.1,
        slidesOffsetBefore: 20,
        slidesOffsetAfter: 20,
      },
    },
    freeMode: true,
    modules: [FreeMode, Pagination, Mousewheel],
    mousewheel: true,
    pagination: { clickable: true },
    slidesOffsetAfter: 20,
    slidesOffsetBefore: 20,
    slidesPerView: 1.3,
    spaceBetween: 15,
  };

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-red-500 mb-4">Error: {error}</p>
        <button 
          onClick={() => setRefreshData(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Coba Lagi
        </button>
      </div>
    );
  }

  return (
    <div className="my-10 px-6 lg:px-6 max-w-[95rem] mx-auto w-full flex flex-col gap-4">
      {/* Header Section with Breadcrumb, History Button, and Animation */}
      <div className="flex items-start justify-between gap-4">
        <div className="flex flex-col gap-4 flex-1">
          <div className="flex justify-between items-center">
            <ul className="flex">
              <Breadcrumbs>
                <BreadcrumbItem href="/dashboard">Dashboard</BreadcrumbItem>
                <BreadcrumbItem>Interview Tersedia</BreadcrumbItem>
              </Breadcrumbs>
            </ul>
          </div>
          
          {interviews.length > 0 && (
            /* Category Tabs/Pills */
            <div className="overflow-x-auto">
              <div className="flex space-x-2">
                {categoryGroups.map((group) => {
                  const theme = getCategoryTheme(group.category_name);
                  return (
                    <button
                      key={group.category_name}
                      onClick={() => handleCategoryChange(group.category_name)}
                      className={`px-4 py-2 rounded-full flex items-center transition-all transform hover:scale-105 ${
                        activeCategory === group.category_name
                          ? `${theme.bgColor} ${theme.textColor} shadow-lg`
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                      }`}
                      style={activeCategory === group.category_name && theme.bgColorStyle ? theme.bgColorStyle : {}}
                    >
                      <span className="mr-2">{theme.icon}</span>
                      <span className="font-medium">{group.category_name}</span>
                      <span className="ml-2 bg-white bg-opacity-25 text-xs px-2 py-1 rounded-full">
                        {group.interviews.length}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
        
        {/* DotLottie Animation - spans both breadcrumb and category sections */}
        <div className="w-20 h-20 flex-shrink-0">
          <DotLottieAnimation
            src="/dotlotties/ai-voice.lottie"
            autoplay
            loop
            width={"100%"}
            height={"100%"}
          />
        </div>
      </div>

      {interviews.length > 0 ? (
        <div className="w-full">

          {/* Active Category Content */}
          {categoryGroups.map((group) => {
            const isActive = group.category_name === activeCategory;
            const theme = getCategoryTheme(group.category_name);
            
            if (!isActive) return null;

            return (
              <div key={group.category_name} className="space-y-6 transition-all duration-300">
                {/* Category Header with Icon and Description */}
                <div 
                  className={`p-6 rounded-xl ${theme.bgColor} ${theme.textColor} shadow-lg`}
                  style={theme.bgColorStyle}
                >
                  <div className="flex items-center mb-2">
                    <span className="text-3xl mr-3">{theme.icon}</span>
                    <h2 className="text-2xl font-bold">{group.category_name} Interview GRATIS (Beta)</h2>
                  </div>
                  <p className="opacity-90">
                    {group.category_name === "LPDP" && 
                      "Simulasi interview LPDP untuk mempersiapkan diri menghadapi proses wawancara beasiswa LPDP dengan percaya diri."}
                    {group.category_name === "CPNS" && 
                      "Berlatih menghadapi interview CPNS dengan simulasi yang mirip situasi aslinya."}
                    {group.category_name === "BUMN" && 
                      "Persiapan wawancara kerja BUMN dengan simulasi interview yang profesional."}
                    {group.category_name === "Uncategorized" && 
                      "Berbagai simulasi interview lainnya untuk membantu kamu berlatih."}
                  </p>
                </div>
            
                {/* Interview History Button - Right Aligned */}
                <div className="flex justify-end w-full">
                  <button
                    onClick={navigateToHistory}
                    className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 to-indigo-600 text-white rounded-lg shadow hover:shadow-lg transition-all transform hover:scale-105"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-medium">Riwayat Interview</span>
                    {sessionCount > 0 && (
                      <span className="inline-flex items-center justify-center px-2 py-1 ml-1 text-xs font-bold leading-none text-white bg-white bg-opacity-25 rounded-full">
                        {sessionCount}
                      </span>
                    )}
                  </button>
                </div>
                {/* Interview Cards in Swiper */}
                <Swiper {...swiperConfig}>
                  {group.interviews.map((interview) => (
                    <SwiperSlide key={interview.id} className="my-6">
                      <InterviewCard
                        interviewData={interview}
                        setRefreshData={() => setRefreshData(true)}
                        showNotification={showNotification}
                      />
                    </SwiperSlide>
                  ))}
                </Swiper>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="flex justify-center items-center h-[calc(100vh-200px)]">
          <div className="text-center py-10">
            <p className="text-lg font-semibold">Belum ada simulasi interview yang tersedia saat ini.</p>
            <p className="text-sm text-gray-500 mt-2">
              Mohon maaf atas ketidaknyamanannya. Tim kami sedang menyiapkan simulasi interview terbaik untuk kebutuhan kamu.
            </p>
          </div>
        </div>
      )}
      <ToastContainer />
    </div>
  );
};