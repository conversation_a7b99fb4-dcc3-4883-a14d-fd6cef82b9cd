"use client";

import React, { useState, useCallback, lazy, Suspense, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>dal,
  <PERSON>dalBody,
  <PERSON>dalContent,
  Spinner
} from "@heroui/react";

import { AvailableExamsType } from "../types";
import { getPaymentInfo } from "@/app/lib/actions/available-exams/actions";

interface Props {
  examData: AvailableExamsType;
  showNotification?: (
    message: string,
    type: "success" | "info" | "error",
  ) => void;
  setRefreshData?: React.Dispatch<React.SetStateAction<boolean>>;
}

interface PaymentInfo {
  orderId: string;
  examId: string;
  invoiceId: string;
  paymentId: string;
  refCode?: string;
}

// Lazy load the actual purchase implementation
const PurchaseExamModal = lazy(() => import('./purchase-exam-modal'));

// Lightweight placeholder component
export const PurchaseExam: React.FC<Props> = ({
  examData,
  showNotification,
  setRefreshData,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isContinueOpen, setIsContinueOpen] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState<PaymentInfo | null>(null);
  const [shouldCheckPayment, setShouldCheckPayment] = useState(0); // Counter to trigger re-checks
  
  // Format price once for the preview button
  const formatPrice = useCallback((price: number): string => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(price);
  }, []);

  const onOpenChange = useCallback(() => {
    setIsOpen(prev => !prev);
    // Trigger a payment info refresh when modal closes
    setShouldCheckPayment(count => count + 1);
  }, []);
  
  const onContinueOpenChange = useCallback(() => {
    setIsContinueOpen(prev => !prev);
    // Trigger a payment info refresh when continue modal closes
    setShouldCheckPayment(count => count + 1);
  }, []);

  // Check for stored payment info on component mount and when modals close
  useEffect(() => {
    let isMounted = true;
    
    const fetchPaymentInfo = async () => {
      try {
        const storedPaymentInfo = await getPaymentInfo('exam');
        if (!isMounted) return;
        
        if (storedPaymentInfo && storedPaymentInfo.examId === examData.id) {
          setPaymentInfo(storedPaymentInfo);
        } else {
          setPaymentInfo(null);
        }
      } catch (error) {
        console.error("Error fetching payment info:", error);
        if (isMounted) {
          setPaymentInfo(null);
        }
      }
    };

    fetchPaymentInfo();
    
    return () => {
      isMounted = false;
    };
  }, [examData.id, shouldCheckPayment]); // Re-run when shouldCheckPayment changes

  return (
    <div className="flex flex-col items-center">
      <Button
        className="px-10 text-md lg:text-lg lg:px-12"
        color="primary"
        size="lg"
        variant="shadow"
        onPress={() => setIsOpen(true)}
      >
        Beli Kartu
      </Button>
      
      {/* Show "Lanjutkan Bayar" button if there's pending payment for this exam */}
      {paymentInfo && paymentInfo.examId === examData.id && (
        <Button 
          className="mt-4 px-6 py-2 text-md animate-pulse shadow-lg transition-all text-white hover:scale-105 hover:animate-none" 
          color="warning"
          variant="shadow"
          startContent={<span className="font-bold">→</span>}
          onPress={() => setIsContinueOpen(true)}
        >
          Lanjutkan Bayar
        </Button>
      )}
      
      {/* Purchase modal */}
      {isOpen && (
        <Modal 
          isOpen={isOpen} 
          placement="center" 
          onOpenChange={onOpenChange}
          onClose={() => {
            // Ensure payment info is refreshed when modal is closed
            setTimeout(() => {
              setShouldCheckPayment(count => count + 1);
            }, 500);
          }}
        >
          <Suspense fallback={
            <ModalContent>
              <ModalBody>
                <div className="flex justify-center items-center p-10">
                  <Spinner size="lg" />
                </div>
              </ModalBody>
            </ModalContent>
          }>
            <PurchaseExamModal 
              examData={examData}
              showNotification={showNotification}
              setRefreshData={setRefreshData}
              onOpenChange={onOpenChange}
              onAfterClose={() => {
                // Callback for after modal is closed
                setShouldCheckPayment(count => count + 1);
              }}
            />
          </Suspense>
        </Modal>
      )}
      
      {/* Continue Payment modal */}
      {isContinueOpen && paymentInfo && (
        <Modal 
          isOpen={isContinueOpen} 
          placement="center" 
          onOpenChange={onContinueOpenChange}
          onClose={() => {
            // Ensure payment info is refreshed when modal is closed
            setTimeout(() => {
              setShouldCheckPayment(count => count + 1);
            }, 500);
          }}
        >
          <Suspense fallback={
            <ModalContent>
              <ModalBody>
                <div className="flex justify-center items-center p-10">
                  <Spinner size="lg" />
                </div>
              </ModalBody>
            </ModalContent>
          }>
            <PurchaseExamModal 
              examData={examData}
              showNotification={showNotification}
              setRefreshData={setRefreshData}
              onOpenChange={onContinueOpenChange}
              continuePayment={true}
              onAfterClose={() => {
                // Callback for after modal is closed
                setShouldCheckPayment(count => count + 1);
              }}
            />
          </Suspense>
        </Modal>
      )}
    </div>
  );
};

export default PurchaseExam;