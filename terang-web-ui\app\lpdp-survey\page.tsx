"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody
} from "@heroui/react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { LpdpGoalTrackerForm } from "@/components/lpdp-goal-tracker/form";
import {
  LpdpGoalTrackerFormData,
  FormErrors,
  validateLpdpGoalTrackerForm,
  submitLpdpGoalTrackerForm
} from "@/components/lpdp-goal-tracker/actions";
import { getUserData } from "./actions";

export default function LpdpSurveyPage() {

  // State variables
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);
  const [formData, setFormData] = useState<Partial<LpdpGoalTrackerFormData>>({});
  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const [userData, setUserData] = useState<{ name: string; email: string } | null>(null);

  // Fetch user data when component mounts
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        const data = await getUserData();
        if (data) {
          setUserData(data);
        } else {
          // User is not authenticated or data couldn't be fetched
          // We'll continue with an empty form
          console.log("[LPDP Survey] No user data available, continuing with empty form");
        }
      } catch (error) {
        console.error("[LPDP Survey] Error fetching user data:", error);
        // Don't show error toast since authentication is optional
        console.log("[LPDP Survey] Continuing with empty form");
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  // Helper function to show notifications
  const showNotification = (message: string, type: "success" | "error" | "info" = "info") => {
    toast[type](message, {
      position: "top-center",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  // Handle form data changes
  const handleFormDataChange = (data: Partial<LpdpGoalTrackerFormData>) => {
    setFormData(data);
  };

  // Clear form errors
  const handleClearError = (field: keyof LpdpGoalTrackerFormData) => {
    setFormErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  };

  // Validate form
  const validateForm = async (data: LpdpGoalTrackerFormData) => {
    return await validateLpdpGoalTrackerForm(data);
  };

  // Handle form decline (user didn't participate in LPDP)
  const handleFormDecline = async () => {
    try {
      setIsSubmitting(true);

      // Create minimal form data with just name and email
      const minimalFormData: LpdpGoalTrackerFormData = {
        name: userData?.name || formData.name || "",
        email: userData?.email || formData.email || "",
        verbalReasoning: 1,
        quantitativeReasoning: 1,
        problemSolving: 1,
        passedLpdpTbs: false,
        feltHelped: false,
        helpfulnessRating: 1, // Harus minimal 1 sesuai validasi backend (min=1,max=10)
        mostHelpfulAspect: "Tidak mengikuti LPDP atau latihan yang berkaitan",
        improvementSuggestions: "-",
        contactConsent: false,
        phoneNumber: "-",
      };

      console.log("[LPDP Survey] Submitting minimal data (user declined):", minimalFormData);
      const result = await submitLpdpGoalTrackerForm(minimalFormData);

      if (result.success) {
        // Show success screen
        setShowSuccessScreen(true);

        // Log successful submission
        console.log("[LPDP Survey] Form declined and submitted successfully");

        // Show success notification
        showNotification("Terima kasih atas respon Anda", "success");

        // No redirect needed, just show success screen
        setTimeout(() => {
          // Close the tab or stay on success screen
          // We don't redirect to dashboard since user might not be authenticated
        }, 3000);
      } else {
        // Log submission failure
        console.error("[LPDP Survey] Form decline submission failed:", result.message);

        // Show error notification
        showNotification(result.message || "Gagal menyimpan data", "error");
      }
    } catch (error) {
      // Log submission error
      console.error("[LPDP Survey] Error submitting declined form:", error);

      // Show error notification
      const errorMessage = error instanceof Error
        ? error.message
        : "Terjadi kesalahan tidak diketahui";
      showNotification(errorMessage, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission
  const handleFormSubmit = async () => {
    if (!formData || Object.keys(formData).length === 0) {
      showNotification("Silakan isi formulir terlebih dahulu", "error");
      setFormErrors({ form: "Please fill out the form" });
      return;
    }

    // Ensure all numeric fields are actually numbers
    const processedFormData = {
      ...formData,
      verbalReasoning: formData.verbalReasoning !== undefined ? Number(formData.verbalReasoning) : undefined,
      quantitativeReasoning: formData.quantitativeReasoning !== undefined ? Number(formData.quantitativeReasoning) : undefined,
      problemSolving: formData.problemSolving !== undefined ? Number(formData.problemSolving) : undefined,
      helpfulnessRating: formData.helpfulnessRating !== undefined ? Number(formData.helpfulnessRating) : undefined,
    };

    console.log("[LPDP Survey] Form data before validation:", processedFormData);

    // Check if all required fields are present
    const requiredFields: (keyof LpdpGoalTrackerFormData)[] = [
      'name', 'email', 'verbalReasoning', 'quantitativeReasoning',
      'problemSolving', 'helpfulnessRating'
    ];

    const missingFields = requiredFields.filter(field => {
      const value = processedFormData[field];
      return value === undefined || value === null || value === '';
    });

    if (missingFields.length > 0) {
      const errorMessage = `Mohon lengkapi field berikut: ${missingFields.join(', ')}`;
      showNotification(errorMessage, "error");

      // Set errors for each missing field
      const errors: FormErrors = {};
      missingFields.forEach(field => {
        errors[field] = `${field} harus diisi`;
      });

      setFormErrors(errors);
      return;
    }

    // Cast to complete form data since we've verified required fields
    const completeFormData = processedFormData as LpdpGoalTrackerFormData;

    // Validate the form
    const errors = await validateForm(completeFormData);
    if (Object.keys(errors).length > 0) {
      console.log("[LPDP Survey] Validation errors:", errors);
      const errorMessages = Object.values(errors).join(", ");
      showNotification(`Kesalahan validasi: ${errorMessages}`, "error");
      setFormErrors(errors);
      return;
    }

    try {
      setIsSubmitting(true);

      console.log("[LPDP Survey] Submitting form data:", completeFormData);
      const result = await submitLpdpGoalTrackerForm(completeFormData);

      if (result.success) {
        // Show success screen
        setShowSuccessScreen(true);

        // Log successful submission
        console.log("[LPDP Survey] Form submitted successfully");

        // Show success notification
        showNotification("Data berhasil disimpan!", "success");

        // No redirect needed, just show success screen
        setTimeout(() => {
          // Close the tab or stay on success screen
          // We don't redirect to dashboard since user might not be authenticated
        }, 5000);
      } else {
        // Log submission failure
        console.error("[LPDP Survey] Form submission failed:", result.message);

        // Show error notification
        showNotification(result.message || "Gagal menyimpan data", "error");
      }
    } catch (error) {
      // Log submission error
      console.error("[LPDP Survey] Error submitting form:", error);

      // Show error notification
      const errorMessage = error instanceof Error
        ? error.message
        : "Terjadi kesalahan tidak diketahui";
      showNotification(errorMessage, "error");
    } finally {
      setIsSubmitting(false);
    }
  };

  // If still loading, show spinner
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Spinner size="lg" />
        <p className="mt-4 text-gray-600">Memuat data...</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50">
      <Card className="w-full max-w-3xl">
        <CardHeader className="flex items-center gap-2 sm:gap-3 bg-white p-3 sm:p-4">
          <img
            src="https://terang.ai/_next/static/media/lpdp.b06d27d5.svg"
            alt="LPDP Logo"
            className="h-8 sm:h-10 w-auto"
          />
          <div>
            <h2 className="text-lg sm:text-xl font-medium">Survei Hasil TBS LPDP Batch 1 2025</h2>
            <p className="text-xs sm:text-sm text-gray-500 mt-1">Bantu kami meningkatkan layanan untuk persiapan LPDP</p>
          </div>
        </CardHeader>
        <CardBody className="p-0">
          {showSuccessScreen ? (
            <div className="flex flex-col items-center justify-center py-8 sm:py-10 px-3 sm:px-4 text-center">
              <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-full bg-green-100 flex items-center justify-center mb-4 sm:mb-6">
                <svg className="w-8 h-8 sm:w-10 sm:h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-xl sm:text-2xl font-semibold mb-2">Terima Kasih!</h3>
              <p className="text-sm sm:text-base text-gray-600 mb-4 sm:mb-6">
                Survei kamu telah berhasil dikirim. Kami sangat menghargai masukan kamu untuk meningkatkan layanan kami.
              </p>
              <div className="flex items-center justify-center gap-2 mb-4">
                <img
                  src="https://cdn.terang.ai/images/logo/logo-terang-ai.svg"
                  alt="Terang AI Logo"
                  className="h-7 sm:h-8 w-auto"
                />
              </div>
              <p className="text-xs sm:text-sm text-gray-500 mt-6 sm:mt-8">
                Halaman akan dialihkan dalam beberapa detik...
              </p>
            </div>
          ) : (
            <div className="flex flex-col bg-gray-50 rounded-lg">
              <LpdpGoalTrackerForm
                clearError={(field: string) => handleClearError(field as keyof LpdpGoalTrackerFormData)}
                formErrors={formErrors}
                initialData={formData}
                onFormDataChange={handleFormDataChange}
                onComplete={handleFormSubmit}
                onDecline={handleFormDecline}
                isSubmitting={isSubmitting}
                userName={userData?.name || ""}
                userEmail={userData?.email || ""}
              />
            </div>
          )}
        </CardBody>
      </Card>
      <ToastContainer />
    </div>
  );
}