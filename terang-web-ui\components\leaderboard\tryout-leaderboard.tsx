import React, { useState, useEffect, NewLifecycle } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Table, 
  TableHeader, 
  TableBody, 
  TableColumn, 
  TableRow, 
  TableCell,
  Input,
  Card,
  CardHeader,
  CardBody,
  Pagination,
  Spinner,
  Divider,
  Button,
  Select,
  SelectItem,
  Chip,
} from "@heroui/react";
import { 
  fetchLeaderboardData, 
  fetchTargetAggregates,
  fetchExamConfig,
  fetchCategories,
  type LeaderboardEntry,
  type TargetAggregates,
  type ExamType,
  type CategoryOption,
} from './actions';
import { getUserSubscription } from "@/app/(pricing)/subscription/actions";
import { getUserId, getUserUsername } from "@/app/lib/actions/account/actions";
import EmptyStateMessage from './empty-component';

interface TryoutFormProps {
  selectedExam: string;
  selectedCategory?: string;
}

// Column interface for table headers
interface Column {
  key: "rank" | "username" | "score" | "time" | "targetJabatan" | "targetInstitution" | "targetUniversity" | "targetMajor" | "completedAt" | "categories";
  label: string;
  width?: string;
}

interface LeaderboardTableProps {
  data: LeaderboardEntry[];
  title: string;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  showTimeColumn?: boolean;
  showTargetSelectors?: boolean;
  targetOptions: TargetAggregates;
  selectedPosition: string;
  selectedInstitution: string;
  selectedUniversity: string;
  selectedMajor: string;
  onPositionChange?: (value: string) => void;
  onInstitutionChange?: (value: string) => void;
  onUniversityChange?: (value: string) => void;
  onMajorChange?: (value: string) => void;
  examConfig: ExamType | null;
  currentUsername: string | boolean | null;
  filterText: string;
  hasAccessToPremium: () => boolean;
  categories: CategoryOption[];
  activeCategory: string | null;
}

// Category theme helper
const getCategoryTheme = (categoryName: string) => {
  const themes = {
    LPDP: {
      icon: "🎓", 
      color: "primary",
    },
    CPNS: {
      icon: "🏛️", 
      color: "success",
    },
    UTBK: {
      icon: "📚", 
      color: "warning",
    },
    SNBT: {
      icon: "📝", 
      color: "secondary",
    },
    Uncategorized: {
      icon: "✨", 
      color: "default",
    }
  };

  // Return the theme for the category or the uncategorized theme if not found
  return themes[categoryName as keyof typeof themes] || themes.Uncategorized;
};
const TryoutForm: React.FC<TryoutFormProps> = ({ selectedExam, selectedCategory }) => {
  const router = useRouter();
  
  // State management
  const [filterText, setFilterText] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [generalCurrentPage, setGeneralCurrentPage] = useState<number>(1);
  const [bidangCurrentPage, setBidangCurrentPage] = useState<number>(1);
  const [generalLeaderboard, setGeneralLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [bidangLeaderboard, setBidangLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [userSubscription, setUserSubscription] = useState<any | null>(null);
  const [targetOptions, setTargetOptions] = useState<TargetAggregates>({ 
    positions: [], 
    institutions: [],
    universities: [],
    majors: [],
    categories: []
  });
  const [categories, setCategories] = useState<CategoryOption[]>([]);
  const [activeCategory, setActiveCategory] = useState<string | null>(selectedCategory || null);
  const [selectedPosition, setSelectedPosition] = useState<string>('');
  const [selectedInstitution, setSelectedInstitution] = useState<string>('');
  const [selectedUniversity, setSelectedUniversity] = useState<string>('');
  const [selectedMajor, setSelectedMajor] = useState<string>('');
  const [currentUsername, setCurrentUsername] = useState<string | boolean | null>('');
  const [examConfig, setExamConfig] = useState<ExamType | null>(null);

  const itemsPerPage = 10;

  const hasAccessToPremium = (): boolean => {
    if (!userSubscription) return false;
    const { tierId, paymentStatus, isActive } = userSubscription;
    return tierId === 'premium_tier_001' && 
           paymentStatus === 'PAID' && 
           isActive === true;
  };

  // Calculate total score for a leaderboard entry
  const calculateTotalScore = (entry: LeaderboardEntry): number => {
    if (!examConfig || !examConfig.subjects) return 0;
    
    // Determine the exam type based on the categories
    const isUtbk = entry.categoryIds?.some(id => 
      categories.some(cat => cat.value === id && cat.label.toUpperCase().includes('UTBK'))
    ) || false;
    
    // If there are no subject scores, return 0
    if (!entry.subjectScores || Object.keys(entry.subjectScores).length === 0) {
      return 0;
    }
    
    if (isUtbk) {
      // UTBK calculation: average of (correct/total * 1000) for each subject
      let totalPoints = 0;
      let subjectCount = 0;
      
      Object.entries(entry.subjectScores).forEach(([key, scoreData]) => {
        const correct = scoreData.correct || 0;
        const total = scoreData.total || 0;
        
        if (total > 0) {
          // Calculate subject score using UTBK formula - Math.ceil((correct/total) * 1000)
          const score = Math.ceil((correct / total) * 1000);
          totalPoints += score;
          subjectCount++;
        }
      });
      
      // Return average score, or 0 if no subjects
      return subjectCount > 0 ? Math.ceil(totalPoints / subjectCount) : 0;
    } else {
      // CPNS/LPDP calculation: sum of (correct * 5) for all subjects except kepribadian
      let totalScore = 0;
      
      Object.entries(entry.subjectScores).forEach(([key, scoreData]) => {
        // Skip kepribadian subject for score calculation
        if (key.toLowerCase() === 'kepribadian') {
          return;
        }
        
        const correctValue = scoreData.correct || 0;
        if (typeof correctValue === 'number') {
          totalScore += correctValue * 5;
        }
      });
      
      return totalScore;
    }
  };
  
  // Sort leaderboard data by score and time
  const sortLeaderboardData = (data: LeaderboardEntry[]): LeaderboardEntry[] => {
    return [...data]
      .sort((a, b) => {
        const scoreA = calculateTotalScore(a);
        const scoreB = calculateTotalScore(b);
        
        if (scoreB !== scoreA) {
          return scoreB - scoreA;
        }
        return a.elapsedSeconds - b.elapsedSeconds;
      })
      .map((entry, index) => ({
        ...entry,
        rank: index + 1
      }));
  };
  
  // Fetch categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await fetchCategories();
        setCategories(categoriesData);
        
        // If not using a category from parent component, set default category
        if (!selectedCategory && categoriesData.length > 0) {
          setActiveCategory(categoriesData[0].value);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };
    
    loadCategories();
  }, [selectedCategory]);

  // Update active category when selectedCategory prop changes
  useEffect(() => {
    if (selectedCategory) {
      setActiveCategory(selectedCategory);
      // Reset filters when category changes
      setSelectedPosition('');
      setSelectedInstitution('');
      setSelectedUniversity('');
      setSelectedMajor('');
      setGeneralCurrentPage(1);
      setBidangCurrentPage(1);
    }
  }, [selectedCategory]);

  // Fetch exam configuration
  useEffect(() => {
    const getExamConfig = async () => {
      if (!selectedExam) return;
      
      setLoading(true);
      try {
        // We'll use fetchExamConfig from actions.ts
        const config = await fetchExamConfig(selectedExam);
        
        if (!config) {
          throw new Error(`Failed to load exam configuration for ${selectedExam}`);
        }
        
        setExamConfig(config);
        console.log(`Loaded exam configuration successfully`);
        
        // Reset any previous errors
        setError(null);
      } catch (error) {
        console.error('Error fetching exam configuration:', error);
        setError('Failed to load exam configuration. Please try again later or contact support if the problem persists.');
      }
    };
    
    getExamConfig();
  }, [selectedExam]);

  // Fetch current username on mount
  useEffect(() => {
    const fetchUsername = async () => {
      try {
        const username = await getUserUsername();
        setCurrentUsername(username);
      } catch (error) {
        console.error('Error fetching username:', error);
      }
    };
    fetchUsername();
  }, []);

  // Fetch user subscription on component mount
  useEffect(() => {
    const fetchSubscription = async (): Promise<void> => {
      try {
        const userId = await getUserId();
        const subscription = await getUserSubscription(userId);
        setUserSubscription(subscription);
      } catch (error) {
        console.error('Error fetching subscription:', error);
        setUserSubscription(null);
      }
    };
    fetchSubscription();
  }, []);
  // Fetch leaderboard data
  useEffect(() => {
    const fetchData = async (): Promise<void> => {
      if (!selectedExam) return;
      
      setLoading(true);
      try {
        const [generalResponse, bidangResponse, aggregatesResponse] = await Promise.all([
          fetchLeaderboardData(selectedExam, 'TRYOUT_GENERAL', activeCategory || undefined),
          fetchLeaderboardData(selectedExam, 'TRYOUT_BIDANG', activeCategory || undefined),
          fetchTargetAggregates()
        ]);

        // Check if responses are empty or invalid
        if (!generalResponse?.leaderboard) {
          setGeneralLeaderboard([]);
        } else {
          console.log(generalResponse);
          // Filter leaderboard data by category if a category is selected
          // Only apply category filter if the API doesn't already filter by category
          const filteredLeaderboard = selectedCategory 
            ? generalResponse.leaderboard.filter(entry => 
                entry.categoryIds && entry.categoryIds.includes(selectedCategory)
              )
            : generalResponse.leaderboard;
          
          setGeneralLeaderboard(filteredLeaderboard);
        }

        if (!bidangResponse?.leaderboard) {
          setBidangLeaderboard([]);
        } else {
          // Filter leaderboard data by category if a category is selected
          // Only apply category filter if the API doesn't already filter by category
          const filteredLeaderboard = selectedCategory 
            ? bidangResponse.leaderboard.filter(entry => 
                entry.categoryIds && entry.categoryIds.includes(selectedCategory)
              )
            : bidangResponse.leaderboard;
            
          setBidangLeaderboard(filteredLeaderboard);
        }

        if (!aggregatesResponse) {
          setTargetOptions({ positions: [], institutions: [], universities: [], majors: [], categories: [] });
        } else {
          console.log("Target aggregates:", aggregatesResponse);
          setTargetOptions({
            positions: aggregatesResponse.positions || [],
            institutions: aggregatesResponse.institutions || [],
            universities: aggregatesResponse.universities || [],
            majors: aggregatesResponse.majors || [],
            categories: aggregatesResponse.categories || []
          });
        }

        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedExam, activeCategory, selectedCategory]);

  // After examConfig is loaded, sort the leaderboards
  useEffect(() => {
    if (examConfig) {
      setGeneralLeaderboard(prev => sortLeaderboardData(prev));
      setBidangLeaderboard(prev => sortLeaderboardData(prev));
    }
  }, [examConfig]);

  // Filter data based on username search
  const filterData = (data: LeaderboardEntry[]): LeaderboardEntry[] => {
    const filtered = data.filter((item) =>
      item.username.toLowerCase().includes(filterText.toLowerCase())
    );
    return sortLeaderboardData(filtered);
  };

  // Get paginated subset of data
  const getPaginatedData = (data: LeaderboardEntry[], currentPage: number): LeaderboardEntry[] => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return data.slice(startIndex, startIndex + itemsPerPage);
  };

  // Render states: loading, error, no selection
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <Spinner label="Loading tryout leaderboards..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-[200px] text-danger">
        {error}
      </div>
    );
  }

  if (!selectedExam) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        Please select an exam to view tryout leaderboards
      </div>
    );
  }
  // LeaderboardTable sub-component
  const LeaderboardTable: React.FC<LeaderboardTableProps> = ({ 
    data,
    title,
    currentPage,
    setCurrentPage,
    showTimeColumn = true,
    showTargetSelectors = false,
    targetOptions,
    selectedPosition,
    selectedInstitution,
    selectedUniversity,
    selectedMajor,
    onPositionChange,
    onInstitutionChange,
    onUniversityChange,
    onMajorChange,
    examConfig,
    currentUsername,
    filterText,
    hasAccessToPremium,
    categories,
    activeCategory
  }) => {
  
    // First apply target filters if enabled
    const filteredData = (() => {
      let filtered = [...data];
      
      // Apply target filters if enabled
      if (showTargetSelectors) {
        filtered = filtered.filter(entry => {
          const matchesPosition = !selectedPosition || entry.targetJabatan === selectedPosition;
          const matchesInstitution = !selectedInstitution || entry.targetInstitution === selectedInstitution;
          const matchesUniversity = !selectedUniversity || entry.targetUniversity === selectedUniversity;
          const matchesMajor = !selectedMajor || entry.targetMajor === selectedMajor;
          return matchesPosition && matchesInstitution && matchesUniversity && matchesMajor;
        });
      }
      
      // Apply username filter and sort
      filtered = filtered.filter(item => 
        item.username.toLowerCase().includes(filterText.toLowerCase())
      );
      
      return sortLeaderboardData(filtered);
    })();
  
    // Get paginated data after all filters are applied
    const paginatedData = getPaginatedData(filteredData, currentPage);
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  
    // Find current user's entry
    const currentUserEntry = filteredData.find(entry => entry.username === currentUsername);
    const currentUserRank = currentUserEntry?.rank;
    const isCurrentUserInCurrentPage = paginatedData.some(entry => entry.username === currentUsername);
  
    // Create final data array including current user's entry if needed
    const finalData = [...paginatedData];
    if (currentUserEntry && !isCurrentUserInCurrentPage) {
      finalData.push({
        ...currentUserEntry,
        isCurrentUser: true
      });
    }
  
    // Ensure targetOptions has default values
    const safeTargetOptions = {
      positions: targetOptions?.positions || [],
      institutions: targetOptions?.institutions || [],
      universities: targetOptions?.universities || [],
      majors: targetOptions?.majors || [],
      categories: targetOptions?.categories || []
    };
  
    // Define columns array
    const columns: Column[] = [
      { key: "rank", label: "RANK", width: "w-[80px]" },
      { key: "username", label: "USERNAME", width: "w-[150px]" },
      { key: "score", label: "SCORE", width: "w-[100px]" },
      { key: "completedAt", label: "TANGGAL UJIAN", width: "w-[150px]" },
      { key: "categories", label: "CATEGORIES", width: "w-[150px]" },
      { key: "targetJabatan", label: "TARGET JABATAN", width: "w-[200px]" },
      { key: "targetInstitution", label: "TARGET INSTITUSI", width: "w-[200px]" },
      { key: "targetUniversity", label: "TARGET UNIVERSITAS", width: "w-[200px]" },
      { key: "targetMajor", label: "TARGET JURUSAN", width: "w-[200px]" },
      ...(showTimeColumn ? [{ key: "time", label: "TIME", width: "w-[150px]" } as Column] : [])
    ];
    // Render individual table cells
    const renderCell = (item: LeaderboardEntry, columnKey: Column["key"]): React.ReactNode => {
      const value = (() => {
        if (!hasAccessToPremium()) {
          switch (columnKey) {
            case "rank": return item.rank;
            case "username": return 'aaa12302';
            case "score": return '**.**';
            case "time": return '**:**';
            case "targetJabatan": return '****';
            case "targetInstitution": return '****';
            case "targetUniversity": return '****';
            case "targetMajor": return '****';
            case "completedAt": return '****';
            case "categories": return '****';
            default: return '';
          }
        }
    
        switch (columnKey) {
          case "rank": return item.rank;
          case "username": return item.username;
          case "score": return calculateTotalScore(item);
          case "time": return item.elapsedTimeFormatted;
          case "targetJabatan": return item.targetJabatan || '-';
          case "targetInstitution": return item.targetInstitution || '-';
          case "targetUniversity": return item.targetUniversity || '-';
          case "targetMajor": return item.targetMajor || '-';
          case "categories": 
            if (!item.categoryNames || item.categoryNames.length === 0) return '-';
            return (
              <div className="flex flex-wrap gap-1">
                {item.categoryNames.slice(0, 2).map((cat, index) => {
                  const theme = getCategoryTheme(cat);
                  return (
                    <Chip key={index} color={theme.color as any} size="sm" variant="flat">
                      {theme.icon} {cat}
                    </Chip>
                  );
                })}
                {item.categoryNames.length > 2 && (
                  <Chip color="default" size="sm" variant="flat">
                    +{item.categoryNames.length - 2}
                  </Chip>
                )}
              </div>
            );
          case "completedAt": 
              if (!item.completedAt) return '-';
              
              try {
                // Extract the date and time parts, ignoring the timezone for now
                const dateTimeStr = item.completedAt.split('+')[0].trim();
                const [datePart, timePart] = dateTimeStr.split(' ');
                const [year, month, day] = datePart.split('-');
                const [hour, minute, secondPart] = timePart.split(':');
                // Handle seconds which might have milliseconds
                const second = secondPart.split('.')[0];
                
                // Create a date in UTC
                const date = new Date(Date.UTC(
                  parseInt(year),
                  parseInt(month) - 1, // Month is 0-based in JavaScript
                  parseInt(day),
                  parseInt(hour),
                  parseInt(minute),
                  parseInt(second)
                ));
                
                // Convert to local timezone
                return date.toLocaleString();
              } catch (error) {
                console.error("Date parsing error:", error);
                return item.completedAt;
              }
          default: return '';
        }
      })();
    
      return (
        <div>
          {value}
          {item.isCurrentUser && <span className="ml-2 text-sm text-gray-500">(You)</span>}
        </div>
      );
    };
    // Show empty state when no data is available
    if (!data || data.length === 0) {
      return (
        <EmptyStateMessage 
          title="No Entries Found"
          message={
            showTargetSelectors 
              ? "No entries found for the selected filters. Try adjusting your selection."
              : "There are no entries in the leaderboard yet. Be the first to complete this exam!"
          }
        />
      );
    }
  
    // Render select options for each target type
    const renderPositions = () => (
      <>
        <SelectItem key="" textValue="">
          All Positions
        </SelectItem>
        {safeTargetOptions.positions && safeTargetOptions.positions.map((pos) => (
          <SelectItem key={pos.value} textValue={pos.value}>
            {`${pos.label} (${pos.count})`}
          </SelectItem>
        ))}
      </>
    );
    
    const renderInstitutions = () => (
      <>
        <SelectItem key="" textValue="">
          All Institutions
        </SelectItem>
        {safeTargetOptions.institutions && safeTargetOptions.institutions.map((inst) => (
          <SelectItem key={inst.value} textValue={inst.value}>
            {`${inst.label} (${inst.count})`}
          </SelectItem>
        ))}
      </>
    );
  
    const renderUniversities = () => (
      <>
        <SelectItem key="" textValue="">
          All Universities
        </SelectItem>
        {safeTargetOptions.universities && safeTargetOptions.universities.map((univ) => (
          <SelectItem key={univ.value} textValue={univ.value}>
            {`${univ.label} (${univ.count})`}
          </SelectItem>
        ))}
      </>
    );
  
    const renderMajors = () => (
      <>
        <SelectItem key="" textValue="">
          All Majors
        </SelectItem>
        {safeTargetOptions.majors && safeTargetOptions.majors.map((major) => (
          <SelectItem key={major.value} textValue={major.value}>
            {`${major.label} (${major.count})`}
          </SelectItem>
        ))}
      </>
    );
    // Render the leaderboard table and its container
    return (
      <Card className="w-full mb-6">
        <CardHeader className="flex flex-col gap-4">
          <h4 className="text-lg font-bold text-center">{title}</h4>
          {currentUserRank && (
              <div className="text-sm text-center text-gray-500">
                Your Rank: #{currentUserRank}
              </div>
          )}
          {showTargetSelectors && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
              {safeTargetOptions.positions && safeTargetOptions.positions.length > 0 && (
                <Select
                  label="Filter by Position"
                  placeholder="Select a position"
                  selectedKeys={selectedPosition ? [selectedPosition] : new Set([])}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0]?.toString() || '';
                    onPositionChange?.(value);
                  }}
                  className="w-full"
                >
                  {renderPositions()}
                </Select>
              )}
  
              {safeTargetOptions.institutions && safeTargetOptions.institutions.length > 0 && (
                <Select
                  label="Filter by Institution"
                  placeholder="Select an institution"
                  selectedKeys={selectedInstitution ? [selectedInstitution] : new Set([])}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0]?.toString() || '';
                    onInstitutionChange?.(value);
                  }}
                  className="w-full"
                >
                  {renderInstitutions()}
                </Select>
              )}
  
              {safeTargetOptions.universities && safeTargetOptions.universities.length > 0 && (
                <Select
                  label="Filter by University"
                  placeholder="Select a university"
                  selectedKeys={selectedUniversity ? [selectedUniversity] : new Set([])}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0]?.toString() || '';
                    onUniversityChange?.(value);
                  }}
                  className="w-full"
                >
                  {renderUniversities()}
                </Select>
              )}
  
              {safeTargetOptions.majors && safeTargetOptions.majors.length > 0 && (
                <Select
                  label="Filter by Major"
                  placeholder="Select a major"
                  selectedKeys={selectedMajor ? [selectedMajor] : new Set([])}
                  onSelectionChange={(keys) => {
                    const value = Array.from(keys)[0]?.toString() || '';
                    onMajorChange?.(value);
                  }}
                  className="w-full"
                >
                  {renderMajors()}
                </Select>
              )}
            </div>
          )}
        </CardHeader>
        <Divider />
        <CardBody>
        <Table
          aria-label={title}
          className="mb-4"
          removeWrapper
        >
          <TableHeader>
            {columns.map((column) => (
              <TableColumn key={column.key} className={column.width}>
                {column.label}
              </TableColumn>
            ))}
          </TableHeader>
          <TableBody
            items={finalData}
            emptyContent="No entries found"
          >
            {(item: LeaderboardEntry) => (
              <TableRow 
                key={`${item.rank}-${item.username}`}
                className={item.username === currentUsername 
                  ? "bg-success-50/25 outline outline-success/50 outline-1 rounded-lg"
                  : ""}
              >
                {(columnKey) => (
                  <TableCell>
                    {renderCell(item, columnKey as Column["key"])}
                  </TableCell>
                )}
              </TableRow>
            )}
          </TableBody>
        </Table>
          
          {filteredData.length > 0 && hasAccessToPremium() && (
            <div className="flex justify-center">
              <Pagination
                total={Math.max(1, totalPages)}
                page={currentPage}
                onChange={setCurrentPage}
                size="sm"
              />
            </div>
          )}
        </CardBody>
      </Card>
    );
  };
  // Main component return
  return (
    <div className="p-6 relative">
      <div className="mb-6 max-w-sm mx-auto">
        <Input
          type="text"
          placeholder="Filter by username..."
          value={filterText}
          onChange={(e) => setFilterText(e.target.value)}
          className="w-full"
          size="sm"
          disabled={!hasAccessToPremium()}
        />
      </div>

      <div className={`flex flex-col gap-6 ${(!hasAccessToPremium()) && 'blur-sm pointer-events-none'}`}>
        <LeaderboardTable
          title="Try Out Nasional General"
          data={generalLeaderboard}
          currentPage={generalCurrentPage}
          setCurrentPage={setGeneralCurrentPage}
          showTimeColumn={true}
          showTargetSelectors={false}
          targetOptions={targetOptions}
          selectedPosition={selectedPosition}
          selectedInstitution={selectedInstitution}
          selectedUniversity={selectedUniversity}
          selectedMajor={selectedMajor}
          examConfig={examConfig}
          currentUsername={currentUsername}
          filterText={filterText}
          hasAccessToPremium={hasAccessToPremium}
          categories={categories}
          activeCategory={activeCategory}
        />
        
        <LeaderboardTable
          title="Try Out Nasional Pilih Target"
          data={bidangLeaderboard}
          currentPage={bidangCurrentPage}
          setCurrentPage={setBidangCurrentPage}
          showTimeColumn={true}
          showTargetSelectors={true}
          targetOptions={targetOptions}
          selectedPosition={selectedPosition}
          selectedInstitution={selectedInstitution}
          selectedUniversity={selectedUniversity}
          selectedMajor={selectedMajor}
          onPositionChange={setSelectedPosition}
          onInstitutionChange={setSelectedInstitution}
          onUniversityChange={setSelectedUniversity}
          onMajorChange={setSelectedMajor}
          examConfig={examConfig}
          currentUsername={currentUsername}
          filterText={filterText}
          hasAccessToPremium={hasAccessToPremium}
          categories={categories}
          activeCategory={activeCategory}
        />
      </div>
      {/* No premium access overlay */}
      {!hasAccessToPremium() && (
        <div className="absolute inset-0 flex items-start justify-center bg-background/30 pt-32">
          <Card className="max-w-md mx-auto p-6 text-center">
            <CardBody>
              <h2 className="text-xl font-bold mb-2">Premium Feature</h2>
              <p className="mb-4">
                {userSubscription?.subscription?.paymentStatus === 'PENDING' 
                  ? "Tunggu pembayaran diverifikasi untuk melihat detail skor dan waktu pengerjaan!"
                  : "Upgrade ke Premium untuk melihat detail skor dan waktu pengerjaan!"}
              </p>
              <Button 
                color="warning" 
                variant="solid"
                className="font-semibold"
                onPress={() => router.push('/subscription')}
              >
                {userSubscription?.subscription?.paymentStatus === 'PENDING' 
                  ? "Cek Status"
                  : "Upgrade Premium"}
              </Button>
            </CardBody>
          </Card>
        </div>
      )}
    </div>
  );
}

export default TryoutForm;