import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  Avatar,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  NavbarItem,
} from "@heroui/react";

import { logout } from "@/app/lib/auth/logout";

// Function to clear user session cookies from client-side
const clearUserSessionCookieClient = (email: string) => {
  console.log("Attempting to clear user session cookie on client-side for:", email);
  
  try {
    // Create encoded cookie name
    const encodedEmail = btoa(email);
    const cookieName = `user_session_${encodedEmail}`;
    
    // Log existing cookies for debugging
    const allCookies = document.cookie.split(';').map(cookie => cookie.trim());
    console.log("All current cookies:", allCookies);
    
    // Check if our target cookie exists
    const targetCookie = allCookies.find(c => c.startsWith(`${cookieName}=`));
    if (targetCookie) {
      console.log(`Found user session cookie on client: ${cookieName}`);
    } else {
      console.log(`No user session cookie found on client: ${cookieName}`);
    }
    
    // Create invalid data for overwriting
    const invalidData = JSON.stringify({
      timestamp: 0,
      data: {
        id: '',
        email: '',
        first_name: '',
        last_name: '',
        picture: '',
        role: ''
      },
      isLoggedOut: true
    });
    
    // Determine if we're in local environment
    const isLocal = window.location.hostname === 'localhost' || 
                    window.location.hostname === '127.0.0.1';
    
    // Set multiple variations to ensure cookie is properly cleared
    // Method 1: Set with maxAge=0
    if (isLocal) {
      document.cookie = `${cookieName}=${invalidData}; path=/; max-age=0;`;
    } else {
      document.cookie = `${cookieName}=${invalidData}; path=/; domain=.terang.ai; max-age=0; secure`;
      document.cookie = `${cookieName}=${invalidData}; path=/; domain=terang.ai; max-age=0; secure`;
    }
    
    // Method 2: Set with expires in the past
    const pastDate = new Date(0).toUTCString();
    if (isLocal) {
      document.cookie = `${cookieName}=${invalidData}; path=/; expires=${pastDate};`;
    } else {
      document.cookie = `${cookieName}=${invalidData}; path=/; domain=.terang.ai; expires=${pastDate}; secure`;
      document.cookie = `${cookieName}=${invalidData}; path=/; domain=terang.ai; expires=${pastDate}; secure`;
    }
    
    // Method 3: Set with empty value
    if (isLocal) {
      document.cookie = `${cookieName}=; path=/; max-age=0;`;
    } else {
      document.cookie = `${cookieName}=; path=/; domain=.terang.ai; max-age=0; secure`;
      document.cookie = `${cookieName}=; path=/; domain=terang.ai; max-age=0; secure`;
    }
    
    // Check if cookies were cleared
    const remainingCookies = document.cookie.split(';').map(cookie => cookie.trim());
    const stillExists = remainingCookies.some(c => c.startsWith(`${cookieName}=`));
    
    if (stillExists) {
      console.log(`WARNING: User session cookie STILL EXISTS after clearing attempts`);
    } else {
      console.log(`Successfully cleared user session cookie from client-side`);
    }
    
    return true;
  } catch (error) {
    console.error("Error clearing client-side session cookie:", error);
    return false;
  }
};

export const UserDropdown: React.FC = () => {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(undefined);

  // Set avatar URL once when session loads
  useEffect(() => {
    if (session?.user?.image) {
      setAvatarUrl(session.user.image);
    }
  }, [session?.user?.image]);

  // Log session data for debugging
  useEffect(() => {
    if (session?.user) {
      console.log("=== NAVBAR USER-DROPDOWN SESSION ===");
      console.log("User session in dropdown:", {
        email: session.user.email,
        name: session.user.name,
        image: session.user.image,
        firstname: session.user.firstname,
        lastname: session.user.lastname
      });
    }
  }, [session]);

  const handleSettings = async () => {
    router.replace('/settings');
  }
  
  const handleLogout = async () => {
    // First try to clear session cookie on client-side
    if (session?.user?.email) {
      console.log("Starting client-side cookie cleanup before logout");
      clearUserSessionCookieClient(session.user.email);
    }
    
    // Then proceed with server-side logout
    if (session?.user?.email) {
      await logout(session.user.email);
    } else {
      await logout();
    }
  };

  if (status === "loading") {
    return (
      <NavbarItem>
        <Avatar
          isBordered
          showFallback
          as="button"
          color="default"
          size="sm"
          src={undefined}
        />
      </NavbarItem>
    );
  }

  const avatarName = session?.user?.name || session?.user?.email || undefined;

  return (
    <Dropdown>
      <NavbarItem>
        <DropdownTrigger>
          <Avatar
            isBordered
            showFallback
            as="button"
            color="default"
            imgProps={{ 
              referrerPolicy: "no-referrer",
              loading: "eager",
              fetchPriority: "high"
            }}
            name={avatarName}
            size="sm"
            src={avatarUrl}
          />
        </DropdownTrigger>
      </NavbarItem>
      <DropdownMenu
        aria-label="User menu actions"
        onAction={(actionKey) => console.log({ actionKey })}
      >
        <DropdownItem
          key="profile"
          className="flex flex-col justify-start w-full items-start"
        >
          <p>Signed in as:</p>
          <p>{session?.user?.name || "Unauthorised Name"}</p>
          <p className="text-xs text-primary">
            ({session?.user?.email || "Unauthorised Email"})
          </p>
        </DropdownItem>
        <DropdownItem key="settings" onPress={handleSettings}>Settings</DropdownItem>
        <DropdownItem
          key="logout"
          className="text-danger"
          color="danger"
          onPress={handleLogout}
        >
          Log Out
        </DropdownItem>
      </DropdownMenu>
    </Dropdown>
  );
};