// app/api/exam-bundles-purchased/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getExamBundlesPurchased } from '@/app/lib/actions/available-exams/actions';

export async function GET(request: NextRequest) {
  try {

    // Call our action function to get the bundles
    const bundles = await getExamBundlesPurchased();
    console.log(bundles)

    // Handle error cases
    if (bundles === false) {
      return NextResponse.json(
        { error: 'Failed to fetch exam bundles' },
        { status: 400 }
      );
    }

    // Get query parameters for pagination (these will be used for client-side pagination since
    // our getExamBundlesPurchased action doesn't support pagination)
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '1000000');
    
    // Calculate pagination values
    const totalData = bundles.length;
    const totalPages = Math.ceil(totalData / pageSize);

    // Return the bundles with pagination data
    return NextResponse.json({
      bundles: bundles,
      pagination: {
        total_data: totalData,
        total_pages: totalPages,
        current_page: page,
        page_size: pageSize
      }
    });
  } catch (error) {
    console.error('Error fetching exam bundles:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}