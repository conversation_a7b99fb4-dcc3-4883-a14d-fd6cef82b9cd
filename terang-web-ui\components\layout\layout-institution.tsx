"use client";

import React from "react";
import { usePathname } from "next/navigation";
import { useLockedBody } from "../hooks/useBodyLock";
import { NavbarWrapper } from "../navbar/navbar";
import { InstitutionSidebarWrapper } from "../sidebar/sidebar-institution";
import { InstitutionNavbar } from "../navbar/navbar-institution";
import { SidebarContext } from "./layout-context";

interface Props {
  children: React.ReactNode;
}

export const Layout = ({ children }: Props) => {
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = React.useState(false);
  const [_, setLocked] = useLockedBody(false);

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
    setLocked(!sidebarOpen);
  };

  // Memoize the sidebar to render it only once
  const memoizedSidebar = React.useMemo(() => <InstitutionSidebarWrapper />, []);
  
  // Memoize the institution navbar to render it only once
  const memoizedInstitutionNav = React.useMemo(() => <InstitutionNavbar />, []);

  // Check if we should show the institution navbar
  const showInstitutionNavbar = pathname !== "/2-institution-create";

  return (
    <SidebarContext.Provider
      value={{
        collapsed: sidebarOpen,
        setCollapsed: handleToggleSidebar,
      }}
    >
      <section className="flex">
        {/* Left Sidebar - Always visible */}
        {memoizedSidebar}
        
        {/* Main Content Area */}
        <div className="flex-1">
          <NavbarWrapper>
            <div className="flex flex-col">
              {/* Institution navbar - conditional render */}
              {showInstitutionNavbar && memoizedInstitutionNav}
              {/* Page content */}
              <div className="flex-1 p-4">
                {children}
              </div>
            </div>
          </NavbarWrapper>
        </div>
      </section>
    </SidebarContext.Provider>
  );
};