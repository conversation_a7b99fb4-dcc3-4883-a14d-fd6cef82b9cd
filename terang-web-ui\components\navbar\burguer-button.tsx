import React from "react";
import { usePathname, useRouter } from "next/navigation";

import { useSidebarContext } from "../layout/layout-context";

import { StyledBurgerButton, StyledBackButton } from "./navbar.styles";

export const BurguerButton = () => {
  const { collapsed, setCollapsed } = useSidebarContext();
  const pathname = usePathname();
  const router = useRouter();

  const isExamPath = pathname && pathname.startsWith("/exam/");
  const isResultPath = pathname && pathname.startsWith("/result");
  const isTrialPath = pathname && pathname.startsWith("/trial");
  const isFeedbackPath = pathname && pathname.startsWith("/feedback");

  const handleClick = () => {
    if (isExamPath || isTrialPath || isFeedbackPath) {
      router.back();
    } else if (isResultPath) {
      router.push("/my-exams");
    } else {
      setCollapsed();
    }
  };

  const ArrowLeft01Icon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      color={"#000000"}
      fill={"none"}
      height={24}
      viewBox="0 0 24 24"
      width={24}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M15 6C15 6 9.00001 10.4189 9 12C8.99999 13.5812 15 18 15 18"
        opacity="0.4"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
      />
      <path
        d="M9 12C9.00001 10.4189 15 6 15 6"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.5"
      />
    </svg>
  );

  return (
    <div
      className={
        isExamPath || isResultPath || isTrialPath || isFeedbackPath
          ? StyledBackButton()
          : StyledBurgerButton()
      }
      role="presentation"
      onClick={handleClick}
    >
      {isExamPath || isResultPath || isTrialPath || isFeedbackPath ? (
        <span className="flex items-center">
          <ArrowLeft01Icon className="ml-3 mr-1" /> Back
        </span>
      ) : (
        <>
          <div />
          <div />
        </>
      )}
    </div>
  );
};
