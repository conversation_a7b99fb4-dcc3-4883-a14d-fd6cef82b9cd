"use client";

import * as React from "react";
import { Hero<PERSON><PERSON>rovider } from "@heroui/system";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { ThemeProviderProps } from "next-themes/dist/types";
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';
import { useEffect, useState } from 'react';
import { Provider } from 'react-redux';
import { makeStore } from '@/store';

// Initialize store outside component to ensure it's only created once
const store = makeStore();

export interface ProvidersProps {
  children: React.ReactNode;
  themeProps?: ThemeProviderProps;
}

export function Providers({ children, themeProps }: ProvidersProps) {
  // Handle mounted state to prevent hydration mismatch
  const [mounted, setMounted] = useState(false);
  
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Initialize PostHog in production only
  useEffect(() => {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
      posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY as string, {
        api_host: process.env.NEXT_PUBLIC_POSTHOG_HOST as string,
        person_profiles: 'identified_only',
        autocapture: true,
      });
    }
  }, []);
  
  // Prevent flash of wrong theme while loading
  if (!mounted) {
    return null;
  }
  
  const content = (
    <Provider store={store}>
      <HeroUIProvider locale="id-ID">
        <NextThemesProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={true}
          {...themeProps}
        >
          {children}
        </NextThemesProvider>
      </HeroUIProvider>
    </Provider>
  );
  
  if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined') {
    return <PostHogProvider client={posthog}>{content}</PostHogProvider>;
  }
  
  return content;
}