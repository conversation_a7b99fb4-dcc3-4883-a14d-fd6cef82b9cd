"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  CardHeader,
  CardBody,
  Button,
  Input,
  Tabs,
  Tab,
  Avatar,
  Chip,
} from "@heroui/react";
import {
  MessageCircle,
  Send,
  Bell,
  Search,
  ArrowLeft,
} from "lucide-react";
import { useRouter } from "next/navigation";

// Types
interface Message {
  id: number;
  senderId: number;
  receiverId: number;
  content: string;
  timestamp: string;
  isRead: boolean;
}

interface Teacher {
  id: number;
  name: string;
  avatar: string;
  subject: string;
  status: 'online' | 'offline';
  lastActive: string;
}

interface Announcement {
  id: number;
  title: string;
  content: string;
  timestamp: string;
  priority: 'high' | 'medium' | 'low';
  teacherName: string;
  subject: string;
}

export default function StudentCommunication() {
  const router = useRouter();
  const [selectedTab, setSelectedTab] = useState("messages");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTeacher, setSelectedTeacher] = useState<Teacher | null>(null);
  const [newMessage, setNewMessage] = useState("");
  const [messages, setMessages] = useState<Message[]>([]);

  const mockTeachers: Teacher[] = [
    {
      id: 1,
      name: "Mrs. <PERSON> <PERSON>",
      avatar: "https://avatar.iran.liara.run/public/girl?username=sarah",
      subject: "Mathematics",
      status: "online",
      lastActive: "2024-12-23T10:30:00"
    },
    {
      id: 2,
      name: "Mr. John Smith",
      avatar: "https://avatar.iran.liara.run/public/boy?username=john",
      subject: "Physics",
      status: "offline",
      lastActive: "2024-12-23T09:15:00"
    },
    {
      id: 3,
      name: "Ms. Emily Parker",
      avatar: "https://avatar.iran.liara.run/public/girl?username=emily",
      subject: "English",
      status: "online",
      lastActive: "2024-12-23T11:00:00"
    }
  ];

  const mockAnnouncements: Announcement[] = [
    {
      id: 1,
      title: "Mathematics Quiz Tomorrow",
      content: "Don't forget to prepare for tomorrow's algebra quiz. Topics include quadratic equations and polynomials.",
      timestamp: "2024-12-23T08:00:00",
      priority: "high",
      teacherName: "Mrs. Sarah Anderson",
      subject: "Mathematics"
    },
    {
      id: 2,
      title: "Physics Lab Report Due Date Extended",
      content: "The deadline for submitting your lab reports has been extended to next Friday.",
      timestamp: "2024-12-23T09:30:00",
      priority: "medium",
      teacherName: "Mr. John Smith",
      subject: "Physics"
    }
  ];

  useEffect(() => {
    if (selectedTeacher) {
      const initialTimestamp = new Date(selectedTeacher.lastActive).toISOString();
      setMessages([
        {
          id: 1,
          senderId: 0,
          receiverId: selectedTeacher.id,
          content: "Hello, I have a question about today's homework.",
          timestamp: initialTimestamp,
          isRead: true
        },
        {
          id: 2,
          senderId: selectedTeacher.id,
          receiverId: 0,
          content: "Of course! What would you like to know?",
          timestamp: initialTimestamp,
          isRead: true
        }
      ]);
    } else {
      setMessages([]);
    }
  }, [selectedTeacher]);

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedTeacher) return;
    
    const newMsg: Message = {
      id: messages.length + 1,
      senderId: 0, // student's ID
      receiverId: selectedTeacher.id,
      content: newMessage.trim(),
      timestamp: new Date().toISOString(),
      isRead: false
    };

    setMessages(prev => [...prev, newMsg]);
    setNewMessage("");
  };

  const renderMessagesTab = () => (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Teachers List */}
      <Card className="md:col-span-1">
        <CardHeader>
          <div className="w-full flex flex-col gap-2">
            <h3 className="text-lg font-semibold">My Teachers</h3>
            <Input
              placeholder="Search teachers..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              startContent={<Search className="text-default-400" size={18} />}
            />
          </div>
        </CardHeader>
        <CardBody>
          <div className="space-y-2">
            {mockTeachers
              .filter(teacher => 
                teacher.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                teacher.subject.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((teacher) => (
                <button
                  key={teacher.id}
                  className={`w-full flex items-center gap-3 p-2 rounded-lg cursor-pointer hover:bg-default-100 ${
                    selectedTeacher?.id === teacher.id ? "bg-default-100" : ""
                  }`}
                  onClick={() => setSelectedTeacher(teacher)}
                  role="option"
                  aria-selected={selectedTeacher?.id === teacher.id}
                >
                  <Avatar src={teacher.avatar} size="sm" />
                  <div className="flex-grow">
                    <p className="text-sm font-medium">{teacher.name}</p>
                    <p className="text-xs text-default-400">{teacher.subject}</p>
                  </div>
                  <div className="flex items-center">
                    <span 
                      className={`w-2 h-2 rounded-full ${
                        teacher.status === "online" ? "bg-success" : "bg-default-300"
                      }`}
                    />
                  </div>
                </button>
              ))}
          </div>
        </CardBody>
      </Card>

      {/* Chat Area */}
      <Card className="md:col-span-2">
        <CardHeader>
          {selectedTeacher ? (
            <div className="flex items-center gap-3">
              <Avatar src={selectedTeacher.avatar} size="sm" />
              <div>
                <h3 className="text-lg font-semibold">{selectedTeacher.name}</h3>
                <p className="text-small text-default-400">{selectedTeacher.subject}</p>
              </div>
            </div>
          ) : (
            <h3 className="text-lg font-semibold">Select a teacher to start messaging</h3>
          )}
        </CardHeader>
        <CardBody>
          <div className="h-[400px] flex flex-col">
            <div className="flex-grow overflow-y-auto space-y-4 p-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${
                    message.senderId === 0 ? "justify-end" : "justify-start"
                  }`}
                >
                  <div
                    className={`max-w-[70%] p-3 rounded-lg ${
                      message.senderId === 0
                        ? "bg-primary text-white"
                        : "bg-default-100"
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                    <p className="text-xs mt-1 opacity-70">
                      {new Date(message.timestamp).toLocaleTimeString([], { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="p-4 border-t">
              <div className="flex gap-2">
                <Input
                  placeholder="Type your message..."
                  value={newMessage}
                  className="flex-1"
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      handleSendMessage();
                    }
                  }}
                />
                <Button
                  className="min-w-[50px]"
                  color="primary"
                  isIconOnly
                  onPress={handleSendMessage}
                  isDisabled={!selectedTeacher || !newMessage.trim()}
                >
                  <Send size={18} />
                </Button>
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );

  const renderAnnouncementsTab = () => (
    <div className="space-y-6">
      <Input
        placeholder="Search announcements..."
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
        startContent={<Search className="text-default-400" size={18} />}
        className="w-64"
      />

      <Card>
        <CardBody>
          <div className="space-y-4">
            {mockAnnouncements
              .filter(announcement => 
                announcement.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                announcement.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                announcement.teacherName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                announcement.subject.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
              .map((announcement) => (
                <Card key={announcement.id} className="w-full">
                  <CardBody>
                    <div className="space-y-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="text-lg font-semibold">{announcement.title}</h4>
                          <div className="flex gap-2 mt-1">
                            <Chip
                              color={
                                announcement.priority === "high"
                                  ? "danger"
                                  : announcement.priority === "medium"
                                  ? "warning"
                                  : "success"
                              }
                              variant="flat"
                              size="sm"
                            >
                              {announcement.priority} priority
                            </Chip>
                            <Chip variant="flat" size="sm">
                              {announcement.subject}
                            </Chip>
                          </div>
                        </div>
                        <p className="text-small text-default-400">
                          {new Date(announcement.timestamp).toLocaleDateString()}
                        </p>
                      </div>
                      <p className="text-default-500">{announcement.content}</p>
                      <p className="text-sm text-default-400">
                        Posted by: {announcement.teacherName}
                      </p>
                    </div>
                  </CardBody>
                </Card>
              ))}
          </div>
        </CardBody>
      </Card>
    </div>
  );

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="mb-6">
        <Button
          variant="light"
          startContent={<ArrowLeft size={18} />}
          onClick={() => router.back()}
          className="mb-2"
        >
          Back to Dashboard
        </Button>
        <h1 className="text-2xl font-bold">My Communication Center</h1>
        <p className="text-default-500">
          Message your teachers and view important announcements
        </p>
      </div>

      <Tabs
        selectedKey={selectedTab}
        onSelectionChange={(key) => setSelectedTab(key as string)}
        aria-label="Communication tabs"
      >
        <Tab
          key="messages"
          title={
            <div className="flex items-center gap-2">
              <MessageCircle size={18} />
              <span>Messages</span>
            </div>
          }
        >
          {renderMessagesTab()}
        </Tab>
        <Tab
          key="announcements"
          title={
            <div className="flex items-center gap-2">
              <Bell size={18} />
              <span>Announcements</span>
            </div>
          }
        >
          {renderAnnouncementsTab()}
        </Tab>
      </Tabs>
    </div>
  );
}