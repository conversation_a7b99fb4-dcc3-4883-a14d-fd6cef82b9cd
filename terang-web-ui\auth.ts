import NextAuth from 'next-auth';
import { authConfig } from './auth.config';
import Google from "next-auth/providers/google";
import Credentials from "next-auth/providers/credentials";
// Remove: import { cookies } from 'next/headers';

// Simple check if logging out flag is set - used throughout the auth flow
async function isLoggingOut(): Promise<boolean> {
  try {
    const { cookies } = await import('next/headers');
    const cookieStore = await cookies();
    return cookieStore.get('logging_out')?.value === 'true';
  } catch (error) {
    return false;
  }
}

// Type for API user response
interface APIUserResponse {
  data: {
    id: string;
    email: string;
    username?: string;
    first_name?: string;
    last_name?: string;
    picture?: string;
    role?: string;
    password?: string;
    is_verified?: boolean;
  };
}

interface PasswordCompareResponse {
  match: boolean;
  error?: string;
}

// Helper function for API calls
async function fetchApi(endpoint: string, options: RequestInit = {}) {
  // Skip if logging out
  const loggingOut = await isLoggingOut();
  if (loggingOut) {
    throw new Error('User is logging out');
  }

  if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
    throw new Error('Missing required environment variables');
  }

  const response = await fetch(`${process.env.BACKEND_BASE_URL}${endpoint}`, {
    ...options,
    headers: {
      ...options.headers,
      'x-api-key': process.env.BACKEND_API_KEY,
      'Content-Type': 'application/json',
    },
  });
  
  if (!response.ok) {
    throw new Error(`API error: ${response.status}`);
  }
  
  return response.json();
}

// Function to determine if input is email
function isEmail(input: unknown): boolean {
  if (typeof input !== 'string') return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(input);
}

// Extended auth configuration with providers
const config = {
  ...authConfig,
  providers: [
    Google({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
      profile(profile) {
        return {
          id: profile.sub,
          email: profile.email,
          firstname: profile.given_name,
          lastname: profile.family_name,
          name: profile.name,
          image: profile.picture,
          role: 'user'
        };
      },
    }),
    Credentials({
      credentials: {
        username: {},
        password: {},
      },
      async authorize(credentials) {
        // Skip if logging out
        const loggingOut = await isLoggingOut();
        if (loggingOut) return null;
        
        try {
          if (!credentials?.username || !credentials?.password) {
            return null;
          }

          let userData: APIUserResponse;
          const isEmailInput = isEmail(credentials.username);

          // Get user data based on input type
          if (isEmailInput) {
            // If input is email, use the existing email endpoint
            const response = await fetchApi(`/v1/users/emails/${credentials.username}`);
            userData = response;
          } else {
            // If input is username, use the username endpoint
            const response = await fetchApi(`/v0/users/username/${credentials.username}`);
            userData = response;
          }

          const user = userData.data;
          if (!user) {
            console.error("User not found");
            return null;
          }

          // Compare password using the new endpoint
          const passwordResponse = await fetchApi(`/v0/users/compare-password`, {
            method: 'POST',
            body: JSON.stringify({
              email: user.email, // Always use the user's email for password comparison
              password: credentials.password
            })
          });

          const passwordResult: PasswordCompareResponse = passwordResponse;

          if (!passwordResult.match) {
            console.error("Password doesn't match");
            return null;
          }

          return {
            id: user.id,
            email: user.email,
            firstname: user.first_name,
            lastname: user.last_name,
            name: `${user.first_name || ''} ${user.last_name || ''}`.trim(),
            image: user.picture,
            role: user.role
          };
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      },
    }),
  ],
};

export const { handlers, auth, signIn, signOut } = NextAuth(config);