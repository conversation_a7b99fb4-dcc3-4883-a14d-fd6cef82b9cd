"use server";

import React from "react";

import { Exam } from "@/components/exam";
import { fetchExamQuestionsBySessionId } from "@/components/my-exams/actions";
import { auth } from "@/auth";
import { Option, Content } from "@/components/types";
import { storeToRedis, retrieveFromRedis } from "@/components/shared/actions";

// Move this function outside of the page component
async function getQuestionByIndex(sessionId: string, index: number) {
  "use server";
  const redisKey = `${sessionId}_examQuestions`;
  let parsedData = await retrieveFromRedis(redisKey);

  if (!parsedData) {
    throw new Error("Question data not found");
  }

  parsedData = JSON.parse(parsedData);

  if (index < 0 || index >= parsedData.length) {
    throw new Error("Question index out of bounds");
  }

  const { hints, explanation, ...sanitizedQuestion } = parsedData[index];

  return sanitizedQuestion;
}

export default async function ExamPage(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    console.log("Starting exam component");

    const session = await auth();

    if (!session || !session.user) {
      console.log("Unauthorized access attempt");

      return <div>Unauthorized</div>;
    }

    const { id } = params;

    console.log(`Fetching exam questions for session ID: ${id}`);

    const redisKey = `${id}_examQuestions`;
    let parsedData = await retrieveFromRedis(redisKey);

    if (!parsedData) {
      const questionData: any = await fetchExamQuestionsBySessionId(id);

      if (
        !questionData ||
        !questionData.data ||
        questionData.data.length === 0
      ) {
        console.log("No question data found");

        return <div>No exam questions found</div>;
      }

      try {
        parsedData = JSON.parse(questionData.data[0].data);
        await storeToRedis(redisKey, JSON.stringify(parsedData));
      } catch (parseError) {
        console.error("Error parsing question data:", parseError);

        return <div>Error parsing exam data</div>;
      }
    } else {
      parsedData = JSON.parse(parsedData);
    }

    // Sanitize the data
    const sanitizedData = parsedData.map((question: any) => {
      const { hints, explanation, ...sanitizedQuestion } = question;

      return {
        ...sanitizedQuestion,
        options: {
          ...sanitizedQuestion.options,
          values: sanitizedQuestion.options.values.map((option: Option) => ({
            id: option.id,
            data: option.data.map((item) => ({
              contents: item.contents.map((content: Content) => ({
                content: content.content,
                type: content.type,
              })),
            })),
            is_correct: option.is_correct,
          })),
        },
      };
    });

    // Get the total number of questions
    const totalQuestions = sanitizedData.length;

    // Store sanitized data in Redis
    await storeToRedis(redisKey, JSON.stringify(sanitizedData));

    return (
      <Exam
        getQuestionByIndex={getQuestionByIndex}
        sanitizedData={sanitizedData}
        sessionId={id}
        totalQuestions={totalQuestions}
      />
    );
  } catch (error) {
    console.error("Error in exam component:", error);

    return <div>An error occurred while loading the exam</div>;
  }
}
