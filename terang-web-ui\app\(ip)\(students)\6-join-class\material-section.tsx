import React, { useState, useCallback } from 'react';
import { Card, CardBody } from "@heroui/card";
import { Progress } from "@heroui/progress";
import { Button } from "@heroui/button";
import { 
  ChevronDown, 
  Book, 
  FileText, 
  Play, 
  Download,
  ExternalLink,
  CheckCircle2
} from 'lucide-react';

// Types and Interfaces
interface Content {
  id: string;
  title: string;
  type: 'text' | 'video';
  content: string;
  createdAt: string;
}

interface Chapter {
  id: string;
  title: string;
  order: number;
  contents: Content[];
}

interface Material {
  id: string;
  title: string;
  description: string;
  status: string;
  createdAt: string;
  chapters: Chapter[];
}

interface MaterialSectionProps {
  material: Material;
  isExpanded: boolean;
  onToggle: () => void;
}

const ContentItem: React.FC<{
  content: Content;
  isCompleted: boolean;
  onComplete: (id: string) => void;
}> = ({ content, isCompleted, onComplete }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleContentClick = useCallback((e: React.MouseEvent | React.KeyboardEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  }, [isExpanded]);

  const handleCompleteClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onComplete(content.id);
  }, [content.id, onComplete]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleContentClick(e);
    }
  }, [handleContentClick]);

  return (
    <Card className="ml-6 my-2">
      <CardBody className="py-3">
        <div className="flex flex-col">
          <div 
            role="button"
            tabIndex={0}
            className="flex justify-between items-center cursor-pointer"
            onClick={handleContentClick}
            onKeyDown={handleKeyDown}
            aria-expanded={isExpanded}
            aria-label={`${content.title} - ${content.type}`}
          >
            <div className="flex items-center space-x-3 flex-1">
              {content.type === 'video' ? (
                <Play className="w-4 h-4 text-blue-500" />
              ) : (
                <FileText className="w-4 h-4 text-blue-500" />
              )}
              <span className="text-sm font-medium">{content.title}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                className={`min-w-[120px] ${
                  isCompleted ? 'bg-green-50 text-green-600' : 'bg-gray-50 text-gray-600'
                }`}
                startContent={<CheckCircle2 className="w-4 h-4" />}
                onClick={handleCompleteClick}
                aria-pressed={isCompleted}
              >
                {isCompleted ? 'Completed' : 'Mark Complete'}
              </Button>
              <ChevronDown 
                className={`w-4 h-4 text-gray-400 transform transition-transform duration-200 
                  ${isExpanded ? 'rotate-180' : ''}`}
                aria-hidden="true"
              />
            </div>
          </div>
          
          {isExpanded && (
            <div className="mt-4 pl-7 pt-4 border-t">
              {content.type === 'text' ? (
                <div className="space-y-4">
                  <div className="prose prose-sm max-w-none text-gray-600">
                    {content.content}
                  </div>
                  <Button
                    size="sm"
                    className="bg-blue-50 text-blue-600 hover:bg-blue-100"
                    startContent={<Download className="w-4 h-4" />}
                  >
                    Download PDF
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
                    <Play className="w-12 h-12 text-gray-400" />
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      className="bg-blue-50 text-blue-600 hover:bg-blue-100"
                      startContent={<Play className="w-4 h-4" />}
                    >
                      Watch Video
                    </Button>
                    <Button
                      size="sm"
                      className="bg-gray-50 text-gray-600 hover:bg-gray-100"
                      startContent={<ExternalLink className="w-4 h-4" />}
                    >
                      Open in New Tab
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
};

const ChapterSection: React.FC<{
    chapter: Chapter;
    completedContents: string[];
    onComplete: (id: string) => void;
  }> = ({ chapter, completedContents, onComplete }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const completedInChapter = chapter.contents.filter(content => 
      completedContents.includes(content.id)
    ).length;
    const progress = (completedInChapter / chapter.contents.length) * 100;
  
    const handleChapterClick = useCallback((e: React.MouseEvent | React.KeyboardEvent) => {
      e.stopPropagation();
      setIsExpanded(!isExpanded);
    }, [isExpanded]);
  
    const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        handleChapterClick(e);
      }
    }, [handleChapterClick]);
  
    return (
      <Card className="my-2 border border-gray-200">
        <CardBody>
          <div className="flex flex-col">
            <div 
              role="button"
              tabIndex={0}
              className="flex justify-between items-center cursor-pointer"
              onClick={handleChapterClick}
              onKeyDown={handleKeyDown}
              aria-expanded={isExpanded}
              aria-label={`${chapter.title} - ${completedInChapter} of ${chapter.contents.length} completed`}
            >
              <div className="flex items-center space-x-3 flex-1">
                <Book className="w-5 h-5 text-blue-500" aria-hidden="true" />
                <div className="flex-1">
                  <span className="font-medium">{chapter.title}</span>
                  <Progress 
                    value={progress} 
                    className="max-w-md mt-2"
                    color={progress === 100 ? "success" : "primary"}
                    size="sm"
                    aria-label={`Chapter progress: ${Math.round(progress)}%`}
                  />
                </div>
                <span className="text-sm text-gray-500">
                  {completedInChapter}/{chapter.contents.length} completed
                </span>
              </div>
              <ChevronDown 
                className={`w-5 h-5 text-gray-400 transform transition-transform duration-200
                  ${isExpanded ? 'rotate-180' : ''}`}
                aria-hidden="true"
              />
            </div>
            
            {isExpanded && (
              <div className="mt-4">
                {chapter.contents.map((content) => (
                  <ContentItem 
                    key={content.id} 
                    content={content}
                    isCompleted={completedContents.includes(content.id)}
                    onComplete={onComplete}
                  />
                ))}
              </div>
            )}
          </div>
        </CardBody>
      </Card>
    );
  };
  
  const MaterialSection: React.FC<MaterialSectionProps> = ({
    material,
    isExpanded,
    onToggle,
  }) => {
    const [completedContents, setCompletedContents] = useState<string[]>([]);
  
    const totalContents = material.chapters.reduce(
      (sum, chapter) => sum + chapter.contents.length,
      0
    );
    const completedCount = completedContents.length;
    const progress = (completedCount / totalContents) * 100;
  
    const handleComplete = useCallback((contentId: string) => {
      setCompletedContents(prev =>
        prev.includes(contentId)
          ? prev.filter(id => id !== contentId)
          : [...prev, contentId]
      );
    }, []);
  
    const handleMaterialClick = useCallback((e: React.MouseEvent | React.KeyboardEvent) => {
      if (e.target === e.currentTarget || e.currentTarget.contains(e.target as Node)) {
        onToggle();
      }
    }, [onToggle]);
  
    const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        handleMaterialClick(e);
      }
    }, [handleMaterialClick]);
  
    return (
      <Card className="bg-white shadow-sm">
        <CardBody>
          <div className="flex flex-col">
            <div 
              role="button"
              tabIndex={0}
              className="flex justify-between items-start cursor-pointer"
              onClick={handleMaterialClick}
              onKeyDown={handleKeyDown}
              aria-expanded={isExpanded}
              aria-label={`${material.title} - ${completedCount} of ${totalContents} items completed`}
            >
              <div className="space-y-2 flex-1">
                <h3 className="text-lg font-semibold">{material.title}</h3>
                <p className="text-sm text-gray-600">{material.description}</p>
                <div className="flex items-center space-x-4">
                  <Progress 
                    value={progress} 
                    className="max-w-md"
                    color={progress === 100 ? "success" : "primary"}
                    size="sm"
                    aria-label={`Overall progress: ${Math.round(progress)}%`}
                  />
                  <span className="text-sm text-gray-500">
                    {completedCount}/{totalContents} completed
                  </span>
                </div>
              </div>
              <ChevronDown
                className={`w-5 h-5 text-gray-400 transform transition-transform duration-200
                  ${isExpanded ? 'rotate-180' : ''}`}
                aria-hidden="true"
              />
            </div>
            
            {isExpanded && (
              <div className="mt-6 pt-4 border-t">
                {material.chapters.map((chapter) => (
                  <ChapterSection 
                    key={chapter.id} 
                    chapter={chapter}
                    completedContents={completedContents}
                    onComplete={handleComplete}
                  />
                ))}
              </div>
            )}
          </div>
        </CardBody>
      </Card>
    );
  };
  
  export default MaterialSection;