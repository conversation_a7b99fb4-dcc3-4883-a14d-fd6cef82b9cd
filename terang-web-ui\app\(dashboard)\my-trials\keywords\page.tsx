// app/my-exams/page.tsx
import React, { Suspense } from "react";
import MyTrials from "@/components/my-trials-subjects";
import { getAvailablePractices } from "@/app/lib/actions/my-exams-and-trials/actions";
import { getUserSubscription } from '@/components/sidebar/actions';
import { getUserId } from "@/app/lib/actions/account/actions";
import { Spinner } from "@heroui/react";
import DotLottieAnimation from "@/components/shared/dotlottie-animation";

interface PageProps {
  searchParams: Promise<{
    page?: string;
    subject?: string;
  }>;
}

const MyPracticePage = async (props: PageProps) => {
  const searchParams = await props.searchParams;
  const userId = await getUserId();

  // Get paginated data
  const examsData = await getAvailablePractices({
    page: Number(searchParams.page) || 1,
    limit: 10,
    subject: searchParams.subject,
    type: 'PRACTICE'
  });

  const subscriptionData = await getUserSubscription();
  const isPremium = subscriptionData?.subscription?.tierId === 'premium_tier_001' 
    && subscriptionData?.subscription.paymentStatus === 'PAID';

  return (
    <div>
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen">
          <Spinner size="lg" />
        </div>
      }>
        {/* <MyTrials 
          examData={examsData}
          isPremium={isPremium}
          userId={userId}
        /> */}
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-80 z-10">
          <div className="text-center">
            <DotLottieAnimation
              src="https://cdn.terang.ai/dotlotties/coming-soon.lottie"
              autoplay
              loop
              width={"100%"}
              height={"100%"}
            />
            <h1 className={`text-center mt-4`}>Soon!</h1>
          </div>
        </div>
      </Suspense>
    </div>
  );
};

export default MyPracticePage;