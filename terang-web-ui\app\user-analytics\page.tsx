// app/analytics/page.tsx
import { Suspense } from "react";
import { getAnalyticsSummary } from "./actions";
import AnalyticsTable from "./components/AnalyticsTable";
import { SummaryCards } from "./components/SummaryCards";
import { TimeframeSelect } from "./components/TimeframeSelect";
import UserActivityInsights from "./components/UserActivityInsights";

export default async function AnalyticsPage() {
  const { success, data, error } = await getAnalyticsSummary("24h");

  if (!success) {
    return (
      <div className="p-4 text-red-600">
        Error loading analytics: {error}
      </div>
    );
  }

  const summary = data?.summary;

  async function updateTimeframe(timeframe: string) {
    "use server";
    await getAnalyticsSummary(timeframe);
  }

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Analytics Dashboard</h1>
          <TimeframeSelect onTimeframeChange={updateTimeframe} />
        </div>

        {summary && <SummaryCards summary={summary} />}
      </div>

      <div className="space-y-6">
        <h2 className="text-xl font-bold">User Activity</h2>
        <Suspense fallback={<div className="animate-pulse">Loading insights...</div>}>
          <UserActivityInsights />
        </Suspense>
      </div>

      <div className="space-y-6">
        <h2 className="text-xl font-bold">Event Log</h2>
        <Suspense fallback={<div className="animate-pulse">Loading events...</div>}>
          <AnalyticsTable totalEvents={summary?.totalEvents || 0} />
        </Suspense>
      </div>
    </div>
  );
}