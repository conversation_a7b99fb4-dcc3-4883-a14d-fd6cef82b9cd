import React, { useState, useEffect, useRef, CSSProperties } from 'react';

interface AdaptiveImageProps {
  src: string;
  alt: string;
  className?: string;
}

/**
 * AdaptiveImage component that adjusts size based on image orientation
 * and provides a hover zoom feature for better viewing
 */
export const AdaptiveImage: React.FC<AdaptiveImageProps> = ({ src, alt, className = '' }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isPortrait, setIsPortrait] = useState(false);
  const [isZoomed, setIsZoomed] = useState(false);
  const [canZoom, setCanZoom] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const img = imgRef.current;
    if (!img) return;

    // Reset states when src changes
    setImageLoaded(false);
    setIsZoomed(false);

    // Create a new Image to detect dimensions
    const tempImg = new Image();
    tempImg.onload = () => {
      // Check if image is portrait (height > width)
      const portrait = tempImg.height > tempImg.width;
      setIsPortrait(portrait);
      setImageLoaded(true);
      
      // Determine if the image is large enough to benefit from zooming
      // Only enable zoom for images that are larger than our display size
      const minDimension = Math.min(tempImg.width, tempImg.height);
      const thresholdForZoom = 400; // Minimum size that would benefit from zoom
      setCanZoom(minDimension > thresholdForZoom);
    };
    tempImg.onerror = () => {
      // Handle loading errors
      setImageLoaded(true); // Still mark as loaded to avoid UI getting stuck
      setCanZoom(false);
    };
    tempImg.src = src;
  }, [src]);

  const getContainerStyle = () => {
    // Base styles for the container
    const baseStyle: React.CSSProperties = {
      position: 'relative',
      overflow: 'hidden',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      cursor: canZoom ? 'zoom-in' : 'default',
      borderRadius: '4px',
      transition: 'all 0.3s ease'
    };
    
    if (isZoomed) {
      return {
        ...baseStyle,
        position: 'relative',
        zIndex: 40,
        cursor: 'zoom-out',
        boxShadow: '0 0 0 2000px rgba(0, 0, 0, 0.4)'
      };
    }
    
    return baseStyle;
  };

  const getImageStyle = () => {
    if (!imageLoaded) {
      // Return placeholder style while loading
      return {
        maxWidth: '250px',
        height: '250px',
        maxHeight: '250px',
        opacity: 0.3
      };
    }
  
    const baseStyle = {
      transition: 'all 0.3s ease',
      width: 'auto',
      height: 'auto'
    };
  
    if (isZoomed) {
      // Constrain the zoomed image to the container's max width and height
      return {
        ...baseStyle,
        maxWidth: '100%', // Ensure it doesn't exceed the container's width
        maxHeight: '100%', // Ensure it doesn't exceed the container's height
        transform: 'scale(1.0)',
        transformOrigin: 'center'
      };
    }
  
    if (isPortrait) {
      // For portrait images, allow more height
      return {
        ...baseStyle,
        maxWidth: '300px',
        maxHeight: '400px'
      };
    } else {
      // For landscape, maintain original constraints
      return {
        ...baseStyle,
        maxWidth: '350px',
        maxHeight: '350px'
      };
    }
  };

  const handleImageClick = () => {
    if (canZoom) {
      setIsZoomed(!isZoomed);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape' && isZoomed) {
      setIsZoomed(false);
    }
  };

  return (
    <div 
      className="flex justify-center items-center my-2"
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label="Toggle zoom"
    >
      <button 
        ref={containerRef}
        style={getContainerStyle() as CSSProperties | undefined}
        onClick={handleImageClick}
        className={`relative rounded ${isZoomed ? 'fixed inset-0 flex items-center justify-center z-50' : ''}`}
        aria-label={isZoomed ? 'Zoom out' : 'Zoom in'}
      >
        {!imageLoaded && (
          <div className="absolute flex items-center justify-center">
            <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
          </div>
        )}
        <img
          ref={imgRef}
          alt={alt}
          className={`object-contain ${className}`}
          src={src}
          style={getImageStyle()}
          onLoad={() => setImageLoaded(true)}
        />
      </button>
    </div>
  );
};

/**
 * Helper function to render media content with adaptive sizing
 */
export const renderMediaContent = (content: { content: string }) => {
  return (
    <AdaptiveImage
      src={content.content}
      alt="Question media"
    />
  );
};