"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { History } from "lucide-react";

export function InterviewHistoryNav() {
  const pathname = usePathname();
  const isActive = pathname === "/interview-history";

  return (
    <Link
      href="/interview-history"
      className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
        isActive
          ? "bg-blue-50 text-blue-700"
          : "text-gray-700 hover:bg-gray-100"
      }`}
    >
      <History size={20} />
      <span>Interview History</span>
    </Link>
  );
}