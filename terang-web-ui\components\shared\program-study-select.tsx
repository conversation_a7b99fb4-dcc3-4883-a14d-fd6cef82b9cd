import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Autocomplete, AutocompleteItem } from "@heroui/react";
import debounce from "lodash/debounce";

import { ProgramStudy } from "../types";
import { fetchProgramStudies } from "./actions";

interface ProgramStudySelectProps {
  selectedEducationLevel: number;
  onSelectionChange: (value: number, name?: string | null) => void;
  required?: boolean;
  initialValue?: { id: number; name: string } | null;
}

export const ProgramStudySelect: React.FC<ProgramStudySelectProps> = ({
  selectedEducationLevel,
  onSelectionChange,
  required = false,
  initialValue = null,
}) => {
  const [programStudies, setProgramStudies] = useState<ProgramStudy[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selection, setSelection] = useState<{
    value: string | null;
    name: string | null;
  }>({
    value: initialValue?.id ? String(initialValue.id) : null,
    name: initialValue?.name || null,
  });
  const [error, setError] = useState<string | null>(null);
  
  // Check if we're on iOS - used to adjust behavior specifically for iOS
  const isIOS = useMemo(() => {
    if (typeof window !== 'undefined') {
      return /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream;
    }
    return false;
  }, []);

  // Increase debounce time for iOS devices
  const debounceTime = isIOS ? 500 : 300;

  const fetchData = useCallback(async (educationLevel: number, search: string = "") => {
    if (!educationLevel) return;
    
    setIsLoading(true);
    try {
      // Use the fetchProgramStudies function but handle errors locally
      const result = await fetchProgramStudies(educationLevel, search);
      
      // Verify result is valid before using it
      if (Array.isArray(result)) {
        setProgramStudies(result);
        
        // If we have an initial value, see if we can find it in the results
        if (initialValue && initialValue.id) {
          const initialProgram = result.find(p => p.kode_pend === initialValue.id);
          if (initialProgram) {
            setSelection({
              value: String(initialValue.id),
              name: initialProgram.nama_pend
            });
          }
        }
      } else {
        console.error("Invalid program studies data returned:", result);
        setProgramStudies([]);
      }
    } catch (error) {
      console.error("Error fetching program studies:", error);
      setError("Failed to fetch program studies. Please try again.");
      setProgramStudies([]);
    } finally {
      setIsLoading(false);
    }
  }, [initialValue]);

  const debouncedFetch = useMemo(
    () => debounce((educationLevel: number, search: string) => {
      fetchData(educationLevel, search).catch(err => {
        console.error("Error in debounced fetch:", err);
      });
    }, debounceTime), 
    [fetchData, debounceTime]
  );

  useEffect(() => {
    if (selectedEducationLevel) {
      if (searchTerm.length >= 2) {
        debouncedFetch(selectedEducationLevel, searchTerm);
      } else if (searchTerm.length === 0) {
        // Only fetch initial data when search term is empty
        fetchData(selectedEducationLevel, "").catch(err => {
          console.error("Error fetching initial data:", err);
        });
      }
    } else {
      setProgramStudies([]);
    }

    return () => {
      debouncedFetch.cancel();
    };
  }, [selectedEducationLevel, searchTerm, debouncedFetch, fetchData]);

  // Modified input handler with iOS-specific adjustments
  const handleInputChange = (value: string) => {
    setSearchTerm(value);
    
    // Clear selection if field is cleared
    if (!value) {
      setSelection({ value: null, name: null });
      onSelectionChange(0, null);
    }
    
    validateSelection(selection.value);
  };

  // Enhanced selection handler that batches state updates
  const handleSelectionChange = (key: React.Key | null) => {
    // Handle deselection
    if (key === null) {
      setSelection({ value: null, name: null });
      onSelectionChange(0, null);
      return;
    }
    
    const keyString = String(key);
    const selectedProgram = programStudies.find(p => String(p.kode_pend) === keyString);
    const name = selectedProgram ? selectedProgram.nama_pend : null;
    
    // Batch state updates to reduce rendering
    setSelection({ value: keyString, name });
    
    // Pass values to parent
    onSelectionChange(Number(keyString), name);
    validateSelection(keyString);
    
    // For iOS: close keyboard after selection
    if (isIOS && document.activeElement instanceof HTMLElement) {
      document.activeElement.blur();
    }
  };

  const validateSelection = (value: string | null) => {
    if (required && !value) {
      setError("Please select a program study.");
    } else {
      setError(null);
    }
  };

  // Fallback UI in case of persistent errors
  if (error && programStudies.length === 0 && !isLoading) {
    return (
      <div>
        <p className="text-red-500">{error}</p>
        <button 
          onClick={() => fetchData(selectedEducationLevel, "")}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <Autocomplete
      errorMessage={error}
      isDisabled={!selectedEducationLevel}
      isInvalid={!!error}
      isLoading={isLoading}
      isRequired={required}
      label="Program Studi"
      placeholder="Cari program studi"
      selectedKey={selection.value}
      onInputChange={handleInputChange}
      onSelectionChange={handleSelectionChange}
      defaultSelectedKey={initialValue?.id ? String(initialValue.id) : undefined}
      // Add iOS-specific props
      inputProps={{
        autoCapitalize: "none",
        autoCorrect: "off",
        // Fix for iOS keyboard issues
        onBlur: isIOS ? () => {
          // Short delay to ensure selection is processed
          setTimeout(() => {
            validateSelection(selection.value);
          }, 100);
        } : undefined
      }}
    >
      {programStudies.map((program) => (
        <AutocompleteItem 
          key={String(program.kode_pend)} 
          textValue={program.nama_pend}
        >
          {program.nama_pend}
        </AutocompleteItem>
      ))}
    </Autocomplete>
  );
};