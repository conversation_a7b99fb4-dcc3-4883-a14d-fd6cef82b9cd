// app/actions/time-management.ts
'use server'

import { getUserId } from "../lib/actions/account/actions"

const API_URL = process.env.BACKEND_BASE_URL as string
const API_KEY = process.env.BACKEND_API_KEY as string

export interface QuestionMetadata {
  level: number
  name: string
  value: string
}

export interface QuestionTimeManagement {
  questionId: string
  title: string
  timeSpent: number
  averageTime: number
  timeDifference: number
  isCorrect: boolean
  isAttempted: boolean
  userAnswer: string
  correctAnswer: string
  timeStatus: string
  attemptedAt: string
  metadata: QuestionMetadata[]
}

// First update the TypeScript interface for TimeDistribution
interface TimeDistribution {
    fastCorrect: number
    fastIncorrect: number
    averageCorrect: number
    averageIncorrect: number
    slowCorrect: number
    slowIncorrect: number
    notAttempted: number
  }

export interface UserStats {
  timeSpent: number
  averageTimeSpent: number
  correctAnswers: number
  attemptedQuestions: number
  accuracyRate: number
  subjectStats: {
    [key: string]: SubjectUserStats
  }
}

export interface SubjectUserStats {
    subject: string
    totalAttempts: number
    totalQuestionsAttempted: number
    totalCorrect: number
    totalErrors: number
    accuracyRate: number
  }
export interface SubjectGlobalStats {
    Subject: string
    TotalAttempts: number
    TotalQuestionsAttempted: number
    TotalCorrect: number
    TotalErrors: number
    AccuracyRate: number
  }

export interface GlobalStats {
    averageTimeSpent: number
    totalAttempts: number
    averageAccuracyRate: number
    fastestTime: number
    slowestTime: number
    subjectStats: {
      [key: string]: SubjectGlobalStats
    }
  }
export interface SessionReport {
  examId: string
  examName: string
  sessionId: string
  totalQuestions: number
  expectedBaseTime: number
  userStats: UserStats
  globalStats: GlobalStats
  timeDistribution: TimeDistribution
  questions: QuestionTimeManagement[]
}

export interface QuestionDifficulty {
    score: number
    level: string
    timeFactor: number
    errorFactor: number
    expectedTime: number
  }
  
export interface ExamDifficulty {
    score: number
    level: string
    bySubject: {
      [key: string]: QuestionDifficulty
    }
  }

export interface TimeManagementReport {
    userId: string | boolean | null
    examId: string
    examName: string
    sessionId: string
    userStats: UserStats
    globalStats: GlobalStats
    totalQuestions: number
    examDifficulty: ExamDifficulty
    timeDistribution: TimeDistribution
    expectedBaseTime: number
    questions: QuestionTimeManagement[]
    sessions?: SessionReport[]
}

export interface InvalidationResponse {
    message: string
    error?: string
}

async function fetchWithHeaders(url: string, options: RequestInit = {}) {
  const response = await fetch(url, {
    headers: {
      'x-api-key': API_KEY,
      ...(options.headers || {})
    },
    ...options,
    cache: 'no-store',
  })
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  
  return response.json()
}

export async function getLatestReport(): Promise<TimeManagementReport> {
    const userId = await getUserId();
    return fetchWithHeaders(`${API_URL}/v0/reports/time-management/user/${userId}/latest`)
}

export async function getAggregateReport(): Promise<TimeManagementReport> {
    const userId = await getUserId();
    return fetchWithHeaders(`${API_URL}/v0/reports/time-management/user/${userId}`)
}

export async function getSpecificReport(sessionId: string): Promise<TimeManagementReport> {
  return fetchWithHeaders(`${API_URL}/v0/reports/time-management/${sessionId}`)
}

export async function getAvailableSessions(): Promise<{ sessionId: string; examName: string }[]> {
    const report = await getAggregateReport()
    console.log(report)
    return report.sessions?.map(session => ({
        sessionId: session.sessionId,
        examName: session.examName,
    })) || []
}

/**
 * Invalidates the cache for a specific user's reports
 * This will clear:
 * - The user's aggregate report
 * - The user's latest report
 * - All session reports for this user
 */
export async function invalidateUserCache(): Promise<InvalidationResponse> {
    const userId = await getUserId();
    return fetchWithHeaders(
        `${API_URL}/v0/reports/time-management/invalidate/user/${userId}`, 
        { method: 'POST' }
    );
}

/**
 * Invalidates the cache for a specific session report
 * This will also invalidate any user-related reports associated with this session
 */
export async function invalidateSessionCache(sessionId: string): Promise<InvalidationResponse> {
    return fetchWithHeaders(
        `${API_URL}/v0/reports/time-management/invalidate/session/${sessionId}`,
        { method: 'POST' }
    );
}

/**
 * Invalidates all caches before fetching new reports
 * Use this when you need completely fresh data
 */
export async function refreshAllReports(): Promise<TimeManagementReport> {
    const userId = await getUserId();
    
    // First invalidate the cache
    await invalidateUserCache();
    
    // Then fetch fresh data
    return fetchWithHeaders(`${API_URL}/v0/reports/time-management/user/${userId}`);
}

/**
 * Invalidates cache for a specific session before fetching it
 * Use this when you need fresh data for a specific session
 */
export async function refreshSessionReport(sessionId: string): Promise<TimeManagementReport> {
    // First invalidate the cache
    await invalidateSessionCache(sessionId);
    
    // Then fetch fresh data
    return fetchWithHeaders(`${API_URL}/v0/reports/time-management/${sessionId}`);
}