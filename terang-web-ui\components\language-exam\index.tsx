"use client";

import React, { useState, useEffect, useRef } from "react";
import { useRouter } from "next/navigation";
import { Questions } from "./questions";
import { QuestionBank } from "../shared/question-bank";
import ExamTimer from "../shared/timer";
import {
  // storeToRedis,
  // retrieveFromRedis,
  // fetchExamSessionBySessionId,
  updateExamSessionDone,
} from "../shared/actions";
// import { updateGamificationWithRedis } from "../shared/actions-gamification";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Card, CardBody, Spinner } from "@heroui/react";
import { QuestionData } from "../types";

interface Props {
  totalQuestions: number;
  sessionId: string;
  getQuestionByIndex: (sessionId: string, index: number) => Promise<any>;
  sanitizedData: QuestionData[];
}

interface TimeInfo {
  examDuration: string;
  elapsedDuration: string;
  remainingDuration: string;
}

interface SessionData {
  status: string;
  exam_duration: string;
  elapsed_duration: string;
  remaining_duration: string;
}

const parseDuration = (durationString: string): number => {
  if (!durationString) return 0;
  const [hours, minutes, secondsAndMillis] = durationString.split(":");
  const [seconds] = (secondsAndMillis || "").split(".");
  return (
    parseInt(hours || "0", 10) * 3600 +
    parseInt(minutes || "0", 10) * 60 +
    parseInt(seconds || "0", 10)
  );
};

export const Exam: React.FC<Props> = ({
  totalQuestions,
  sessionId,
  getQuestionByIndex,
  sanitizedData,
}) => {
  const router = useRouter();
  
  // States
  const [currentPage, setCurrentPage] = useState(0);
  const [currentQuestion, setCurrentQuestion] = useState<QuestionData | null>(null);
  const [questionIds, setQuestionIds] = useState<string[]>([]);
  const [flaggedQuestions, setFlaggedQuestions] = useState<{ [key: string]: boolean }>({});
  const [selectedOptions, setSelectedOptions] = useState<{ [key: string]: string }>({});
  const [questionTimes, setQuestionTimes] = useState<{ [key: string]: number }>({});
  const [timeInfo, setTimeInfo] = useState<TimeInfo | null>(null);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const [questionStartTime, setQuestionStartTime] = useState<number>(Date.now());
  const [isTimeUp, setIsTimeUp] = useState(false);
  const [loading, setLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [questionLoading, setQuestionLoading] = useState(false);

  const storeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const shouldSkipInitialStore = useRef<boolean>(true);
  const lastStoredState = useRef<string>("");
  const optionsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Simplified notification function
  const showNotification = (message: string, type: "success" | "error" | "info") => {
    toast[type](message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  // Optimized question time update with debouncing
  async function updateQuestionTime(questionId: string, forceUpdate: boolean = false) {
    const timeSpent = (Date.now() - questionStartTime) / 1000;
    const updatedTimes = {
      ...questionTimes,
      [questionId]: (questionTimes[questionId] || 0) + timeSpent,
    };

    setQuestionTimes(updatedTimes);

    if (storeTimeoutRef.current) {
      clearTimeout(storeTimeoutRef.current);
    }

    const updateRedis = async () => {
      try {
        // await updateGamificationWithRedis(sessionId, {
        //   question_times: updatedTimes,
        // });
      } catch (error) {
        console.error("Error updating question time:", error);
        showNotification("Failed to update question time", "error");
      }
    };

    if (forceUpdate) {
      await updateRedis();
    } else {
      storeTimeoutRef.current = setTimeout(updateRedis, 1000);
    }
  }

  async function handleOptionSelect(questionId: string, optionId: string) {
    try {
      setSelectedOptions((prev) => {
        const newState = { ...prev };
  
        if (newState[questionId] === optionId) {
          delete newState[questionId];
        } else {
          newState[questionId] = optionId;
        }
  
        // Clear any pending timeouts
        if (optionsTimeoutRef.current) {
          clearTimeout(optionsTimeoutRef.current);
        }
  
        // Store the updated selected options in Redis
        // storeToRedis(`${sessionId}_selectedOptions`, JSON.stringify(newState));
  
        // Update question time logic can remain outside setState
        const currentTime = Date.now();
        // updateQuestionTime(questionId, true);
        setQuestionStartTime(currentTime);
  
        return newState;
      });
  
      console.log("Option selected and state updated");
    } catch (error) {
      console.error("Error during option selection:", error);
      showNotification("Failed to save your answer. Please try again.", "error");
    }
  } 

  // Simplified flag question handler
  function handleFlagQuestion(questionId: string) {
    setFlaggedQuestions(prev => {
      const newState = { ...prev };
      const key = questionId.toString();
      if (newState[key]) {
        delete newState[key];
      } else {
        newState[key] = true;
      }
      return newState;
    });
  }

  // Simplified exit exam handler
  async function handleExitExam() {
    setIsSubmitting(true);
    try {
      if (currentQuestion) {
        await updateQuestionTime(currentQuestion.id.toString(), true);
      }
      
      // Get exam type from institute metadata
      if (sanitizedData && sanitizedData.length > 0) {
        const instituteMetadata = sanitizedData[0].metadata.find(
          (meta) => meta.name === "institute"
        );
        
        if (instituteMetadata && instituteMetadata.value) {
          const examType = instituteMetadata.value;
          console.log(`Found exam type in metadata: ${examType}`);
          
          await updateExamSessionDone(sessionId, examType);
          showNotification(
            "Exam session completed. Thank you for participating!",
            "success"
          );
          setTimeout(() => {
            router.push(`/result/${sessionId}`);
          }, 3000);
          return;
        }
      }
      
      console.error("No institute metadata found in questions");
      showNotification("Error completing exam: missing exam type information", "error");
      setIsSubmitting(false);
    } catch (error) {
      console.error("Error completing exam session:", error);
      showNotification("Failed to complete the exam session.", "error");
      setIsSubmitting(false);
    }
  }

  // Simplified time handlers
  function handleTimeUp() {
    setIsTimeUp(true);
    handleExitExam();
  }

  function updateTimeInfo(newRemainingTime: number) {
    if (timeInfo) {
      const elapsedSeconds = parseDuration(timeInfo.examDuration) - newRemainingTime;
      const newElapsedDuration = new Date(elapsedSeconds * 1000)
        .toISOString()
        .substr(11, 8);
      const newRemainingDuration = new Date(newRemainingTime * 1000)
        .toISOString()
        .substr(11, 8);
  
      // Use requestAnimationFrame to schedule state updates outside of the render phase
      requestAnimationFrame(() => {
        setTimeInfo(prev => ({
          ...prev!,
          elapsedDuration: newElapsedDuration,
          remainingDuration: newRemainingDuration,
        }));
        setTimeLeft(newRemainingTime);
      });
  
      // Handle time up separately
      if (newRemainingTime === 0) {
        requestAnimationFrame(() => {
          handleTimeUp();
        });
      }
    }
  }

  // Initial data fetch effect
  // useEffect(() => {
  //   async function fetchDataAndCheckStatus() {
  //     try {
  //       const sessionData = await fetchExamSessionBySessionId(sessionId) as SessionData;

  //       if (
  //         sessionData.status === "ABANDONED" ||
  //         sessionData.status === "COMPLETED"
  //       ) {
  //         router.push("/my-exams");
  //         return;
  //       }

  //       setQuestionIds(sanitizedData.map((question) => question.id.toString()));
  //       setCurrentQuestion(sanitizedData[0]);

  //       const newTimeInfo: TimeInfo = {
  //         examDuration: sessionData.exam_duration,
  //         elapsedDuration: sessionData.elapsed_duration,
  //         remainingDuration: sessionData.remaining_duration,
  //       };

  //       setTimeInfo(newTimeInfo);
  //       setTimeLeft(parseDuration(newTimeInfo.remainingDuration));

  //       const [
  //         storedFlaggedQuestions,
  //         storedSelectedOptions,
  //         storedQuestionTimes,
  //       ] = await Promise.all([
  //         retrieveFromRedis(`${sessionId}_flaggedQuestions`),
  //         retrieveFromRedis(`${sessionId}_selectedOptions`),
  //         retrieveFromRedis(`${sessionId}_questionTimes`),
  //       ]);

  //       if (storedFlaggedQuestions) {
  //         setFlaggedQuestions(JSON.parse(storedFlaggedQuestions));
  //       }
  //       if (storedSelectedOptions) {
  //         setSelectedOptions(JSON.parse(storedSelectedOptions));
  //       }
  //       if (storedQuestionTimes) {
  //         setQuestionTimes(JSON.parse(storedQuestionTimes));
  //       }

  //       setIsInitialized(true);
  //     } catch (error) {
  //       console.error("Error fetching data:", error);
  //       showNotification("Error loading exam data. Please try again.", "error");
  //     } finally {
  //       setLoading(false);
  //     }
  //   }

  //   fetchDataAndCheckStatus();
  // }, [sessionId, router, sanitizedData]);

  // Update current question effect
  useEffect(() => {
    setCurrentQuestion(sanitizedData[currentPage]);
    setQuestionStartTime(Date.now());
  }, [currentPage, sanitizedData]);

  // Store state to Redis effect
  // useEffect(() => {
  //   if (loading || !isInitialized) return;

  //   if (shouldSkipInitialStore.current) {
  //     shouldSkipInitialStore.current = false;
  //     return;
  //   }

  //   const currentState = JSON.stringify({
  //     flaggedQuestions,
  //     selectedOptions,
  //     timeInfo,
  //     questionTimes,
  //   });

  //   if (currentState === lastStoredState.current) {
  //     return;
  //   }

  //   lastStoredState.current = currentState;

  //   const storeTimeout = setTimeout(async () => {
  //     try {
  //       await Promise.all([
  //         storeToRedis(`${sessionId}_flaggedQuestions`, JSON.stringify(flaggedQuestions)),
  //         storeToRedis(`${sessionId}_selectedOptions`, JSON.stringify(selectedOptions)),
  //         storeToRedis(`${sessionId}_timeInfo`, JSON.stringify(timeInfo)),
  //         storeToRedis(`${sessionId}_questionTimes`, JSON.stringify(questionTimes)),
  //       ]);
  //     } catch (error) {
  //       console.error("Error storing state to Redis:", error);
  //       showNotification(
  //         "Error saving progress. Please check your connection.",
  //         "error"
  //       );
  //     }
  //   }, 500);

  //   return () => clearTimeout(storeTimeout);
  // }, [
  //   flaggedQuestions,
  //   selectedOptions,
  //   timeInfo,
  //   questionTimes,
  //   sessionId,
  //   loading,
  //   isInitialized,
  // ]);

  if (loading || !timeInfo || isSubmitting) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-100">
        <Card className="w-80">
          <CardBody className="flex flex-col items-center justify-center p-8">
            <Spinner color="primary" size="lg" />
            <p className="mt-4 text-lg font-semibold">
              {isSubmitting ? "Completing exam..." : "Loading exam..."}
            </p>
            <p className="mt-2 text-sm text-gray-500">Please wait</p>
          </CardBody>
        </Card>
        <ToastContainer style={{ zIndex: 9999 }} />
      </div>
    );
  }

  return (
    <div className="my-10 px-4 lg:px-16 mx-auto w-full flex flex-col gap-4">
      <div className="lg:hidden w-full mb-4">
        <ExamTimer
          elapsedDuration={timeInfo.elapsedDuration}
          examDuration={timeInfo.examDuration}
          remainingDuration={timeInfo.remainingDuration}
          sessionId={sessionId}
          onTimeUp={handleTimeUp}
          onTimeUpdate={updateTimeInfo}
        />
      </div>
      <div className="w-full flex flex-col lg:flex-row gap-4">
        <div className="w-full lg:w-[75%] order-0">
          {currentQuestion && (
            <Questions
              currentPage={currentPage}
              flaggedQuestions={flaggedQuestions}
              questionData={currentQuestion}
              questionLoading={questionLoading}
              selectedOptions={selectedOptions}
              sessionId={sessionId}
              setCurrentPage={setCurrentPage}
              setFlaggedQuestions={handleFlagQuestion}
              setSelectedOptions={handleOptionSelect}
              totalQuestions={totalQuestions}
            />
          )}
        </div>
        <div className="w-full lg:w-[25%] order-1 lg:order-none">
          <div className="hidden lg:block">
            <ExamTimer
              elapsedDuration={timeInfo.elapsedDuration}
              examDuration={timeInfo.examDuration}
              remainingDuration={timeInfo.remainingDuration}
              sessionId={sessionId}
              onTimeUp={handleTimeUp}
              onTimeUpdate={updateTimeInfo}
            />
          </div>
          <QuestionBank
            currentPage={currentPage}
            flaggedQuestions={flaggedQuestions}
            isLoading={isSubmitting}
            isTimeUp={isTimeUp}
            questionIds={questionIds}
            selectedOptions={selectedOptions}
            setCurrentPage={setCurrentPage}
            totalQuestions={totalQuestions}
            onExitExam={handleExitExam}
          />
        </div>
      </div>
      <ToastContainer style={{ zIndex: 9999 }} />
    </div>
  );
};