"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";

import { Questions } from "./questions";

import { QuestionBank } from "./question-bank";
import {
  storeToRedis,
  retrieveFromRedis,
  fetchExamSessionBySessionId,
  updateExamSessionDone,
  updateGamificationWithRedis,
  fetchGamificationState,
  handleAnswerSubmission,
  isGameOver,
  Gamification,
} from "./actions";

import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Card, CardBody, Spinner } from "@heroui/react";
import GameOverModal from "./game-over-modal";

interface Props {
  totalQuestions: number;
  subject: string;
  sessionId: string;
  getQuestionByIndex: (sessionId: string, index: number) => Promise<any>;
  sanitizedData: any[];
  isPremium: boolean;
}

const parseDuration = (durationString: string): number => {
  if (!durationString) return 0;
  const [hours, minutes, secondsAndMillis] = durationString.split(":");
  const [seconds] = (secondsAndMillis || "").split(".");

  return (
    parseInt(hours || "0", 10) * 3600 +
    parseInt(minutes || "0", 10) * 60 +
    parseInt(seconds || "0", 10)
  );
};

export const ExamTrial: React.FC<Props> = ({
  totalQuestions,
  subject,
  sessionId,
  getQuestionByIndex,
  sanitizedData,
  isPremium,
}) => {
  const router = useRouter();
  const isMounted = useRef(false);
  
  // Core states
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [selectedOptions, setSelectedOptions] = useState<{ [key: string]: string }>({});
  const [correctAnswers, setCorrectAnswers] = useState<{ [key: string]: boolean }>({});
  const [flaggedQuestions, setFlaggedQuestions] = useState<{ [key: string]: boolean }>({});
  const [questionTimes, setQuestionTimes] = useState<{ [key: string]: number }>({});

  // UI states
  const [loading, setLoading] = useState(true);
  const [currentQuestion, setCurrentQuestion] = useState<any>(null);
  const [questionLoading, setQuestionLoading] = useState(false);
  const [showAnswerStatus, setShowAnswerStatus] = useState(false);
  const [showHints, setShowHints] = useState(false);
  const [canInteract, setCanInteract] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [questionStartTime, setQuestionStartTime] = useState<number>(Date.now());
  const [isGameOverModalOpen, setIsGameOverModalOpen] = useState(false);
  
  // Game states
  const [gamification, setGamification] = useState<Gamification | null>(null);
  const [questionIds, setQuestionIds] = useState<string[]>([]);
  
  // For tracking hydration status
  const isHydrated = useRef(false);
  
  // Reference to track if an update is in progress
  const isUpdating = useRef(false);
  
  // Timeout reference for store operations
  const storeTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  // Debug logs for selected options
  useEffect(() => {
    if (currentQuestion && selectedOptions) {
      console.log("Current selectedOptions:", selectedOptions);
      console.log("Current question ID:", currentQuestion.id);
      console.log("Selected option for current question:", selectedOptions[currentQuestion.id]);
    }
  }, [selectedOptions, currentQuestion]);

  const showNotification = (
    message: string,
    type: "success" | "error" | "info",
  ) => {
    toast[type](message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  const handleExitExam = async () => {
    setIsSubmitting(true);
    try {
      // Ensure current state is saved before exiting
      await storeCurrentState(true);
      await updateExamSessionDone(sessionId, subject);
      showNotification(
        "Trial exam session completed. Thank you for participating!",
        "success",
      );
      setTimeout(() => {
        router.push(`/result/${sessionId}/${subject}`);
      }, 1000);
    } catch (error) {
      console.error("Error completing trial exam session:", error);
      showNotification("Failed to complete the trial exam session.", "error");
      setIsSubmitting(false);
    }
  };

  // Initialize state from Redis once, at component mount
  useEffect(() => {
    const initializeState = async () => {
      if (isMounted.current) {
        // Skip initialization if already mounted to prevent hydration issues
        return;
      }
      
      isMounted.current = true;
      
      try {
        setLoading(true);
        
        // Load session data
        const sessionData = await fetchExamSessionBySessionId(sessionId);
        if (sessionData.status === "ABANDONED" || sessionData.status === "COMPLETED") {
          router.push("/my-exams");
          return;
        }

        // Fetch all stored state from Redis in parallel for better performance
        const [
          storedPage,
          storedOptions,
          storedCorrect,
          storedFlagged,
          storedTimes,
          gamificationData
        ] = await Promise.all([
          retrieveFromRedis(`${sessionId}_currentPage`),
          retrieveFromRedis(`${sessionId}_selectedOptions`),
          retrieveFromRedis(`${sessionId}_correctAnswers`),
          retrieveFromRedis(`${sessionId}_flaggedQuestions`),
          retrieveFromRedis(`${sessionId}_questionTimes`),
          fetchGamificationState(sessionId)
        ]);

        // Set states with stored values or defaults
        const parsedPage = storedPage ? parseInt(storedPage) : 0;
        setCurrentPage(parsedPage);
        
        // Parse stored options with fallback to empty object
        let parsedOptions = {};
        try {
          if (storedOptions) {
            parsedOptions = JSON.parse(storedOptions);
          }
        } catch (e) {
          console.error("Error parsing stored options:", e);
        }
        setSelectedOptions(parsedOptions);
        
        setCorrectAnswers(storedCorrect ? JSON.parse(storedCorrect) : {});
        setFlaggedQuestions(storedFlagged ? JSON.parse(storedFlagged) : {});
        setQuestionTimes(storedTimes ? JSON.parse(storedTimes) : {});
        setGamification(gamificationData);
        
        // Set question IDs and current question
        const ids = sanitizedData.map(q => q.id);
        setQuestionIds(ids);
        setCurrentQuestion(sanitizedData[parsedPage]);
        
        // Clear loading state and mark as hydrated
        isHydrated.current = true;
        
      } catch (error) {
        console.error("Error initializing state:", error);
        showNotification("Error loading exam data", "error");
      } finally {
        setLoading(false);
      }
    };

    initializeState();
    
    // Cleanup function to clear any pending timeouts
    return () => {
      if (storeTimeoutRef.current) {
        clearTimeout(storeTimeoutRef.current);
      }
    };
  }, [sessionId, router, sanitizedData]);

  // Function to store current state to Redis
  const storeCurrentState = useCallback(async (immediate = false) => {
    // If an update is already in progress and not immediate, skip
    if (isUpdating.current && !immediate) return;
    
    // Clear any pending timeouts
    if (storeTimeoutRef.current) {
      clearTimeout(storeTimeoutRef.current);
      storeTimeoutRef.current = null;
    }
    
    // If immediate, store now; otherwise, schedule
    const doStore = async () => {
      if (isUpdating.current) return;
      
      try {
        isUpdating.current = true;
        
        console.log("Storing to Redis, selectedOptions:", selectedOptions);
        
        // Store all state at once for better performance
        await Promise.all([
          storeToRedis(`${sessionId}_currentPage`, currentPage.toString()),
          storeToRedis(`${sessionId}_selectedOptions`, JSON.stringify(selectedOptions)),
          storeToRedis(`${sessionId}_correctAnswers`, JSON.stringify(correctAnswers)),
          storeToRedis(`${sessionId}_flaggedQuestions`, JSON.stringify(flaggedQuestions)),
          storeToRedis(`${sessionId}_questionTimes`, JSON.stringify(questionTimes))
        ]);
      } catch (error) {
        console.error("Error storing state to Redis:", error);
      } finally {
        isUpdating.current = false;
      }
    };
    
    if (immediate) {
      await doStore();
    } else {
      storeTimeoutRef.current = setTimeout(doStore, 200);
    }
  }, [sessionId, currentPage, selectedOptions, correctAnswers, flaggedQuestions, questionTimes]);

  // Effect to update current question when page changes
  useEffect(() => {
    if (!loading && sanitizedData && currentPage < sanitizedData.length) {
      setCurrentQuestion(sanitizedData[currentPage]);
      setQuestionStartTime(Date.now());
      
      // Store the updated page to Redis
      storeCurrentState();
    }
  }, [currentPage, sanitizedData, loading, storeCurrentState]);

  // Function to update question time
  const handleQuestionTimeUpdate = useCallback(async (questionId: string) => {
    const timeSpent = (Date.now() - questionStartTime) / 1000;
    
    setQuestionTimes(prev => {
      const updated = { ...prev, [questionId]: timeSpent };
      
      // Update gamification in the background
      updateGamificationWithRedis(sessionId, {
        question_times: updated
      }).catch(error => {
        console.error("Error updating question time:", error);
      });
      
      return updated;
    });
    
    // Store the updated times
    storeCurrentState();
  }, [sessionId, questionStartTime, storeCurrentState]);

  // Function to handle option selection
  const handleOptionClick = useCallback(async (questionId: string, optionId: string) => {
    if (!canInteract || showAnswerStatus) return;

    try {
      console.log(`Setting option for question ${questionId} to ${optionId}`);
      
      // First update the UI state immediately
      setSelectedOptions(prev => {
        const newState = { ...prev, [questionId]: optionId };
        console.log("New selectedOptions state:", newState);
        
        // Store the updated selection immediately
        storeToRedis(`${sessionId}_selectedOptions`, JSON.stringify(newState))
          .catch(error => console.error("Error storing selected options:", error));
        
        return newState;
      });

      // Update question time
      await handleQuestionTimeUpdate(questionId);
      
      setShowAnswerStatus(false);
      setShowHints(false);
    } catch (error) {
      console.error("Error handling option selection:", error);
      showNotification("Failed to update selection", "error");
    }
  }, [canInteract, showAnswerStatus, sessionId, handleQuestionTimeUpdate]);

  // Function to handle answer check
  const handleEnhancedAnswerCheck = async (questionId: string, isCorrect: boolean) => {
    if (!canInteract) return;
  
    try {
      setCanInteract(false);
      
      console.log(`Checking answer for question ${questionId}, correct: ${isCorrect}`);
      console.log("Current selectedOptions before answer check:", selectedOptions);
      
      // Immediately update UI state - only correctAnswers, not selectedOptions
      setCorrectAnswers(prev => {
        const newState = { ...prev, [questionId]: isCorrect };
        
        // Store immediately
        storeToRedis(`${sessionId}_correctAnswers`, JSON.stringify(newState))
          .catch(error => console.error("Error storing correct answers:", error));
        
        return newState;
      });
      
      // Handle gamification updates
      if (gamification) {
        const updatedGamification = await handleAnswerSubmission(
          sessionId,
          isCorrect,
          gamification
        );
        setGamification(updatedGamification);
  
        if (await isGameOver(updatedGamification)) {
          setIsGameOverModalOpen(true);
          setCanInteract(true);
          return;
        }
      }
  
      // For incorrect answers, show feedback
      if (!isCorrect) {
        setShowAnswerStatus(true);
        setShowHints(true);
      } else {
        // For correct answers, continue with normal flow
        setShowAnswerStatus(true);
        setShowHints(false);
      }
      
      console.log("Current selectedOptions after answer check:", selectedOptions);
      setCanInteract(true);
    } catch (error) {
      console.error("Error handling answer check:", error);
      showNotification("Failed to process answer", "error");
      setCanInteract(true);
    }
  };
  
  // Function to handle restart - IMPORTANT: don't reset selection
  const handleRestart = async () => {
    if (!canInteract) return;
  
    try {
      setCanInteract(false);
      
      console.log("Restarting question...");
      console.log("Current selectedOptions before restart:", selectedOptions);
      
      // Only reset UI states, NOT the selection
      setShowAnswerStatus(false);
      setShowHints(false);
      
      // Reset the question start time without changing selection
      setQuestionStartTime(Date.now());
      
      // Update gamification
      await updateGamificationWithRedis(sessionId, {
        streakCount: 0,
        question_times: questionTimes
      });
      
      console.log("Current selectedOptions after restart:", selectedOptions);
    } catch (error) {
      console.error("Error handling restart:", error);
      showNotification("Failed to restart question", "error");
    } finally {
      setCanInteract(true);
    }
  };

  // Function to handle previous button click
  const handlePrevClick = () => {
    if (currentPage <= 0 || !canInteract) return;
    
    // Update UI immediately
    setShowAnswerStatus(false);
    setShowHints(false);
    setCurrentPage(prev => {
      const newPage = prev - 1;
      
      // Store immediately
      storeToRedis(`${sessionId}_currentPage`, newPage.toString())
        .catch(error => console.error("Error storing current page:", error));
      
      return newPage;
    });
  };

  if (loading || isSubmitting) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-100">
        <Card className="w-80">
          <CardBody className="flex flex-col items-center justify-center p-8">
            <Spinner color="primary" size="lg" />
            <p className="mt-4 text-lg font-semibold">
              {isSubmitting ? "Completing trial exam..." : "Loading trial exam..."}
            </p>
            <p className="mt-2 text-sm text-gray-500">Please wait</p>
          </CardBody>
        </Card>
        <ToastContainer style={{ zIndex: 9999 }} />
      </div>
    );
  }

  return (
    <div className="my-10 px-4 lg:px-16 mx-auto w-full flex flex-col gap-4">
      <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-4" role="alert">
        <p className="font-bold">Trial Exam</p>
        <p>This is a trial exam. Your answers will be saved, but won&apos;t affect your official score.</p>
      </div>
      <div className="w-full flex flex-col lg:flex-row gap-4">
        <div className="w-full lg:w-[75%] order-0">
          {currentQuestion && (
            <Questions
              currentPage={currentPage}
              flaggedQuestions={flaggedQuestions}
              questionData={currentQuestion}
              questionLoading={questionLoading}
              selectedOptions={selectedOptions}
              sessionId={sessionId}
              setCurrentPage={setCurrentPage}
              totalQuestions={totalQuestions}
              showAnswerStatus={showAnswerStatus}
              showHints={showHints}
              onUpdateShowStatus={(showAnswer, showHint) => {
                setShowAnswerStatus(showAnswer);
                setShowHints(showHint);
              }}
              onAnswerCheck={handleEnhancedAnswerCheck}
              onOptionClick={handleOptionClick}
              onPrevClick={handlePrevClick}
              onRestart={handleRestart}
              onExitExam={handleExitExam}
              gamification={gamification}
              canInteract={canInteract}
              isPremium={isPremium}
            />
          )}
        </div>
        <div className="w-full lg:w-[25%] order-1 lg:order-none">
          <QuestionBank
            currentPage={currentPage}
            flaggedQuestions={flaggedQuestions}
            isLoading={isSubmitting}
            isTimeUp={false}
            questionIds={questionIds}
            selectedOptions={selectedOptions}
            setCurrentPage={setCurrentPage}
            totalQuestions={totalQuestions}
            onExitExam={handleExitExam}
            correctAnswers={correctAnswers}
            sessionId={sessionId}
            gamification={gamification}
            isPremium={isPremium}
          />
        </div>
      </div>
      <GameOverModal
        isOpen={isGameOverModalOpen}
        onClose={() => setIsGameOverModalOpen(false)}
        sessionId={sessionId}
        subject={subject}
        onExitExam={handleExitExam}
      />
      <ToastContainer style={{ zIndex: 9999 }} />
    </div>
  );
};