"use server";

import { createClient } from "redis";

// Types for exam configuration
interface SubjectInfo {
  id: string;
  name: string;
  key: string;
}

interface ExamType {
  id: string;
  name: string;
  subjects: SubjectInfo[];
}

interface ExamConfig {
  examTypes: Record<string, ExamType>;
}

interface PassingGrade {
  nama: string;
  score: number;
}

interface ProgramThreshold {
  program: string;
  min_score: number;
}

interface ParsedGrade {
  passing_grade?: PassingGrade[];
  program_thresholds?: ProgramThreshold[];
}

// Function to get subject details for an exam session
export async function fetchExamSubjectDetails(sessionId: string) {
  try {
    // Fetch the exam questions first to get passing grades and other metadata
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v1/exam-question-hints/sessions/${sessionId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Failed to fetch exam questions. Response text:", errorText);
      throw new Error("Failed to fetch exam questions.");
    }

    const result = await response.json();
    
    // Get the exam type from metadata in the first question
    let examType = "";
    if (result.data?.[0]?.metadata) {
      // Find the institute metadata
      const instituteMeta = result.data[0].metadata.find(
        (meta: any) => meta.name === "institute"
      );
      examType = instituteMeta?.value || "";
    }

    // Fetch the exam configuration
    const configResponse = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/exam-config`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
      }
    );

    if (!configResponse.ok) {
      const errorText = await configResponse.text();
      console.error("Failed to fetch exam config. Response text:", errorText);
      throw new Error("Failed to fetch exam configuration.");
    }

    const configResult = await configResponse.json();
    const examConfig: ExamConfig = configResult.config;

    // Get the subjects information for this exam type
    const subjects = examType && examConfig.examTypes[examType] 
      ? examConfig.examTypes[examType].subjects 
      : [];

    // Parse the passing grade and program thresholds if available
    let passingGrade: ParsedGrade | PassingGrade[] = [];
    
    if (result.data?.[0]?.passing_grade) {
      try {
        const parsedData = JSON.parse(result.data[0].passing_grade);
        
        // Check if it's the new structure with program_thresholds
        if (parsedData.passing_grade && Array.isArray(parsedData.passing_grade)) {
          // New structure: object with passing_grade and possibly program_thresholds
          passingGrade = {
            passing_grade: parsedData.passing_grade || [],
            program_thresholds: parsedData.program_thresholds || []
          };
        } else if (Array.isArray(parsedData)) {
          // Old structure: direct array of passing grades
          passingGrade = parsedData;
        } else {
          // Unexpected structure, but try to handle it gracefully
          passingGrade = parsedData.passing_grade || parsedData || [];
        }
      } catch (error) {
        console.error("Error parsing passing grade:", error);
      }
    }

    return {
      examType,
      subjects,
      passingGrade,
      questions: result.data || []
    };
  } catch (error) {
    console.error("Error fetching exam subject details:", error);
    throw error;
  }
}

// Redis utility functions (reused from your code)
const createWriteClient = () => createClient({
  url: process.env.REDIS_URL_WRITE as string,
});

const createReadClient = () => createClient({
  url: process.env.REDIS_URL_READ as string,
});

export async function storeToRedis(key: string, value: any) {
  const redis = createWriteClient();

  try {
    await redis.connect();
    await redis.set(key, JSON.stringify(value), {
      EX: 21600, // Set expiration to 6 hours (21600 seconds)
    });
  } catch (error) {
    console.error("Failed to store data in Redis:", error);
  } finally {
    await redis.disconnect();
  }
}

export async function retrieveFromRedis(key: string): Promise<any | null> {
  const redis = createReadClient();

  try {
    await redis.connect();
    const value = await redis.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    console.error("Failed to retrieve data from Redis:", error);
    return null;
  } finally {
    await redis.disconnect();
  }
}