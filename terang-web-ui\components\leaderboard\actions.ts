"use server";

interface SubjectScore {
  correct: number;
  score: number;
  total: number;
}

export interface CategoryOption {
  value: string;
  label: string;
  imageUrl?: string;
  count?: number;
}

export interface LeaderboardEntry {
  rank: number;
  userId: string | boolean | null;
  username: string;
  totalScore: number;
  totalCorrect: number;
  totalQuestions: number;
  completedAt: string;
  examId: string;
  examName: string;
  subject: string;
  sessionType: string;
  startTime: string;
  endTime: string;
  elapsedSeconds: number;
  elapsedTimeFormatted: string;
  targetJabatan?: string;
  targetInstitution?: string;
  isCurrentUser?: boolean;
  targetUniversity?: string;
  targetMajor?: string;
  categoryIds?: string[];  // Added for category IDs
  categoryNames?: string[]; // Added for category names
  // Use index signature to handle dynamic subject-specific fields
  subjectScores: { [key: string]: SubjectScore }; // No conflict with index signature
}

interface LeaderboardResponse {
  leaderboard: LeaderboardEntry[];
  message: string;
  type: string;
  categories?: CategoryOption[]; // Added for categories list
}

type TargetOption = {
  value: string;
  label: string;
  count: number;
  imageUrl?: string;
};

export interface TargetAggregates {
  positions: TargetOption[];
  institutions: TargetOption[];
  universities: TargetOption[];
  majors: TargetOption[];
  categories: CategoryOption[]; // Added for categories
}

interface TargetAggregatesResponse {
  data: TargetAggregates;
  message: string;
}

// Types
interface SubjectInfo {
  id: string;
  name: string;
  key: string;
}

export interface ExamType {
  id: string;
  name: string;
  subjects: SubjectInfo[];
}

interface ExamConfig {
  examTypes: Record<string, ExamType>;
}

// New function to fetch categories
export const fetchCategories = async (): Promise<CategoryOption[]> => {
  try {
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/exams/categories`,
      {
        headers: {
          'X-API-KEY': process.env.BACKEND_API_KEY as string,
          'Content-Type': 'application/json'
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (err) {
    console.error('Error fetching categories:', err);
    return [];
  }
};

export const fetchLeaderboardData = async (
  examId: string, 
  subject: string, 
  categoryId?: string
): Promise<LeaderboardResponse> => {
  try {
    const queryParams = new URLSearchParams();
    if (examId) queryParams.append('examId', examId);
    if (subject) queryParams.append('subject', subject);
    if (categoryId) queryParams.append('categoryId', categoryId);

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/exams/leaderboard?${queryParams.toString()}`, 
      {
        headers: {
          'X-API-KEY': process.env.BACKEND_API_KEY as string,
          'Content-Type': 'application/json'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (err) {
    console.error(`Error fetching leaderboard:`, err);
    return { leaderboard: [], message: "Error loading leaderboard", type: "exam" };
  }
};

export const fetchTargetAggregates = async (): Promise<TargetAggregates> => {
  try {
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v0/exams/leaderboard-targets`,
      {
        headers: {
          'X-API-KEY': process.env.BACKEND_API_KEY as string,
          'Content-Type': 'application/json'
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: TargetAggregatesResponse = await response.json();
    
    // Ensure all fields are properly initialized, including the new categories field
    return {
      positions: data.data.positions || [],
      institutions: data.data.institutions || [],
      universities: data.data.universities || [],
      majors: data.data.majors || [],
      categories: data.data.categories || []
    };
  } catch (err) {
    console.error('Error fetching target aggregates:', err);
    return {
      positions: [],
      institutions: [],
      universities: [],
      majors: [],
      categories: []
    };
  }
};

/**
 * Fetches exam configuration from backend
 * @param examId The exam ID
 * @returns The exam configuration for the specified exam
 */
export async function fetchExamConfig(examId: string): Promise<ExamType | null> {
  if (!examId) {
    console.error("No exam ID provided");
    return null;
  }
  
  try {
    // Use the specific endpoint for getting exam config by exam ID
    const configUrl = `${process.env.BACKEND_BASE_URL}/v0/exam-config/exam/${examId}`;
    const response = await fetch(configUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-API-KEY": process.env.BACKEND_API_KEY as string,
      },
    });

    if (!response.ok) {
      // If the specific endpoint fails, fall back to using CPNS as default
      console.warn(`Failed to fetch exam config for exam ID ${examId}. Falling back to CPNS configuration.`);
      return fetchDefaultConfig();
    }

    const result = await response.json();
    
    // Handle the response structure
    const examConfig = result.config;
    
    if (!examConfig) {
      console.error(`No exam config found for ${examId}`);
      return fetchDefaultConfig();
    }
    
    return examConfig;
  } catch (error) {
    console.error(`Error fetching exam config for ${examId}:`, error);
    return fetchDefaultConfig();
  }
}

/**
 * Fallback function to fetch the default CPNS configuration
 */
async function fetchDefaultConfig(): Promise<ExamType | null> {
  try {
    const configUrl = `${process.env.BACKEND_BASE_URL}/v0/exam-config/CPNS`;
    const response = await fetch(configUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "X-API-KEY": process.env.BACKEND_API_KEY as string,
      },
    });

    if (!response.ok) {
      console.error("Failed to fetch default CPNS config");
      return null;
    }

    const result = await response.json();
    return result.config || result.examInfo;
  } catch (error) {
    console.error("Error fetching default config:", error);
    return null;
  }
}