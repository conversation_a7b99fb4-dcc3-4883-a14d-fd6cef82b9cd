import { NextRequest, NextResponse } from 'next/server';

// Define types for the Midtrans notification payload
interface VaNumber {
  va_number: string;
  bank: string;
}

interface PaymentAmount {
  paid_at: string;
  amount: string;
}

interface MidtransNotification {
  // Common fields for all payment types
  transaction_time: string;
  transaction_status: string;
  transaction_id: string;
  status_message: string;
  status_code: string;
  signature_key: string;
  payment_type: string;
  order_id: string;
  merchant_id: string;
  gross_amount: string;
  fraud_status?: string;
  expiry_time: string;
  currency: string;
  settlement_time?: string;
  
  // Bank transfer specific fields
  va_numbers?: VaNumber[];
  payment_amounts?: PaymentAmount[];
  
  // QRIS specific fields
  transaction_type?: string;
  issuer?: string;
  acquirer?: string;
}

interface FetchOptions {
  method?: string;
  body?: any;
}

/**
 * Helper function to call the backend
 */
async function fetchFromBackend<T>(
  url: string,
  options: FetchOptions = {},
): Promise<T> {
  const requestOptions: RequestInit = {
    method: options.method || "GET",
    headers: {
      "Content-Type": "application/json",
      "X-Api-Key": process.env.BACKEND_API_KEY as string,
    },
    body: options.body ? JSON.stringify(options.body) : undefined,
  };

  const response = await fetch(url, requestOptions);

  if (!response.ok) {
    const errorText = await response.text();

    throw new Error(
      `Network response was not ok: ${response.status} - ${response.statusText}. Details: ${errorText}`,
    );
  }

  return response.json();
}

/**
 * Route handler for Midtrans payment notifications
 * Forwards the notification to the Go backend for processing
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Parse the notification payload
    const notification: MidtransNotification = await request.json();
    
    // Log the received notification (optional, for debugging)
    console.log('Received payment notification:', JSON.stringify(notification));
    
    // Forward the notification to backend
    const backendUrl = `${process.env.BACKEND_BASE_URL}/v0/payment/reconciliation`;
    
    // Send the notification to the Go backend
    const backendResponse = await fetchFromBackend<{
      success: boolean;
      message: string;
      source?: string;
      order_id?: string;
      status?: string;
    }>(backendUrl, {
      method: "POST",
      body: notification
    });
    
    // If the backend processed successfully
    if (backendResponse.success) {
      console.log(`Backend processed payment: ${backendResponse.message}`);
      
      // Extract data from notification for the response
      const { 
        order_id,
        transaction_status,
        transaction_id,
        payment_type,
        gross_amount,
        expiry_time,
        fraud_status,
        status_message,
        settlement_time
      } = notification;
      
      // Create response payload
      const paymentData = {
        orderId: order_id,
        transactionId: transaction_id,
        status: transaction_status,
        paymentType: payment_type,
        amount: gross_amount,
        fraudStatus: fraud_status || 'na',
        statusMessage: status_message,
        settlementTime: settlement_time || null,
        expiryTime: expiry_time,
        source: backendResponse.source || 'unknown',
        backendMessage: backendResponse.message
      };
      
      // Add payment-type specific data
      if (payment_type === 'bank_transfer') {
        Object.assign(paymentData, {
          vaNumbers: notification.va_numbers || [],
          paymentAmounts: notification.payment_amounts || []
        });
      } else if (payment_type === 'qris') {
        Object.assign(paymentData, {
          transactionType: notification.transaction_type || null,
          issuer: notification.issuer || null,
          acquirer: notification.acquirer || null
        });
      }
      
      // Return successful response
      return NextResponse.json({
        success: true,
        message: getStatusMessage(transaction_status),
        data: paymentData
      }, { status: 200 });
    } else {
      // Backend reported an error
      console.error('Backend failed to process payment:', backendResponse.message);
      
      return NextResponse.json({
        success: false,
        message: backendResponse.message || 'Payment processing failed',
        error: 'Backend processing error'
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('Failed to process payment notification:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to process notification',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 400 });
  }
}

/**
 * Helper function to get status message based on transaction status
 */
function getStatusMessage(status: string): string {
  switch (status) {
    case 'pending':
      return 'Payment is pending and waiting to be paid';
    case 'capture':
      return 'Payment captured successfully. Funds are reserved.';
    case 'settlement':
      return 'Payment settled successfully. Funds have been received.';
    case 'deny':
      return 'Payment was denied or rejected';
    case 'cancel':
      return 'Payment was cancelled';
    case 'expire':
      return 'Payment has expired';
    case 'failure':
      return 'Payment failed due to processing error';
    case 'refund':
      return 'Payment has been refunded';
    case 'chargeback':
      return 'Payment has been charged back';
    case 'partial_refund':
      return 'Payment has been partially refunded';
    case 'partial_chargeback':
      return 'Payment has been partially charged back';
    case 'authorize':
      return 'Payment is authorized but not yet captured';
    default:
      return `Payment notification received with status: ${status}`;
  }
}