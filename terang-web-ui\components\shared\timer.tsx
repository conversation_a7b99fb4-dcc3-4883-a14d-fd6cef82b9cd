import React, { useState, useEffect } from "react";

interface TimerProps {
  examDuration: string;
  elapsedDuration: string;
  remainingDuration: string;
  sessionId: string;
  onTimeUp: () => void;
  onTimeUpdate: (newTime: number) => void;
}

const ExamTimer: React.FC<TimerProps> = ({
  examDuration,
  elapsedDuration,
  remainingDuration,
  sessionId,
  onTimeUp,
  onTimeUpdate,
}) => {
  const parseDuration = (duration: string): number => {
    const [hours, minutes, secondsAndMillis] = duration.split(":");
    const [seconds] = (secondsAndMillis || "").split(".");

    return (
      parseInt(hours, 10) * 3600 +
      parseInt(minutes, 10) * 60 +
      parseInt(seconds, 10)
    );
  };

  const initialRemainingTime = parseDuration(remainingDuration);
  const [timeLeft, setTimeLeft] = useState<number>(initialRemainingTime);
  const [startTime] = useState<number>(parseDuration(examDuration));
  const [elapsedTime, setElapsedTime] = useState<number>(
    parseDuration(elapsedDuration),
  );

  useEffect(() => {
    if (timeLeft <= 0) {
      onTimeUp();

      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prevTime) => {
        const newTime = prevTime - 1;

        if (newTime <= 0) {
          clearInterval(timer);
          onTimeUp();

          return 0;
        }
        setElapsedTime((prevElapsed) => prevElapsed + 1);
        onTimeUpdate(newTime);

        return newTime;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, onTimeUp, onTimeUpdate]);

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const getColorClass = (seconds: number): string => {
    if (seconds <= 60) return "text-red-500";
    if (seconds <= 120) return "text-orange-500";
    if (seconds <= 180) return "text-yellow-500";
    if (seconds <= 300) return "text-blue-500";

    return "text-green-500";
  };

  const getProgressColor = (seconds: number): string => {
    if (seconds <= 60) return "bg-red-500";
    if (seconds <= 120) return "bg-orange-500";
    if (seconds <= 180) return "bg-yellow-500";
    if (seconds <= 300) return "bg-blue-500";

    return "bg-green-500";
  };

  const progressPercentage = (timeLeft / startTime) * 100;

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-4">
      <h2 className="text-xl font-bold mb-4 text-center">Exam Timer</h2>
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <p className="text-sm font-semibold">Duration</p>
          <p className="text-lg">{formatTime(startTime)}</p>
        </div>
        {/* <div className="text-center">
          <p className="text-sm font-semibold">Elapsed</p>
          <p className="text-lg">{formatTime(elapsedTime)}</p>
        </div> */}
        <div className="text-center">
          <p className="text-sm font-semibold">Remaining</p>
          <p className={`text-lg font-bold ${getColorClass(timeLeft)}`}>
            {formatTime(timeLeft)}
          </p>
        </div>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700 relative">
        <div
          className={`h-2.5 rounded-full ${getProgressColor(timeLeft)} absolute left-0 top-0 transition-all duration-1000 ease-linear`}
          style={{ width: `${progressPercentage}%` }}
        />
      </div>
    </div>
  );
};

export default ExamTimer;
