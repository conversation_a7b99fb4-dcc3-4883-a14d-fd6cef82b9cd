import React from "react";
import styled from "styled-components";

interface ThemeProps {
  text: string;
  body: string;
}

const Btn = styled.a<{ theme: ThemeProps }>`
  display: inline-block;
  background-color: ${(props) => props.theme.text};
  color: ${(props) => props.theme.body};
  outline: none;
  border: none;
  font-weight: 600;
  font-size: 1.15em;
  padding: 1.2rem 1rem;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  white-space: nowrap; // Add this line to prevent text wrapping

  &:hover {
    transform: scale(0.9);
  }

  &::after {
    content: " ";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0);
    border: 2px solid ${(props) => props.theme.text};
    width: 100%;
    height: 100%;
    border-radius: 50px;
    transition: all 0.2s ease;
  }

  &:hover::after {
    transform: translate(-50%, -50%) scale(1);
    padding: 0.3rem;
  }
`;

interface ButtonProps {
  text: string;
  link?: string;
  newTab?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  text,
  link = "#",
  newTab = false,
}) => {
  return (
    <Btn
      aria-label={text}
      href={link}
      rel="noreferrer"
      target={newTab ? "_blank" : "_self"}
    >
      {text}
    </Btn>
  );
};

export default Button;
