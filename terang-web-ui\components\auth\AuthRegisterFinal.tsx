"use client";

import React, { useState, useEffect } from "react";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "react-google-recaptcha";
import { z } from "zod";
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import { usePathname } from "next/navigation";
import { Input, Button, Checkbox } from "@heroui/react";

import { registerUser } from "@/app/lib/actions/auth/actions";
import { registerType } from "@/app/(auth)/types/auth/auth";
import FingerprintLoading from "@/public/images/icons/FingerprintLoading.gif";
import VerifiedLoading from "@/public/images/icons/VerifiedLoading.gif";
import { logoutBase } from "@/app/lib/auth/logout";
import { validateAndTrackReferralCode, registerDaerah3TForUser } from '@/app/(dashboard)/referral/actions';
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { 
  getReferralCode<PERSON>rom<PERSON><PERSON>ie, 
  setReferral<PERSON>ode<PERSON>ookie, 
  deleteReferralCodeCookie 
} from './cookies-actions'

type FormValues = {
  firstname: string;
  lastname: string;
  email: string;
  username: string;
  password: string;
  cpassword: string;
  referralCode: string;
  emailConsent: boolean; // Added new field for email consent
  termsAgreed: boolean;
};

// Extend the type definition for form errors to include emailConsent error message
type FormErrorValues = {
  [K in keyof FormValues]?: string;
};

interface DiscountValidation {
  isValid: boolean;
  discountPercentage?: number;
  expiresAt?: string;
  errorMessage?: string;
}

const AuthRegisterFinal = ({ session, title, subtext }: registerType) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  // Use optional chaining to safely access searchParams
  const redirectParam = searchParams?.get('redirect') || null;
  
  const [redirectPath, setRedirectPath] = useState<string | null>(null);
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);
  const recaptchaRef = React.createRef<ReCAPTCHA>();
  const [showPassword, setShowPassword] = useState(false);
  const [showCPassword, setShowCPassword] = useState(false);

  const [formErrors, setFormErrors] = useState<FormErrorValues>({});
  const [loading, setLoading] = useState(false); 
  const [doneDispatch, setDoneDispatch] = useState(false); 
  const [referralValidation, setReferralValidation] = useState<DiscountValidation | null>(null);
  const [isValidating, setIsValidating] = useState(false);
  const [cookieReferralCode, setCookieReferralCode] = useState<string | null>(null);
  const [hasManualInput, setHasManualInput] = useState(false);
  const [showValidateButton, setShowValidateButton] = useState(false);

  const [hideReferralInput, setHideReferralInput] = useState(false);

  const [showReferralInput, setShowReferralInput] = useState(false);
  const [askedAboutReferral, setAskedAboutReferral] = useState(false);

  const RecaptchasiteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;

  // Local state for form fields
  const [formValues, setFormValues] = useState<FormValues>({
    firstname: session?.user?.firstname || "",
    lastname: session?.user?.lastname || "",
    email: session?.user?.email || "",
    username: "",
    password: "",
    cpassword: "",
    referralCode: "",
    emailConsent: false, // Default to false (unchecked)
    termsAgreed: false,
  });

  const showNotification = (
    message: string,
    type: "success" | "info" | "error" | "warning"
  ) => {
    toast[type](message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  useEffect(() => {
    // Check for redirect path in localStorage
    const savedPath = localStorage.getItem('auth_redirect_uri');
    
    // If the saved path includes 'events', hide the referral input
    if (savedPath && savedPath.includes('events')) {
      setRedirectPath(savedPath);
      setHideReferralInput(true);
    } else if (redirectParam) {
      setRedirectPath(redirectParam);
    }
    
    // Still process the initial referral code regardless
    handleInitialReferralCode();
  }, [redirectParam]);
  
  // Add useEffect to handle initial referral code check
  useEffect(() => {
    handleInitialReferralCode();
  }, []); // Run once on component mount

  const handleInitialReferralCode = async () => {
    try {
      const result = await getReferralCodeFromCookie();
      if (result.success && result.value) {
        console.log('Found referral code:', result.value);
        setCookieReferralCode(result.value);
        setFormValues(prev => ({
          ...prev,
          referralCode: result.value || '' 
        }));
        
        // When we find a referral code in the cookie, automatically show the refcode input
        // and set the "asked about referral" flag to true
        setShowReferralInput(true);
        setAskedAboutReferral(true);
        
        await handleValidateReferral(result.value, false);
      }
    } catch (error) {
      console.error('Error handling initial referral code:', error);
      showNotification('Failed to load referral code', 'error');
    }
  };

  const handleReferralInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.toUpperCase();
    
    setHasManualInput(true);
    setShowValidateButton(!!value); // Show button only when there's input
    setFormValues(prev => ({
      ...prev,
      referralCode: value
    }));
    
    // Clear previous validation state when user starts typing
    setReferralValidation(null);
    
    // If there was a cookie referral code, clear it when user starts typing
    if (cookieReferralCode) {
      deleteReferralCodeCookie();
      setCookieReferralCode(null);
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    
    setFormValues((prevValues) => ({
      ...prevValues,
      [name]: checked,
    }));
    
    // Clear the error message when the checkbox is checked
    if (checked) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleValidateReferral = async (code: string, isManual: boolean = true) => {
    if (!code) return;
    
    setIsValidating(true);
    
    try {
      const validation = await validateAndTrackReferralCode(code, 'REGISTRATION');
      
      setReferralValidation(validation);
      
      if (validation.isValid) {
        // Set the cookie if validation is successful
        if (isManual) {
          const cookieResult = await setReferralCodeCookie(code);
          if (cookieResult.success) {
            setCookieReferralCode(code);
          }
          showNotification?.("Kode referral valid!", "success");
          setShowValidateButton(false); // Hide button after successful validation
        }
      } else {
        if (isManual) {
          setFormValues(prev => ({
            ...prev,
            referralCode: ''
          }));
          showNotification?.(validation.errorMessage || "Kode referral tidak valid", "error");
          // Clear cookie if validation fails
          deleteReferralCodeCookie();
          setCookieReferralCode(null);
        }
      }
    } catch (error) {
      console.error("Error validating referral code:", error);
      setReferralValidation({
        isValid: false,
        errorMessage: "Gagal memvalidasi kode referral"
      });
      if (isManual) {
        showNotification?.("Gagal memvalidasi kode referral", "error");
      }
    } finally {
      setIsValidating(false);
    }
  };

  // ZOD Validator
  const FormSchema = z.object({
    username: z
      .string({ invalid_type_error: "Please input username." })
      .min(1, "Username cannot be empty!")
      .min(6, "Username must be at least 6 characters long")
      .regex(
        /^[a-zA-Z]+(?=.*\d)[a-zA-Z\d]*$/,
        "Username must start with a letter and contain at least one number",
      )
      .transform((val) => val.toLowerCase()), // Transform username to lowercase,
    email: z
      .string()
      .min(1, "Email cannot be empty!")
      .email({ message: "Please input the correct email address" }),
    firstname: z
      .string({ invalid_type_error: "Please input firstname." })
      .min(1, "Firstname cannot be empty!"),
    lastname: z
      .string({ invalid_type_error: "Please input lastname." })
      .min(1, "Lastname cannot be empty, please also fill out this field"),
    picture: z
      .string()
      .url({ message: "Please input a valid picture URL." })
      .optional(),
  });

  const PasswordSchema = z.object({
    password: z
      .string()
      .min(8, "Password must be at least 8 characters long")
      .regex(/[a-z]/, "Password must contain at least one lowercase letter")
      .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
      .regex(/\d/, "Password must contain at least one digit")
      .regex(
        /[^a-zA-Z0-9]/,
        "Password must contain at least one special character",
      ),
  });
  const ConfirmPasswordSchema = z
    .object({
      cpassword: z
        .string()
        .min(8, "Confirm password field must be at least 8 characters long"),
    })
    .refine((data) => data.cpassword === formValues.password, {
      message: "Passwords don't match",
      path: ["cpassword"], // path of error
    });

  type FormSchemaKeys = keyof z.infer<typeof FormSchema>;
  function isFormSchemaKey(key: string): key is FormSchemaKeys {
    return key in FormSchema.shape;
  }
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
  
    setFormValues((prevValues) => ({
      ...prevValues,
      [name]: value,
    }));
  
    // Validate the specific field
    try {
      if (name === "password") {
        PasswordSchema.parse({ password: value });
      } else if (name === "cpassword") {
        ConfirmPasswordSchema.parse({
          cpassword: value,
          password: formValues.password,
        });
      } else if (isFormSchemaKey(name)) {
        type PickObject = { [K in FormSchemaKeys]?: true };
        const pickObject: PickObject = { [name]: true };
        const partialSchema = FormSchema.pick(pickObject);
  
        partialSchema.parse({ [name]: value });
      }
      setFormErrors((prevErrors) => ({
        ...prevErrors,
        [name]: undefined,
      }));
    } catch (error: unknown) {
      if (error instanceof z.ZodError) {
        const errorMessage = error.issues[0]?.message || "Validation error";
  
        setFormErrors((prevErrors) => ({
          ...prevErrors,
          [name]: errorMessage,
        }));
      }
    }
  };

  const handleRecaptchaChange = (token: string | null) => {
    setRecaptchaToken(token);
  };

  // Redirect after successful registration based on stored redirect path
  // Modified useEffect for redirect logic
  // In your AuthRegisterFinal.tsx, modify the useEffect to call the route directly:

  useEffect(() => {
    if (doneDispatch) {
      const handleRedirect = async () => {
        const savedPath = localStorage.getItem('auth_redirect_uri');
        console.log("Saved path:", savedPath);
        
        if (savedPath && savedPath.includes('events') && savedPath.includes('gratis')) {
          console.log("Path includes both 'events' and 'gratis'");
          
          if (session?.user?.email) {
            console.log("Attempting to register user for Daerah 3T:", session.user.email);
            showNotification("Registering you for free program...", "info");
            
            try {
              // Use the route directly instead of the server function
              const response = await fetch('/api/daerah-3t-register', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  email: session.user.email
                }),
              });
              
              console.log("Response status:", response.status);
              
              if (response.ok) {
                const result = await response.json();
                console.log("Registration successful:", result);
                showNotification("Successfully registered for free program!", "success");
              } else {
                const errorText = await response.text();
                console.error("Failed to register. Server response:", errorText);
                // Don't show error to the user
              }
            } catch (error) {
              console.error("Exception during registration:", error);
            }
          } else {
            console.log("No valid email in session, skipping registration");
          }
          
          // Redirect regardless of registration outcome
          router.push(savedPath);
        } 
        else if (savedPath && savedPath.includes('events')) {
          console.log("Regular events path, redirecting without registration");
          router.push(savedPath);
        } 
        else if (savedPath && (savedPath.includes('register') || savedPath?.includes('login'))){
          console.log("auth paths redirecting to dashboard");
          router.push("/dashboard");
        }
        else {
          console.log("No special path, redirecting to dashboard");
          router.push("/dashboard");
        }
      };
      
      // Execute the async function
      handleRedirect();
    }
  }, [doneDispatch, router, session, showNotification]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setLoading(true);
    
    // Check if email consent is checked
    if (!formValues.emailConsent) {
      setFormErrors(prev => ({
        ...prev,
        emailConsent: "You must agree to receive transactional emails to create an account"
      }));
      setLoading(false);
      return;
    }

    if (!formValues.termsAgreed) {
      setFormErrors(prev => ({
        ...prev,
        termsAgreed: "You must agree to the Terms and Conditions and Privacy Policy"
      }));
      setLoading(false);
      return;
    }
  
    // Validate referral code before submission if one is present
    const referralToUse = formValues.referralCode;
    if (referralToUse && !referralValidation?.isValid) {
      await handleValidateReferral(referralToUse);
      if (!referralValidation?.isValid) {
        setLoading(false);
        return;
      }
    }
    
    try {
      FormSchema.parse(formValues);
      PasswordSchema.parse({ password: formValues.password });
      ConfirmPasswordSchema.parse({
        cpassword: formValues.cpassword,
        password: formValues.password,
      });
  
      if (recaptchaRef.current) {
        recaptchaRef.current.reset();
        const token = await recaptchaRef.current.executeAsync?.();
  
        if (!token) {
          setLoading(false);
          throw new Error(
            "No reCAPTCHA token received, please send this message to Terang AI Developer.",
          );
        }
  
        const formData = new FormData();
  
        // Add all form values to FormData, including referralCode and emailConsent
        Object.keys(formValues).forEach((key) => {
          // Special handling for boolean values
          if (typeof formValues[key as keyof FormValues] === 'boolean') {
            formData.append(key, formValues[key as keyof FormValues] ? 'true' : 'false');
          } else {
            formData.append(key, formValues[key as keyof FormValues] as string);
          }
        });
  
        formData.append("recaptchaToken", token);
        
        // Add the redirect path to the form data if it exists
        if (redirectPath) {
          formData.append("redirectPath", redirectPath);
        }
  
        try {
          const result = await registerUser(formData);
  
          if (result && result.error) {
            const postgresError = result.error.message.error;
  
            if (
              postgresError.includes(
                'duplicate key value violates unique constraint "users_username_key"',
              )
            ) {
              alert('Username already exists. Please choose a different username.');
            } else {
              alert("Registration failed: " + result.error.message.error);
            }
            setLoading(false);
          } else if (result && result.result) {
            console.log("Everything looks good! All verification done.");
            setDoneDispatch(true);
          } else {
            throw new Error("Unexpected response from server");
          }
        } catch (error) {
          if (error instanceof Error) {
            console.error("Error registering user:", error.message);
            setLoading(false);
          } else {
            throw new Error("Failed to register user.");
          }
        }
      } else {
        setLoading(false);
        console.error(
          "reCAPTCHA ref not found, please send report to Terang AI Dev.",
        );
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Create a properly typed error object
        const fieldErrors: FormErrorValues = {};
        
        // Iterate through errors and add them to the object if they match a form field
        error.errors.forEach(currentError => {
          const path = currentError.path[0];
          if (path && typeof path === 'string' && Object.keys(formValues).includes(path)) {
            // Create a type-safe way to assign error messages
            // This uses type assertion to help TypeScript understand the assignment is valid
            fieldErrors[path as keyof FormValues] = currentError.message;
          }
        });
        
        setFormErrors(fieldErrors);
      }
    } finally {      
      setLoading(false);
    }
  };

  if (!session?.user) return null;
  const BulbChargingIcon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg
      color={"#000000"}
      fill={"none"}
      height={50}
      viewBox="0 0 24 24"
      width={50}
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        clipRule="evenodd"
        d="M11.9999 3.5C8.12425 3.5 4.99988 6.60727 4.99988 10.4189C4.99988 11.7227 5.36382 12.94 5.99692 13.98C6.28409 14.4518 6.13446 15.067 5.6627 15.3542C5.19095 15.6414 4.57572 15.4917 4.28855 15.02C3.47063 13.6763 2.99988 12.1008 2.99988 10.4189C2.99988 5.48352 7.03895 1.5 11.9999 1.5C16.9608 1.5 20.9999 5.48352 20.9999 10.4189C20.9999 12.1008 20.5291 13.6763 19.7112 15.02C19.424 15.4917 18.8088 15.6414 18.3371 15.3542C17.8653 15.067 17.7157 14.4518 18.0028 13.98C18.6359 12.94 18.9999 11.7227 18.9999 10.4189C18.9999 6.60727 15.8755 3.5 11.9999 3.5Z"
        fill="currentColor"
        fillRule="evenodd"
      />
      <path
        d="M15.8431 15.75C15.9735 15.75 16.1021 15.75 16.2104 15.755C16.3248 15.7602 16.4786 15.7728 16.6388 15.8212C17.1373 15.9717 17.4732 16.3363 17.4986 16.7498C17.5068 16.8826 17.4737 16.9996 17.444 17.0857C17.1126 18.7676 16.4284 19.2499 15.843 19.2499H8.15678C7.57139 19.2499 6.88718 18.7676 6.55573 17.0857C6.52602 16.9996 6.49297 16.8826 6.50114 16.7498C6.52658 16.3363 6.86248 15.9717 7.36097 15.8212C7.52115 15.7728 7.67491 15.7602 7.78933 15.755C7.89769 15.75 8.02625 15.75 8.15666 15.75H8.15672H15.843H15.8431Z"
        fill="currentColor"
      />
      <path
        d="M10.3319 22.4013C10.6773 22.5018 11.0855 22.5018 11.9018 22.5018C12.7181 22.5018 13.1263 22.5018 13.4716 22.4013C14.0056 22.2458 14.4549 21.9204 14.7323 21.4884C14.8355 21.3276 14.9058 21.1423 14.9775 20.8791C15.0673 20.5491 15.1122 20.3841 15.0221 20.2663C14.9321 20.1484 14.755 20.1484 14.4007 20.1484H9.40283C9.04861 20.1484 8.87149 20.1484 8.78144 20.2663C8.69139 20.3841 8.73629 20.5491 8.8261 20.8791C8.89773 21.1423 8.96807 21.3276 9.0713 21.4884C9.34864 21.9204 9.798 22.2458 10.3319 22.4013Z"
        fill="currentColor"
      />
      <path
        clipRule="evenodd"
        d="M13.299 6.24616C13.7153 6.60904 13.7586 7.24072 13.3958 7.65706L12.3299 8.87997C12.2791 8.93827 12.3109 9.02973 12.3869 9.04397L13.051 9.16837C14.2789 9.39838 14.6391 10.9207 13.7697 11.7187L11.571 13.7367C11.1641 14.1102 10.5315 14.0831 10.158 13.6762C9.78459 13.2693 9.81169 12.6368 10.2186 12.2633L11.5106 11.0774C11.5713 11.0217 11.5424 10.9206 11.4614 10.9054L10.9488 10.8094C9.79375 10.593 9.38836 9.21097 10.1157 8.37646L11.8881 6.34297C12.2509 5.92663 12.8826 5.88329 13.299 6.24616Z"
        fill="currentColor"
        fillRule="evenodd"
      />
    </svg>
  );

  return (
    <div className="container mx-auto">
      <form onSubmit={handleSubmit}>
        <div className="w-full flex justify-center">
          <div className="w-10/12">
            <div className="mt-3 flex flex-col justify-center items-center text-center">
              <div className="flex items-center">
                <Image
                  alt="Terang AI Logo"
                  className="mr-2"
                  height={50}
                  src="https://cdn.terang.ai/images/logo/logo-terang-ai-simple.svg"
                  width={50}
                />{" "}
                {/* Add margin to the right for spacing */}
                <h3 className="font-bold text-3xl mb-1">{title}</h3>
              </div>
              {subtext}
              
              {/* Display redirect info if available */}
              {redirectPath && (redirectPath.includes('events')) && (
                <p className="text-blue-600 mt-2">
                  Setelah pendaftaran, Anda akan diarahkan kembali ke halaman sebelumnya.
                </p>
              )}
            </div>
          </div>
        </div>
        <hr className="w-full border-t border-gray-300 my-4" />
        <div className="space-y-4 px-4">
          <div className="flex flex-col mb-3">
            <Input
              isDisabled
              id="email"
              label="Email Address"
              name="email"
              value={formValues.email}
              variant="bordered"
            />
          </div>

          <div className="flex flex-col mb-3">
            <Input
              isRequired
              id="firstname"
              label="First Name"
              name="firstname"
              value={formValues.firstname}
              variant="bordered"
              onChange={handleInputChange}
            />
            {formErrors.firstname && (
              <p className="text-red-500">{formErrors.firstname}</p>
            )}
          </div>

          <div className="flex flex-col mb-3">
            <Input
              isRequired
              id="lastname"
              label="Last Name"
              name="lastname"
              value={formValues.lastname}
              variant="bordered"
              onChange={handleInputChange}
            />
            {formErrors.lastname && (
              <p className="text-red-500">{formErrors.lastname}</p>
            )}
          </div>

          <div className="flex flex-col mb-3">
            <Input
              isRequired
              id="username"
              label="Username"
              name="username"
              value={formValues.username}
              variant="bordered"
              onChange={handleInputChange}
            />
            {formErrors.username && (
              <p className="text-red-500">{formErrors.username}</p>
            )}
          </div>

          <div className="flex flex-col mb-3">
            <div className="relative w-full">
              <Input
                isRequired
                id="password"
                label="Password"
                name="password"
                type={showPassword ? "text" : "password"}
                value={formValues.password}
                variant="bordered"
                onChange={handleInputChange}
              />
              <button
                className="absolute right-3 top-4 text-gray-600"
                tabIndex={-1}
                type="button"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? "🙈" : "👁️"}
              </button>
            </div>
            {formErrors.password && (
              <p className="text-red-500">{formErrors.password}</p>
            )}
          </div>

          <div className="flex flex-col mb-3">
            <div className="relative w-full">
              <Input
                isRequired
                id="cpassword"
                label="Confirm Password"
                name="cpassword"
                type={showCPassword ? "text" : "password"}
                value={formValues.cpassword}
                variant="bordered"
                onChange={handleInputChange}
              />
              <button
                className="absolute right-3 top-4 text-gray-600"
                tabIndex={-1}
                type="button"
                onClick={() => setShowCPassword(!showCPassword)}
              >
                {showCPassword ? "🙈" : "👁️"}
              </button>
            </div>
            {formErrors.cpassword && (
              <p className="text-red-500">{formErrors.cpassword}</p>
            )}
          </div>

          {!hideReferralInput && !askedAboutReferral ? (
            <div className="flex flex-col mb-3">
              <p className="mb-2">Do you have a referral code?</p>
              <div className="flex gap-2">
                <Button 
                  size="sm"
                  onPress={() => {
                    setShowReferralInput(true);
                    setAskedAboutReferral(true);
                  }}
                >
                  Yes, I have a code
                </Button>
                <Button 
                  size="sm"
                  variant="ghost"
                  onPress={() => {
                    setShowReferralInput(false);
                    setAskedAboutReferral(true);
                    // Clear any existing referral code
                    setFormValues(prev => ({
                      ...prev,
                      referralCode: ""
                    }));
                    // Clear cookie if exists
                    if (cookieReferralCode) {
                      deleteReferralCodeCookie();
                      setCookieReferralCode(null);
                    }
                  }}
                >
                  No, continue without code
                </Button>
              </div>
            </div>
          ) : !hideReferralInput && showReferralInput ? (
            <div className="flex flex-col mb-3">
              <div className="flex gap-2">
                <Input
                  id="referralCode"
                  label="Referral Code"
                  name="referralCode"
                  value={formValues.referralCode}
                  variant="bordered"
                  onChange={handleReferralInputChange}
                  isInvalid={referralValidation?.isValid === false}
                  isDisabled={isValidating}
                />
                {showValidateButton && hasManualInput && (
                  <Button 
                    className="mt-1"
                    size="lg"
                    isDisabled={isValidating}
                    onPress={() => handleValidateReferral(formValues.referralCode)}
                  >
                    {isValidating ? "Validating..." : "Terapkan"}
                  </Button>
                )}
              </div>
              {isValidating && (
                <p className="text-gray-500 text-sm mt-1">
                  Validating...
                </p>
              )}
              {referralValidation?.isValid && (
                <p className="text-green-500 text-sm mt-1">
                  ✓ Valid referral code, you will get +1000 AI Credits <br />
                  (2000 initial + 1000 referral bonus = 3000 Total credits)
                </p>
              )}
              {referralValidation?.errorMessage && (
                <p className="text-red-500 text-sm mt-1">
                  {referralValidation.errorMessage}
                </p>
              )}
              {cookieReferralCode && !hasManualInput && (
                <p className="text-gray-500 text-sm mt-1">
                  Using referral code from your previous session
                </p>
              )}
              <Button 
                size="sm" 
                variant="ghost" 
                className="self-start mt-2"
                onPress={() => {
                  setShowReferralInput(false);
                  setFormValues(prev => ({
                    ...prev,
                    referralCode: ""
                  }));
                  if (cookieReferralCode) {
                    deleteReferralCodeCookie();
                    setCookieReferralCode(null);
                  }
                  setReferralValidation(null);
                }}
              >
                Skip referral code
              </Button>
            </div>
          ) : (
            // Even when hidden, we still include the value as a hidden input for form submission
            <div style={{ display: 'none' }}>
              <Input
                id="referralCode"
                name="referralCode"
                value={formValues.referralCode}
                variant="bordered"
              />
            </div>
          )}

          {/* Email opt-in consent checkbox */}
          <div className="flex flex-col mb-3">
            <div className="flex items-start gap-2">
              <input
                id="emailConsent"
                name="emailConsent"
                type="checkbox"
                className="mt-1"
                checked={formValues.emailConsent}
                onChange={handleCheckboxChange}
              />
              <label htmlFor="emailConsent" className="text-sm text-gray-700">
                I agree to receive transactional emails, including account notifications, service updates, and security alerts. I can opt-out anytime in account settings.
              </label>
            </div>
            {formErrors.emailConsent && (
              <p className="text-red-500 text-xs">{formErrors.emailConsent}</p>
            )}
          </div>

          <div className="flex flex-col mb-3">
            <div className="flex items-start gap-2">
              <input
                id="termsAgreed"
                name="termsAgreed"
                type="checkbox"
                className="mt-1"
                checked={formValues.termsAgreed}
                onChange={handleCheckboxChange}
              />
              <label htmlFor="termsAgreed" className="text-sm text-gray-700">
                I agree to the <a href="https://terang.ai/terms-and-conditions" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Terms and Conditions</a> and <a href="https://terang.ai/privacy-policy" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Privacy Policy</a>
              </label>
            </div>
            {formErrors.termsAgreed && (
              <p className="text-red-500 text-xs">{formErrors.termsAgreed}</p>
            )}
          </div>

          {/* Hidden input to store the redirect path */}
          {redirectPath && (
            <input type="hidden" name="redirectPath" value={redirectPath} />
          )}

          {/* ReCAPTCHA */}
          <ReCAPTCHA
            ref={recaptchaRef}
            sitekey={RecaptchasiteKey as string}
            size="invisible"
            onChange={handleRecaptchaChange}
          />

          {/* Submit Button */}
          <Button
            className={`w-full py-2 text-white font-bold ${
              loading ? "bg-gray-400 cursor-not-allowed" : "bg-black"
            }`}
            isDisabled={loading}
            radius="full"
            size="lg"
            type="submit"
          >
            {loading ? (
              <Image alt="" height={40} src={FingerprintLoading} width={40} />
            ) : doneDispatch ? (
              <span className="inline-flex items-center">
                <Image alt="" height={40} src={VerifiedLoading} width={40} />
                Redirecting...
              </span>
            ) : (
              "Register my account"
            )}
          </Button>

          {/* Cancel Button */}
          <Button
            className="w-full py-2 mt-3 border border-gray-300 text-gray-700"
            radius="full"
            variant="ghost"
            onClick={() => {
              logoutBase();
            }}
          >
            I&apos;ll do it later
          </Button>
        </div>
      </form>
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
};

export default AuthRegisterFinal;