import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>, Spinner } from "@heroui/react";
import { CheckmarkCircle01Icon } from 'hugeicons-react';
import { useSession } from 'next-auth/react';
import { sendEmailVerification } from '@/app/lib/actions/auth/actions';

interface User {
  firstname?: string;
  email?: string;
  id?: string;
}

interface CustomSession {
  user?: User;
  expires: string;
}

const VerificationSnackbar: React.FC = () => {
  const [isLoading, setLoading] = useState<boolean>(false);
  const [isSent, setSent] = useState<boolean>(false);
  const { data: session } = useSession() as { data: CustomSession | null };

  const handleResendVerification = async (): Promise<void> => {
    if (!session?.user?.firstname) {
      console.error('No user firstname found in session');
      return;
    }

    setLoading(true);
    try {
      const res = await sendEmailVerification(session.user.firstname);
      if (res) {
        setLoading(false);
        setSent(true);
        setTimeout(() => {
          setSent(false);
        }, 3000);
      }
    } catch (error) {
      console.error('Error during resend verification:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card 
      className="w-full bg-gradient-to-r from-[#667eea] to-[#764ba2] shadow-lg rounded-none"
    >
      <div className="flex items-center justify-between p-4 text-white max-w-[1400px] mx-auto w-full">
        <span className="text-sm font-semibold truncate mr-4">
          Please verify your account.
        </span>
        
        <Button
          className={`
            min-w-[140px] h-10 px-4
            bg-gradient-to-r from-[#FE6B8B] to-[#FF8E53]
            text-white font-bold
            hover:from-[#FF8E53] hover:to-[#FE6B8B]
            rounded-lg shadow-md
            transition-all duration-300
          `}
          onClick={handleResendVerification}
          disabled={isLoading || isSent}
        >
          {isLoading ? (
            <Spinner 
              size="sm"
              color="white"
              className="mr-0"
            />
          ) : isSent ? (
            <CheckmarkCircle01Icon className="w-5 h-5" />
          ) : (
            'Resend verification'
          )}
        </Button>
      </div>
    </Card>
  );
};

export default VerificationSnackbar;