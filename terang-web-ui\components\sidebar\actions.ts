"use server";

import { getUserId } from "@/app/lib/actions/account/actions";

// Define a type for our cache
interface ServerCache {
  [key: string]: {
    data: any;
    timestamp: number;
  };
}

// Create a cache object (outside the function to persist between requests)
// This is properly typed and won't cause TypeScript errors
const serverCache: ServerCache = {};

export async function getSubscriptionTiers() {
  const apiKey = process.env.BACKEND_API_KEY;

  if (!apiKey) {
    throw new Error("API key is not set");
  }

  try {
    // Add rate limiting protection
    const cacheKey = 'subscription_tiers_cache';
    const cachedItem = serverCache[cacheKey];
    
    // Use cached data if it's less than 5 minutes old
    if (cachedItem && (Date.now() - cachedItem.timestamp) < 5 * 60 * 1000) {
      return cachedItem.data;
    }
    
    const response = await fetch(`${process.env.BACKEND_BASE_URL}/v0/subscription-tiers`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'X-API-Key': apiKey,
        // Add cache control headers to prevent excessive requests
        'Cache-Control': 'max-age=300'
      },
      // Add next.js fetch cache options
      next: { revalidate: 300 } // Cache for 5 minutes
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch subscription tiers: ${response.statusText}`);
    }

    // Get the raw response
    const rawData = await response.json();
    
    let result;
    
    // Handle different response formats
    if (Array.isArray(rawData)) {
      // API returns array directly
      result = { tiers: rawData };
    } else if (rawData && typeof rawData === 'object') {
      if (Array.isArray(rawData.tiers)) {
        // API returns { tiers: [...] }
        result = rawData;
      } else if (rawData.tiers && typeof rawData.tiers === 'object' && !Array.isArray(rawData.tiers)) {
        // API returns { tiers: {...} } (object instead of array)
        // Convert object to array
        const tiersArray = Object.values(rawData.tiers);
        result = { tiers: tiersArray };
      } else {
        // If we can't determine the format, return empty array
        result = { tiers: [] };
      }
    } else {
      result = { tiers: [] };
    }
    
    // Cache the result in our typed cache object
    serverCache[cacheKey] = {
      data: result,
      timestamp: Date.now()
    };
    
    return result;
  } catch (error) {
    console.error("Error fetching subscription tiers:", error);
    return { tiers: [] };
  }
}

export async function getUserSubscription() {
  const userId = await getUserId();
  const apiKey = process.env.BACKEND_API_KEY;

  if (!apiKey) {
    throw new Error("API key is not set");
  }

  // Replace $ with actual userId
  const response = await fetch(`${process.env.BACKEND_BASE_URL}/v0/user-subscription/${userId}`, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'X-API-Key': apiKey,
    },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch user subscription: ${response.statusText}`);
  }

  // Get raw data from API
  const rawData = await response.json();
  
  // Format it in the way the component expects
  if (rawData && typeof rawData === 'object' && rawData.tierId) {
    // API returns direct subscription object, wrap it
    return { 
      subscription: {
        tierId: rawData.tierId,
        startDate: rawData.startDate || new Date().toISOString(),
        nextBillingDate: rawData.nextBillingDate || rawData.startDate || new Date().toISOString(),
        isActive: rawData.isActive !== false,
        paymentStatus: rawData.paymentStatus || 'PAID'
      }
    };
  } else if (rawData && rawData.subscription) {
    // Already in correct format
    return rawData;
  } else {
    // Fallback
    return { 
      subscription: {
        tierId: 'free_tier_001',
        startDate: new Date().toISOString(),
        nextBillingDate: new Date().toISOString(),
        isActive: true,
        paymentStatus: 'PAID'
      }
    };
  }
}

export async function createUserSubscription(tierId: string) {
  const userId = await getUserId();
  const apiKey = process.env.BACKEND_API_KEY;

  if (!apiKey) {
    throw new Error("API key is not set");
  }

  // Replace $ with actual userId
  const response = await fetch(`${process.env.BACKEND_BASE_URL}/v0/user-subscription/${userId}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': apiKey,
    },
    body: JSON.stringify({ tierId }),
  });

  if (!response.ok) {
    throw new Error(`Failed to create user subscription: ${response.statusText}`);
  }

  // Get the raw response and format it
  const rawData = await response.json();
  
  // Return in the format expected by the component
  if (rawData && rawData.subscription) {
    return rawData;
  } else if (rawData && typeof rawData === 'object' && rawData.tierId) {
    return { 
      subscription: {
        tierId: rawData.tierId,
        startDate: rawData.startDate || new Date().toISOString(),
        nextBillingDate: rawData.nextBillingDate || rawData.startDate || new Date().toISOString(),
        isActive: rawData.isActive !== false,
        paymentStatus: rawData.paymentStatus || 'PAID'
      }
    };
  } else {
    console.error("Unexpected create subscription response:", rawData);
    return { 
      subscription: {
        tierId: tierId,
        startDate: new Date().toISOString(),
        nextBillingDate: new Date().toISOString(),
        isActive: true,
        paymentStatus: 'PENDING'
      }
    };
  }
}

export async function checkSubscriptionRemainingTime() {
  const userId = await getUserId();
  const apiKey = process.env.BACKEND_API_KEY;

  if (!apiKey) {
    throw new Error("API key is not set");
  }

  // Replace $ with actual userId
  const response = await fetch(`${process.env.BACKEND_BASE_URL}/v0/user-subscription/${userId}/remaining-time`, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'X-API-Key': apiKey,
    },
  });

  // For 404, just return default values
  if (response.status === 404) {
    return {
      remaining_days: 0,
      remaining_hours: 0,
      remaining_minutes: 0
    };
  }

  if (!response.ok) {
    throw new Error(`Failed to check remaining subscription time: ${response.statusText}`);
  }

  const data = await response.json();
  
  // Log the response for debugging
  console.log("Remaining time API response:", data);
  
  // Ensure the response has the expected format
  if (data && 
      (typeof data.remaining_days === 'number' || 
       typeof data.remaining_hours === 'number' || 
       typeof data.remaining_minutes === 'number')) {
    return data;
  } else {
    console.error("Unexpected time remaining format:", data);
    return {
      remaining_days: 0,
      remaining_hours: 0,
      remaining_minutes: 0
    };
  }
}