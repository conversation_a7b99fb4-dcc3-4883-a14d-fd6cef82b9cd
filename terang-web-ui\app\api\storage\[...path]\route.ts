import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

/**
 * Proxy storage requests to the backend server
 * This handles requests like /api/storage/users/images/profile/:userId/:filename
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
      console.error('Missing required environment variables for storage proxy');
      return new NextResponse(null, { status: 500 });
    }

    // Await the params object as it's now a Promise
    const resolvedParams = await params;
    const fullPath = resolvedParams.path.join('/');
    
    // Try multiple URL patterns to handle different backend structures
    const urlPatterns = [
      // Pattern 1: Direct storage endpoint
      `${process.env.BACKEND_BASE_URL}/storage/${fullPath}`,
      
      // Pattern 2: With /api prefix
      `${process.env.BACKEND_BASE_URL}/api/storage/${fullPath}`,
      
      // Pattern 3: With /v1 prefix (common in REST APIs)
      `${process.env.BACKEND_BASE_URL}/v1/storage/${fullPath}`,
    ];
    
    // Try each URL pattern until one works
    let response = null;
    let successUrl = '';
    
    for (const url of urlPatterns) {
      console.log(`Trying storage URL: ${url}`);
      
      try {
        response = await fetch(url, {
          headers: {
            'X-API-KEY': process.env.BACKEND_API_KEY,
          },
        });
        
        if (response.ok) {
          successUrl = url;
          console.log(`Successfully found image at: ${url}`);
          break;
        }
      } catch (fetchError) {
        console.log(`Failed to fetch from ${url}:`, fetchError);
      }
    }
    
    // If none of the patterns worked
    if (!response || !response.ok) {
      console.error(`Storage proxy error: Could not find image at any of the attempted URLs`);
      return new NextResponse(null, { status: 404 });
    }

    // Get the file content
    const fileBuffer = await response.arrayBuffer();
    
    // Get content type from response
    const contentType = response.headers.get('content-type') || 'application/octet-stream';
    
    // Return the file with appropriate headers
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
      },
    });
  } catch (error) {
    console.error('Error proxying storage request:', error);
    return new NextResponse(null, { status: 500 });
  }
}