import React, { useState } from 'react';
import { Input, Button, useDisclosure } from "@heroui/react";
import { updatePassword, type UpdatePasswordRequest } from './password-actions';
import { toast } from 'sonner';

interface IconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
}

const ViewIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 24 24" 
    width={size} 
    height={size} 
    color={"#000000"} 
    fill={"none"} 
    aria-hidden="true"
    {...props}
  >
    <path opacity="0.4" d="M12 4.25C9.42944 4.25 7.22595 5.38141 5.52031 6.71298C3.81313 8.04576 2.55126 9.61974 1.84541 10.6095L1.79219 10.6837C1.53904 11.0358 1.25 11.4378 1.25 12C1.25 12.5622 1.53904 12.9642 1.79219 13.3163L1.84541 13.3905C2.55126 14.3803 3.81313 15.9542 5.52031 17.287C7.22595 18.6186 9.42944 19.75 12 19.75C14.5706 19.75 16.774 18.6186 18.4797 17.287C20.1869 15.9542 21.4487 14.3803 22.1546 13.3905L22.2078 13.3163C22.461 12.9642 22.75 12.5622 22.75 12C22.75 11.4378 22.461 11.0358 22.2078 10.6837L22.1546 10.6095C21.4487 9.61974 20.1869 8.04576 18.4797 6.71298C16.774 5.38141 14.5706 4.25 12 4.25Z" fill="currentColor" />
    <path fillRule="evenodd" clipRule="evenodd" d="M12 15.5C10.067 15.5 8.5 13.933 8.5 12C8.5 10.067 10.067 8.5 12 8.5C13.933 8.5 15.5 10.067 15.5 12C15.5 13.933 13.933 15.5 12 15.5Z" fill="currentColor" />
  </svg>
);

const ViewOffIcon: React.FC<IconProps> = ({ size = 24, ...props }) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 24 24" 
    width={size} 
    height={size} 
    color={"#000000"} 
    fill={"none"} 
    aria-hidden="true"
    {...props}
  >
    <path d="M22 8C22 8 18 14 12 14C6 14 2 8 2 8" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
    <path opacity="0.4" d="M15 13.5L16.5 16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path opacity="0.4" d="M20 11L22 13" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path opacity="0.4" d="M2 13L4 11" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
    <path opacity="0.4" d="M9 13.5L7.5 16" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

interface PasswordFormData extends UpdatePasswordRequest {
    confirmPassword: string;
  }
  
  interface PasswordFormErrors {
    currentPassword?: string;
    newPassword?: string;
    confirmPassword?: string;
  }

const PasswordForm: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState<PasswordFormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [errors, setErrors] = useState<PasswordFormErrors>({});
  const [showPasswords, setShowPasswords] = useState({
    currentPassword: false,
    newPassword: false,
    confirmPassword: false
  });
  const { isOpen: isToastOpen, onOpen: onToastOpen, onClose: onToastClose } = useDisclosure();
  const [toastConfig, setToastConfig] = useState<{ type: 'success' | 'error'; message: string }>({
    type: 'success',
    message: ''
  });

  const handleInputChange = (value: string, id: keyof PasswordFormData): void => {
    setFormData(prev => ({ ...prev, [id]: value }));
    // Clear any existing errors for this field when the user starts typing
    if (errors[id]) {
      setErrors(prev => ({ ...prev, [id]: undefined }));
    }
  };

  const togglePasswordVisibility = (field: keyof typeof showPasswords): void => {
    setShowPasswords(prev => ({ ...prev, [field]: !prev[field] }));
  };
  
  const handleSubmit = async (): Promise<void> => {
    if (!validateForm()) return;
    
    setIsLoading(true);
    const loadingToast = toast.loading('Updating password...');

    try {
      const updateData: UpdatePasswordRequest = {
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword
      };
  
      const result = await updatePassword(updateData);
      if (!result.success) {
        throw new Error(result.error);
      }
  
      // Reset form on success
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
  
      toast.dismiss(loadingToast);
      toast.success('Password updated', {
        description: 'Your password has been changed successfully.',
        duration: 3000
      });
    } catch (error) {
      toast.dismiss(loadingToast);
      toast.error('Update failed', {
        description: error instanceof Error ? error.message : 'Failed to update password',
        duration: 4000
      });
    } finally {
      setIsLoading(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: PasswordFormErrors = {};
    const passwordErrors: string[] = [];
    
    if (!formData.currentPassword) {
      newErrors.currentPassword = 'Current password is required';
    }
    
    if (!formData.newPassword) {
      newErrors.newPassword = 'New password is required';
    } else {
      // Include dash in the symbols list - at the end to avoid regex range issues
      const symbolRegex = /[!@#$%^&*(),.?":{}|<>\-]/;
      const hasSymbol = symbolRegex.test(formData.newPassword);
      
      if (formData.newPassword.length < 8) {
        passwordErrors.push('Password must be at least 8 characters');
      }
      if (!/[A-Z]/.test(formData.newPassword)) {
        passwordErrors.push('Password must contain at least one capital letter');
      }
      if (!hasSymbol) {
        passwordErrors.push('Password must contain at least one symbol (!@#$%^&*(),.?":{}|<>-)');
      }
      if (passwordErrors.length > 0) {
        newErrors.newPassword = passwordErrors.join('. ');
      }
    }
    
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Confirm password is required';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const renderPasswordInput = (
    label: string,
    id: keyof PasswordFormData,
    description?: string
  ) => (
    <Input
      label={label}
      type={showPasswords[id] ? "text" : "password"}
      value={formData[id]}
      onValueChange={(value) => handleInputChange(value, id)}
      isInvalid={!!errors[id]}
      errorMessage={errors[id]}
      description={id === "newPassword" ? "Password must be at least 8 characters long, include 1 capital letter and 1 special character (!@#$%^&*(),.?\":{}|<>-)" : description}
      aria-describedby={description ? `${id}-description` : undefined}
      endContent={
        <Button
          isIconOnly
          variant="light"
          onClick={() => togglePasswordVisibility(id)}
          aria-label={showPasswords[id] ? "Hide password" : "Show password"}
          tabIndex={-1}
          className="focus:outline-none"
        >
          {showPasswords[id] ? (
            <ViewOffIcon className="text-2xl text-default-400 pointer-events-none" />
          ) : (
            <ViewIcon className="text-2xl text-default-400 pointer-events-none" />
          )}
        </Button>
      }
    />
  );
  
  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold">Change Password</h1>
      <p className="text-gray-500">Update your password to keep your account secure</p>

      <form 
        onSubmit={(e) => {
          e.preventDefault();
          handleSubmit();
        }}
        className="space-y-6"
      >
        {renderPasswordInput("Current Password", "currentPassword")}
        {renderPasswordInput(
          "New Password",
          "newPassword",
          "Password must be at least 8 characters long, include 1 capital letter and 1 symbol"
        )}
        {renderPasswordInput("Confirm New Password", "confirmPassword")}

        <Button
          type="submit"
          color="primary"
          isLoading={isLoading}
        >
          Update Password
        </Button>
      </form>
    </div>
  );
};

export default PasswordForm;