// app/my-exams/page.tsx
import React, { Suspense } from "react";
import MyTrials from "@/components/my-trials-subjects";
import { getAvailablePractices } from "@/app/lib/actions/my-exams-and-trials/actions";
import { getUserSubscription } from '@/components/sidebar/actions';
import { getUserId } from "@/app/lib/actions/account/actions";
import { fetchCompletionData } from "@/components/my-trials-subjects/actions";
import { fetchExamGradingInfo } from '@/components/my-trials-subjects/grading-server-actions';

interface PageProps {
  searchParams: Promise<{
    page?: string;
    subject?: string;
    category_id?: string;
  }>;
}

const MyPracticePage = async (props: PageProps) => {
  const searchParams = await props.searchParams;
  const userId = await getUserId();

  // Get paginated data with category filtering
  const examsData = await getAvailablePractices({
    type: 'PRACTICE',
    limit: 1000000
  });

  const examsDataPagination = await getAvailablePractices({
    page: Number(searchParams.page) || 1,
    limit: 10,
    subject: searchParams.subject,
    category_id: searchParams.category_id,
    type: 'PRACTICE'
  });

  const subscriptionData = await getUserSubscription();
  const isPremium = subscriptionData?.subscription?.tierId === 'premium_tier_001' 
    && subscriptionData?.subscription.paymentStatus === 'PAID';

  // Fetch the trial completion data at the parent level
  // Use Promise.all to fetch multiple resources in parallel
  const [trialCompletionData, gradingInfoData] = await Promise.all([
    fetchCompletionData(),
    fetchExamGradingInfo()
  ]);

  return (
    <div>
      <Suspense fallback={
          <div className="flex items-center justify-center min-h-screen">
          Loading halaman latihan...
        </div>
      }>
        <MyTrials 
          examData={examsData}
          examDataPagination={examsDataPagination}
          isPremium={isPremium}
          userId={userId}
          // Pass the pre-fetched data to the component
          trialCompletionData={trialCompletionData}
          gradingInfoData={gradingInfoData}
        />
      </Suspense>
    </div>
  );
};

export default MyPracticePage;