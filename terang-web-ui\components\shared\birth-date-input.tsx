import React, { useState, useEffect, ChangeEvent } from "react";
import { SelectItem, Select } from "@heroui/react";
import { today, getLocalTimeZone } from "@internationalized/date";

interface BirthDateInputProps {
  onChange: (date: string | null) => void;
  required?: boolean;
}

export const BirthDateInput: React.FC<BirthDateInputProps> = ({
  onChange,
  required = false,
}) => {
  const [birthYear, setBirthYear] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  
  // Generate year options from current year back 120 years
  const currentYear = today(getLocalTimeZone()).year;
  const startYear = currentYear - 120;
  const yearOptions = Array.from({ length: currentYear - startYear + 1 }, (_, i) => {
    const year = currentYear - i;
    return { key: year.toString(), value: year.toString(), label: year.toString() };
  });

  useEffect(() => {
    validateYear(birthYear);
  }, [birthYear]);

  const handleYearChange = (event: ChangeEvent<HTMLSelectElement>) => {
    const year = event.target.value;
    setBirthYear(year);
    
    if (year) {
      // Always use January 1st of the selected year
      const formattedDate = `${year}-01-01`;
      onChange(formattedDate);
      validateYear(year);
    } else {
      onChange(null);
      setError(required ? "Tahun lahir diperlukan" : null);
    }
  };

  const validateYear = (year: string) => {
    if (required && !year) {
      setError("Tahun lahir diperlukan");
    } else if (year) {
      const yearNum = parseInt(year);
      if (isNaN(yearNum)) {
        setError("Tahun lahir tidak valid");
      } else if (yearNum > currentYear) {
        setError("Tahun lahir tidak boleh di masa depan");
      } else if (yearNum < startYear) {
        setError("Tahun lahir terlalu jauh di masa lalu");
      } else {
        setError(null);
      }
    } else {
      setError(null);
    }
  };

  const calculateAge = (birthYear: string): number => {
    return birthYear ? currentYear - parseInt(birthYear) : 0;
  };

  return (
    <div>
      <Select
        className="max-w-[1000px]"
        errorMessage={error || undefined}
        isRequired={required}
        label="Tahun Lahir"
        placeholder="Pilih tahun lahir"
        validationState={error ? "invalid" : "valid"}
        value={birthYear}
        onChange={handleYearChange}
      >
        {yearOptions.map((option) => (
          <SelectItem key={option.key} textValue={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </Select>
      {birthYear && !error && (
        <p className="text-sm text-gray-600 mt-2">
          Usia kamu: {calculateAge(birthYear)} tahun
        </p>
      )}
    </div>
  );
};