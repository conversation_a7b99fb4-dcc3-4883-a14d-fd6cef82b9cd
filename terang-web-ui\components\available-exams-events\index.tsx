"use client";

import React, { useEffect, useState, useMemo, useCallback } from "react";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/breadcrumbs";
import { Swiper, SwiperSlide } from "swiper/react";
import { FreeMode, Pagination, Mousewheel } from "swiper/modules";
import { toast, ToastContainer } from "react-toastify";
import { useSession } from "next-auth/react"; // Import the session hook

import { AvailableExamsType } from "../types";
import { ExamCard } from "../cards/exam-card-events";
import EnhancedBundlingPurchaseButton from "./enhanced-bundling-purchase-button";

// Import Redux hooks
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchAvailableExams, setExamType, initializeWithServerData } from "@/store/slices/availableExamsSlice";
import { 
  fetchBundles, 
  fetchBundleDetails, 
  NullableInt, 
  BundleUI,
  clearBundleDetails
} from "@/store/slices/bundlesSlice";

// Remove the import for validateAndTrackReferralCode since we're not using it anymore

import "react-toastify/dist/ReactToastify.css";
import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/pagination";

// Interface for referral info from server
interface ReferralInfo {
  isValid: boolean;
  referralCode: string;
  discountPercentage: number | null;
  discountedPrices: Record<string, number>;
}

interface Props {
  initialData?: AvailableExamsType[];
  predefinedCategories?: string[]; // Changed from predefinedCategory (string) to predefinedCategories (array)
  referralInfo?: ReferralInfo;
  components?: {
    ExamCard?: React.ComponentType<any>;
    EnhancedBundlingPurchaseButton?: React.ComponentType<any>;
  };
}


// Interface for our category groups
interface CategoryGroup {
  category_name: string;
  exams: AvailableExamsType[];
  newest_exam_date: Date;
}

interface CategoryTheme {
  icon: string;
  bgColor: string;
  textColor: string;
  bgColorStyle?: React.CSSProperties;
}

// Constants for localStorage keys
const LOCAL_STORAGE_CATEGORY_KEY = 'activeAvailableExamCategory';

const getCategoryTheme = (categoryName: string): CategoryTheme => {
  const themes = {
    LPDP: {
      icon: "🎓", 
      bgColor: "bg-gradient-to-r from-blue-400 to-indigo-500",
      textColor: "text-white"
    },
    CPNS: {
      icon: "🏛️", 
      bgColor: "bg-gradient-to-r from-teal-600 to-emerald-400",
      textColor: "text-white"
    },
    UTBK: {
      icon: "📚", 
      bgColorStyle: { background: "linear-gradient(to right, #15BBBB, #7fd8d8)" },
      bgColor: "", // Empty string as we'll use the style approach
      textColor: "text-white"
    },
    Uncategorized: {
      icon: "✨", 
      bgColor: "bg-gradient-to-r from-gray-400 to-gray-600",
      textColor: "text-white"
    }
  };

  // Return the theme for the category or the uncategorized theme if not found
  return themes[categoryName as keyof typeof themes] || themes.Uncategorized;
};

export const AvailableExams: React.FC<Props> = ({ 
  initialData, 
  predefinedCategories = [],
  referralInfo,
  components = {} // Default to empty object
}) => {
  // Redux hooks
  const { data: session, status } = useSession();
  const isAuthenticated = status === "authenticated";
  const dispatch = useAppDispatch();
  const { items: reduxExams, loading, error, examType } = useAppSelector((state) => state.availableExams);
  const { 
    items: bundles, 
    filteredItems, 
    activeCategory: bundlesCategory,
    currentBundleDetails,
    bundleDetailsLoading
  } = useAppSelector((state) => state.bundles);
  
  // Local state
  const [refreshData, setRefreshData] = useState(false);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [selectedBundleId, setSelectedBundleId] = useState<string | null>(null);

  // Use either Redux data or initialData
  const availableExams = useMemo(() => {
    return reduxExams || initialData || [];
  }, [reduxExams, initialData]);

  // Use custom components or fallback to defaults
  const CustomExamCard = components.ExamCard || ExamCard;
  const CustomBundlingPurchaseButton = components.EnhancedBundlingPurchaseButton || EnhancedBundlingPurchaseButton;

  // Initialize Redux store with initialData if available and fetch bundles
  useEffect(() => {
    if (initialData && initialData.length > 0 && !reduxExams) {
      // Initialize Redux store with server data to avoid unnecessary API call
      dispatch(initializeWithServerData(initialData));
    }

    // Fetch bundles on component mount
    dispatch(fetchBundles());
  }, [dispatch, initialData, reduxExams]);

  // Fetch bundle details when a bundle is selected
  useEffect(() => {
    if (selectedBundleId) {
      dispatch(fetchBundleDetails(selectedBundleId));
    }
  }, [dispatch, selectedBundleId]);

  // REMOVED: The useEffect for validating referral code since we get it from the server now

  const showNotification = (
    message: string,
    type: "success" | "info" | "error" | "warning",
  ) => {
    toast[type](message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  // Handle category change with localStorage caching
  const handleCategoryChange = useCallback((categoryName: string) => {
    // If predefinedCategories is set, only allow changing to categories in that array
    if (predefinedCategories.length > 0 && !predefinedCategories.includes(categoryName)) {
      showNotification(`Only ${predefinedCategories.join(' and ')} categories are available`, "info");
      return;
    }
    
    // Update the active category
    setActiveCategory(categoryName);
    
    // Save to localStorage
    try {
      localStorage.setItem(LOCAL_STORAGE_CATEGORY_KEY, categoryName);
    } catch (error) {
      console.error("Failed to save category to localStorage:", error);
    }
  }, [predefinedCategories, showNotification]);

  // Handle refresh data - now uses Redux
  useEffect(() => {
    if (refreshData) {
      dispatch(fetchAvailableExams(examType))
        .unwrap()
        .then(() => {
          setRefreshData(false);
        })
        .catch(() => {
          showNotification("Failed to refresh data", "error");
          setRefreshData(false);
        });
    }
  }, [refreshData, dispatch, examType, showNotification]);

  // Group exams by category
  const categoryGroups = useMemo(() => {
    // Create a map to group exams by category
    const groupMap = new Map<string, CategoryGroup>();
    
    availableExams.forEach((exam) => {
      // Handle null/undefined category by providing a default value
      const categoryName = exam.category_name || "Uncategorized";
      
      // Determine the date to use for "newest" calculation
      const examDate = new Date(exam.created_at || "");
      
      if (!groupMap.has(categoryName)) {
        // Initialize a new category group
        groupMap.set(categoryName, {
          category_name: categoryName,
          exams: [exam],
          newest_exam_date: examDate,
        });
      } else {
        // Add to existing category and update newest date if applicable
        const group = groupMap.get(categoryName)!;
        group.exams.push(exam);
        
        if (examDate > group.newest_exam_date) {
          group.newest_exam_date = examDate;
        }
      }
    });
    
    // Convert map to array and sort by newest_exam_date (newest first)
    return Array.from(groupMap.values()).sort((a, b) => 
      b.newest_exam_date.getTime() - a.newest_exam_date.getTime()
    );
  }, [availableExams]);

  // Filter category groups based on predefinedCategory if set
  const filteredCategoryGroups = useMemo(() => {
    if (predefinedCategories.length > 0) {
      return categoryGroups.filter(group => 
        predefinedCategories.includes(group.category_name));
    }
    return categoryGroups;
  }, [categoryGroups, predefinedCategories]);

  // Find the best bundle to display for each category
  const getDiscountValue = (discountPercentage: number | null | NullableInt): number => {
    // If it's null
    if (discountPercentage === null) {
      return 0;
    }
    
    // If it's a number
    if (typeof discountPercentage === 'number') {
      return discountPercentage;
    }
    
    // If it's the NullableInt object from API
    if (discountPercentage && 
        typeof discountPercentage === 'object' && 
        'Valid' in discountPercentage && 
        discountPercentage.Valid) {
      return discountPercentage.Int32;
    }
    
    // Default fallback
    return 0;
  };
  
  // Modified findBestBundle function
  const findBestBundle = useCallback((categoryName: string): BundleUI | null => {
    if (!bundles) {
      console.log('Bundle array is null or undefined');
      return null;
    }
    
    if (bundles.length === 0) {
      console.log('Bundle array is empty');
      return null;
    }
    
    // Normalize the category name for better matching
    const normalizedCategoryName = categoryName.toLowerCase().trim();
    
    // Filter bundles by category
    const categoryBundles = bundles.filter(bundle => {
      // Safety check
      if (!bundle) return false;
      
      // Get category name from both possible locations
      const bundleCategoryNameFlat = (bundle.categoryName || '').toLowerCase().trim();
      const bundleCategoryNameNested = (bundle.category?.name || '').toLowerCase().trim();
      
      // Match against our normalized category
      return bundleCategoryNameFlat === normalizedCategoryName || 
            bundleCategoryNameNested === normalizedCategoryName;
    });
    
    if (categoryBundles.length === 0) return null;
    
    // Find the bundle with highest discount
    try {
      return categoryBundles.reduce((best, current) => {
        // Get discount values safely
        const currentDiscountValue = getDiscountValue(current.discountPercentage);
        const bestDiscountValue = getDiscountValue(best.discountPercentage);
        
        return currentDiscountValue > bestDiscountValue ? current : best;
      });
    } catch (error) {
      // Fallback to first matching bundle
      return categoryBundles[0] || null;
    }
  }, [bundles]);

  // Update selectedBundleId when activeCategory changes
  useEffect(() => {
    if (!activeCategory || !bundles) return;
    
    const bestBundle = findBestBundle(activeCategory);
    if (bestBundle) {
      setSelectedBundleId(bestBundle.id);
    }
  }, [activeCategory, bundles, findBestBundle]);

  // Set default category or retrieve from localStorage
  useEffect(() => {
    if (categoryGroups.length > 0) {
      try {
        // Only set the active category if it's not already set
        // or if it's not valid for the current available categories
        const isActiveValid = activeCategory && (
          predefinedCategories.length === 0 || 
          (predefinedCategories.includes(activeCategory) && 
           categoryGroups.some(group => group.category_name === activeCategory))
        );
        
        if (isActiveValid) {
          // If the current active category is valid, keep it
          return;
        }
        
        // Try to use predefined categories first
        if (predefinedCategories.length > 0) {
          const validPredefinedCategories = predefinedCategories.filter(cat => 
            categoryGroups.some(group => group.category_name === cat));
          
          if (validPredefinedCategories.length > 0) {
            setActiveCategory(validPredefinedCategories[0]);
            return;
          }
        }
        
        // Try to use cached category from localStorage
        const cachedCategory = localStorage.getItem(LOCAL_STORAGE_CATEGORY_KEY);
        
        if (cachedCategory && 
            categoryGroups.some(group => group.category_name === cachedCategory) &&
            (predefinedCategories.length === 0 || predefinedCategories.includes(cachedCategory))) {
          setActiveCategory(cachedCategory);
          return;
        }
        
        // Default to the first available category
        const availableCategories = predefinedCategories.length > 0 
          ? categoryGroups.filter(group => predefinedCategories.includes(group.category_name)) 
          : categoryGroups;
        
        if (availableCategories.length > 0) {
          setActiveCategory(availableCategories[0].category_name);
        }
      } catch (error) {
        console.error("Error accessing localStorage:", error);
      }
    }
  }, [categoryGroups, predefinedCategories]);

  // Configuration for Swiper that can be reused
  const swiperConfig = {
    breakpoints: {
      640: {
        slidesPerView: 1.5,
        slidesOffsetBefore: 20,
        slidesOffsetAfter: 20,
      },
      768: {
        slidesPerView: 2.1,
        slidesOffsetBefore: 20,
        slidesOffsetAfter: 20,
      },
      1024: {
        slidesPerView: 3.1,
        slidesOffsetBefore: 20,
        slidesOffsetAfter: 20,
      },
    },
    freeMode: true,
    modules: [FreeMode, Pagination, Mousewheel],
    mousewheel: true,
    pagination: { clickable: true },
    slidesOffsetAfter: 20,
    slidesOffsetBefore: 20,
    slidesPerView: 1.3,
    spaceBetween: 15,
  };

  // Show loading state when Redux is loading
  if (loading && !initialData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-700"></div>
      </div>
    );
  }

  // Show error state
  if (error && !availableExams.length) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <p className="text-red-500 mb-4">Error: {error}</p>
        <button 
          onClick={() => dispatch(fetchAvailableExams(examType))}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Coba Lagi
        </button>
      </div>
    );
  }

  return (
    <div className="my-10 px-6 lg:px-6 max-w-[95rem] mx-auto w-full flex flex-col gap-4">
      <ul className="flex">
        <Breadcrumbs>
          <BreadcrumbItem href="/dashboard">Dashboard</BreadcrumbItem>
          <BreadcrumbItem>Ujian Tersedia</BreadcrumbItem>
          {predefinedCategories.length > 0 && (
            <BreadcrumbItem>{predefinedCategories.join(' & ')}</BreadcrumbItem>
          )}
        </Breadcrumbs>
      </ul>

      {availableExams.length > 0 ? (
        <div className="w-full">
          {/* Category Tabs/Pills - Only show if predefinedCategory is not set */}
          {(predefinedCategories.length !== 1) && categoryGroups.length > 1 && (
            <div className="mb-8 overflow-x-auto p-2">
              <div className="flex space-x-2">
                {categoryGroups.map((group) => {
                  // Only show allowed categories if predefinedCategories is set
                  if (predefinedCategories.length > 0 && !predefinedCategories.includes(group.category_name)) {
                    return null;
                  }
                  
                  const theme = getCategoryTheme(group.category_name);
                  return (
                    <button
                      key={group.category_name}
                      onClick={() => handleCategoryChange(group.category_name)}
                      className={`px-4 py-2 rounded-full flex items-center transition-all transform hover:scale-105 ${
                        activeCategory === group.category_name
                          ? `${theme.bgColor} ${theme.textColor} shadow-lg`
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                      }`}
                      style={activeCategory === group.category_name && theme.bgColorStyle ? theme.bgColorStyle : {}}
                    >
                      <span className="mr-2">{theme.icon}</span>
                      <span className="font-medium">{group.category_name}</span>
                      <span className="ml-2 bg-white bg-opacity-25 text-xs px-2 py-1 rounded-full">
                        {group.exams.length}
                      </span>
                    </button>
                  );
                })}
              </div>
            </div>
          )}

          {/* Active Category Content */}
          {filteredCategoryGroups.map((group) => {
            const isActive = group.category_name === activeCategory;
            const theme = getCategoryTheme(group.category_name);
            
            // If predefinedCategories is set, only show categories that match the active category
            // Otherwise, only show the active category
            if ((predefinedCategories?.length > 0 && !isActive) || 
                (!predefinedCategories?.length && !isActive)) {
              return null;
            }
            
            // Find the best bundle for this category
            const bestBundle = findBestBundle(group.category_name);

            return (
              <div key={group.category_name} className="space-y-6 transition-all duration-300 categoryContent">
                {/* Category Header with Icon and Description */}
                <div className={`p-6 rounded-xl ${theme.bgColor} ${theme.textColor} shadow-lg`} style={theme.bgColorStyle}>
                  <div className="flex items-center mb-2">
                    <span className="text-3xl mr-3">{theme.icon}</span>
                    <h2 className="text-2xl font-bold">{group.category_name}</h2>
                  </div>
                  <p className="opacity-90">
                    {group.category_name === "LPDP" && "Simulasi tes untuk beasiswa LPDP dalam berbagai paket pilihan."}
                    {group.category_name === "CPNS" && "Persiapkan diri kamu untuk tes CPNS dengan simulasi yang terakurat."}
                    {group.category_name === "BUMN" && "Latihan soal untuk persiapan tes masuk BUMN terkemuka."}
                    {group.category_name === "UTBK" && "Latihan SNBT UTBK terbaru sesuai kurikulum."}
                    {group.category_name === "Uncategorized" && "Berbagai tes persiapan lainnya untuk menunjang persiapan kamu."}
                  </p>
                </div>
            
                {/* Add Bundle Purchase Button - Priority to pre-defined bundles */}
                {bestBundle ? (
                  <CustomBundlingPurchaseButton 
                    examIds={group.exams.slice(0, Math.min(3, group.exams.length)).map(exam => exam.id)}
                    discountPercentage={getDiscountValue(bestBundle.discountPercentage)}
                    bundleId={bestBundle.id}
                    bundleName={bestBundle.name}
                    categoryName={group.category_name}
                    bundlePrice={bestBundle.price}
                    // Pass bundle details
                    bundleDetails={currentBundleDetails}
                    bundleDetailsLoading={bundleDetailsLoading}
                    showNotification={showNotification}
                    setRefreshData={setRefreshData}
                    hideReferralInput={true}
                    referralCode={referralInfo?.referralCode || "TEST2025"}
                    // Add the server discount info here
                    serverDiscountInfo={referralInfo}
                    isUserLoggedIn={isAuthenticated}
                  />
                ) : group.exams.length >= 2 ? (
                  <CustomBundlingPurchaseButton 
                    examIds={group.exams.slice(0, Math.min(3, group.exams.length)).map(exam => exam.id)}
                    discountPercentage={15}
                    categoryName={group.category_name}
                    showNotification={showNotification}
                    setRefreshData={setRefreshData}
                    hideReferralInput={true}
                    referralCode={referralInfo?.referralCode || "TEST2025"}
                    // Add the server discount info here
                    serverDiscountInfo={referralInfo}
                    isUserLoggedIn={isAuthenticated}
                    onPurchase={async (ids) => {
                      try {
                        // API call to purchase bundle would go here
                        showNotification("Paket berhasil dibeli!", "success");
                      } catch (error) {
                        showNotification("Gagal membeli paket", "error");
                      }
                    }}
                  />
                ) : null}
            
                {/* Exam Cards in Swiper */}
                <div className="mt-4">
                  <Swiper {...swiperConfig}>
                    {group.exams.map((exam) => (
                      <SwiperSlide key={exam.id} className="my-8">
                        <CustomExamCard
                          examData={exam}
                          setRefreshData={setRefreshData}
                          showNotification={showNotification}
                          // Use server-provided referral code
                          referralCode={referralInfo?.referralCode || "TEST2025"}
                          // Use server-calculated discount information
                          discountPercentage={referralInfo?.discountPercentage || null}
                          discountedPrice={referralInfo?.discountedPrices[exam.id] || null}
                        />
                      </SwiperSlide>
                    ))}
                  </Swiper>
                </div>
              </div>
            );
          })}
          </div>
          ) : (
          <div className="flex justify-center items-center h-[calc(100vh-200px)]">
            <div className="text-center py-10">
              <p className="text-lg font-semibold">
                {predefinedCategories?.length > 0 
                  ? `Sayangnya belum ada ujian ${predefinedCategories.join(' atau ')} yang ditampilkan oleh sistem.`
                  : "Sayangnya belum ada ujian yang ditampilkan oleh sistem."}
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Ini bukan kesalahan kamu kok, tapi memang kita lagi persiapkan ujian yang terbaik untuk kamu.
              </p>
            </div>
          </div>
          )}
        <ToastContainer />
    </div>
  );
};