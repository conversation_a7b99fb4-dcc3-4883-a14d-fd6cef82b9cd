import React, { lazy, Suspense } from "react";
import styled from "styled-components";
import Image from "next/image";

import Loading from "../Loading";

const ConfettiComponent = lazy(() => import("../Confetti"));

interface ThemeProps {
  body: string;
  text: string;
  fontxxl: string;
  fontxl: string;
  fontlg: string;
  fontmd: string;
  carouselColor: string;
  textRgba: string;
}

const Section = styled.section<{ theme: ThemeProps }>`
  margin-top: 1rem;
  width: 100vw;
  background-color: ${(props) => props.theme.body};
  position: relative;
  overflow: hidden;
`;

const Title = styled.h1<{ theme: ThemeProps }>`
  font-size: ${(props) => props.theme.fontxxl};
  text-transform: capitalize;
  color: ${(props) => props.theme.text};
  font-family: "Sora", sans-serif;
  font-weight: 600;
  line-height: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1rem auto;
  border-bottom: 2px solid ${(props) => props.theme.text};
  width: fit-content;

  @media (max-width: 40em) {
    font-size: ${(props) => props.theme.fontxl};
  }
`;

const Container = styled.div`
  width: 75%;
  margin: 2rem auto;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;

  @media (max-width: 64em) {
    width: 80%;
  }
  @media (max-width: 48em) {
    width: 90%;
  }
`;

const Item = styled.div<{ theme: ThemeProps }>`
  width: calc(20rem - 4vw);
  padding: 1rem 0;
  color: ${(props) => props.theme.body};
  margin: 2rem 1rem;
  position: relative;
  z-index: 5;
  backdrop-filter: blur(4px);
  border: 2px solid ${(props) => props.theme.text};
  border-radius: 20px;

  &:hover {
    img {
      transform: translateY(-2rem) scale(1.2);
    }
  }

  @media (max-width: 30em) {
    width: 70vw;
  }
`;

const ImageContainer = styled.div<{ theme: ThemeProps }>`
  width: 90%;
  margin: 0 auto;
  background-color: ${(props) => props.theme.carouselColor};
  border: 1px solid ${(props) => props.theme.text};
  padding: 0px;
  border-radius: 20px;
  cursor: pointer;

  img {
    width: 100%;
    border-radius: 20px;
    height: auto;
    transition: all 0.3s ease;
  }
`;

const Name = styled.h2<{ theme: ThemeProps }>`
  font-size: ${(props) => props.theme.fontlg};
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  text-transform: uppercase;
  color: ${(props) => props.theme.text};
  margin-top: 1rem;
`;

const Position = styled.h2<{ theme: ThemeProps }>`
  font-size: ${(props) => props.theme.fontmd};
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: capitalize;
  color: ${(props) => `rgba(${props.theme.textRgba},0.9)`};
  font-weight: 400;
`;

interface MemberComponentProps {
  img: string;
  name: string;
  position: string;
}

const MemberComponent: React.FC<MemberComponentProps> = ({
  img,
  name,
  position,
}) => {
  return (
    <Item>
      <ImageContainer>
        <Image alt={name} height={400} src={img} width={500} />
      </ImageContainer>
      <Name>{name}</Name>
      <Position>{position}</Position>
    </Item>
  );
};

const Team: React.FC = () => {
  return (
    <Section id="team">
      <Suspense fallback={<Loading />}>
        <ConfettiComponent />
      </Suspense>
      <Title>Founders</Title>
      <Container>
        <MemberComponent
          img={"https://cdn.terang.ai/landingpage-assets/founders/alfian.jpeg"}
          name="Alfian Firmansyah"
          position="CPO & Co-Founder"
        />
        <MemberComponent
          img={"https://cdn.terang.ai/landingpage-assets/founders/fahriza.jpeg"}
          name="Muhamad Fahriza Novriansyah"
          position="CEO & Co-Founder"
        />
        <MemberComponent 
          img={"https://cdn.terang.ai/landingpage-assets/founders/syahrul.jpeg"} 
          name="Syahrul Hidayat" 
          position="CTO & Co-Founder" 
        />
      </Container>
    </Section>
  );
};

export default Team;
