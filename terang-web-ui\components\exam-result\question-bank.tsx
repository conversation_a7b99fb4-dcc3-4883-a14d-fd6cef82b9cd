import React, { useRef, useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardBody } from "@heroui/react";
import { ExamSession } from "../types";

export interface Metadata {
  level: number;
  name: string;
  value: string | null;
}

export interface Content {
  content: string;
  type: string;
}

export interface Option {
  id: string;
  data: Array<{ contents: Content[] }>;
  is_correct: boolean;
}

export interface QuestionData {
  id: string;
  metadata: Metadata[];
  instruction: any[];
  question: Array<{ contents: Content[] }>;
  options: {
    shuffle: boolean;
    values: Option[];
  };
  hints: any[];
  explanation: Array<{ contents: Content[] }>;
  insight: any[];
}

interface QuestionBankProps {
  totalQuestions: number;
  currentPage: number;
  setCurrentPage: (page: number) => void;
  flaggedQuestions: { [key: string]: boolean };
  selectedOptions: { [key: string]: string };
  isTimeUp: boolean;
  onExitExam: () => void;
  isLoading: boolean;
  questionIds: string[];
  score: number | null;
  questionData: QuestionData[];
  sessionData: ExamSession | null;
}

export const QuestionBank: React.FC<QuestionBankProps> = ({
  totalQuestions,
  currentPage,
  setCurrentPage,
  flaggedQuestions,
  selectedOptions,
  isTimeUp,
  onExitExam,
  isLoading,
  questionIds,
  score,
  questionData,
  sessionData,
}) => {
  const [showScrollbar, setShowScrollbar] = useState<boolean>(false);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkOverflow = () => {
      if (scrollContainerRef.current) {
        const isOverflowing =
          scrollContainerRef.current.scrollHeight >
          scrollContainerRef.current.clientHeight;
        setShowScrollbar(isOverflowing);
      }
    };

    checkOverflow();
    window.addEventListener("resize", checkOverflow);
    return () => window.removeEventListener("resize", checkOverflow);
  }, [questionIds]);

  // Calculate insights
  const answeredCount = Object.keys(selectedOptions).length;
  const unansweredCount = totalQuestions - answeredCount;
  const flaggedCount = Object.keys(flaggedQuestions).length;

  const getAnswerStatus = (
    questionId: string,
  ): "correct" | "incorrect" | "unanswered" => {
    const selectedOptionId = selectedOptions[questionId];
    if (!selectedOptionId) return "unanswered";

    const question = questionData.find((q) => q.id === questionId);
    if (!question) return "unanswered";

    const correctOption = question.options.values.find(
      (option) => option.is_correct,
    );
    if (!correctOption) return "unanswered";

    return selectedOptionId === correctOption.id ? "correct" : "incorrect";
  };

  const isQuestionAccessible = (index: number): boolean => {
    // If not practice mode or loading, all questions are accessible
    if (!sessionData || sessionData.type !== "PRACTICE" || isLoading) {
      return true;
    }
  
    // First question is always accessible
    if (index === 0) return true;
  
    // For practice mode, check if this is the next unanswered question
    const prevAnswered = questionIds.slice(0, index).every(id => !!selectedOptions[id]);
    const currentNotAnswered = !selectedOptions[questionIds[index]];
    
    // Allow access if:
    // 1. All previous questions are answered AND this is the next unanswered question
    // 2. OR if this question has already been answered
    return (prevAnswered && currentNotAnswered) || !!selectedOptions[questionIds[index]];
  };

  const handleQuestionClick = (index: number) => {
    if (isQuestionAccessible(index) && !isTimeUp) {
      setCurrentPage(index);
    }
  };

  const getButtonStyle = (questionId: string, isCurrent: boolean, index: number): string => {
    const status = getAnswerStatus(questionId);
    const isAccessible = isQuestionAccessible(index);
    const isPracticeMode = sessionData?.type === "PRACTICE";
    const isAnswered = !!selectedOptions[questionId];

    let bgColor = "bg-gray-200";
    let textColor = "text-black";
    let opacity = "opacity-100";
    let cursor = "cursor-pointer";

    // Handle practice mode restrictions
    if (isPracticeMode && !isAccessible && !isAnswered) {
      opacity = "opacity-50";
      cursor = "cursor-not-allowed";
      bgColor = "bg-gray-300";
    }

    // Set colors based on answer status
    if (status === "correct") {
      bgColor = "bg-green-100";
      textColor = "text-green-700";
    } else if (status === "incorrect") {
      bgColor = "bg-red-100";
      textColor = "text-red-700";
    }

    // Current question highlighting (only if accessible or answered)
    if (isCurrent && (isAccessible || isAnswered)) {
      bgColor = "bg-blue-500";
      textColor = "text-white";
    }

    return `
      relative w-[48px] h-[48px] lg:w-[48px] lg:h-[48px] rounded-lg
      ${bgColor} ${textColor} ${opacity} ${cursor}
      flex justify-center items-center
      focus:outline-none
      transition-colors duration-200
      ${isAccessible || isAnswered ? 'hover:opacity-80' : ''}
    `;
  };

  return (
    <Card className="w-full py-4" style={{ fontFamily: "'Nunito', sans-serif" }}>
      <CardHeader className="flex flex-col justify-center items-center pb-0 pt-2 px-4">
        <h4 className="font-bold text-large text-center">
          {sessionData?.type === "PRACTICE" ? "Practice Questions" : "Nomor Soal"}
        </h4>
        {sessionData?.type === "PRACTICE" && (
          <p className="text-sm text-blue-600 mt-2">
            Complete each question to unlock the next one
          </p>
        )}
      </CardHeader>
      <CardBody className="overflow-visible py-2 flex flex-col items-center">
        <div
          ref={scrollContainerRef}
          className={`h-[400px] overflow-y-auto ${showScrollbar ? "pr-2" : ""} mb-4`}
          style={{
            scrollbarWidth: showScrollbar ? "thin" : "none",
            scrollbarColor: showScrollbar ? "#9CA3AF #E5E7EB" : "transparent transparent",
          }}
        >
          <div className="grid grid-cols-5 lg:grid-cols-4 gap-2">
            {questionIds.map((questionId, index) => {
              const isCurrent = currentPage === index;
              const isFlagged = flaggedQuestions[questionId];
              const isAccessible = isQuestionAccessible(index);
              const isAnswered = !!selectedOptions[questionId];

              return (
                <button
                  key={questionId}
                  aria-label={`Question ${index + 1}`}
                  aria-pressed={isCurrent}
                  className={getButtonStyle(questionId, isCurrent, index)}
                  disabled={(!isAccessible && !isAnswered) || isTimeUp}
                  onClick={() => handleQuestionClick(index)}
                  title={!isAccessible && !isAnswered ? "Answer previous questions first" : `Question ${index + 1}`}
                >
                  <span className="text-base font-semibold">{index + 1}</span>
                  {isFlagged && (
                    <span className="absolute top-0.5 right-0.5 w-3 h-3 bg-yellow-500 rounded-full" />
                  )}
                </button>
              );
            })}
          </div>
        </div>

        {sessionData?.type === "PRACTICE" && !isLoading && (
          <div className="w-full px-4 mb-4">
            <div className="bg-blue-50 border-l-4 border-blue-500 p-4 rounded-r">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-700">
                    Practice Mode: Answer questions in sequence to proceed
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Insights Section */}
        <div className="grid px-6 grid-cols-2 gap-4 w-full mt-4 text-sm">
          <div className="flex items-center">
            <span className="w-3 h-3 bg-green-200 rounded-full mr-1" />
            <span>Benar: {score || 0}</span>
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-red-200 rounded-full mr-1" />
            <span>Salah: {answeredCount - (score || 0)}</span>
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-gray-300 rounded-full mr-1" />
            <span>Belum terisi: {unansweredCount}</span>
          </div>
          <div className="flex items-center">
            <span className="w-3 h-3 bg-yellow-500 rounded-full mr-1" />
            <span>Ragu: {flaggedCount}</span>
          </div>
        </div>
      </CardBody>
    </Card>
  );
};