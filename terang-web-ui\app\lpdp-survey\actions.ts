"use server";

import { auth } from "@/auth";

/**
 * Fetches the current user's data (name and email) from the database
 * @returns Promise containing the user's name and email or null if not found
 */
export async function getUserData(): Promise<{ name: string; email: string } | null> {
  try {
    // Get the current user's session
    const session = await auth();

    // If user is not authenticated, return null without error
    // This makes authentication optional
    if (!session || !session.user?.email) {
      console.log("[LPDP Survey] User not authenticated, continuing as guest");
      return null;
    }

    const userEmail = session.user.email;

    // Fetch user data from the backend API
    if (!process.env.BACKEND_BASE_URL) {
      throw new Error("BACKEND_BASE_URL is not defined");
    }

    if (!process.env.BACKEND_API_KEY) {
      throw new Error("BACKEND_API_KEY is not defined");
    }

    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v1/users/emails/${userEmail}`,
      {
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
        cache: "no-store", // Don't cache the response
      }
    );

    if (!response.ok) {
      // If we can't fetch user data, just return null instead of throwing an error
      // This allows the form to work without authentication
      console.log("[LPDP Survey] Could not fetch user data, continuing as guest");
      return null;
    }

    const result = await response.json();

    if (!result.data) {
      console.log("[LPDP Survey] User data not found, continuing as guest");
      return null;
    }

    // Extract name and email from the response
    const userData = {
      name: `${result.data.first_name || ''} ${result.data.last_name || ''}`.trim(),
      email: result.data.email
    };

    console.log("[LPDP Survey] User data fetched successfully:", userData);

    return userData;
  } catch (error) {
    console.error("[LPDP Survey] Error fetching user data:", error);
    throw error;
  }
}