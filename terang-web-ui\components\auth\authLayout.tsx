import React, { useState, useEffect } from 'react';
import { Image } from "@heroui/react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Link from 'next/link';

interface CarouselSlide {
  title: string;
  description: string;
  image: string;
}

interface AuthLayoutWrapperProps {
  children: React.ReactNode;
}

const carouselData: CarouselSlide[] = [
  {
    title: "AI Menjadi Teman Belajar yang Cerdas",
    description: "Di dunia pendidikan, AI bukan hanya sekadar tren, tapi sudah mulai membawa perubahan besar. Membantu siswa memahami materi hingga mendukung guru dalam mengelola kelas.",
    image: "https://cdn.terang.ai/images/auth/feed-1.jpg"
  },
  {
    title: "Tahu Nggak Sih?",
    description: "48% pendidik bilang AI bawa dampak positif ke pengalaman belajar. Soalnya AI bantu bikin belajar lebih personal, hemat waktu sampai 13 jam seminggu, dan kasih feedback langsung!",
    image: "https://cdn.terang.ai/images/auth/feed-2.jpg"
  },
  {
    title: "<PERSON><PERSON><PERSON><PERSON> Kamu?",
    description: "AI membuat belajar lebih efektif, efisien dan menyenangkan dengan akses 24/7, analisis kinerja, dan pengalaman interaktif.",
    image: "https://cdn.terang.ai/images/auth/feed-3.jpg"
  },
  {
    title: "3 Fungsi AI Untuk Pendidikan",
    description: "AI dapat menyesuaikan materi pembelajaran, membantu mendeteksi kesulitan soal, dan menganalisis hasil belajar untuk pengalaman belajar yang lebih baik.",
    image: "https://cdn.terang.ai/images/auth/feed-4.jpg"
  }
];

const AuthLayoutWrapper: React.FC<AuthLayoutWrapperProps> = ({ children }) => {
  const [currentSlide, setCurrentSlide] = useState<number>(0);

  useEffect(() => {
    // Only make the body non-scrollable, let our content area scroll
    document.body.style.overflow = 'hidden';
    
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselData.length);
    }, 3000);

    return () => {
      clearInterval(timer);
      document.body.style.overflow = 'auto';
    };
  }, []);

  const nextSlide = (): void => {
    setCurrentSlide((prev) => (prev + 1) % carouselData.length);
  };

  const prevSlide = (): void => {
    setCurrentSlide((prev) => (prev - 1 + carouselData.length) % carouselData.length);
  };

  const handleDotClick = (index: number): void => {
    setCurrentSlide(index);
  };

  return (
    <div className="flex min-h-screen overflow-hidden">
      {/* Left side - Content */}
      <div className="flex-1 flex flex-col relative">
        {/* Logo */}
        <div className="absolute top-6 left-6 z-10">
          <Link href={"/"}>
            <Image
              alt="Logo"
              className="w-48"
              src="https://cdn.terang.ai/images/logo/logo-terang-ai.svg"
            />
          </Link>
        </div>
        
        {/* Mobile background */}
        <div className="md:hidden absolute inset-0 z-[-1]">
          <Image
            alt="gradient"
            className="w-full h-full object-cover"
            src="https://nextui.org/gradients/docs-right.png"
          />
        </div>
        
        {/* Content area with scroll */}
        <div className="flex-1 flex items-center justify-center px-4 sm:px-6 md:px-8 py-16">
          {/* Scrollable container with fixed height and auto overflow */}
          <div className="w-full max-w-md max-h-[calc(100vh-8rem)] overflow-y-auto">
            <div className="py-4">
              {children}
            </div>
          </div>
        </div>
      </div>

      {/* Right side - Carousel */}
      <div className="hidden md:flex flex-1 relative">
        <div className="absolute inset-0 z-0">
          <Image
            alt="gradient"
            className="w-full h-full object-cover"
            src="https://nextui.org/gradients/docs-right.png"
          />
        </div>

        <div className="relative z-10 flex items-center justify-center w-full h-full pr-6">
          <div className="w-full max-w-2xl max-h-[95vh] aspect-[4/5] rounded-2xl bg-white/5 backdrop-blur-lg overflow-hidden">
              <div 
                className="flex h-full transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
              >
                {carouselData.map((slide, index) => (
                  <div key={index} className="w-full flex-shrink-0">
                    <div className="w-full h-full">
                      <Image
                        alt={`Slide ${index + 1}`}
                        className="w-full h-full object-contain rounded-2xl" 
                        src={slide.image}
                      />
                    </div>
                  </div>
                ))}
              </div>

              <button 
                onClick={prevSlide}
                className="absolute left-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                aria-label="Previous slide"
              >
                <ChevronLeft className="w-6 h-6 text-white" />
              </button>
              <button 
                onClick={nextSlide}
                className="absolute right-4 top-1/2 -translate-y-1/2 p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors"
                aria-label="Next slide"
              >
                <ChevronRight className="w-6 h-6 text-white" />
              </button>

              <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex justify-center gap-2">
                {carouselData.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => handleDotClick(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      currentSlide === index ? 'bg-white' : 'bg-white/30'
                    }`}
                    aria-label={`Go to slide ${index + 1}`}
                    aria-current={currentSlide === index ? 'true' : 'false'}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
    </div>
  );
};

export default AuthLayoutWrapper;