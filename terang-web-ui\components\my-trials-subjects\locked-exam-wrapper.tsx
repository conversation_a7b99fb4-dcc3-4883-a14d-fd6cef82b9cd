import React, { ReactNode } from 'react';
import { But<PERSON> } from "@heroui/react";
import { SquareLock02Icon } from 'hugeicons-react';

interface PremiumExamWrapperProps {
  children: ReactNode;
  index: number;
  subject: string;
  isPremium: boolean;
}

const PremiumExamWrapper: React.FC<PremiumExamWrapperProps> = ({
  children,
  index,
  subject,
  isPremium,
}) => {
  // Direct conditional rendering based on props - no state needed
  const shouldShowContent = isPremium || index < 5;

  // Show content if user has premium or it's within first 5 items
  if (shouldShowContent) {
    return <>{children}</>;
  }

  // Show locked state for premium content
  return (
    <div className="relative">
      {children}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm 
                   flex flex-col items-center justify-center p-4 rounded-lg"
      >
        <SquareLock02Icon className="w-8 h-8 text-default-400 mb-2" />
        <p className="text-sm text-center text-default-600 mb-3">
          Upgrade ke Premium untuk akses semua soal {subject}
        </p>
        <Button
          href="/subscription"
          as="a"
          color="primary"
          variant="flat"
          className="font-medium"
        >
          Upgrade Sekarang
        </Button>
      </div>
    </div>
  );
};

// Wrap with React.memo to prevent re-renders when props don't change
export default React.memo(PremiumExamWrapper);