import type { NextAuthConfig } from "next-auth";
// Remove both static imports - we'll use dynamic imports instead
// import { cookies } from 'next/headers';
// import { headers } from 'next/headers';

import { getUserSessionCookieAction, setUserSessionCookieAction, clearUserSessionCookie } from "./app/lib/actions/account/actions";

export interface APIUserResponse {
  data: {
    id: string;
    email: string;
    first_name?: string;
    last_name?: string;
    picture?: string;
    role?: string;
    password?: string;
    is_verified?: boolean;
  };
}

// Extend the default session type
declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      emailVerified?: Date;
      firstname?: string;
      lastname?: string;
      name?: string;
      image?: string;
      role?: string;
    }
  }
}

// Conditional logger for dev environment using dynamic import
const conditionalLogger = {
  log: async (...args: any[]) => {
    try {
      const { headers } = await import('next/headers');
      const headersList = await headers();
      const host = headersList.get('host');
      if (host === 'dev.terang.ai' || host === 'localhost') {
        // console.log(...args);
      }
    } catch (error) {
      // Silently fail if headers are not available
    }
  },
  error: async (...args: any[]) => {
    try {
      const { headers } = await import('next/headers');
      const headersList = await headers();
      const host = headersList.get('host');
      if (host === 'dev.terang.ai' || host === 'localhost') {
        console.error(...args);
      }
    } catch (error) {
      // Silently fail if headers are not available
    }
  }
};

// Sync wrapper that handles the async nature internally
const logger = {
  log: (...args: any[]) => {
    conditionalLogger.log(...args).catch(() => {});
  },
  error: (...args: any[]) => {
    conditionalLogger.error(...args).catch(() => {});
  }
};

// Robust check if logging out flag is set - used throughout the auth flow
async function isLoggingOut(): Promise<boolean> {
  try {
    const { cookies } = await import('next/headers');
    const cookieStore = await cookies();
    return cookieStore.get('logging_out')?.value === 'true';
  } catch (error) {
    logger.error("Error checking logout status:", error);
    return false;
  }
}

export const authConfig = {
  providers: [], // Providers will be added in auth.ts
  callbacks: {
    async redirect({ url, baseUrl }) {
      // Check if the URL has a refresh parameter (used for forcing fresh data)
      if (url.includes('refresh=')) {
        logger.log('Detected refresh parameter in URL - ensuring fresh data');
        // The presence of this parameter will force the session to be recalculated
        // No need to remove it as it doesn't affect functionality
      }
      // Return the URL to allow Next.js to handle the redirect
      return url;
    },
    async signIn({ user, account, profile, email, credentials }) {
      // Early return if logging out
      const loggingOut = await isLoggingOut();
      if (loggingOut) {
        logger.log("Sign in callback skipped - logging out");
        return false;
      }
      
      try {
        if (!user.email) return false;

        // First, clear any potentially invalid/stale session cache
        try {
          await clearUserSessionCookie(user.email);
          logger.log("Cleared potentially invalid session cache during sign-in");
        } catch (clearError) {
          logger.error("Failed to clear session cache during sign-in:", clearError);
        }

        const baseUrl = process.env.BACKEND_BASE_URL;
        const apiKey = process.env.BACKEND_API_KEY;

        if (!baseUrl || !apiKey) {
          logger.error("Missing environment variables");
          return false;
        }

        try {
          // Format timestamp in ISO format with Z
          const now = new Date();
          const formattedTimestamp = now.toISOString(); // This will give format like 2024-11-04T20:02:34.179Z
          logger.log("=== SIGN IN DEBUG ===");
          logger.log("User from auth provider:", { 
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image, 
            firstname: user.firstname,
            lastname: user.lastname
          });
          if (profile) {
            logger.log("Profile from provider:", {
              given_name: profile.given_name,
              family_name: profile.family_name,
              picture: profile.picture
            });
          }

          // Check if user exists
          const response = await fetch(`${baseUrl}/v1/users/emails/${user.email}`, {
            headers: {
              'x-api-key': apiKey,
              'Content-Type': 'application/json',
            },
            cache: 'no-store' // Ensure we're getting fresh data
          });

          if (response.ok) {
            // User exists - ONLY update last_login timestamp
            // This prevents Google OAuth data from overwriting user's customized profile
            const updateResponse = await fetch(`${baseUrl}/v1/users/emails/${user.email}`, {
              method: 'PUT',
              headers: {
                'x-api-key': apiKey,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                last_login: formattedTimestamp
              })
            });
            
            if (updateResponse.ok) {
              // Set account_exists cookie
              const { cookies } = await import('next/headers');
              const cookieStore = await cookies();
              cookieStore.set('account_exists', 'true');
              
              // Get fresh user data from the backend after updating last_login
              const freshUserResponse = await fetch(`${baseUrl}/v1/users/emails/${user.email}`, {
                headers: {
                  'x-api-key': apiKey,
                  'Content-Type': 'application/json',
                },
                cache: 'no-store' // Ensure we're getting fresh data
              });
              
              if (freshUserResponse.ok) {
                const userData: APIUserResponse = await freshUserResponse.json();
                const freshUser = userData.data;
                logger.log("Fresh user data from backend:", {
                  id: freshUser.id,
                  email: freshUser.email,
                  first_name: freshUser.first_name,
                  last_name: freshUser.last_name,
                  picture: freshUser.picture
                });
                
                // Clear any existing session cache first
                await clearUserSessionCookie(user.email);
                
                // Update the session cache with fresh data from the backend
                await setUserSessionCookieAction(user.email, freshUser);
                logger.log("Updated session cache with fresh user data after login");
              }
            }
          } else {
            // New user - create with Google profile data
            const newUserData = {
              id: user.id,
              email: user.email,
              first_name: user.firstname || profile?.given_name,
              last_name: user.lastname || profile?.family_name,
              picture: user.image,
              role: user.role || 'user',
              last_login: formattedTimestamp
            };
            
            // Create new user with Google data
            await fetch(`${baseUrl}/v1/users`, {
              method: 'POST',
              headers: {
                'x-api-key': apiKey,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(newUserData)
            });
          }
        } catch (error) {
          logger.error("Error managing user:", error);
          return false;
        }

        return true;
      } catch (error) {
        logger.error("Sign in error:", error);
        return false;
      }
    },
    async session({ session, user, token }) {
      // Early return if logging out
      const loggingOut = await isLoggingOut();
      if (loggingOut) {
        logger.log("Session callback skipped - logging out");
        return session;
      }
      
      // If the session doesn't have user data, return as is
      if (!session?.user) return session;

      logger.log("=== SESSION DEBUG ===");
      logger.log("Input session:", {
        id: session.user.id,
        email: session.user.email, 
        name: session.user.name,
        image: session.user.image,
        firstname: session.user.firstname,
        lastname: session.user.lastname
      });
      logger.log("Input token:", {
        name: token.name,
        picture: token.picture,
        image: token.image,
        given_name: token.given_name,
        family_name: token.family_name
      });

      session.user.id = token.id as string;
      session.user.emailVerified = token.email_verified as Date;
      session.user.firstname = token.given_name as string;
      session.user.lastname = token.family_name as string;
      session.user.role = token.role as string;
      
      try {
        // Check for cached user data first
        const cachedUserData = await getUserSessionCookieAction(session.user.email);
        
        if (cachedUserData) {
          // Use cached data
          logger.log("Using cached user data:", {
            first_name: cachedUserData.first_name,
            last_name: cachedUserData.last_name,
            picture: cachedUserData.picture
          });
          
          session.user.firstname = cachedUserData.first_name;
          session.user.lastname = cachedUserData.last_name;
          session.user.role = cachedUserData.role;
          session.user.image = cachedUserData.picture;
          // Set the name property by combining first and last name
          session.user.name = `${cachedUserData.first_name} ${cachedUserData.last_name}`;
          
          logger.log("Final session after using cache:", {
            id: session.user.id,
            email: session.user.email,
            name: session.user.name,
            image: session.user.image,
            firstname: session.user.firstname,
            lastname: session.user.lastname
          });
          
          return session;
        }
        
        // Double-check logging out status before making API call
        const stillLoggingOut = await isLoggingOut();
        if (stillLoggingOut) {
          logger.log("API call for session skipped - logging out");
          return session;
        }
        
        // If no cache, get fresh user data from API
        logger.log("No cache found, fetching fresh user data from API");
        const baseUrl = process.env.BACKEND_BASE_URL;
        const apiKey = process.env.BACKEND_API_KEY;
        
        if (baseUrl && apiKey && session.user.email) {
          // Add a small random delay to prevent multiple simultaneous requests
          const randomDelay = Math.floor(Math.random() * 200);
          await new Promise(resolve => setTimeout(resolve, randomDelay));
          
          const response = await fetch(`${baseUrl}/v1/users/emails/${session.user.email}`, {
            headers: {
              'x-api-key': apiKey,
              'Content-Type': 'application/json',
            },
            // Add cache control headers
            cache: 'no-cache'
          });
          
          if (response.ok) {
            const userData: APIUserResponse = await response.json();
            const user = userData.data;
            
            logger.log("API returned fresh user data:", {
              id: user.id,
              email: user.email,
              first_name: user.first_name,
              last_name: user.last_name,
              picture: user.picture
            });
            
            // Update session
            session.user.firstname = user.first_name;
            session.user.lastname = user.last_name;
            session.user.role = user.role;
            session.user.image = user.picture;
            // Set the name property by combining first and last name
            session.user.name = `${user.first_name} ${user.last_name}`;
            
            logger.log("Final session after API fetch:", {
              id: session.user.id,
              email: session.user.email,
              name: session.user.name,
              image: session.user.image,
              firstname: session.user.firstname,
              lastname: session.user.lastname
            });
            
            // Cache the user data (clear old cache first)
            await clearUserSessionCookie(session.user.email);
            await setUserSessionCookieAction(session.user.email, user);
            logger.log("Refreshed and updated session cache with fresh user data");
          } else {
            logger.error("API returned error:", response.status, response.statusText);
          }
        }
      } catch (error) {
        logger.error("Error refreshing session data:", error);
      }

      return session;
    },
    async jwt({ token, user, trigger, session, account, profile }) {
      // Early return if logging out
      const loggingOut = await isLoggingOut();
      if (loggingOut) {
        logger.log("JWT callback skipped - logging out");
        return token;
      }
      
      // Update token when session is updated
      if (trigger === "update") {
        if (session?.firstname) {
          token.given_name = session.firstname;
          logger.log("JWT updated with firstname:", session.firstname);
          // Update the name field if we have both first and last name
          if (token.family_name) {
            token.name = `${session.firstname} ${token.family_name}`;
            logger.log("JWT name updated to:", token.name);
          }
        }
        if (session?.lastname) {
          token.family_name = session.lastname;
          logger.log("JWT updated with lastname:", session.lastname);
          // Update the name field if we have both first and last name
          if (token.given_name) {
            token.name = `${token.given_name} ${session.lastname}`;
            logger.log("JWT name updated to:", token.name);
          }
        }
        if (session?.name) {
          token.name = session.name;
          logger.log("JWT name set directly:", session.name);
        }
        if (session?.user?.image) {
          token.picture = session.user.image;
          token.image = session.user.image;
          logger.log("JWT updated with image:", session.user.image);
        }
      }
      
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.image = user.image;
        token.picture = user.image; // Ensure picture is also set in token
        token.name = user.name;
        token.role = (user as any).role;
      }
      if (profile) {
        token.email_verified = profile.email_verified;
        token.given_name = profile.given_name;
        token.family_name = profile.family_name;
        token.locale = profile.locale;
        token.role = (profile as any).role || 'user';
      }

      return token;
    },
  },
  pages: {
    signIn: "/login",
  },
  session: { strategy: "jwt" },
} satisfies NextAuthConfig;

// Export isLoggingOut so it can be used in auth.ts
export { isLoggingOut };
export default authConfig;