"use client";

import React, { useState } from 'react';
import { Card, CardBody, CardHeader } from "@heroui/card";
import { <PERSON><PERSON> } from "@heroui/button";
import { Input, Textarea } from "@heroui/input";
import { <PERSON><PERSON>, <PERSON>dalContent, ModalHeader, ModalBody } from "@heroui/modal";
import { Divider } from "@heroui/divider";
import { CheckCircle, XCircle, School, User, BookOpen, Clock } from 'lucide-react';

// Mock invitation data - in real app, this would come from API
const mockInvitation = {
  institutionName: "ABC University",
  role: "Teacher",
  expiresAt: "2024-12-31",
  department: "Computer Science"
};

interface ProfileData {
  fullName: string;
  specialization: string;
  bio: string;
  experience: string;
}

const TeacherProfileForm = ({ onSubmit }: { onSubmit: (data: ProfileData) => void }) => {
  const [profile, setProfile] = useState<ProfileData>({
    fullName: '',
    specialization: '',
    bio: '',
    experience: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(profile);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Input
        label="Full Name"
        value={profile.fullName}
        onChange={(e) => setProfile(prev => ({...prev, fullName: e.target.value}))}
        required
        variant="bordered"
        className="max-w-full"
      />

      <Input
        label="Specialization"
        value={profile.specialization}
        onChange={(e) => setProfile(prev => ({...prev, specialization: e.target.value}))}
        required
        variant="bordered"
        className="max-w-full"
      />

      <Textarea
        label="Brief Bio"
        value={profile.bio}
        onChange={(e) => setProfile(prev => ({...prev, bio: e.target.value}))}
        required
        variant="bordered"
        className="max-w-full"
      />

      <Textarea
        label="Teaching Experience"
        value={profile.experience}
        onChange={(e) => setProfile(prev => ({...prev, experience: e.target.value}))}
        required
        variant="bordered"
        className="max-w-full"
      />

      <Button 
        type="submit" 
        color="primary" 
        className="w-full"
      >
        Complete Profile
      </Button>
    </form>
  );
};

const InstitutionInvitationPage = () => {
  const [status, setStatus] = useState<'pending' | 'accepted' | 'rejected' | 'completed'>('pending');
  const [showProfile, setShowProfile] = useState(false);

  const handleAccept = () => {
    setStatus('accepted');
    setShowProfile(true);
  };

  const handleReject = () => {
    setStatus('rejected');
  };

  const handleProfileSubmit = (profileData: ProfileData) => {
    console.log('Profile submitted:', profileData);
    setStatus('completed');
    setShowProfile(false);
  };

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-3xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-2">
          <School className="w-8 h-8 text-blue-600" />
          <h1 className="text-2xl font-bold">Institution Invitation</h1>
        </div>

        {/* Invitation Card */}
        <Card>
          <CardHeader className="flex gap-3">
            <div className="flex flex-col">
              <p className="text-xl font-bold">You have been Invited!</p>
            </div>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <p className="text-sm text-gray-500">Institution</p>
                <div className="flex items-center space-x-2">
                  <School className="w-4 h-4" />
                  <p className="font-medium">{mockInvitation.institutionName}</p>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-gray-500">Role</p>
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4" />
                  <p className="font-medium">{mockInvitation.role}</p>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-gray-500">Department</p>
                <div className="flex items-center space-x-2">
                  <BookOpen className="w-4 h-4" />
                  <p className="font-medium">{mockInvitation.department}</p>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-sm text-gray-500">Expires</p>
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4" />
                  <p className="font-medium">{mockInvitation.expiresAt}</p>
                </div>
              </div>
            </div>

            <Divider className="my-4" />

            {status === 'pending' && (
              <div className="flex gap-4">
                <Button 
                  onPress={handleAccept} 
                  color="primary"
                  className="flex-1"
                  startContent={<CheckCircle size={20} />}
                >
                  Accept Invitation
                </Button>
                <Button 
                  onPress={handleReject} 
                  color="danger"
                  className="flex-1"
                  startContent={<XCircle size={20} />}
                >
                  Decline
                </Button>
              </div>
            )}
          </CardBody>
        </Card>

        {/* Status Messages */}
        {status === 'rejected' && (
          <Card>
            <CardBody>
              <div className="flex items-center gap-2 text-danger">
                <XCircle />
                <div>
                  <p className="text-lg font-semibold">Invitation Declined</p>
                  <p className="text-sm">
                    You have declined the invitation to join {mockInvitation.institutionName}.
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {status === 'completed' && (
          <Card>
            <CardBody>
              <div className="flex items-center gap-2 text-success">
                <CheckCircle />
                <div>
                  <p className="text-lg font-semibold">Welcome aboard!</p>
                  <p className="text-sm">
                    Your profile has been completed and you are now part of {mockInvitation.institutionName}.
                  </p>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {/* Teacher Profile Modal */}
        <Modal 
          isOpen={showProfile} 
          onClose={() => setShowProfile(false)}
          size="2xl"
          scrollBehavior="inside"
        >
          <ModalContent>
            <ModalHeader>Complete Your Teacher Profile</ModalHeader>
            <ModalBody className="pb-6">
              <TeacherProfileForm onSubmit={handleProfileSubmit} />
            </ModalBody>
          </ModalContent>
        </Modal>
      </div>
    </div>
  );
};

export default InstitutionInvitationPage;