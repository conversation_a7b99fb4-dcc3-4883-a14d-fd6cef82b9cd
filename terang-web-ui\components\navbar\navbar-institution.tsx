import React, { useMemo, useEffect, useState } from 'react';
import { usePathname } from "next/navigation";
import Link from "next/link";
import { 
  UserAccountIcon, 
  ChartBarLineIcon, 
  Settings01Icon,
  Book01Icon,
  TicketStarIcon,
  CreditCardIcon,
  GraduateMaleIcon,
  DashboardSquare02Icon,
  ArrowLeft02Icon,
  ArrowRight02Icon,
} from "hugeicons-react";

interface NavItem {
  href: string;
  icon: React.ReactNode;
  title: string;
}

interface NavLinkProps extends NavItem {
  isActive: boolean;
}

// Custom scrollbar and fade styles
const style = document.createElement('style');
style.textContent = `
  .nav-scroll::-webkit-scrollbar {
    height: 6px;
  }
  
  .nav-scroll::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .nav-scroll::-webkit-scrollbar-thumb {
    background: #E5E7EB;
    border-radius: 3px;
  }
  
  .nav-scroll::-webkit-scrollbar-thumb:hover {
    background: #D1D5DB;
  }
  
  .nav-scroll {
    scrollbar-width: thin;
    scrollbar-color: #E5E7EB transparent;
    -ms-overflow-style: none;
  }

  .fade-left {
    background: linear-gradient(to right, white, transparent);
  }
  
  .fade-right {
    background: linear-gradient(to left, white, transparent);
  }

  .scroll-hint {
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% { opacity: 0.4; }
    50% { opacity: 0.8; }
    100% { opacity: 0.4; }
  }
`;
document.head.appendChild(style);

const NavLink: React.FC<NavLinkProps> = ({ href, icon, title, isActive }) => (
  <Link
    href={href}
    className={`flex items-center gap-2 px-3 py-2 text-sm transition-colors whitespace-nowrap rounded-md shrink-0 ${
      isActive 
        ? "text-blue-600 font-medium bg-blue-50" 
        : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
    }`}
  >
    <span className={`flex items-center justify-center w-4 h-4 ${isActive ? "text-blue-600" : "text-gray-500"}`}>
      {icon}
    </span>
    <span className="flex items-center">{title}</span>
  </Link>
);

export const InstitutionNavbar: React.FC = () => {
  const rawPathname = usePathname();
  const pathname = rawPathname ?? '/';
  const [showLeftScroll, setShowLeftScroll] = useState(false);
  const [showRightScroll, setShowRightScroll] = useState(false);
  const [scrollPosition, setScrollPosition] = useState(0);

  const scrollContainerRef = React.useRef<HTMLDivElement>(null);

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setScrollPosition(scrollLeft);
      setShowLeftScroll(scrollLeft > 0);
      setShowRightScroll(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      handleScroll(); // Check initial state
      scrollContainer.addEventListener('scroll', handleScroll);
      
      // Check if content exceeds container width
      const checkOverflow = () => {
        setShowRightScroll(scrollContainer.scrollWidth > scrollContainer.clientWidth);
      };
      
      checkOverflow();
      window.addEventListener('resize', checkOverflow);

      return () => {
        scrollContainer.removeEventListener('scroll', handleScroll);
        window.removeEventListener('resize', checkOverflow);
      };
    }
  }, []);

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const scrollAmount = 200;
      const newScrollPosition = direction === 'left' 
        ? scrollPosition - scrollAmount 
        : scrollPosition + scrollAmount;
      
      scrollContainerRef.current.scrollTo({
        left: newScrollPosition,
        behavior: 'smooth'
      });
    }
  };

  const institutionId = useMemo(() => {
    const match = pathname.match(/\/2-institution-create\/([^/]+)/);
    return match ? match[1] : '';
  }, [pathname]);

  const hasInstitutionContext = institutionId && institutionId !== 'dashboard';
  const basePath = hasInstitutionContext ? `/2-institution-create/${institutionId}` : '/2-institution-create';

  const isPathActive = (path: string): boolean => {
    if (path.includes('[id]') && institutionId) {
      const normalizedPath = path.replace('[id]', institutionId);
      return pathname === normalizedPath;
    }
    return pathname === path;
  };

  const navigationItems: NavItem[] = useMemo(() => [
    {
      href: `${basePath}/3-user-management`,
      icon: <UserAccountIcon />,
      title: "Users"
    },
    {
      href: `${basePath}/classes`,
      icon: <Book01Icon />,
      title: "Classes"
    },
    {
      href: `${basePath}/exams`,
      icon: <GraduateMaleIcon />,
      title: "Exams"
    },
    {
      href: `${basePath}/5-analytics`,
      icon: <ChartBarLineIcon />,
      title: "Analytics"
    },
    {
      href: `${basePath}/4-support`,
      icon: <TicketStarIcon />,
      title: "Support"
    },
    {
      href: `${basePath}/6-subscription`,
      icon: <CreditCardIcon />,
      title: "Subscription"
    }
  ], [basePath]);

  return (
    <nav className="sticky top-0 w-full bg-white border-b border-gray-200 z-10">
      <div className="max-w-full mx-auto">
        <div className="flex h-14">
          {/* Left side - Fixed */}
          <div className="flex items-center gap-4 px-4 border-r border-gray-200 shrink-0 bg-white">
            <Link href="/institution" className="flex items-center whitespace-nowrap">
              <span className="text-gray-900 font-medium">Institution Dashboard</span>
            </Link>

            <NavLink
              href={`${basePath}/dashboard`}
              icon={<DashboardSquare02Icon />}
              title="Dashboard"
              isActive={isPathActive(`${basePath}/dashboard`)}
            />
          </div>

          {/* Scrollable Navigation Items with Scroll Indicators */}
          <div className="relative flex-1 overflow-hidden">
            {/* Left fade + button */}
            {showLeftScroll && (
              <div className="absolute left-0 top-0 bottom-0 z-10 flex items-center">
                <div className="absolute inset-y-0 left-0 w-8 fade-left pointer-events-none" />
                <button 
                  onClick={() => scroll('left')}
                  className="h-8 w-8 flex items-center justify-center bg-white rounded-full shadow-lg ml-2 hover:bg-gray-50 transition-colors"
                  aria-label="Scroll left"
                >
                  <ArrowLeft02Icon className="w-4 h-4 text-gray-600" />
                </button>
              </div>
            )}

            {/* Right fade + button */}
            {showRightScroll && (
              <div className="absolute right-0 top-0 bottom-0 z-10 flex items-center">
                <div className="absolute inset-y-0 right-0 w-8 fade-right pointer-events-none" />
                <button 
                  onClick={() => scroll('right')}
                  className="h-8 w-8 flex items-center justify-center bg-white rounded-full shadow-lg mr-2 hover:bg-gray-50 transition-colors scroll-hint"
                  aria-label="Scroll right"
                >
                  <ArrowRight02Icon className="w-4 h-4 text-gray-600" />
                </button>
              </div>
            )}

            {/* Scroll container */}
            <div 
              ref={scrollContainerRef}
              className="absolute inset-0 overflow-x-auto nav-scroll"
            >
              <div className="flex items-center gap-2 px-4 h-full min-w-max">
                {navigationItems.map((item) => (
                  <NavLink
                    key={item.href}
                    {...item}
                    isActive={isPathActive(item.href)}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default InstitutionNavbar;