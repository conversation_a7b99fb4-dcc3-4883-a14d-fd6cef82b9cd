import React, { Suspense } from "react";
import LaporanPersonal from "./component";


const PersonalisedReports = async () => {

  return (
    <div>
      {/* Use Suspense to handle asynchronous rendering */}
      <Suspense
        fallback={
          <div className="flex items-center justify-center min-h-screen">
            Loading your exams...
          </div>
        }
      >
        {/* Render the asynchronous component */}
        <LaporanPersonal />
      </Suspense>
    </div>
  );
};

export default PersonalisedReports;
