// Login.tsx
"use client";

import { Formik } from "formik";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useCallback, useState } from "react";
import { signIn } from "next-auth/react";
import { Button, Input } from "@heroui/react";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import * as Yup from "yup";

import AuthSocialButtons from "./AuthSocialButtons";

interface LoginFormType {
  username: string;
  password: string;
}

const LoginSchema = Yup.object().shape({
  username: Yup.string()
    .required("Email or username is required"),
  password: Yup.string()
    .required("Password is required")
});

export const Login = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const showNotification = (
    message: string,
    type: "success" | "info" | "error" | "warning"
  ) => {
    toast[type](message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };

  const initialValues: LoginFormType = {
    username: "",
    password: "",
  };

  const handleLogin = useCallback(
    async (values: LoginFormType) => {
      try {
        setIsLoading(true);
        
        const result = await signIn("credentials", {
          username: values.username,
          password: values.password,
          redirect: false,
        });

        if (result?.error) {
          showNotification("Invalid credentials. Please try again.", "error");
          return;
        }

        router.replace("/dashboard");
      } catch (error) {
        console.error("Login error:", error);
        showNotification("An unexpected error occurred. Please try again later.", "error");
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  return (
    <div className="flex flex-col items-center w-full max-w-md mx-auto">
      <div className="text-center text-[25px] font-bold mb-6">Login</div>

      <div className="w-full mb-6">
        <AuthSocialButtons 
          title="Login with"
        />
      </div>

      <div className="flex items-center gap-3 w-full mb-6">
        <div className="h-[1px] flex-grow bg-gray-300"></div>
        <span className="text-sm text-gray-500 bg-white px-2">
          Or continue with email/username
        </span>
        <div className="h-[1px] flex-grow bg-gray-300"></div>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={LoginSchema}
        onSubmit={handleLogin}
      >
        {({ values, errors, touched, handleChange, handleSubmit }) => (
          <form onSubmit={handleSubmit} className="w-full space-y-6">
            <div className="flex flex-col w-full gap-4">
              <div className="space-y-4">
                <Input
                  name="username"
                  type="text"
                  label="Email or Username"
                  value={values.username}
                  onChange={handleChange}
                  isDisabled={isLoading}
                  errorMessage={touched.username && errors.username}
                  isInvalid={touched.username && !!errors.username}
                  variant="bordered"
                />

                <Input
                  name="password"
                  type="password"
                  label="Password"
                  value={values.password}
                  onChange={handleChange}
                  isDisabled={isLoading}
                  errorMessage={touched.password && errors.password}
                  isInvalid={touched.password && !!errors.password}
                  variant="bordered"
                />
              </div>

              <Button
                type="submit"
                color="primary"
                isLoading={isLoading}
                variant="flat"
                fullWidth
              >
                Sign in
              </Button>
            </div>
          </form>
        )}
      </Formik>

      <div className="font-light text-slate-400 mt-4 text-sm">
        Don&apos;t have an account?{" "}
        <Link 
          href="/register"
          className="inline-block font-bold text-primary hover:underline px-2 py-1 -mx-2 touch-manipulation"
        >
          Register here
        </Link>
      </div>

      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </div>
  );
};

export default Login;