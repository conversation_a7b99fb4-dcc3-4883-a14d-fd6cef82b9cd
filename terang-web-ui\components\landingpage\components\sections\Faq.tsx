import React from 'react';
import { Accordion, AccordionItem } from "@heroui/react";
import "@fontsource/sora"; // Importing the Sora font
import styled from "styled-components";
import { useLanguage } from '@/app/language-wrapper'; // Adjust import path according to your project structure

interface FAQItem {
  key: string;
  question: string;
  answer: string;
}

interface ThemeProps {
  body: string;
  text: string;
  fontxxl: string;
  fontxl: string;
  fontlg: string;
  fontmd: string;
  carouselColor: string;
  textRgba: string;
}

// Get FAQ data with translations
const getFaqData = (t: (key: string) => string): FAQItem[] => [
  {
    key: 'progress',
    question: t('faq_progress_question'),
    answer: t('faq_progress_answer')
  },
  {
    key: 'what_is',
    question: t('faq_what_is_question'),
    answer: t('faq_what_is_answer')
  },
  {
    key: 'why_simulation',
    question: t('faq_simulation_question'),
    answer: t('faq_simulation_answer')
  },
  {
    key: 'how_simulation',
    question: t('faq_how_simulation_question'),
    answer: t('faq_how_simulation_answer')
  },
  {
    key: 'materials',
    question: t('faq_materials_question'),
    answer: t('faq_materials_answer')
  },
  {
    key: 'ai_help',
    question: t('faq_ai_help_question'),
    answer: t('faq_ai_help_answer')
  }
];

const Title = styled.h1<{ theme: ThemeProps }>`
  font-size: ${(props) => props.theme.fontxxl};
  text-transform: capitalize;
  color: ${(props) => props.theme.body};
  font-family: "Sora", sans-serif;
  font-weight: 600;
  line-height: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1rem auto;
  border-bottom: 2px solid ${(props) => props.theme.body};
  width: fit-content;

  @media (max-width: 40em) {
    font-size: ${(props) => props.theme.fontxl};
  }
`;

const Faq: React.FC = () => {
  const { t } = useLanguage();
  const faqData = React.useMemo(() => getFaqData(t), [t]);

  return (
    <section className="bg-[#202020] py-12 px-4 sm:px-6 lg:px-8" id="faq">
      <div className="max-w-3xl mx-auto">
        <Title>FAQ</Title>
        <Accordion 
          variant="bordered"
          className="gap-2"
          selectionMode="multiple"
        >
          {faqData.map((item) => (
            <AccordionItem 
              key={item.key} 
              aria-label={item.question} 
              title={item.question}
              className="bg-white rounded-lg px-6 mb-1"
            >
              <p className="text-gray-700">{item.answer}</p>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
};

export default Faq;