"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import {
  storeToRedis,
  retrieveFromRedis,
  fetchExamSessionBySessionId,
  checkFeedbackRecords,
} from "../shared/actions";
import { Results } from "./results";
import { QuestionBank } from "./question-bank";
import ScoreDisplay from "./score-display";
import "react-toastify/dist/ReactToastify.css";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "@heroui/react";
import AIExplanationComponent from "./ai-explanation-component";
import { ExamSession, QuestionData } from "../types";
import ChatAI from "@/app/(terang-ai)/chat-ai/result";

const ZapIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    color={"#ffffff"}
    fill={"none"}
    height={24}
    viewBox="0 0 24 24"
    width={24}
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M16.0174 2.32514C16.5423 2.40348 17.1136 2.59299 17.4412 3.16038C17.7679 3.72637 17.6485 4.31617 17.4564 4.81136C17.2716 5.2879 16.9439 5.86433 16.5622 6.53565L15.4612 8.47252C15.253 8.8388 15.1189 9.07576 15.0336 9.25737C14.9695 9.39366 14.9577 9.4495 14.9558 9.45874C14.9591 9.56981 15.0177 9.67007 15.109 9.7266C15.1189 9.72978 15.173 9.74649 15.3181 9.75782C15.5168 9.77336 15.8183 9.7739 16.2381 9.7739C16.7269 9.77389 17.137 9.77388 17.4569 9.79838C17.7661 9.82206 18.1304 9.87463 18.4357 10.0776C19.0285 10.4716 19.3368 11.1749 19.2287 11.8773C19.173 12.2386 18.968 12.5434 18.7774 12.7893C18.5804 13.0435 18.3047 13.3484 17.9758 13.7121L12.3841 19.8952C11.8712 20.4625 11.4394 20.94 11.0881 21.2463C10.9095 21.402 10.6984 21.5621 10.463 21.6576C10.2036 21.7629 9.86139 21.8076 9.52149 21.6305C9.18235 21.4538 9.02299 21.1486 8.9596 20.8774C8.90187 20.6304 8.91024 20.3659 8.93387 20.1296C8.98034 19.6648 9.11958 19.035 9.28511 18.2864L9.98337 15.1277C10.1219 14.5012 10.2064 14.1103 10.228 13.8242C10.2577 13.5884 10.052 13.4872 9.94542 13.4662C9.6639 13.4254 9.267 13.4236 8.62816 13.4236L8.1121 13.4236C7.41921 13.4236 6.81605 13.4237 6.34997 13.3543C5.85689 13.2808 5.32903 13.1044 4.99753 12.5916C4.66682 12.0801 4.72084 11.5262 4.85378 11.0454C4.97975 10.5899 5.22449 10.0364 5.50617 9.39944L7.35643 5.21448L7.35643 5.21448C7.617 4.62508 7.83611 4.12946 8.05957 3.74142C8.2964 3.33017 8.57004 2.98046 8.9698 2.71931C9.36982 2.45798 9.79964 2.34835 10.2707 2.2977C10.7147 2.24997 11.2547 2.24998 11.896 2.25L14.0837 2.25C14.8523 2.24995 15.5134 2.24991 16.0174 2.32514Z"
      fill="currentColor"
    />
  </svg>
);

interface Props {
  totalQuestions: number;
  sessionId: string;
  subject?: string; // Add this line
  getResultByIndex: (sessionId: string, index: number) => Promise<any>;
  sanitizedData: any[];
  examScores: {
    id: string;
    session_id: string;
    total_questions: number;
    correct_answers: number;
    score: number;
    accuracy: number;
    metadata_scores: string;
    created_at: string;
    modified_at: string;
  };
}

interface TimeInfo {
  examDuration: string;
  elapsedDuration: string;
  remainingDuration: string;
}

const checkFeedbackEmailRecords = async (): Promise<boolean> => {
  try {
    const count = await checkFeedbackRecords();
    return count > 0;
  } catch (error) {
    console.error('Error checking feedback email records:', error);
    throw error;
  }
};

const parseMetadataScores = (metadata: string) => {
  try {
    return JSON.parse(metadata);
  } catch (error) {
    console.error('Error parsing metadata scores:', error);
    return null;
  }
};

export const ExamResult: React.FC<Props> = ({
  totalQuestions,
  sessionId,
  getResultByIndex,
  sanitizedData,
  examScores,
  subject,
}) => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState<number>(0);
  const [flaggedQuestions, setFlaggedQuestions] = useState<{ [key: string]: boolean }>({});
  const [selectedOptions, setSelectedOptions] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(true);
  const [currentResult, setCurrentResult] = useState<any>({});
  const [sessionStatus, setSessionStatus] = useState<string | null>(null);
  const [allSessionData, setAllSessionData] = useState<ExamSession | null>(null);
  const [timeInfo, setTimeInfo] = useState<TimeInfo | null>(null);
  const [score, setScore] = useState<number | null>(null);
  const [questionIds, setQuestionIds] = useState<string[]>([]);
  const [resultLoading, setResultLoading] = useState(false);
  const [showAIExplanation, setShowAIExplanation] = useState(false);

  const showNotification = (message: string, type: "success" | "error" | "info") => {
    toast[type](message, {
      position: "top-right",
      autoClose: 5000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
    });
  };
  useEffect(() => {
    const fetchDataAndCheckStatus = async () => {
      try {
        // Set loading state to prevent UI rendering
        setLoading(true);
        
        const sessionData = await fetchExamSessionBySessionId(sessionId);
        setSessionStatus(sessionData.status);
        setAllSessionData(sessionData);
  
        if (sessionData.status !== "COMPLETED") {
          showNotification("This exam session is not completed yet.", "info");
          router.push("/my-exams");
          // Return early to prevent further code execution
          return;
        }
  
        // Skip feedback check for practice sessions
        if (sessionData.type === "EXAM") {
          try {
            console.log("Checking feedback records...");
            const count = await checkFeedbackRecords();
            console.log("Feedback record count:", count);
            
            const hasFeedback = count > 0;
            
            if (!hasFeedback) {
              console.log("No feedback found, redirecting to feedback page");
              showNotification("Please submit feedback before viewing your exam results.", "info");
              
              // Force the redirect synchronously and prevent further code execution
              window.location.href = "/feedback";
              // The function won't reach this point if redirect succeeds
              return;
            }
            console.log("Feedback found, proceeding to show results");
          } catch (error) {
            console.error("Error checking feedback:", error);
            // On error, still allow viewing results
          }
        }
  
        // Only reached if no redirection needed or session is practice type
        if (sanitizedData && sanitizedData.length > 0) {
          setQuestionIds(sanitizedData.map((question) => question.id));
          setCurrentResult(sanitizedData[0]);
        }
  
        const newTimeInfo: TimeInfo = {
          examDuration: sessionData.exam_duration,
          elapsedDuration: sessionData.elapsed_duration,
          remainingDuration: "00:00:00",
        };
        setTimeInfo(newTimeInfo);
  
        setFlaggedQuestions(JSON.parse(sessionData.flagged_questions));
        const parsedSelectedOptions = JSON.parse(sessionData.answers);
        setSelectedOptions(parsedSelectedOptions);
        
        if (examScores) {
          const totalCorrect = examScores.correct_answers;
          setScore(totalCorrect);
        }
  
      } catch (error) {
        console.error("Error fetching data:", error);
        showNotification("Error loading exam results. Please try again.", "error");
      } finally {
        // Only set loading to false if we're not redirecting
        setLoading(false);
      }
    };
  
    // Only fetch data if we have a sessionId and don't already have session data
    if (sessionId && !allSessionData) {
      console.log("Fetching exam session data");
      fetchDataAndCheckStatus();
    }
    
  }, [sessionId, router]);

  useEffect(() => {
    // Only update the current result if we have sanitized data available
    if (sanitizedData && sanitizedData.length > 0 && currentPage >= 0) {
      // No need for resultLoading if we already have the data
      if (sanitizedData[currentPage]) {
        setCurrentResult(sanitizedData[currentPage]);
      } else {
        // Only fetch and show loading if we don't have the data
        const fetchResult = async () => {
          setResultLoading(true);
          try {
            const result = await getResultByIndex(sessionId, currentPage);
            setCurrentResult(result);
          } catch (error) {
            console.error("Error fetching result:", error);
            showNotification("Error loading result. Please try again.", "error");
          } finally {
            setResultLoading(false);
          }
        };
        console.log("fetching result data")
        fetchResult();
      }
    }
  }, [currentPage, sessionId, sanitizedData]);

  useEffect(() => {
    if (showAIExplanation) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [showAIExplanation]);

  const handleFlagQuestion = (questionId: string) => {
    setFlaggedQuestions((prev) => {
      const newState = { ...prev, [questionId]: !prev[questionId] };
      storeToRedis(`${sessionId}_flaggedQuestions`, JSON.stringify(newState));
      return newState;
    });
  };

  const handleOptionSelect = (questionId: string, optionId: string) => {
    setSelectedOptions((prev) => {
      const newState = { ...prev, [questionId]: optionId };
      storeToRedis(`${sessionId}_selectedOptions`, JSON.stringify(newState));
      return newState;
    });
  };

  if (loading) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-100">
        <Card className="w-80">
          <CardBody className="flex flex-col items-center justify-center p-8">
            <Spinner color="primary" size="lg" />
            <p className="mt-4 text-lg font-semibold">Loading exam results...</p>
            <p className="mt-2 text-sm text-gray-500">Please wait</p>
          </CardBody>
        </Card>
        <ToastContainer style={{ zIndex: 9999 }} />
      </div>
    );
  }

  return (
    <div className="w-full h-screen flex overflow-hidden relative">
      <div 
        className={`transition-all duration-300 ease-in-out ${
          showAIExplanation ? 'w-full lg:w-2/3 pr-4' : 'w-full'
        } overflow-y-auto`}
      >
        <div className="my-10 px-3 lg:px-3 mx-auto w-full flex flex-col gap-4">
          <h1 className="text-3xl font-bold mb-8 text-center">
            {allSessionData?.type === "PRACTICE" ? "Practice Results" : "Exam Results"}
          </h1>

          <ScoreDisplay 
            score={score} 
            totalQuestions={totalQuestions} 
            examScores={examScores} 
            sessionId={sessionId} 
            subject={subject} 
            sessionData={allSessionData} 
          />

          {timeInfo && (
            <div className="bg-blue-100 border-l-4 border-blue-500 text-blue-700 p-4 mb-4" role="alert">
              <p className="font-bold">Exam Duration</p>
              <p>Total Duration: {timeInfo.examDuration}</p>
              <p>Time Taken: {timeInfo.elapsedDuration}</p>
            </div>
          )}

          <div className={`w-full flex flex-col lg:flex-row ${showAIExplanation ? 'flex-col' : ''} gap-4 pb-20`}>
            <div className={`w-full ${showAIExplanation ? 'w-full' : 'lg:w-2/3'} transition-all duration-300`}>
              {currentResult && (
                <Results
                  currentPage={currentPage}
                  flaggedQuestions={flaggedQuestions}
                  questionData={currentResult}
                  resultLoading={resultLoading}
                  selectedOptions={selectedOptions}
                  sessionData={allSessionData}
                  setCurrentPage={setCurrentPage}
                  setFlaggedQuestions={handleFlagQuestion}
                  setSelectedOptions={handleOptionSelect}
                  totalQuestions={totalQuestions}
                  questionIds={questionIds}
                />
              )}
            </div>
            
            <div className={`w-full ${showAIExplanation ? 'w-full' : 'lg:w-1/3'} transition-all duration-300`}>
              <QuestionBank
                currentPage={currentPage}
                flaggedQuestions={flaggedQuestions}
                isLoading={false}
                isTimeUp={false}
                questionData={sanitizedData}
                questionIds={questionIds}
                score={score}
                selectedOptions={selectedOptions}
                setCurrentPage={setCurrentPage}
                totalQuestions={totalQuestions}
                sessionData={allSessionData}
                onExitExam={() => router.push("/my-exams")}
              />
            </div>
          </div>
        </div>
      </div>

      {/* AI Button */}
      <Button
        color="primary"
        className={`fixed bottom-8 right-8 z-50 bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold shadow-lg rounded-full px-6 py-3 flex items-center gap-3 text-lg transition-opacity duration-300 ${
          showAIExplanation ? 'opacity-0 pointer-events-none' : 'opacity-100'
        }`}
        onClick={() => setShowAIExplanation(true)}
      >
        <span>Ask AI</span>
        <ZapIcon className="w-6 h-6" />
      </Button>

      {/* AI Explanation Panel */}
      <div 
        className={`fixed top-0 bottom-0 right-0 w-full lg:w-1/3 bg-white z-50 transition-all duration-300 ease-in-out transform 
          ${showAIExplanation ? 'translate-x-0' : 'translate-x-full'}
          flex flex-col overflow-hidden shadow-lg border-l border-gray-200`}
      >
        {/* Header with close button - now with higher z-index */}
        <div className="p-4 flex justify-between items-center border-b bg-white relative z-[60]">
          <h2 className="text-xl font-bold">Tanya Pembahasan AI</h2>
          <button
            onClick={() => setShowAIExplanation(false)}
            className="text-gray-500 hover:text-gray-700 p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* ChatAI container with lower z-index */}
        <div className="flex-grow overflow-y-auto relative z-[55]">
          <ChatAI 
            systemContext={JSON.stringify(currentResult)} 
            selectedOptions={JSON.stringify(selectedOptions)} 
          />
        </div>
      </div>

      {/* Mobile overlay */}
      {showAIExplanation && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setShowAIExplanation(false)}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              setShowAIExplanation(false);
            }
          }}
          role="button"
          tabIndex={0}
          aria-label="Close AI explanation"
        />
      )}

      <ToastContainer style={{ zIndex: 9999 }} />
    </div>
  );
};