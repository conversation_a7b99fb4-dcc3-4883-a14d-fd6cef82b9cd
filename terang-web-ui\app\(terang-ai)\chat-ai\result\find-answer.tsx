// answerFinder.ts

interface AnswerResult {
    content: string | null;
    index: number;
    label: string;
  }
  
  /**
   * Finds the selected answer text, index, and alphabetic label for a given question
   * @param question Object containing question data with options
   * @param userAnswers Object containing user answers mapped by question ID
   * @returns Object containing the answer content, index, and alphabetic label
   */
  export const findUserAnswer = (
    question: {
      id: string;
      options: {
        values: Array<{
          id: string;
          data: Array<{
            contents: Array<{
              type: string;
              content: string;
            }>;
          }>;
          is_correct: boolean;
        }>;
      };
    },
    userAnswers: {
      [questionId: string]: string;
    }
  ): AnswerResult => {
    // Get the user's selected option ID for this question
    const selectedOptionId = userAnswers[question.id];
    
    if (!selectedOptionId) {
      return {
        content: null,
        index: -1,
        label: ''
      };
    }
  
    // Find the matching option and its index in the question's options
    const selectedIndex = question.options.values.findIndex(
      option => option.id === selectedOptionId
    );
  
    if (selectedIndex === -1) {
      return {
        content: null,
        index: -1,
        label: ''
      };
    }
  
    // Get the selected option
    const selectedOption = question.options.values[selectedIndex];
  
    // Convert index to alphabetic label (0 = A, 1 = B, etc.)
    const label = String.fromCharCode(65 + selectedIndex); // 65 is ASCII for 'A'
  
    return {
      content: selectedOption?.data[0]?.contents[0]?.content || null,
      index: selectedIndex,
      label
    };
  };
  
  // Usage example:
  /*
  const questionData = {
    id: "193573",
    options: {
      values: [
        {
          id: "1935730",
          data: [{
            contents: [{
              type: "text",
              content: "Semboyan Hakko ichiu, yakni dunia dalam satu keluarga dan Jepang adalah pemimpin keluarga tersebut yang berusaha menciptakan kemakmuran bersama"
            }]
          }],
          is_correct: false
        }
      ]
    }
  };
  
  const userAnswers = {
    "193573": "1935730",
    "193574": "1935743",
    "193575": "1935752"
  };
  
  const answer = findUserAnswer(questionData, userAnswers);
  console.log(answer);
  // Output example:
  // {
  //   content: "Semboyan Hakko ichiu...",
  //   index: 0,
  //   label: "A"
  // }
  */