import NextAuth from "next-auth";
import { NextRequest, NextResponse } from "next/server";
import { Session } from "next-auth";
import { trackEvent } from "@/app/user-analytics/actions";
import { UAParser } from 'ua-parser-js';
import { cookies } from 'next/headers';
import { signOut } from "./auth";
import { setLoggingOutFlagToFalse, clearLoggingOutCookie } from "./app/lib/actions/account/actions";

import {
  privateRoutes,
  authRoutes,
  registerRoutes,
  DEFAULT_REDIRECT_LOGIN_URL,
  DEFAULT_REDIRECT_HOME_URL,
  DEFAULT_REDIRECT_REGISTER_FINAL_URL,
  DEFAULT_REDIRECT_LOGOUT_URL,
} from "./routes";
import { checkAccountAlreadyExist } from "./app/lib/auth/checkAccount";
import { logout } from "@/app/lib/auth/logout";
import { authConfig } from "@/auth.config";
import { serverLogout } from "./app/lib/auth/server-logout";

const { auth } = NextAuth(authConfig);

// Add a delay function (returns a promise that resolves after a specified time)
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const isRouteMatch = (path: string, routes: string[]): boolean => {
  return routes.some(route => {
    if (route === path) return true;
    return path.startsWith(`${route}/`);
  });
};

const createRedirectUrl = (req: NextRequest, redirectPath: string): URL => {
  return new URL(redirectPath, req.url);
};

// Helper function to parse user agent with extended information
function parseUserAgent(userAgent: string) {
  const parser = new UAParser(userAgent);
  return {
    browser: {
      name: parser.getBrowser().name,
      version: parser.getBrowser().version,
      major: parser.getBrowser().major, // Major version number
    },
    engine: {
      name: parser.getEngine().name, // Layout engine (e.g., Gecko, WebKit)
      version: parser.getEngine().version,
    },
    os: {
      name: parser.getOS().name,
      version: parser.getOS().version,
      architecture: parser.getCPU().architecture, // CPU architecture
    },
    device: {
      type: parser.getDevice().type, // mobile, tablet, desktop, etc.
      model: parser.getDevice().model,
      vendor: parser.getDevice().vendor,
    },
    cpu: {
      architecture: parser.getCPU().architecture,
    },
    ua: {
      full: parser.getUA(), // Full user agent string
      browser: {
        name: parser.getBrowser().name,
        version: parser.getBrowser().version,
        major: parser.getBrowser().major,
      },
      device: parser.getDevice(),
      engine: parser.getEngine(),
      os: parser.getOS(),
      deviceType: getDeviceCategory(parser), // Custom categorization
      isBot: isBotAgent(userAgent), // Bot detection
    },
    // Additional derived information
    isMobile: parser.getDevice().type === 'mobile',
    isTablet: parser.getDevice().type === 'tablet',
    isDesktop: !parser.getDevice().type,
    features: detectBrowserFeatures(parser),
  };
}

// Helper function to detect browser features
function detectBrowserFeatures(parser: UAParser) {
  const browser = parser.getBrowser();
  return {
    webGL: typeof window !== 'undefined' && !!window.WebGLRenderingContext,
    canvas: typeof document !== 'undefined' && !!document.createElement('canvas').getContext,
    cookies: typeof navigator !== 'undefined' && navigator.cookieEnabled,
    touch: typeof window !== 'undefined' && ('ontouchstart' in window || navigator.maxTouchPoints > 0),
    modernBrowser: isBrowserModern(browser.version),
  };
}

// Helper function to determine if browser is modern
function isBrowserModern(version?: string): boolean {
  if (!version) return false;
  const majorVersion = parseInt(version.split('.')[0]);
  const modernVersions = {
    Chrome: 90,
    Firefox: 88,
    Safari: 14,
    Edge: 90,
    Opera: 76,
  };
  return Object.entries(modernVersions).some(([browser, minVersion]) => 
    browser.toLowerCase() === browser.toLowerCase() && majorVersion >= minVersion
  );
}

// Helper function to categorize device type
function getDeviceCategory(parser: UAParser): string {
  const device = parser.getDevice();
  const os = parser.getOS();
  
  if (device.type === 'mobile') return 'smartphone';
  if (device.type === 'tablet') return 'tablet';
  if (os.name === 'Windows' || os.name === 'Mac OS' || os.name === 'Linux') return 'desktop';
  if (device.type === 'smarttv') return 'smart-tv';
  if (device.type === 'console') return 'gaming-console';
  if (device.type === 'embedded') return 'embedded';
  return 'unknown';
}

// Helper function to detect bots
function isBotAgent(userAgent: string): boolean {
  const botPatterns = [
    'bot', 'crawler', 'spider', 'slurp', 'googlebot',
    'baiduspider', 'bingbot', 'yandexbot', 'ahrefsbot',
    'msnbot', 'facebookexternalhit', 'semrushbot',
    'ia_archiver', 'mediapartners-google'
  ];
  const lowerUA = userAgent.toLowerCase();
  return botPatterns.some(pattern => lowerUA.includes(pattern));
}

// Helper function to get client IP
function getClientIP(req: NextRequest): string {
  const forwardedFor = req.headers.get('x-forwarded-for');
  if (forwardedFor) {
    return forwardedFor.split(',')[0].trim();
  }
  return req.headers.get('x-real-ip') || 
         req.headers.get('cf-connecting-ip') || 
         'unknown';
}

// Helper function to generate session ID
function generateSessionId(): string {
  return 'sess_' + Math.random().toString(36).substr(2, 9);
}

async function trackPageView(req: NextRequest & { auth: Session | null }) {
  try {
    // Only track if user is authenticated
    const userId = req.auth?.user?.id;
    if (!userId) return;

    const startTime = Date.now();
    const userAgent = req.headers.get('user-agent') || '';
    const sessionId = req.cookies.get('session_id')?.value || generateSessionId();
    
    // Get previous page start time from cookie if exists
    const previousPageStartTime = req.cookies.get('page_start_time')?.value;

    // Calculate duration if previous page time exists
    let durationSeconds = 0;
    if (previousPageStartTime) {
      durationSeconds = Math.floor((Date.now() - parseInt(previousPageStartTime)) / 1000);
    }

    // Enhanced device info with null checks and type safety
    const deviceInfo = {
      ...parseUserAgent(userAgent),
      language: req.headers.get('accept-language') || undefined,
      screenResolution: req.headers.get('sec-ch-viewport-width') && req.headers.get('sec-ch-viewport-height')
        ? `${req.headers.get('sec-ch-viewport-width')}x${req.headers.get('sec-ch-viewport-height')}`
        : undefined,
      colorDepth: req.headers.get('sec-ch-color-depth') || undefined,
      platform: req.headers.get('sec-ch-ua-platform') || undefined,
      mobile: req.headers.get('sec-ch-ua-mobile') === '?1',
    };

    // Enhanced location info with null checks
    const locationInfo = {
      referer: req.headers.get('referer') || undefined,
      host: req.headers.get('host') || undefined,
      origin: req.headers.get('origin') || undefined,
      ip: getClientIP(req),
      country: req.headers.get('cf-ipcountry') || undefined,
      timezone: req.headers.get('cf-timezone') || undefined,
      city: req.headers.get('cf-city') || undefined,
      region: req.headers.get('cf-region') || undefined,
    };

    // Track server processing time
    const serverTiming = req.headers.get('server-timing');
    const timeToFirstByte = serverTiming 
      ? parseInt(serverTiming.split('=')[1] || '0')
      : undefined;

    const loadTime = Date.now() - startTime;

    const eventMetadata = {
      query: Object.fromEntries(req.nextUrl.searchParams.entries()),
      timestamp: new Date().toISOString(),
      url: {
        full: req.url,
        path: req.nextUrl.pathname,
        queryString: req.nextUrl.search || undefined,
        hash: req.nextUrl.hash || undefined,
      },
      performance: {
        timeToFirstByte,
        loadTime,
        pageViewDuration: durationSeconds,
      },
      headers: {
        accept: req.headers.get('accept') || undefined,
        acceptEncoding: req.headers.get('accept-encoding') || undefined,
        connection: req.headers.get('connection') || undefined,
        dnt: req.headers.get('dnt') || undefined,
        cacheControl: req.headers.get('cache-control') || undefined,
      }
    };

    // Create FormData for tracking
    const formData = new FormData();
    formData.append("eventType", "page_view");
    formData.append("path", req.nextUrl.pathname);
    formData.append("sessionId", sessionId);
    formData.append("durationSeconds", String(durationSeconds));
    formData.append("deviceInfo", JSON.stringify(deviceInfo));
    formData.append("locationInfo", JSON.stringify(locationInfo));
    formData.append("eventMetadata", JSON.stringify(eventMetadata));
    formData.append("status", "success");

    // Track the page view
    await trackEvent(formData);

    // Prepare response with updated cookies
    const response = NextResponse.next();

    // Set page start time cookie for next duration calculation
    response.cookies.set('page_start_time', String(Date.now()), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 // 24 hours
    });

    // Set/update session cookie if needed
    if (!req.cookies.get('session_id')) {
      response.cookies.set('session_id', sessionId, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        maxAge: 60 * 60 * 24 // 24 hours
      });
    }

    return response;

  } catch (error) {
    console.error('Failed to track page view:', error);
    return undefined;
  }
}

// Also add a client-side tracking for more accurate duration
// Add this to your _app.tsx or similar
export function setupPageDurationTracking() {
  if (typeof window === 'undefined') return;

  let pageStartTime = Date.now();

  // Track duration when user leaves page
  window.addEventListener('beforeunload', async () => {
    const durationSeconds = Math.floor((Date.now() - pageStartTime) / 1000);
    
    // Create form data
    const formData = new FormData();
    formData.append('eventType', 'page_exit');
    formData.append('path', window.location.pathname);
    formData.append('durationSeconds', String(durationSeconds));
    
    // Use sendBeacon for more reliable tracking on page exit
    if (navigator.sendBeacon) {
      navigator.sendBeacon('/api/analytics/track', formData);
    } else {
      try {
        await fetch('/api/analytics/track', {
          method: 'POST',
          body: formData,
        });
      } catch (e) {
        console.error('Failed to track page duration:', e);
      }
    }
  });
}

// Simple check if logging out flag is set
async function isLoggingOut(): Promise<boolean> {
  try {
    const cookieStore = await cookies();
    return cookieStore.get('logging_out')?.value === 'true';
  } catch (error) {
    return false;
  }
}

// Helper function to create redirect with delay
// Helper function to create redirect with delay
async function createDelayedRedirect(req: NextRequest, redirectPath: string, delayMs: number = 1000, isDevSubdomain: boolean = false): Promise<Response> {
  // Wait for the specified delay
  // Create the redirect response

  await delay(delayMs);
  const response = NextResponse.redirect(createRedirectUrl(req, redirectPath));

  await delay(delayMs);
  

  // Delete the session token cookie if needed (especially for logout scenarios)
  if (req.cookies.has('__Secure-authjs.session-token')) {
    // Next.js cookies.delete() takes the name as first arg and doesn't accept options in the same call
    response.cookies.delete('__Secure-authjs.session-token');
    await signOut({ redirect: false });
  }

  if (req.cookies.has('authjs.session-token')) {
    // Next.js cookies.delete() takes the name as first arg and doesn't accept options in the same call
    response.cookies.delete('authjs.session-token');
    await signOut({ redirect: false });
  }
    
  // Add noindex header for dev subdomains
  if (isDevSubdomain) {
    response.headers.set('X-Robots-Tag', 'noindex, nofollow');
  }
  
  return response;
}

export default auth(
  async (
    req: NextRequest & { auth: Session | null },
  ): Promise<Response | void> => {
    const { nextUrl } = req;
    
    const isLoggedIn = !!req.auth;

    // Enhanced route checking
    const isPrivateRoute = isRouteMatch(nextUrl.pathname, privateRoutes);
    const isAuthRoute = isRouteMatch(nextUrl.pathname, authRoutes);
    const isRegisterRouteFinal = isRouteMatch(nextUrl.pathname, registerRoutes);
    
    // Check for dedicated logout route
    const isLogoutRoute = nextUrl.pathname === DEFAULT_REDIRECT_LOGOUT_URL;

    // Check for dev subdomain
    const host = req.headers.get('host') || '';
    const isDevSubdomain = host.startsWith('dev.');

    // Skip analytics for certain paths
    const skipAnalytics = 
      nextUrl.pathname.startsWith('/_next') ||
      nextUrl.pathname.startsWith('/api') ||
      nextUrl.pathname.startsWith('/user-analytics') ||
      nextUrl.pathname.includes('.') ||
      nextUrl.pathname === '/favicon.ico';

    if (!skipAnalytics) {
      await trackPageView(req);
    }

    // Handle career page redirect
    if (nextUrl.pathname === '/career') {
      return NextResponse.redirect('https://careers.giighire.com/terangai');
    }

    try {
      // Special handling for logout route
      if (isLogoutRoute) {
        console.log("Handling dedicated logout route");
        
        try {
          // Execute server-side logout
          await serverLogout();
          try {
            await setLoggingOutFlagToFalse()
            await clearLoggingOutCookie();
            console.log("Logging_out cookie cleared server-side");
          } catch (e) {
            console.error("Failed to clear logging_out cookie server-side:", e);
            // clearCookiesClientSide();
            console.log("Attempted to clear logging_out cookie client-side");
          }
          console.log("Server logout completed successfully");
          
          // Redirect to login page with delay
          return await createDelayedRedirect(req, DEFAULT_REDIRECT_LOGIN_URL, 1000, isDevSubdomain);
        } catch (logoutError) {
          console.error("Error during server logout:", logoutError);
          // Still redirect to login page even if there's an error
          return await createDelayedRedirect(req, DEFAULT_REDIRECT_LOGIN_URL, 1000, isDevSubdomain);
        }
      }

      // Handle registered user accessing register final route
      if (isRegisterRouteFinal && isLoggedIn) {
        const accountCheck = await checkAccountAlreadyExist(req);
        const isAccountExist = accountCheck === true; // Convert to strict boolean

        if (isAccountExist) {
          console.log("Redirecting to dashboard, since user is registered");
          const response = NextResponse.redirect(createRedirectUrl(req, DEFAULT_REDIRECT_HOME_URL));
          if (isDevSubdomain) {
            response.headers.set('X-Robots-Tag', 'noindex, nofollow');
          }
          return response;
        }
      }

      // Handle logged-in user accessing auth routes
      if (isAuthRoute && isLoggedIn) {
        console.log("Redirecting to home as user is logged in and accessing an auth route");
        const response = NextResponse.redirect(createRedirectUrl(req, DEFAULT_REDIRECT_HOME_URL));
        if (isDevSubdomain) {
          response.headers.set('X-Robots-Tag', 'noindex, nofollow');
        }
        return response;
      }

      // Handle non-logged-in user accessing private routes
      if (!isLoggedIn && isPrivateRoute) {
        console.log("Handling non-logged user accessing private route - cleaning up any stuck sessions");
        try {
          await serverLogout();
          // Redirect to login page with delay
          return await createDelayedRedirect(req, DEFAULT_REDIRECT_LOGIN_URL, 1000, isDevSubdomain);
        } catch (logoutError) {
          console.warn('Failed to logout stuck session:', logoutError);
          // Redirect to login page with delay
          return await createDelayedRedirect(req, DEFAULT_REDIRECT_LOGIN_URL, 1000, isDevSubdomain);
        }
      }

      // Handle logged-in user accessing private routes without complete registration
      if (isLoggedIn && isPrivateRoute) {
        const accountExistsCookie = req.cookies.get('account_exists');
        
        // Only call the backend if we don't have the cookie
        let isAccountExist = false;
        if (accountExistsCookie) {
          isAccountExist = accountExistsCookie.value === 'true';
        } else {
          const result = await checkAccountAlreadyExist();
          isAccountExist = result === true; // Convert to strict boolean
        }

        if (!isAccountExist) {
          console.log("Redirecting to register final as account does not exist");
          const response = NextResponse.redirect(createRedirectUrl(req, DEFAULT_REDIRECT_REGISTER_FINAL_URL));
          if (isDevSubdomain) {
            response.headers.set('X-Robots-Tag', 'noindex, nofollow');
          }
          return response;
        }

        // Set the cookie if account exists but cookie doesn't
        if (isAccountExist && !accountExistsCookie) {
          const loggingOut = await isLoggingOut();
          if (loggingOut) {
            console.log("Session auth callback skipped - logging out");
            return NextResponse.next(); // Return a response instead of false
          }
          const response = NextResponse.next();
          response.cookies.set('account_exists', 'true');
          if (isDevSubdomain) {
            response.headers.set('X-Robots-Tag', 'noindex, nofollow');
          }
          return response;
        }
      }

      // No redirect needed, continue with request
      const response = NextResponse.next();
      response.headers.set('Server-Timing', `pageStart;dur=${Date.now()}`);
      if (isDevSubdomain) {
        response.headers.set('X-Robots-Tag', 'noindex, nofollow');
      }
      return response;

    } catch (error) {
      console.error('Middleware error:', error);
      // Redirect to login page with delay
      return await createDelayedRedirect(req, DEFAULT_REDIRECT_LOGIN_URL, 1000, isDevSubdomain);
    }
  },
);

export const config = {
  matcher: [
    "/((?!.+\\.[\\w]+$|_next).*)", // Match all paths except static files
    "/",                           // Match root path
    "/(api|trpc)(.*)",            // Match API routes
  ],
};