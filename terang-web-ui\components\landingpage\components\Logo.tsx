import React from "react";
import Link from "next/link";
import Image from "next/image";
import Head from "next/head";
import styled from "styled-components";

const LogoWrapper = styled.div`
  display: flex;
  align-items: center;
  cursor: pointer;

  @media (max-width: 64em) {
    // Mobile view
    width: 60px;
    height: 60px;
  }

  @media (max-width: 48em) {
    // Tablet view
    width: 65px;
    height: 65px;
  }
`;

const Logo = () => {
  return (
    <>
      <Head>
        <link
          href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap"
          rel="stylesheet"
        />
      </Head>
      <Link passHref href="/">
        <LogoWrapper>
          <picture>
            <source
              height={60}
              media="(max-width: 64em)"
              srcSet="https://cdn.terang.ai/images/logo/logo-terang-ai-simple.svg"
              width={60}
            />
            <Image
              alt="Logo"
              className="logo-image"
              height={250}
              src="https://cdn.terang.ai/images/logo/logo-terang-ai.svg"
              style={{ display: "block" }}
              width={250}
            />
          </picture>
        </LogoWrapper>
      </Link>
    </>
  );
};

export default Logo;
