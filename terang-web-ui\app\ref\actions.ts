// app/ref/actions.ts
'use server'

import { cookies } from 'next/headers';
import { validateAndTrackReferralCode } from '@/app/(dashboard)/referral/actions';
import { redirect } from 'next/navigation';

export async function handleReferralRedirect(code: string) {
  try {
    const validation = await validateAndTrackReferralCode(code, 'CLICK');
    if (!validation.isValid) {
      redirect('/ref/expired');
    }

    const cookieStore = await cookies();
    cookieStore.set('referralCode', code, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 24 * 30, // 30 days in seconds
    });

    redirect(`/register/ref/${code}`);
  } catch (error) {
    console.error('Error handling referral:', error);
    throw error;
  }
}