import React, { useState, useRef, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Spinner,
  Input,
} from "@heroui/react";

interface Props {
  explanation: string;
}

interface Message {
  type: 'ai' | 'user';
  content: string;
}

const AIExplanationComponent: React.FC<Props> = ({ explanation }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState("");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setIsLoading(true);
    // Simulate AI generating initial explanation
    setTimeout(() => {
      setIsLoading(false);
      setMessages([{ type: 'ai', content: explanation }]);
    }, 2000);
  }, [explanation]);

  const handleSendMessage = () => {
    if (inputValue.trim() === "") return;

    const newUserMessage: Message = { type: 'user', content: inputValue };
    setMessages(prevMessages => [...prevMessages, newUserMessage]);
    setInputValue("");
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: Message = { type: 'ai', content: `Here's more information about "${inputValue}": [AI-generated response would go here]` };
      setMessages(prevMessages => [...prevMessages, aiResponse]);
      setIsLoading(false);
    }, 1500);
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  return (
    <div className="flex flex-col h-full">
      <div className="flex-grow overflow-auto p-4">
        {isLoading && messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <Spinner color="primary" size="lg" />
            <span className="ml-2">Generating explanation...</span>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`p-2 rounded-lg ${
                  message.type === 'ai'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-gray-100 text-gray-800'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                <p className="text-sm whitespace-pre-wrap">{message.content}</p>
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      <div className="border-t p-4">
        <div className="flex items-center space-x-2">
          <Input
            className="flex-grow"
            placeholder="Ask a follow-up question..."
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
          />
          <Button
            color="primary"
            onClick={handleSendMessage}
            disabled={isLoading}
          >
            Send
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AIExplanationComponent;