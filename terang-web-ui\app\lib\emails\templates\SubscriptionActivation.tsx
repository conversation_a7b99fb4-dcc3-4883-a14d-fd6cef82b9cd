import {
  Body,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";

export interface SubscriptionActivationProps {
  institutionName?: string;
  planName?: "Basic" | "Professional" | "Enterprise";
  managerName?: string;
  studentCount?: number;
  billingCycle?: "monthly" | "yearly";
  expiryDate?: string;
  dashboardLink?: string;
}

const logoUrl = `https://cdn.terang.ai/images/logo/logo-terang-ai-combined.png`;
const currentYear = new Date().getFullYear();

const planFeatures = {
  Basic: [
    "Basic AI-powered learning",
    "Standard analytics",
    "Email support",
    "Basic question bank",
    "5 teachers per 100 students",
    "Standard reporting",
    "Basic classroom management"
  ],
  Professional: [
    "Advanced AI learning paths",
    "Comprehensive analytics",
    "Priority support",
    "Extended question bank",
    "Custom branding",
    "15 teachers per 100 students",
    "Advanced reporting",
    "Parent portal access",
    "Integration with LMS"
  ],
  Enterprise: [
    "Full AI capabilities",
    "Enterprise analytics",
    "24/7 dedicated support",
    "Unlimited question bank",
    "API access",
    "Custom integrations",
    "Unlimited teachers",
    "Advanced security features",
    "Custom development options",
    "Dedicated account manager",
    "Multi-campus support"
  ]
};

const planDescriptions = {
  Basic: "Perfect for small schools starting their digital transformation journey",
  Professional: "Ideal for growing institutions seeking advanced features and scalability",
  Enterprise: "Enterprise-grade solution for large educational institutions"
};

const SubscriptionActivation: React.FC<SubscriptionActivationProps> = ({
  institutionName = "",
  planName = "Professional",
  studentCount = 0,
  billingCycle = "yearly",
  expiryDate = "",
  dashboardLink = "https://terang.ai/dashboard",
}) => (
  <Html>
    <Head />
    <Preview>Welcome to Terang AI {planName} Plan!</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={coverSection}>
          <Section style={imageSection}>
            <Img
              alt="Terang AI's Logo"
              src={logoUrl}
              style={{
                margin: "0 auto",
                display: "block",
                maxWidth: "250px",
                height: "auto",
              }}
            />
          </Section>
          <Section style={upperSection}>
            <Heading style={h1}>🎉 Your institutional subscription is active!</Heading>
            <Text style={mainText}>
              Dear {institutionName} Manager, thank you for choosing Terang AI {planName} Plan! Your subscription 
              for {studentCount} students is now active with {billingCycle} billing and will expire on {expiryDate}.
            </Text>
            
            <Text style={featureTitle}>About Your {planName} Plan:</Text>
            <Text style={mainText}>{planDescriptions[planName]}</Text>
            
            <Section style={featureSection}>
              <Text style={featureTitle}>Your {planName} Features:</Text>
              {planFeatures[planName].map((feature, index) => (
                <Text key={index} style={featureItem}>
                  ✓ {feature}
                </Text>
              ))}
            </Section>
            
            <Section style={buttonContainer}>
              <Link href={dashboardLink} style={button}>
                Access Your Dashboard
              </Link>
            </Section>
          </Section>
          <Hr />
          <Section style={lowerSection}>
            <Text style={cautionText}>
              Need help getting started? Our support team is ready to assist you with onboarding and setup. For {planName === 'Enterprise' ? 'dedicated support' : 'assistance'}, 
              please contact our {planName === 'Enterprise' ? 'account manager' : 'support team'}.
            </Text>
          </Section>
        </Section>
        <Text style={footerText}>
          This message was produced and distributed by Terang.ai, Inc., 
          Gedung Bursa Efek Indonesia (IDX) Tower 1, SCBD Jl. Jendral Sudirman Kav 52-53, 
          Kebayoran Baru, Jakarta Selatan, DKI Jakarta, Indonesia 12190 © {currentYear}, 
          All rights reserved.
        </Text>
      </Container>
    </Body>
  </Html>
);

export default SubscriptionActivation;

const main = {
  backgroundColor: "#fff",
  color: "#212121",
};

const container = {
  padding: "20px",
  margin: "0 auto",
  backgroundColor: "#eee",
  maxWidth: "600px",
};

const featureSection = {
  backgroundColor: "#f5f5f5",
  padding: "20px",
  margin: "20px 0",
  borderRadius: "8px",
};

const featureTitle = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "16px",
  fontWeight: "bold",
  marginBottom: "10px",
};

const featureItem = {
  color: "#666",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  margin: "8px 0",
};

const h1 = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "24px",
  fontWeight: "bold",
  marginBottom: "15px",
};

const imageSection = {
  backgroundColor: "#252f3d",
  alignItems: "center",
  justifyContent: "center",
  padding: "20px 0",
  textAlign: "center" as const,
};

const coverSection = { backgroundColor: "#fff" };
const upperSection = { padding: "25px 35px" };
const lowerSection = { padding: "25px 35px" };

const buttonContainer = {
  marginTop: "24px",
  alignItems: "center",
  justifyContent: "center",
  textAlign: "center" as const,
};

const button = {
  alignItems: "center",
  justifyContent: "center",
  textAlign: "center" as const,
  background: "linear-gradient(135deg, #89CFF0, #0095ff)",
  border: "none",
  fontSize: "20px",
  lineHeight: "1.2",
  padding: "20px 25px",
  borderRadius: "10px",
  maxWidth: "250px",
  color: "#fff",
  cursor: "pointer",
  boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
};

const text = {
  color: "#333",
  fontFamily:
    "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
  fontSize: "14px",
  margin: "24px 0",
};

const footerText = {
  ...text,
  fontSize: "12px",
  padding: "0 20px",
};

const mainText = { ...text, marginBottom: "14px" };
const cautionText = { ...text, margin: "0px" };