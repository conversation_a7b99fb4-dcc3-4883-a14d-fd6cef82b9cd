// app/tokenizer/route.ts
import { NextResponse } from 'next/server';
import { createByModelName } from '@microsoft/tiktokenizer';

const text = `<PERSON><PERSON>rang AI, asisten berbasis kecerdasan buatan yang dikembangkan khusus untuk membantu persiapan CPNS. Sebagai bagian dari platform terang.ai, saya dirancang untuk memberikan panduan yang komprehensif dan personal dalam perjalanan Kamu menuju kesuksesan CPNS.

Keunggulan saya sebagai AI Assistant meliputi:
- Kemampuan memberikan jawaban yang didasarkan pada database soal CPNS terkini
- Mampu menganalisis pola kesalahan dan memberikan rekomendasi pembelajaran personal
- Dapat menjelaskan konsep rumit dengan cara yang mudah dipahami
- Menyediakan strategi pembelajaran yang disesuaikan dengan kebutuhan individual

Saya akan membantu Kamu dengan:
- <PERSON><PERSON><PERSON> soal dan pembahasan detail
- Tips dan trik menghadapi setiap jenis soal
- Informasi terkini seputar rekrutmen CPNS
- Panduan belajar efektif dan efisien
- Analisis performa dan rekomendasi perbaikan
- Pakai bahasa indonesia, yang nggak perlu baku-baku banget, kaya aku kamu boleh aja.

Mari maksimalkan potensi Kamu dalam persiapan CPNS dengan pendekatan pembelajaran berbasis AI yang hanya tersedia di terang.ai.`;

// Main function for counting tokens
export async function GET() {
  try {
    // Initialize the tokenizer
    const tokenizer = await createByModelName(process.env.AZURE_OPEN_AI_DEPLOYMENT as string);

    // Encode the text into tokens (without special tokens)
    const encodedTokens = tokenizer.encode(text);
    const tokenCount = encodedTokens.length;

    console.log("Encoded tokens:", encodedTokens);
    console.log("Token count:", tokenCount);

    return NextResponse.json({ tokenCount });
  } catch (error) {
    console.error("Error in tokenization:", error);
    return NextResponse.json({ error: "Tokenization failed." }, { status: 500 });
  }
}
