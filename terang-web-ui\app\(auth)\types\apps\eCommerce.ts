export interface ProductType {
  comments: number;
  likes: number;
  artistProfilePhoto: string;
  title: string;
  price: number;
  discount: number;
  related: boolean;
  salesPrice: number;
  category: string[];
  gender: string;
  rating: number;
  stock: boolean;
  qty: number;
  colors: string[];
  photo: string;
  id: number | string;
  created: Date;
  description: string;
  artistFullname: string;
  artistUsername: string;
  vipStatus: boolean;
  verifiedAccount: boolean;
  verifiedCommission: boolean;
  commissionId: string;
}

export interface ProductFiterType {
  id: number;
  filterbyTitle?: string;
  name?: string;
  sort?: string;
  icon?: any;
  devider?: boolean;
}

export interface ProductCardProps {
  id?: string | number;
  color?: string;
  like: string;
  star: number;
  value?: string;
}
