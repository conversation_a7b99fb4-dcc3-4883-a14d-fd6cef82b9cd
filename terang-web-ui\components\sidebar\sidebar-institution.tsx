import React from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { useSidebarContext } from "../layout/layout-context";
import { Sidebar } from "./sidebar.styles";
import { SidebarItem } from "./sidebar-item";
import { SidebarMenu } from "./sidebar-menu";
import { CollapseItems } from "./collapse-items";
import CompanyCard from "./company-card";
import { 
  Building02Icon, 
  UserAccountIcon, 
  ChartBarLineIcon, 
  Settings01Icon,
  Book01Icon,
  TicketStarIcon,
  CreditCardIcon,
  GraduateMaleIcon,
  DashboardSquare02Icon,
  Message02Icon
} from "hugeicons-react";

export const InstitutionSidebarWrapper = () => {
  const pathname = usePathname();
  const { collapsed, setCollapsed } = useSidebarContext();

  const userManagementItems = [
    {
      title: "Teachers",
      href: "/institution/teachers",
      icon: <UserAccountIcon size={18} />
    },
    {
      title: "Students",
      href: "/institution/students",
      icon: <GraduateMaleIcon size={18} />
    }
  ].map(item => (
    <Link 
      href={item.href}
      key={item.href}
      className={`w-full flex items-center gap-3 text-default-500 hover:text-default-900 transition-colors ${
        pathname === item.href ? "text-default-900 font-medium" : ""
      }`}
    >
      {item.icon}
      {item.title}
    </Link>
  ));

  return (
    <aside className="h-screen z-[20] sticky top-0">
      {collapsed ? (
        <div
          className={Sidebar.Overlay()}
          role="presentation"
          onClick={setCollapsed}
        />
      ) : null}
      <div
        className={Sidebar({
          collapsed: collapsed,
        })}
      >
        <Link href="/institution">
          <div className={Sidebar.Header()}>
            <CompanyCard
              location="Institution Dashboard"
              logo="https://cdn.terang.ai/images/logo/logo-terang-ai.svg"
              name="Institution Name"
            />
          </div>
        </Link>
        <div className="flex flex-col justify-between h-full">
          <div className={Sidebar.Body()}>
            <SidebarItem
              href="/institution/dashboard"
              icon={<DashboardSquare02Icon />}
              isActive={pathname === "/institution/dashboard"}
              title="Dashboard"
            />

            <SidebarMenu title="Institution Management">
              <SidebarItem
                href="/2-institution-create"
                icon={<Building02Icon />}
                isActive={pathname?.includes("institution")}
                title="Institutions"
              />
            </SidebarMenu>

            <SidebarMenu title="Support & Settings">
              <SidebarItem
                href="/institution/support"
                icon={<TicketStarIcon />}
                isActive={pathname === "/institution/support"}
                title="Support Tickets"
              />
              <SidebarItem
                href="/institution/settings"
                icon={<Settings01Icon />}
                isActive={pathname === "/institution/settings"}
                title="Settings"
              />
            </SidebarMenu>
          </div>
          
          <SidebarMenu title="">
            <div className="mt-auto">
              <SidebarItem
                href="/institution/subscription"
                icon={<CreditCardIcon />}
                isActive={pathname === "/institution/subscription"}
                title="Subscription"
              />
            </div>
          </SidebarMenu>
        </div>
      </div>
    </aside>
  );
};