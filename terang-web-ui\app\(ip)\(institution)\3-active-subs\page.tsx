"use client";

import React, { useState, useEffect } from 'react';
import { useRouter } from "next/navigation";
import {
  <PERSON><PERSON>,
  Card,
  CardBody,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Chip,
  Progress,
  Textarea,
  RadioGroup,
  Radio,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow
} from "@heroui/react";
import {
  ArrowLeft,
  Calendar,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Building2,
  Users,
  Crown,
  Calculator
} from "lucide-react";

interface Subscription {
  tier: string;
  status: 'active' | 'inactive' | 'cancelled';
  billingCycle: 'monthly' | 'yearly';
  startDate: string;
  endDate: string;
  nextBilling: string;
  studentCount: number;
  monthlyPrice: number;
  totalPrice: number;
  institution: {
    type: string;
    name: string;
  };
}

interface CancellationOption {
  value: string;
  label: string;
}

const SubscriptionManagement: React.FC = () => {
  const router = useRouter();
  const [showCancelModal, setShowCancelModal] = useState<boolean>(false);
  const [cancellationReason, setCancellationReason] = useState<string>("");
  const [confirmCancel, setConfirmCancel] = useState<boolean>(false);
  const [cancellationOption, setCancellationOption] = useState<string>("");
  const [progressData, setProgressData] = useState({
    daysRemaining: 0,
    progressPercentage: 0
  });
  const [mounted, setMounted] = useState(false);
  const [showBillingModal, setShowBillingModal] = useState<boolean>(false);
  const [newBillingCycle, setNewBillingCycle] = useState<'monthly' | 'yearly'>('yearly');
  const [showBillingConfirm, setShowBillingConfirm] = useState<boolean>(false);

  const subscription: Subscription = {
    tier: "Professional",
    status: "active",
    billingCycle: "yearly",
    startDate: "2024-01-01",
    endDate: "2024-12-31",
    nextBilling: "2024-12-31",
    studentCount: 350,
    monthlyPrice: 8000,
    totalPrice: 25200000,
    institution: {
      type: "sma-negeri",
      name: "SMA Negeri 1"
    }
  };

  useEffect(() => {
    setMounted(true);
    const today = new Date().getTime();
    const endDate = new Date(subscription.endDate).getTime();
    const totalDays = 365;
    const daysRemaining = Math.max(0, Math.ceil((endDate - today) / (1000 * 60 * 60 * 24)));
    const progressPercentage = ((totalDays - daysRemaining) / totalDays) * 100;

    setProgressData({
      daysRemaining,
      progressPercentage
    });
  }, [subscription.endDate]);

  const cancellationOptions: CancellationOption[] = [
    { value: "too-expensive", label: "Too expensive" },
    { value: "not-using", label: "Not using the service enough" },
    { value: "missing-features", label: "Missing important features" },
    { value: "switching", label: "Switching to another service" },
    { value: "other", label: "Other reason" }
  ];

  const handleCancellation = (): void => {
    console.log("Cancellation submitted:", {
      reason: cancellationReason,
      option: cancellationOption,
      subscriptionId: subscription.tier,
      cancellationDate: new Date().toISOString()
    });
    setShowCancelModal(false);
    setConfirmCancel(true);
  };

  const calculatePrices = () => {
    const monthlyTotal = subscription.monthlyPrice * subscription.studentCount;
    const yearlyTotal = (subscription.monthlyPrice * 12 * 0.75) * subscription.studentCount;
    
    return {
      monthly: {
        perStudent: subscription.monthlyPrice,
        total: monthlyTotal,
        totalAnnual: monthlyTotal * 12
      },
      yearly: {
        perStudent: subscription.monthlyPrice * 12 * 0.75,
        total: yearlyTotal,
        savings: (monthlyTotal * 12) - yearlyTotal
      }
    };
  };

  const handleBillingChange = (): void => {
    console.log("Changing billing cycle to:", newBillingCycle);
    setShowBillingModal(false);
    setShowBillingConfirm(true);
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-10">
      <div className="max-w-7xl mx-auto px-4 py-6">
        <Button
          variant="light"
          startContent={<ArrowLeft className="w-4 h-4" />}
          onClick={() => router.back()}
          className="mb-4"
        >
          Back to Dashboard
        </Button>
        
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">Subscription Management</h1>
            <p className="text-gray-600">Manage your current subscription plan and billing</p>
          </div>
          <Chip 
            color="success" 
            variant="flat" 
            startContent={<CheckCircle2 className="w-4 h-4" />}
          >
            Active
          </Chip>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="md:col-span-2">
            <CardBody className="p-6">
              <h2 className="text-xl font-semibold mb-4">Current Plan</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Crown className="w-5 h-5 text-primary" />
                      <span className="font-semibold">{subscription.tier} Plan</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Chip variant="flat" size="sm">
                        {subscription.billingCycle === 'yearly' ? 'Annual billing' : 'Monthly billing'}
                      </Chip>
                      <Chip variant="flat" size="sm" startContent={<Users className="w-3 h-3" />}>
                        {subscription.studentCount} students
                      </Chip>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-1">Price per student</p>
                    <p className="font-semibold">
                      Rp {subscription.monthlyPrice.toLocaleString()} / month
                    </p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-1">Total price</p>
                    <p className="font-semibold">
                      Rp {subscription.totalPrice.toLocaleString()} / year
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="flex items-center gap-2 mb-2">
                      <Building2 className="w-5 h-5 text-primary" />
                      <span className="font-semibold">Institution Details</span>
                    </div>
                    <p className="text-gray-600">{subscription.institution.name}</p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-1">Next billing date</p>
                    <p className="font-semibold">
                      {mounted && new Date(subscription.nextBilling).toLocaleDateString()}
                    </p>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-1">Subscription period</p>
                    <div className="space-y-2">
                      <Progress 
                        value={progressData.progressPercentage} 
                        color="primary"
                        className="w-full"
                      />
                      <div className="flex justify-between text-sm">
                        <span>{progressData.daysRemaining} days remaining</span>
                        <span>{subscription.endDate}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>

          <Card>
            <CardBody className="p-6">
              <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
              <div className="space-y-3">
                <Button
                  color="primary"
                  variant="bordered"
                  className="w-full"
                  startContent={<Calendar className="w-4 h-4" />}
                  onClick={() => setShowBillingModal(true)}
                >
                  Change Billing Cycle
                </Button>
                <Button
                  color="danger"
                  variant="light"
                  className="w-full"
                  startContent={<XCircle className="w-4 h-4" />}
                  onClick={() => setShowCancelModal(true)}
                >
                  Cancel Subscription
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>

      {/* Billing Cycle Change Modal */}
      <Modal isOpen={showBillingModal} onClose={() => setShowBillingModal(false)} size="2xl">
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <span className="text-xl">Change Billing Cycle</span>
            <span className="text-sm text-gray-500">
              Choose your preferred billing cycle
            </span>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Card className="bg-primary-50">
                <CardBody className="py-3">
                  <div className="flex items-start gap-2">
                    <Calculator className="w-5 h-5 text-primary shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium text-primary mb-1">Compare Billing Options</p>
                      <p className="text-sm text-gray-600">
                        Save 25% with annual billing
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>

              <RadioGroup
                value={newBillingCycle}
                onValueChange={(value) => setNewBillingCycle(value as 'monthly' | 'yearly')}
                className="mt-4"
              >
                <div className="grid gap-4">
                  <Card className={`border-2 ${newBillingCycle === 'monthly' ? 'border-primary' : 'border-transparent'}`}>
                    <CardBody className="p-4">
                      <Radio value="monthly">
                        <div className="ml-2">
                          <p className="font-semibold">Monthly Billing</p>
                          <p className="text-sm text-gray-600">
                            Rp {calculatePrices().monthly.perStudent.toLocaleString()} per student/month
                          </p>
                        </div>
                      </Radio>
                    </CardBody>
                  </Card>

                  <Card className={`border-2 ${newBillingCycle === 'yearly' ? 'border-primary' : 'border-transparent'}`}>
                    <CardBody className="p-4">
                      <Radio value="yearly">
                        <div className="ml-2">
                          <p className="font-semibold flex items-center gap-2">
                            Annual Billing
                            <Chip color="success" variant="flat" size="sm">Save 25%</Chip>
                          </p>
                          <p className="text-sm text-gray-600">
                            Rp {(calculatePrices().monthly.perStudent * 0.75).toLocaleString()} per student/month
                          </p>
                        </div>
                      </Radio>
                    </CardBody>
                  </Card>
                </div>
              </RadioGroup>

              <Table aria-label="Billing comparison">
                <TableHeader>
                  <TableColumn>BILLING DETAILS</TableColumn>
                  <TableColumn className="text-end">AMOUNT</TableColumn>
                </TableHeader>
                <TableBody>
                  {[
                    {
                      key: "base-payment",
                      label: `Total ${newBillingCycle === 'yearly' ? 'yearly' : 'monthly'} payment`,
                      amount: newBillingCycle === 'yearly' 
                        ? calculatePrices().yearly.total 
                        : calculatePrices().monthly.total,
                      isSuccess: false
                    },
                    ...(newBillingCycle === 'yearly' ? [{
                      key: "annual-savings",
                      label: "Annual savings",
                      amount: calculatePrices().yearly.savings,
                      isSuccess: true
                    }] : [])
                  ].map((row) => (
                    <TableRow key={row.key}>
                      <TableCell className={row.isSuccess ? "text-success" : ""}>
                        {row.label}
                      </TableCell>
                      <TableCell className={`text-end ${row.isSuccess ? "text-success" : ""}`}>
                        Rp {row.amount.toLocaleString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="light"
              onClick={() => setShowBillingModal(false)}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onClick={handleBillingChange}
            >
              Confirm Change
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Billing Change Confirmation Modal */}
      <Modal isOpen={showBillingConfirm} onClose={() => setShowBillingConfirm(false)}>
        <ModalContent>
          <ModalBody className="py-6">
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <CheckCircle2 className="w-12 h-12 text-success" />
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-2">Billing Cycle Updated</h3>
                <p className="text-gray-600">
                  Your subscription has been updated to {newBillingCycle} billing.
                  The new billing cycle will take effect on your next billing date.
                </p>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="primary"
              variant="light"
              onClick={() => setShowBillingConfirm(false)}
              className="w-full"
            >
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Cancellation Modal */}
      <Modal isOpen={showCancelModal} onClose={() => setShowCancelModal(false)} size="2xl">
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <span className="text-xl">Cancel Subscription</span>
            <span className="text-sm text-gray-500">
              Your subscription will remain active until the end of your billing period
            </span>
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Card className="bg-danger-50">
                <CardBody className="py-3">
                  <div className="flex items-start gap-2">
                    <AlertCircle className="w-5 h-5 text-danger shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium text-danger mb-1">Are you sure you want to cancel?</p>
                      <p className="text-sm text-gray-600">
                        You will lose access to all features at the end of your billing period on {subscription.endDate}
                      </p>
                    </div>
                  </div>
                </CardBody>
              </Card>

              <div>
                <label htmlFor="cancellation-reason" className="text-sm font-medium mb-2 block">
                  What is the main reason for cancelling?
                </label>
                <RadioGroup
                  id="cancellation-reason"
                  value={cancellationOption}
                  onValueChange={setCancellationOption}
                  className="mt-2"
                >
                  {cancellationOptions.map((option) => (
                    <Radio key={option.value} value={option.value}>
                      {option.label}
                    </Radio>
                  ))}
                </RadioGroup>
              </div>

              <div>
                <label htmlFor="additional-feedback" className="text-sm font-medium mb-2 block">
                  Please provide additional feedback (optional)
                </label>
                <Textarea
                  id="additional-feedback"
                  value={cancellationReason}
                  onChange={(e) => setCancellationReason(e.target.value)}
                  placeholder="Tell us more about your decision..."
                  className="w-full"
                />
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              variant="light"
              onClick={() => setShowCancelModal(false)}
            >
              Keep Subscription
            </Button>
            <Button
              color="danger"
              onClick={handleCancellation}
            >
              Confirm Cancellation
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Cancellation Confirmation Modal */}
      <Modal isOpen={confirmCancel} onClose={() => setConfirmCancel(false)}>
        <ModalContent>
          <ModalBody className="py-6">
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <CheckCircle2 className="w-12 h-12 text-success" />
              </div>
              <div>
                <h3 className="text-xl font-semibold mb-2">Cancellation Confirmed</h3>
                <p className="text-gray-600">
                  Your subscription will remain active until {subscription.endDate}. 
                  You can continue using all features until then.
                </p>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="primary"
              variant="light"
              onClick={() => setConfirmCancel(false)}
              className="w-full"
            >
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};

export default SubscriptionManagement;