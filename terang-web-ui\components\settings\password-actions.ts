"use server";

import { getUserId } from "@/app/lib/actions/account/actions";

export interface UpdatePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

export interface UpdatePasswordResponse {
  success: boolean;
  data?: {
    message: string;
  };
  error?: string;
}

/**
 * Updates user password after verifying current password
 * @param data Current and new password data
 * @returns Promise containing the response with success message or error
 */
export async function updatePassword(
  formData: FormData | UpdatePasswordRequest
): Promise<UpdatePasswordResponse> {
  try {
    const userId = await getUserId();
    
    if (!userId) {
      throw new Error('User ID not found');
    }

    if (!process.env.BACKEND_BASE_URL || !process.env.BACKEND_API_KEY) {
      throw new Error('Missing required environment variables');
    }

    // Handle both FormData and direct object submission
    const data: UpdatePasswordRequest = formData instanceof FormData
      ? {
          currentPassword: formData.get('currentPassword') as string,
          newPassword: formData.get('newPassword') as string,
        }
      : formData;

    // Validate required fields
    if (!data.currentPassword || !data.newPassword) {
      throw new Error('Missing required fields');
    }

    // First verify current password
    const verifyResponse = await fetch(`${process.env.BACKEND_BASE_URL}/v0/users/${userId}/verify-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': process.env.BACKEND_API_KEY
      },
      body: JSON.stringify({
        password: data.currentPassword
      })
    });

    if (!verifyResponse.ok) {
      throw new Error('Current password is incorrect');
    }

    // Validate new password requirements
    if (data.newPassword.length < 8) {
      throw new Error('New password must be at least 8 characters');
    }
    if (!/[A-Z]/.test(data.newPassword)) {
      throw new Error('New password must contain at least one capital letter');
    }
    if (!/[!@#$%^&*(),.?":{}|<>\-]/.test(data.newPassword)) {
      throw new Error('New password must contain at least one symbol (!@#$%^&*(),.?":{}|<>-)');
    }

    // If verification passed, proceed with password update
    const updateResponse = await fetch(`${process.env.BACKEND_BASE_URL}/v0/users/${userId}/password`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-API-KEY': process.env.BACKEND_API_KEY
      },
      body: JSON.stringify({
        new_password: data.newPassword
      })
    });

    if (!updateResponse.ok) {
      const errorData = await updateResponse.json().catch(() => null);
      throw new Error(
        errorData?.message || `Failed to update password: ${updateResponse.status} ${updateResponse.statusText}`
      );
    }
    
    return {
      success: true,
      data: {
        message: 'Password updated successfully'
      }
    };
  } catch (error) {
    console.error('Password update error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}