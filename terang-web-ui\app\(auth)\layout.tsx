"use client";

import AuthLayoutWrapper from "@/components/auth/authLayout";

import "@/styles/globals.css";
import { usePathname } from "next/navigation";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const condition = pathname === "/register/final";

  return (
    <div className="h-screen">
      {condition ? (
        <>{children}</>
      ) : (
        <AuthLayoutWrapper>{children}</AuthLayoutWrapper>
      )}
    </div>
  );
}
