import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>B<PERSON> } from "@heroui/react";
import { But<PERSON> } from "@heroui/button";
import { Star, Rotate3D } from 'lucide-react';

interface CardContent {
  type: 'text' | 'image';
  content: string;
  title?: string;
}

interface CardBack extends CardContent {
  explanation?: string;
  examples?: string[];
  mnemonics?: string;
}

interface Flashcard {
  id: string;
  front: CardContent;
  back: CardBack;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
}

interface CardProgress {
  id: string;
  understanding: UnderstandingLevel;
  lastReviewed: Date;
  repetitions: number;
}

type UnderstandingLevel = 'mastered' | 'reviewing' | 'learning' | 'struggling';

interface UnderstandingInfo {
  label: string;
  description: string;
  color: string;
  icon: React.ReactNode;
  interval: number;
}

interface Colors {
  frontGradient: string;
  backGradient: string;
  accentColor: string;
  background: string;
}

interface FlashcardComponentProps {
  currentCard: Flashcard;
  isFlipped: boolean;
  onFlip: () => void;
  onMark: (e: React.MouseEvent) => void;
  markedCards: Set<string>;
  colors: Colors;
  cardProgress: Record<string, CardProgress>;
  handleAssessUnderstanding: (level: UnderstandingLevel) => void;
  understandingLevels: Record<UnderstandingLevel, UnderstandingInfo>;
}

const FlashcardComponent: React.FC<FlashcardComponentProps> = ({
    currentCard,
    isFlipped,
    onFlip,
    onMark,
    markedCards,
    colors,
    cardProgress,
    handleAssessUnderstanding,
    understandingLevels
  }) => {
    return (
      <div className="relative w-full mb-6">
        <div
          className="w-full transition-transform duration-500 relative"
          style={{
            transformStyle: "preserve-3d",
            transform: isFlipped ? "rotateY(180deg)" : "rotateY(0deg)"
          }}
          onClick={onFlip}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              onFlip();
            }
          }}
          role="button"
          tabIndex={0}
          aria-label={`Flashcard: ${currentCard.front.title}. Press Enter or Space to flip.`}
        >
          {/* Front of Card */}
          <Card 
            className="w-full"
            style={{ 
              backfaceVisibility: "hidden",
              background: colors.frontGradient,
              position: isFlipped ? 'absolute' : 'relative',
              top: 0,
              left: 0
            }}
          >
            <CardHeader className="flex justify-between border-b border-white/20">
              <div className="flex items-center gap-2">
                <h3 className="text-xl font-semibold text-white">
                  {currentCard.front.title}
                </h3>
                {cardProgress[currentCard.id] && (
                  <div className={`px-2 py-1 rounded-full text-xs ${understandingLevels[cardProgress[currentCard.id].understanding].color}`}>
                    {understandingLevels[cardProgress[currentCard.id].understanding].label}
                  </div>
                )}
              </div>
              <Button
                isIconOnly
                variant="light"
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  onMark(e);
                }}
                aria-label={markedCards.has(currentCard.id) ? "Remove from favorites" : "Add to favorites"}
              >
                <Star 
                  className={`w-5 h-5 ${markedCards.has(currentCard.id) ? 'fill-yellow-400' : ''}`}
                  color={markedCards.has(currentCard.id) ? '#FBBF24' : 'white'}
                />
              </Button>
            </CardHeader>
            <CardBody className="flex items-center justify-center p-6">
              <div className="text-center space-y-4">
                <p className="text-lg text-white">{currentCard.front.content}</p>
                <div className="mt-4 text-sm text-white/80 flex items-center justify-center">
                  <Rotate3D className="w-4 h-4 mr-2" />
                  Click or press Enter to flip
                </div>
              </div>
            </CardBody>
          </Card>
  
          {/* Back of Card */}
          <Card 
            className="w-full"
            style={{ 
              backfaceVisibility: "hidden",
              transform: "rotateY(180deg)",
              background: colors.backGradient,
              position: isFlipped ? 'relative' : 'absolute',
              top: 0,
              left: 0
            }}
          >
            <CardHeader className="flex justify-between border-b border-white/20">
              <h3 className="text-xl font-semibold text-white">Answer</h3>
            </CardHeader>
            <CardBody className="p-6">
              <div className="space-y-4 text-white">
                <p className="text-lg font-medium">{currentCard.back.content}</p>
                
                {currentCard.back.explanation && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Explanation:</h4>
                    <p className="text-white/90">{currentCard.back.explanation}</p>
                  </div>
                )}
  
                {currentCard.back.examples && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Examples:</h4>
                    <ul className="list-disc list-inside text-white/90 space-y-1">
                      {currentCard.back.examples.map((example, index) => (
                        <li key={index}>{example}</li>
                      ))}
                    </ul>
                  </div>
                )}
  
                {currentCard.back.mnemonics && (
                  <div className="mt-4">
                    <h4 className="font-semibold mb-2">Memory Aid:</h4>
                    <p className="text-white/90 italic">{currentCard.back.mnemonics}</p>
                  </div>
                )}
  
                {/* Understanding Assessment */}
                <div className="mt-6 border-t border-white/20 pt-4">
                  <h4 className="font-semibold mb-3">How well did you know this?</h4>
                  <div className="grid grid-cols-2 gap-3">
                    {(Object.entries(understandingLevels) as [UnderstandingLevel, UnderstandingInfo][]).map(([level, info]) => (
                      <Button
                        key={level}
                        className={`${info.color} text-white hover:opacity-90`}
                        onClick={(e: React.MouseEvent) => {
                          e.stopPropagation();
                          handleAssessUnderstanding(level);
                        }}
                      >
                        <div className="flex items-center gap-2">
                          {info.icon}
                          <div>
                            <div className="font-semibold">{info.label}</div>
                            <div className="text-xs">{info.description}</div>
                          </div>
                        </div>
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </div>
      </div>
    );
  };
  
  export default FlashcardComponent;