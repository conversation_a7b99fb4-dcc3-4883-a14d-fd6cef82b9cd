import { useState, useEffect, useCallback } from 'react'
import { Select, SelectItem, Card, CardBody, Progress, Button } from "@heroui/react"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  ChartData,
  ChartOptions,
  TooltipItem,
} from 'chart.js'
import { Bar, Radar } from 'react-chartjs-2'
import { 
  Clock, 
  Zap, 
  Timer, 
  AlertCircle, 
  Award, 
  BookOpen, 
  Brain,
  AlertTriangle,
  CheckCircle2, XCircle, Info,
  Rocket,
  Target,
  RefreshCw
} from 'lucide-react'
import { 
  getLatestReport, 
  getSpecificReport, 
  getAvailableSessions, 
  invalidateUserCache,
  invalidateSessionCache,
  refreshAllReports,
  refreshSessionReport,
  type TimeManagementReport,
  type QuestionMetadata 
} from './action'
import DotLottieAnimation from '@/components/shared/dotlottie-animation'

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Toolt<PERSON>,
  <PERSON>,
  <PERSON>dial<PERSON>inearScale,
  PointElement,
  LineElement,
  Filler
)

type ReportType = 'latest' | 'aggregate' | 'specific'

interface ReportTypeOption {
  value: ReportType
  label: string
}

interface SessionOption {
  sessionId: string
  examName: string
}

interface SubjectData {
  timeSpent: number
  averageTime: number
  correct: number
  total: number
  attempted: number
}

interface SubjectDataMap {
  [key: string]: SubjectData
}

interface WeaknessStats {
  total: number
  incorrect: number
  slow: number
  notAttempted: number
}

interface WeaknessItem {
  name: string
  score: number
  stats: WeaknessStats
}

const reportTypes: ReportTypeOption[] = [
  { value: 'latest', label: 'Sesi Terakhir' },
//   { value: 'aggregate', label: 'Laporan Agregat' },
  { value: 'specific', label: 'Pilih Sesi Tertentu' },
]

const getDifficultyColor = (level: string) => {
  switch (level) {
    case 'VERY_HARD':
      return 'text-red-600'
    case 'HARD':
      return 'text-red-500'
    case 'MODERATE':
      return 'text-yellow-500'
    case 'EASY':
      return 'text-green-500'
    case 'VERY_EASY':
      return 'text-green-600'
    default:
      return 'text-gray-500'
  }
}

const getDifficultyText = (level: string) => {
  switch (level) {
    case 'VERY_HARD':
      return 'Sangat Sulit'
    case 'HARD':
      return 'Sulit'
    case 'MODERATE':
      return 'Sedang'
    case 'EASY':
      return 'Mudah'
    case 'VERY_EASY':
      return 'Sangat Mudah'
    default:
      return level
  }
}

const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.round(seconds % 60)
  if (minutes > 0) {
    return `${minutes} menit ${remainingSeconds} detik`
  }
  return `${remainingSeconds} detik`
}

export default function TimeManagementVisualization() {
  const [reportType, setReportType] = useState<ReportType>('latest')
  const [sessionId, setSessionId] = useState<string>('')
  const [sessions, setSessions] = useState<SessionOption[]>([])
  const [report, setReport] = useState<TimeManagementReport | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [refreshing, setRefreshing] = useState<boolean>(false) // New state for refresh action

  const fetchSessions = async () => {
    try {
      const sessionsData = await getAvailableSessions()
      setSessions(sessionsData)
    } catch (error) {
      console.error('Error fetching sessions:', error)
    }
  }

  const fetchReport = async () => {
    setLoading(true)
    try {
      let data: TimeManagementReport | null = null
      switch (reportType) {
        case 'latest':
          data = await getLatestReport()
          break
        case 'specific':
          if (sessionId) {
            data = await getSpecificReport(sessionId)
          }
          break
      }
      setReport(data)
    } catch (error) {
      console.error('Error fetching report:', error)
    }
    setLoading(false)
  }

  // New function to refresh reports
  // Add handleRefresh function with revalidation
  const handleRefresh = useCallback(async () => {
    setRefreshing(true)
    try {
      let data: TimeManagementReport | null = null
      
      // First invalidate the cache
      await invalidateUserCache()
      if (sessionId) {
        await invalidateSessionCache(sessionId)
      }
      
      // Then fetch fresh data
      switch (reportType) {
        case 'latest':
          data = await refreshAllReports()
          break
        case 'specific':
          if (sessionId) {
            data = await refreshSessionReport(sessionId)
          }
          break
      }
      
      if (data) {
        setReport(data)
        await fetchSessions()
        
        // Force reload the current report to ensure we have the latest data
        await fetchReport()
      }
    } catch (error) {
      console.error('Error refreshing report:', error)
    } finally {
      setRefreshing(false)
    }
  }, [reportType, sessionId])

  useEffect(() => {
    fetchSessions()
  }, [])

  useEffect(() => {
    if (reportType !== 'specific' || sessionId) {
      fetchReport()
    }
  }, [reportType, sessionId])

    // Create report type options with dynamic latest session label
    // const reportTypes = [
    //   {
    //     value: "latest", 
    //     label: sessions.length > 0 ? (() => {
    //       const latestSession = sessions[0];
    //       const totalAttempts = sessions.filter(s => s.examName === latestSession.examName).length;
    //       const attemptNumber = sessions
    //         .filter(s => s.examName === latestSession.examName)
    //         .findIndex(s => s.sessionId === latestSession.sessionId);
              
    //       return totalAttempts > 1 
    //         ? `Sesi Terakhir - ${latestSession.examName} - Percobaan ${totalAttempts - attemptNumber}`
    //         : `Sesi Terakhir - ${latestSession.examName}`;
    //     })() 
    //     : "Sesi Terakhir"
    //   },
    //   {value: "specific", label: "Pilih Sesi Tertentu"}
    // ];

    // Prepare session options with display names
    const sessionOptions = sessions.reduce<(SessionOption & { displayName: string })[]>((acc, session, _, array) => {
      const totalAttempts = array.filter(s => s.examName === session.examName).length;
      const attemptNumber = array
        .filter(s => s.examName === session.examName)
        .findIndex(s => s.sessionId === session.sessionId);
      
      if (!acc.some(s => s.sessionId === session.sessionId)) {
        acc.push({
          ...session,
          displayName: totalAttempts > 1 
            ? `${session.examName} - Percobaan ${totalAttempts - attemptNumber}`
            : session.examName
        });
      }
      return acc;
    }, []);

  const renderPerformanceInsights = (report: TimeManagementReport) => {
    if (!report) return null;
    
    const { userStats, globalStats, totalQuestions, timeDistribution } = report
    const attemptedPercentage = (userStats.attemptedQuestions / totalQuestions) * 100
    const timeComparison = ((globalStats.averageTimeSpent - userStats.averageTimeSpent) / globalStats.averageTimeSpent) * 100

    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
        <Card>
          <CardBody>
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <Award className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold">Performa Keseluruhan</h3>
              </div>
              
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Progress Pengerjaan</span>
                    <span className="font-medium">{userStats.attemptedQuestions} dari {totalQuestions} soal</span>
                  </div>
                  <Progress 
                    value={attemptedPercentage} 
                    color="primary"
                    className="w-full"
                    showValueLabel={true}
                    label="Dikerjakan"
                  />
                </div>

                <div className="grid grid-cols-2 gap-2 text-sm mt-4">
                    {/* Fast */}
                    <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-green-500" />
                    <div className="flex gap-1">
                        <span>Cepat & Benar:</span>
                        <span className="font-medium">{timeDistribution.fastCorrect}</span>
                    </div>
                    </div>
                    <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-500" />
                    <div className="flex gap-1">
                        <span>Cepat & Salah:</span>
                        <span className="font-medium">{timeDistribution.fastIncorrect}</span>
                    </div>
                    </div>

                    {/* Average */}
                    <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-blue-500" />
                    <div className="flex gap-1">
                        <span>Normal & Benar:</span>
                        <span className="font-medium">{timeDistribution.averageCorrect}</span>
                    </div>
                    </div>
                    <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-orange-500" />
                    <div className="flex gap-1">
                        <span>Normal & Salah:</span>
                        <span className="font-medium">{timeDistribution.averageIncorrect}</span>
                    </div>
                    </div>

                    {/* Slow */}
                    <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-yellow-500" />
                    <div className="flex gap-1">
                        <span>Lambat & Benar:</span>
                        <span className="font-medium">{timeDistribution.slowCorrect}</span>
                    </div>
                    </div>
                    <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-600" />
                    <div className="flex gap-1">
                        <span>Lambat & Salah:</span>
                        <span className="font-medium">{timeDistribution.slowIncorrect}</span>
                    </div>
                    </div>

                    {/* Not Attempted */}
                    <div className="flex items-center gap-2 col-span-2">
                    <div className="w-3 h-3 rounded-full bg-gray-300" />
                    <div className="flex gap-1">
                        <span>Belum Dikerjakan:</span>
                        <span className="font-medium">{timeDistribution.notAttempted}</span>
                    </div>
                    </div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardBody>
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <Clock className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold">Analisis Waktu</h3>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <div className="text-sm text-gray-500">Rata-rata Kamu</div>
                    <div className="font-semibold">{formatTime(userStats.averageTimeSpent)}/soal</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm text-gray-500">Rata-rata Global</div>
                    <div className="font-semibold">{formatTime(globalStats.averageTimeSpent)}/soal</div>
                  </div>
                </div>

                <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-900">
                  <div className="flex items-center gap-2">
                    {timeComparison > 0 ? (
                      <Zap className="w-5 h-5 text-green-500" />
                    ) : (
                      <AlertCircle className="w-5 h-5 text-yellow-500" />
                    )}
                    <span className="text-sm">
                      {timeComparison > 0 ? (
                        <span>Kamu <span className="font-medium text-green-500">{Math.abs(timeComparison).toFixed(1)}% lebih cepat</span> dari rata-rata</span>
                      ) : (
                        <span>Kamu <span className="font-medium text-yellow-500">{Math.abs(timeComparison).toFixed(1)}% lebih lambat</span> dari rata-rata</span>
                      )}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="space-y-1">
                    <div className="text-gray-500">Waktu Tercepat Global</div>
                    <div className="font-medium">{formatTime(globalStats.fastestTime)}/soal</div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-gray-500">Waktu Terlambat Global</div>
                    <div className="font-medium">{formatTime(globalStats.slowestTime)}/soal</div>
                  </div>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
            <Card>
            <CardBody>
                <div className="flex items-center gap-2 mb-4">
                <BookOpen className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold">Akurasi per Mata Uji</h3>
                </div>
                <div className="h-[300px]">
                {barChartData() && (
                    <Bar 
                    data={barChartData() as ChartData<'bar'>} 
                    options={chartOptions} 
                    />
                )}
                </div>
            </CardBody>
            </Card>

            <Card>
            <CardBody>
                <div className="flex items-center gap-2 mb-4">
                <Brain className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold">Efisiensi Waktu per Mata Uji</h3>
                </div>
                <div className="h-[300px]">
                {radarChartData() && (
                    <Radar 
                    data={radarChartData() as ChartData<'radar'>} 
                    options={radarOptions}
                    />
                )}
                </div>
            </CardBody>
            </Card>
      </div>
    )
  }

  const renderDifficultyOverview = (report: TimeManagementReport) => {
    if (!report || !report.examDifficulty) return null;
    
    const { examDifficulty } = report
    
    return (<>

      <Card className="mb-4">
        <CardBody>
          <div className="space-y-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-primary" />
              <h3 className="text-lg font-semibold">Tingkat Kesulitan Ujian</h3>
            </div>

            <div>
              <div className="mb-6">
                <p className="text-sm text-gray-600 mb-4">
                  Tingkat kesulitan dihitung berdasarkan kombinasi dari waktu pengerjaan (time factor) 
                  dan tingkat kesalahan (error factor) dari seluruh peserta ujian. 
                  Skor yang lebih tinggi menunjukkan tingkat kesulitan yang lebih tinggi.
                </p>
                
                <div className="p-4 rounded-lg bg-gray-50 dark:bg-gray-900">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">Tingkat Kesulitan Keseluruhan</span>
                    <span className={`font-medium ${getDifficultyColor(examDifficulty.level)}`}>
                      {getDifficultyText(examDifficulty.level)}
                    </span>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Skor Kesulitan Global</span>
                      <span>{(examDifficulty.score * 100).toFixed(1)}%</span>
                    </div>
                    <Progress 
                      value={examDifficulty.score * 100}
                      color={examDifficulty.score > 0.8 ? "danger" : 
                             examDifficulty.score > 0.6 ? "warning" : "success"}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
                    <div className="flex items-center gap-2 mb-4">
                      <Timer className="w-5 h-5 text-primary" />
                      <h3 className="text-lg font-semibold">Detail Kesulitan per Mata Uji</h3>
                    </div>
                    
                    {report.examDifficulty.bySubject && (
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {Object.entries(report.examDifficulty.bySubject).map(([subject, data]) => (
                          <div key={subject} className="space-y-3 p-4 rounded-lg bg-gray-50 dark:bg-gray-900">
                            <h4 className="font-medium text-sm">{subject}</h4>
                            
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-500">Tingkat Kesulitan</span>
                                <span className={`font-medium ${getDifficultyColor(data.level)}`}>
                                  {getDifficultyText(data.level)}
                                </span>
                              </div>
                              
                              <div>
                                <div className="flex justify-between text-sm mb-1">
                                  <span className="text-gray-500">Skor</span>
                                  <span>{(data.score * 100).toFixed(1)}%</span>
                                </div>
                                <Progress 
                                  value={data.score * 100}
                                  color={
                                    data.score > 0.8 ? "danger" :
                                    data.score > 0.6 ? "warning" : "success"
                                  }
                                  className="w-full"
                                />
                              </div>
    
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-500">Waktu yang Diharapkan</span>
                                <span>{formatTime(data.expectedTime)}</span>
                              </div>
    
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-500">Faktor Waktu</span>
                                <span>{(data.timeFactor).toFixed(2)}x</span>
                              </div>
    
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-500">Faktor Kesalahan</span>
                                <span>{(data.errorFactor).toFixed(2)}x</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardBody>
                </Card>
                </>
    )
  }

  const renderWeaknessAnalysis = (report: TimeManagementReport) => {
    if (!report || !report.questions) return null;
    
    // Function to analyze strengths and weaknesses from hierarchical data
    const analyzeOverallPerformance = (hierarchicalData: HierarchicalWeakness[]) => {
      const getMasteryScore = (stats: WeaknessStats) => {
        const attempted = stats.total - stats.notAttempted;
        const correct = attempted - stats.incorrect;
        const accuracy = attempted ? (correct / attempted) : 0;
        const completion = attempted / stats.total;
        return (accuracy * completion) * 100;
      };
  
      // Sort by mastery score
      const sortedSubjects = [...hierarchicalData].sort((a, b) => 
        getMasteryScore(b.subjectStats) - getMasteryScore(a.subjectStats)
      );
  
      // Get all categories across all subjects
      const allCategories = hierarchicalData.flatMap(subject => 
        subject.categories.map(category => ({
          name: category.name,
          subject: subject.subject,
          score: getMasteryScore(category.stats),
          stats: category.stats
        }))
      ).sort((a, b) => b.score - a.score);
  
      return {
        strongestSubject: sortedSubjects[0],
        weakestSubject: sortedSubjects[sortedSubjects.length - 1],
        strongestCategory: allCategories[0],
        weakestCategory: allCategories[allCategories.length - 1]
      };
    };
  
    // Function to render a performance card for strengths/weaknesses
    const renderPerformanceCard = (stats: WeaknessStats, name: string, type: 'strength' | 'weakness') => {
      const attempted = stats.total - stats.notAttempted;
      const correct = attempted - stats.incorrect;
      const accuracy = attempted ? (correct / attempted) * 100 : 0;
      const completion = (attempted / stats.total) * 100;
      const masteryResult = getMasteryLevel(stats);
  
      const isStrength = type === 'strength';
      const bgColor = isStrength ? 'bg-green-50' : 'bg-red-50';
      const borderColor = isStrength ? 'border-green-200' : 'border-red-200';
      const textColor = isStrength ? 'text-green-700' : 'text-red-700';
      const Icon = isStrength ? Rocket : Target;
  
      return (
        <div className={`p-4 rounded-lg ${bgColor} border ${borderColor}`}>
          <div className="flex items-center gap-2 mb-2">
            <Icon className={`w-4 h-4 ${textColor}`} />
            <h4 className={`font-medium ${textColor}`}>
              {isStrength ? 'Area Terkuat' : 'Area Pengembangan'} - {name}
            </h4>
          </div>
          <div className="space-y-1">
            <p className={`text-sm ${textColor}`}>
              Akurasi: {accuracy.toFixed(1)}%
            </p>
            <p className={`text-sm ${textColor}`}>
              Progress: {completion.toFixed(1)}% selesai
            </p>
            <p className={`text-sm ${masteryResult.color}`}>
              Status: {masteryResult.level}
            </p>
            <div className="flex items-center gap-2 text-xs">
              <div className="flex items-center gap-1">
                <CheckCircle2 className={`w-3 h-3 ${textColor}`} />
                <span>{correct} benar</span>
              </div>
              <div className="flex items-center gap-1">
                <XCircle className={`w-3 h-3 ${textColor}`} />
                <span>{stats.incorrect} salah</span>
              </div>
              {stats.slow > 0 && (
                <div className="flex items-center gap-1">
                  <Clock className={`w-3 h-3 ${textColor}`} />
                  <span>{stats.slow} lambat</span>
                </div>
              )}
            </div>
          </div>
        </div>
      );
    };
  
    // Initialize hierarchical structure
    interface HierarchicalWeakness {
      subject: string;
      subjectStats: WeaknessStats;
      categories: {
        name: string;
        stats: WeaknessStats;
        keywords: {
          name: string;
          stats: WeaknessStats;
        }[];
      }[];
    }
  
    const hierarchicalWeaknesses: HierarchicalWeakness[] = [];
  
    // Process all questions to build hierarchical structure
    report.questions.forEach(q => {
      const subject = q.metadata.find(m => m.name === 'subject')?.value || 'Unknown';
      const category = q.metadata.find(m => m.name === 'category')?.value || 'Unknown';
      const keywordsStr = q.metadata.find(m => m.name === 'keywords')?.value || '';
      const keywords = keywordsStr.split(';').filter(k => k.trim());
  
      // Find or create subject
      let subjectData = hierarchicalWeaknesses.find(hw => hw.subject === subject);
      if (!subjectData) {
        subjectData = {
          subject,
          subjectStats: { total: 0, incorrect: 0, slow: 0, notAttempted: 0 },
          categories: []
        };
        hierarchicalWeaknesses.push(subjectData);
      }
  
      // Update subject stats
      subjectData.subjectStats.total++;
      if (!q.isAttempted) {
        subjectData.subjectStats.notAttempted++;
      } else {
        if (!q.isCorrect) subjectData.subjectStats.incorrect++;
        if (q.timeStatus === 'slow') subjectData.subjectStats.slow++;
      }
  
      // Find or create category
      let categoryData = subjectData.categories.find(c => c.name === category);
      if (!categoryData) {
        categoryData = {
          name: category,
          stats: { total: 0, incorrect: 0, slow: 0, notAttempted: 0 },
          keywords: []
        };
        subjectData.categories.push(categoryData);
      }
  
      // Update category stats
      categoryData.stats.total++;
      if (!q.isAttempted) {
        categoryData.stats.notAttempted++;
      } else {
        if (!q.isCorrect) categoryData.stats.incorrect++;
        if (q.timeStatus === 'slow') categoryData.stats.slow++;
      }
  
      // Process keywords
      keywords.forEach(keyword => {
        // Ensure categoryData exists before proceeding
        if (categoryData) {
          let keywordData = categoryData.keywords.find(k => k.name === keyword);
          if (!keywordData) {
            keywordData = {
              name: keyword,
              stats: { total: 0, incorrect: 0, slow: 0, notAttempted: 0 }
            };
            categoryData.keywords.push(keywordData);
          }
      
          // Update keyword stats
          keywordData.stats.total++;
          if (!q.isAttempted) {
            keywordData.stats.notAttempted++;
          } else {
            if (!q.isCorrect) keywordData.stats.incorrect++;
            if (q.timeStatus === 'slow') keywordData.stats.slow++;
          }
        }
      });
    });
  
    // Sort subjects by mastery level (ascending)
    hierarchicalWeaknesses.sort((a, b) => {
      const aAttempted = a.subjectStats.total - a.subjectStats.notAttempted;
      const bAttempted = b.subjectStats.total - b.subjectStats.notAttempted;
      const aAccuracy = aAttempted ? ((aAttempted - a.subjectStats.incorrect) / aAttempted) : 0;
      const bAccuracy = bAttempted ? ((bAttempted - b.subjectStats.incorrect) / bAttempted) : 0;
      return aAccuracy - bAccuracy;
    });
  
    // Keep getMasteryLevel function the same...
    const getMasteryLevel = (stats: WeaknessStats) => {
      const attempted = stats.total - stats.notAttempted;
      const correct = attempted - stats.incorrect;
      
      const accuracy = attempted ? (correct / attempted) : 0;
      const completion = attempted / stats.total;
      const overallScore = (accuracy * completion) * 100;
  
      if (completion == 0) return { 
        level: 'Belum Dikerjakan', 
        color: 'text-gray-500', 
        bg: 'bg-gray-50' 
      };
  
      if (completion < 0.5 && completion != 0) return { 
        level: 'Pengerjaan Belum Maksimal', 
        color: 'text-gray-500', 
        bg: 'bg-gray-50' 
      };
      
      if (overallScore >= 80) return { 
        level: 'Sangat Baik', 
        color: 'text-green-600', 
        bg: 'bg-green-50' 
      };
      if (overallScore >= 60) return { 
        level: 'Baik', 
        color: 'text-green-500', 
        bg: 'bg-green-50' 
      };
      if (overallScore >= 40) return { 
        level: 'Perlu Latihan', 
        color: 'text-yellow-500', 
        bg: 'bg-yellow-50' 
      };
      if (overallScore >= 20) return { 
        level: 'Perlu Perhatian', 
        color: 'text-orange-500', 
        bg: 'bg-orange-50' 
      };
      return { 
        level: 'Perlu Fokus', 
        color: 'text-red-500', 
        bg: 'bg-red-50' 
      };
    };
  
    const renderStatsDetail = (stats: WeaknessStats) => {
      const attempted = stats.total - stats.notAttempted;
      const correct = attempted - stats.incorrect;
      const accuracy = attempted ? (correct / attempted) : 0;
      const completion = attempted / stats.total;
      const overallProgress = (accuracy * completion) * 100;
  
      return (
        <div className="space-y-2">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <Info className="w-4 h-4" />
              <span>Total: {stats.total}</span>
            </div>
            
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <CheckCircle2 className="w-4 h-4 text-green-500" />
              <span>Benar: {correct}</span>
            </div>
            
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <XCircle className="w-4 h-4 text-red-500" />
              <span>Salah: {stats.incorrect}</span>
            </div>
  
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <AlertCircle className="w-4 h-4 text-gray-500" />
              <span>Belum: {stats.notAttempted}</span>
            </div>
          </div>
          
          {stats.slow > 0 && (
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <Clock className="w-4 h-4 text-yellow-500" />
              <span>Lambat: {stats.slow}</span>
            </div>
          )}
  
          <div className="mt-2">
            <div className="flex justify-between text-xs mb-1">
              <span>Tingkat Penguasaan</span>
              <span>{overallProgress.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  overallProgress >= 80 ? 'bg-green-500' :
                  overallProgress >= 60 ? 'bg-yellow-500' :
                  'bg-red-500'
                }`}
                style={{ width: `${overallProgress}%` }}
              />
            </div>
  
            <div className="flex justify-between text-xs mt-2 text-gray-500">
              <span>Progress Pengerjaan</span>
              <span>{(completion * 100).toFixed(1)}%</span>
            </div>
          </div>
        </div>
      );
    };
  
    const performance = analyzeOverallPerformance(hierarchicalWeaknesses);
  
    return (
      <Card className="mb-4">
        <CardBody className="overflow-visible">
          <div className="space-y-6">
            {/* Header and guide */}
            <div>
              <div className="flex items-center gap-2 mb-4">
                <AlertTriangle className="w-5 h-5 text-primary" />
                <h3 className="text-lg font-semibold">Analisis Area Pengembangan</h3>
              </div>
  
              {/* Performance Summary Section */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-700 mb-4">Ringkasan Performa (Kekuatan dan Kelemahan):</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {performance.strongestSubject && renderPerformanceCard(
                    performance.strongestSubject.subjectStats,
                    performance.strongestSubject.subject,
                    'strength'
                  )}
                  
                  {performance.weakestSubject && renderPerformanceCard(
                    performance.weakestSubject.subjectStats,
                    performance.weakestSubject.subject,
                    'weakness'
                  )}
                </div>
  
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {performance.strongestCategory && renderPerformanceCard(
                    performance.strongestCategory.stats,
                    performance.strongestCategory.name,
                    'strength'
                  )}
                  
                  {performance.weakestCategory && renderPerformanceCard(
                    performance.weakestCategory.stats,
                    performance.weakestCategory.name,
                    'weakness'
                  )}
                </div>
              </div>
              
                <div className="p-4 rounded-lg bg-blue-50 border border-blue-200 mb-6">
                  <h4 className="font-medium text-blue-700 mb-2">Panduan Membaca Detail Performa dan Pengembangan:</h4>
                  <ul className="space-y-1 text-sm text-blue-600">
                    <li>• Sangat Baik (≥80%): Kamu menguasai materi dengan sangat baik</li>
                    <li>• Baik (60-79%): Pemahaman baik, perlu sedikit pengayaan</li>
                    <li>• Perlu Latihan (40-59%): Butuh latihan tambahan</li>
                    <li>• Perlu Perhatian (20-39%): Fokus pada pemahaman dasar</li>
                    <li>• Perlu Fokus (≤19%): Prioritaskan pembelajaran di area ini</li>
                  </ul>
                </div>
              </div>
    
              <div className="space-y-8">
                {hierarchicalWeaknesses.map((subject) => {
                  const subjectMastery = getMasteryLevel(subject.subjectStats);
                  
                  return (
                    <div key={subject.subject} className="relative">
                      {/* Subject section with thread line container */}
                      <div className="flex">
                        <div className="relative flex flex-col items-center mr-4 min-h-full">
                          <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                            <BookOpen className="w-5 h-5 text-white" />
                          </div>
                          <div className="w-0.5 bg-primary absolute h-full top-8 left-1/2 -translate-x-1/2" />
                        </div>
    
                        {/* Subject Content */}
                        <div className="flex-grow">
                          <div className={`p-4 rounded-lg ${subjectMastery.bg} border-l-4 border-${subjectMastery.color}/50`}>
                            <div className="flex justify-between items-center mb-3">
                              <div>
                                <h4 className="font-semibold text-lg">Mata Uji: {subject.subject}</h4>
                                <p className="text-sm text-gray-500">Penguasaan materi secara keseluruhan</p>
                              </div>
                              <span className={`text-sm ${subjectMastery.color} font-medium px-3 py-1 rounded-full bg-white shadow-sm`}>
                                {subjectMastery.level}
                              </span>
                            </div>
                            {renderStatsDetail(subject.subjectStats)}
                          </div>
    
                          {/* Categories */}
                          <div className="mt-4 space-y-6">
                            {subject.categories.map((category, idx) => {
                              const categoryMastery = getMasteryLevel(category.stats);
                              
                              return (
                                <div key={category.name} className="relative ml-8">
                                  {/* Category with thread line */}
                                  <div className="flex">
                                    <div className="relative flex flex-col items-center mr-4">
                                      <div className="w-7 h-7 rounded-full bg-blue-400 flex items-center justify-center">
                                        <Brain className="w-4 h-4 text-white" />
                                      </div>
                                        <div className="w-0.5 bg-blue-400 absolute h-full top-7 left-1/2 -translate-x-1/2" />
                                    </div>
    
                                    {/* Category Content */}
                                    <div className="flex-grow">
                                      <div className={`p-4 rounded-lg ${categoryMastery.bg} border-l-4 border-${categoryMastery.color}/50`}>
                                        <div className="flex justify-between items-center mb-6">
                                          <div>
                                            <h5 className="font-medium">Kategori: {category.name}</h5>
                                            <p className="text-sm text-gray-500">Penguasaan topik spesifik</p>
                                          </div>
                                          <span className={`text-sm ${categoryMastery.color} font-medium px-3 py-1 rounded-full bg-white shadow-sm`}>
                                            {categoryMastery.level}
                                          </span>
                                        </div>
                                        {renderStatsDetail(category.stats)}
                                      </div>
    
                                      {/* Keywords */}
                                      <div className="mt-4 ml-8">
                                        <div className="flex">
                                          <div className="relative flex flex-col items-center mr-4">
                                            <div className="w-6 h-6 rounded-full bg-indigo-400 flex items-center justify-center">
                                              <Timer className="w-4 h-4 text-white" />
                                            </div>
                                            <div className="w-0.5 bg-indigo-400 absolute h-full top-7 left-1/2 -translate-x-1/2" />
                                          </div>
                                          <div className="flex-grow">
                                            <h6 className="text-sm font-medium text-gray-600 mb-4">Konsep-konsep Terkait</h6>
                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                              {category.keywords.map((keyword) => {
                                                const keywordMastery = getMasteryLevel(keyword.stats);
                                                
                                                return (
                                                  <div 
                                                    key={keyword.name}
                                                    className={`p-4 rounded-lg ${keywordMastery.bg} border-l-4 border-${keywordMastery.color}/50`}
                                                  >
                                                    <div className="flex justify-between items-center mb-3">
                                                      <div>
                                                        <h6 className="font-medium">{keyword.name}</h6>
                                                        <p className="text-xs text-gray-500">Konsep spesifik</p>
                                                      </div>
                                                      <span className={`text-xs ${keywordMastery.color} font-medium px-2 py-1 rounded-full bg-white shadow-sm`}>
                                                        {keywordMastery.level}
                                                      </span>
                                                    </div>
                                                    {renderStatsDetail(keyword.stats)}
                                                  </div>
                                                );
                                              })}
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardBody>
        </Card>
      );
    }
  
    const getSubjectData = (): SubjectDataMap | null => {
      if (!report || !report.questions) return null;

      const subjectData: SubjectDataMap = {}
      
      // Now safely accessing questions after null check
      report.questions.forEach(question => {
        const subjectMeta = question.metadata.find(m => m.name === 'subject')
        if (subjectMeta) {
          const subject = subjectMeta.value
          if (!subjectData[subject]) {
            subjectData[subject] = {
              timeSpent: 0,
              averageTime: 0,
              correct: 0,
              total: 0,
              attempted: 0
            }
          }
          if (question.isAttempted) {
            subjectData[subject].timeSpent += question.timeSpent
            subjectData[subject].averageTime += question.averageTime
            if (question.isCorrect) subjectData[subject].correct++
            subjectData[subject].attempted++
          }
          subjectData[subject].total++
        }
      })

      return subjectData
    }

    const barChartData = (): ChartData<'bar'> | null => {
        // Make sure we have both user and global stats
        if (!report?.userStats?.subjectStats || !report?.globalStats?.subjectStats) return null;
      
        // Get all subjects
        const subjects = Object.keys(report.userStats.subjectStats);
        
        return {
          labels: subjects,
          datasets: [
            {
              label: 'Akurasi Kamu (%)',
              data: subjects.map(subject => 
                report.userStats.subjectStats[subject]?.accuracyRate || 0
              ),
              backgroundColor: 'rgba(99, 102, 241, 0.8)', // Indigo color
              borderRadius: 4,
            },
            {
              label: 'Akurasi Global (%)',
              data: subjects.map(subject => 
                report.globalStats.subjectStats[subject]?.AccuracyRate || 0
              ),
              backgroundColor: 'rgba(244, 114, 182, 0.8)', // Pink color
              borderRadius: 4,
            }
          ]
        }
      }

    const radarChartData = (): ChartData<'radar'> | null => {
      const subjectData = getSubjectData();
      if (!subjectData) return null;

      return {
        labels: Object.keys(subjectData),
        datasets: [{
          label: 'Efisiensi Waktu (%)',
          data: Object.values(subjectData).map(data => 
            data.attempted ? (data.averageTime / data.timeSpent * 100) : 0
          ),
          backgroundColor: 'rgba(99, 102, 241, 0.2)',
          borderColor: 'rgb(99, 102, 241)',
          borderWidth: 2,
          pointBackgroundColor: '#FFFFFF',
          pointBorderColor: 'rgb(99, 102, 241)',
          pointHoverBackgroundColor: '#FFFFFF',
          pointHoverBorderColor: 'rgb(99, 102, 241)',
        }]
      }
    }

    // Update chart options to accommodate multiple datasets
    const chartOptions: ChartOptions<'bar'> = {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
        y: {
            beginAtZero: true,
            max: 100,
            ticks: {
            callback: (value) => `${value}%`,
            }
        }
        },
        plugins: {
        legend: {
            position: 'top',
            align: 'start',
        },
        tooltip: {
            callbacks: {
            label: (context: TooltipItem<'bar'>) => {
                const label = context.dataset.label || ''
                return `${label}: ${context.parsed.y.toFixed(1)}%`
            }
            }
        }
        }
    }
    
    const radarOptions: ChartOptions<'radar'> = {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        r: {
          beginAtZero: true,
          max: 100,
          ticks: {
            stepSize: 20,
          },
          pointLabels: {
            font: {
              size: 12
            }
          }
        }
      },
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: (context: TooltipItem<'radar'>) => {
              return `Efisiensi: ${(context.parsed.r || 0).toFixed(1)}%`
            }
          }
        }
      }
    }

    const renderReportTypes = () => { 
      return (
        <div className="w-full space-y-6">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-4">
            <div className="flex flex-col md:flex-row gap-4 items-start md:items-center w-full md:w-auto">
              <Select
                label="Tipe Laporan"
                selectedKeys={new Set([reportType])}
                onChange={(e) => setReportType(e.target.value as ReportType)}
                className="w-full md:w-64"
              >
                {reportTypes.map((type) => (
                  <SelectItem key={type.value} textValue={type.label}>
                    {type.label}
                  </SelectItem>
                ))}
              </Select>
              
              {/* Only show session select when specific type is selected */}
              {reportType === 'specific' && sessions.length > 0 && (
                <Select
                  label="Pilih Sesi"
                  selectedKeys={new Set(sessionId ? [sessionId] : [])}
                  onChange={(e) => setSessionId(e.target.value)}
                  className="w-full md:w-80"
                >
                  {sessionOptions.map((session) => (
                    <SelectItem key={session.sessionId} textValue={session.displayName}>
                      {session.displayName}
                    </SelectItem>
                  ))}
                </Select>
              )}
            </div>
            
            {/* Right-aligned refresh button */}
            <Button 
              color="primary" 
              variant="flat" 
              isLoading={refreshing}
              startContent={refreshing ? null : <RefreshCw className="w-4 h-4" />}
              onClick={handleRefresh}
              className="h-10 whitespace-nowrap"
            >
              {refreshing ? 'Memperbarui Data...' : 'Perbarui Data'}
            </Button>
          </div>
        </div>
      );
    }
    
    return (
      <div className="w-full space-y-6">
        <div className="flex gap-4">
          {renderReportTypes()}
        </div>
    
        {loading ? (
          <div className="text-center flex flex-col items-center justify-center py-1">
            <div className="w-full flex justify-center">
              <DotLottieAnimation
                src="/dotlotties/ghibli-monster-loading-fan.lottie"
                autoplay
                loop
                width="250px"
                height="250px"
              />
            </div>
            <h1 className="text-center mt-4 text-xl font-semibold">Tunggu bentar ya!</h1>
            <div className="text-center max-w-lg mx-auto">
              Data sedang dikumpulkan, biasanya membutuhkan 1-2 menit... <br />
              Kamu bisa kembali lagi nanti ke halaman ini...
            </div>
          </div>
        ) : report && (
          <>
            {renderPerformanceInsights(report)}
            {renderDifficultyOverview(report)}
            {renderWeaknessAnalysis(report)}
          </>
        )}
      </div>
    );
  }