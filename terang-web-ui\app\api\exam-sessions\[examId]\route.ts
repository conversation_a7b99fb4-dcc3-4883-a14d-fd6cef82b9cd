// app/api/exam-sessions/[examId]/route.ts
import { NextResponse } from 'next/server';
import { createClient } from 'redis';
import { headers } from 'next/headers';
import { getUserId } from '@/app/lib/actions/account/actions';

// Redis client creators
const createReadClient = () => createClient({
  url: process.env.REDIS_URL_READ as string,
});

const createWriteClient = () => createClient({
  url: process.env.REDIS_URL_WRITE as string,
});

// Cache keys
const CACHE_KEYS = {
  lastTrialSession: (examId: string, userId: string | boolean | null, subject: string) => 
    `last_trial_session:${examId}:${userId}:${subject}`,
};

export async function GET(request: Request, props: { params: Promise<{ examId: string }> }) {
  const params = await props.params;
  const headersList = await headers();
  const subject = headersList.get('x-subject');

  if (!subject) {
    return NextResponse.json(
      { error: 'Subject is required' },
      { status: 400 }
    );
  }

  try {
    const userId = await getUserId();
    if (!userId) {
      return NextResponse.json(
        { error: 'User not authenticated' },
        { status: 401 }
      );
    }

    // Try to get from Redis first
    const redisRead = createReadClient();
    await redisRead.connect();
    
    const cacheKey = CACHE_KEYS.lastTrialSession(params.examId, userId, subject);
    const cachedData = await redisRead.get(cacheKey);
    await redisRead.disconnect();

    if (cachedData) {
      return NextResponse.json(JSON.parse(cachedData));
    }

    // If not in cache, fetch from backend
    const payload = { subject };
    const response = await fetch(
      `${process.env.BACKEND_BASE_URL}/v1/exam-sessions/last/${params.examId}/${userId}/practice`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.BACKEND_API_KEY as string,
        },
        body: JSON.stringify(payload)
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        return NextResponse.json({
          session_id: '',
          status: 'NOT_STARTED',
          progress: 0,
          subject: subject
        });
      }
      throw new Error('Failed to fetch session');
    }

    const data = await response.json();
    const sessionData = data.data;

    // Cache the successful response
    const redisWrite = createWriteClient();
    await redisWrite.connect();
    await redisWrite.set(cacheKey, JSON.stringify(sessionData), {
      EX: 3600 // 1 hour cache
    });
    await redisWrite.disconnect();

    return NextResponse.json(sessionData);
  } catch (error) {
    console.error('Error in exam session API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}