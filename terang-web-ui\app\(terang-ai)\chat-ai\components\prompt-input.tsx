"use client";

import { FC, FormEvent, useRef, useState, useEffect, KeyboardEventHandler } from "react";
import { Textarea } from "@heroui/react";
import { UploadSquare01Icon, Loading01Icon, StopIcon } from "hugeicons-react";

interface Props {
  onSubmit: (prompt: string) => void;
  isLoading: boolean;
  onStop?: () => void;
  showStop?: boolean;
  disabled?: boolean; // Add disabled prop
}

export const PromptInput: FC<Props> = ({ 
  onSubmit, 
  isLoading, 
  onStop, 
  showStop, 
  disabled = false // Default to false
}) => {
  const formRef = useRef<HTMLFormElement>(null);
  const [inputValue, setInputValue] = useState<string>("");
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const lastKeyWasShift = useRef<boolean>(false);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent.toLowerCase();
      const isMobileDevice = /mobile|android|ios|iphone|ipad|ipod|windows phone/i.test(userAgent);
      setIsMobile(isMobileDevice);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle form submission
  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (inputValue.trim().length === 0 || isLoading || disabled) return;
    onSubmit(inputValue.trim());
    setInputValue("");
  };

  const handleKeyDown: KeyboardEventHandler<HTMLInputElement> & ((e: KeyboardEvent) => void) = (e) => {
    // We can safely use the event object since the functionality is the same
    if (e.key === 'Shift') {
      lastKeyWasShift.current = true;
    }
    
    // Prevent form submission on Enter if disabled
    if (e.key === 'Enter' && !e.shiftKey && !isMobile) {
      e.preventDefault();
      if (inputValue.trim().length > 0 && !isLoading && !disabled) {
        onSubmit(inputValue.trim());
        setInputValue("");
      }
    }
  };
  
  const handleKeyUp: KeyboardEventHandler<HTMLInputElement> & ((e: KeyboardEvent) => void) = (e) => {
    if (e.key === 'Shift') {
      lastKeyWasShift.current = false;
    }
  };

  // Handle value change
  const handleValueChange = (value: string) => {
    if (disabled) return; // Prevent value changes if disabled
    
    if (isMobile) {
      setInputValue(value);
      return;
    }

    // If the value ends with newline but it's not from Shift+Enter,
    // we handle it in keyDown instead
    if (value.endsWith('\n') && !lastKeyWasShift.current) {
      return;
    }

    setInputValue(value);
  };

  // Handle clear
  const handleClear = () => {
    if (disabled) return; // Prevent clearing if disabled
    setInputValue("");
  };

  return (
    <form ref={formRef} onSubmit={handleSubmit} className="w-full flex gap-2 items-end">
      <Textarea
        name="message"
        value={inputValue}
        variant="bordered"
        radius="lg"
        minRows={1}
        maxRows={5}
        placeholder={
          disabled 
            ? "Kredit AI kamu sudah habis. Silakan beli kredit untuk melanjutkan." 
            : "Ketik pesan kamu disini..."
        }
        classNames={{
          input: "text-sm py-2",
          inputWrapper: `${
            disabled 
              ? "bg-gray-50 border-0 opacity-50 cursor-not-allowed" 
              : "bg-gray-100 border-0 hover:bg-gray-200 transition-colors"
          }`
        }}
        autoComplete="off"
        required
        isDisabled={disabled}
        onValueChange={handleValueChange}
        onClear={handleClear}
        onKeyDown={handleKeyDown}
        onKeyUp={handleKeyUp}
      />
      <div className="flex gap-2">
        {showStop && (
          <button
            type="button"
            onClick={onStop}
            disabled={disabled}
            className="bg-red-500 text-white p-4 rounded-2xl hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <StopIcon width={20} height={20} />
          </button>
        )}
        <button
          type="submit"
          disabled={isLoading || disabled}
          className="bg-blue-500 text-white p-4 rounded-2xl hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label={isLoading ? "Loading..." : disabled ? "Chat disabled" : "Send message"}
        >
          {isLoading ? (
            <Loading01Icon width={20} height={20} />
          ) : (
            <UploadSquare01Icon width={20} height={20} />
          )}
        </button>
      </div>
    </form>
  );
};

export default PromptInput;