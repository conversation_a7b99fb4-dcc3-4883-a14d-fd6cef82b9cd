"use client";
import React, { useState } from "react";
import { Accordion, AccordionItem } from "@heroui/react";
import { ChevronDownIcon } from "../icons/sidebar/chevron-down-icon";

interface Props {
  icon: React.ReactNode;
  title: string;
  items: React.ReactNode[];
}

export const CollapseItems = ({ icon, items, title }: Props) => {
  const [open, setOpen] = useState(false);

  return (
    <div className="flex gap-1 h-full items-center cursor-pointer">
      <Accordion className="px-0">
        <AccordionItem
          aria-label="Accordion 1"
          classNames={{
            indicator: "data-[open=true]:-rotate-180",
            trigger:
              "py-0 min-h-[32px] hover:bg-default-100 rounded-lg active:scale-[0.98] transition-transform px-2.5",
            title:
              "px-0 text-sm h-full w-full",
          }}
          indicator={<ChevronDownIcon />}
          title={
            <div className="flex items-center gap-1"> {/* Added items-center */}
              <span className="flex items-center">{icon}</span> {/* Added flex items-center */}
              <span className="flex items-center">{title}</span> {/* Added flex items-center */}
            </div>
          }
        >
          <div className="pl-8">
            {items.map((item, index) => (
              <div key={index} className="py-1.5 text-sm">
                {item}
              </div>
            ))}
          </div>
        </AccordionItem>
      </Accordion>
    </div>
  );
};