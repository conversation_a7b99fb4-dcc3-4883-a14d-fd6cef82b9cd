import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getBundleDetails } from '@/app/lib/actions/available-exams/actions';

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    const params = await props.params;

    // Check if user is authenticated
    if (!session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get the id from the URL params
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Bundle ID is required' },
        { status: 400 }
      );
    }

    // Call our action function with the id parameter
    const bundleDetails = await getBundleDetails(id);

    // Handle error cases
    if (bundleDetails === null) {
      return NextResponse.json(
        { error: 'Failed to fetch bundle details' },
        { status: 404 }
      );
    }

    // Return the bundle details
    return NextResponse.json(bundleDetails);
  } catch (error) {
    console.error('Error fetching bundle details:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}