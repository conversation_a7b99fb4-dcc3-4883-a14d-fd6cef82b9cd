import { NextRequest, NextResponse } from 'next/server';

const GOOGLE_SEARCH_API_URL = 'https://customsearch.googleapis.com/customsearch/v1';
const API_KEY = process.env.SEARCH_ENGINE_API_KEY;
const SEARCH_ENGINE_ID = process.env.SEARCH_ENGINE_ID;

interface GoogleSearchResponse {
  items?: any[];
  error?: {
    message: string;
    code: number;
  };
}

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Get the query parameters from the URL
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query');
    const languageRestrict = searchParams.get('lr') || 'lang_id'; // Default to Indonesian
    const numResults = searchParams.get('num') || '5';
    const safeSearch = searchParams.get('safe') || 'active';
    
    if (!query) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      );
    }

    // Build the Google Search API URL with all parameters properly encoded
    const params = new URLSearchParams();
    params.append('key', API_KEY || '');
    params.append('cx', SEARCH_ENGINE_ID || '');
    params.append('q', query);
    params.append('num', numResults);
    params.append('lr', languageRestrict);
    params.append('safe', safeSearch);

    // Call Google Custom Search API with properly encoded parameters
    const searchResponse = await fetch(`${GOOGLE_SEARCH_API_URL}?${params.toString()}`);

    if (!searchResponse.ok) {
      const errorData = await searchResponse.json() as GoogleSearchResponse;
      console.error('Google Search API error:', errorData);
      return NextResponse.json(
        { error: `Failed to fetch data from Google Search API: ${errorData.error?.message || searchResponse.statusText}` },
        { status: searchResponse.status }
      );
    }

    const searchData = await searchResponse.json() as GoogleSearchResponse;
    
    // Return formatted results
    return NextResponse.json({
      items: searchData.items || []
    });
  } catch (error) {
    console.error('Error in Google Search API route:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}