"use client";

import React, { useState, useEffect, useMemo, useCallback, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Breadcrumbs, BreadcrumbItem } from "@heroui/breadcrumbs";
import {
  <PERSON><PERSON>,
  Card,
  Chip,
  Tab,
  Tabs,
  Pagination,
} from "@heroui/react";
import { Swiper, SwiperSlide } from "swiper/react";
import { FreeMode, Mousewheel, Virtual } from "swiper/modules";
import ExamCard from "./exam-card";
import PremiumExamWrapper from "./locked-exam-wrapper";
import { File02Icon } from "hugeicons-react";
import { 
  fetchLastTrialSession, 
  AvailableExam
} from "./actions";

// Import the helper functions only (not the fetch functions)
import { getSubjectInfo } from './grading-helpers';

import "swiper/css";
import "swiper/css/free-mode";
import "swiper/css/pagination";

// Constants
const ITEMS_PER_PAGE = 10;
const LOCAL_STORAGE_CATEGORY_KEY = 'activeExamCategory';
const LOCAL_STORAGE_SUBJECT_PREFIX = 'activeSubject_';
const LOCAL_STORAGE_PAGE_PREFIX = 'activePage_';

// Interfaces
interface ActiveCategoryData {
  subjects: {
    [key: string]: Exam[];
  };
  pagination: {
    currentPage: number;
    totalPages: number;
    perSubjectLimit: number;
    subjectCounts: Array<{ Subject: string; Count: number }>;
    totalExams: number;
  };
}

// Define the interface for exam grading response
interface ExamGradingResponse {
  grading_info: Array<{
    category: string;
    passing_grade: Array<{
      nama: string;
      score: number;
      question_count: number;
    }>;
    program_thresholds: Array<{
      program: string;
      min_score: number;
    }>;
  }>;
  question_counts: Array<{
    CategoryID: string;
    Category: string;
    Subject: string;
    QuestionCount: number;
  }>;
}

interface TrialSession {
  id: string;
  sessionId: string;
  userId: string | boolean | null;
  examId: string;
  type: string;
  status: string;
  startTime: string;
  endTime: string;
  answers: string;
  flaggedQuestion: string;
  createdAt: string;
  modifiedAt: string;
  subject: string;
  remaining_duration?: string;
  exam_id?: string;
}

interface Exam {
  id: string;
  name: string;
  subname: string;
  description: string;
  baselinePrice: number;
  visibility: string;
  duration: string;
  type: string;
  subject: string;
  categories: Array<{
    id: string;
    name: string;
  }>;
}

interface ExamData {
  data: {
    [key: string]: Exam[];
  };
  pagination: {
    currentPage: number;
    totalPages: number;
    perSubjectLimit: number;
    subjectCounts: Array<{ Subject: string; Count: number }>;
    totalExams: number;
  };
}

interface CategoryGroup {
  category_name: string;
  categories: Array<{
    id: string;
    name: string;
  }>;
  subjects: {
    [key: string]: Exam[];
  };
  exam_count: number;
  // Add this field to store the cached total exam count
  total_exam_count?: number;
}

interface CategoryTheme {
  icon: string;
  bgColor: string;
  textColor: string;
  bgColorStyle?: React.CSSProperties;
}

// Update Props interface to include the pre-fetched data
interface Props {
  examData: ExamData;
  examDataPagination: ExamData;
  isPremium: boolean;
  userId: string | boolean | null;
  trialCompletionData: AvailableExam | null;
  gradingInfoData: ExamGradingResponse | null;
}

// Add interfaces for memoized component props
interface ExamContentSectionProps {
  subject: string;
  activeCategory: string;
  currentPage: number;
  contentLoading: boolean;
  categoryGroups: Record<string, CategoryGroup>;
  trialSessions: Record<string, TrialSession | null>;
  trialCompletion: AvailableExam | null;
  isPremium: boolean;
  userId: string | boolean | null;
  ITEMS_PER_PAGE: number;
  handlePageChange: (page: number) => void;
  getPassingGrade: (subject: string) => number | null;
  getTotalQuestions: (subject: string) => number;
}

interface CategoryPillsProps {
  categoryGroups: Record<string, CategoryGroup>;
  activeCategory: string;
  handleCategoryChange: (categoryName: string) => void;
  calculateCategoryTotalExams: (category: string) => number;
}

interface CategoryHeaderProps {
  activeCategory: string;
}

interface CustomPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  isDisabled: boolean;
}

// CSS Styles - Restored from original content
const containerStyle = `
.swiper-container-wrapper {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  overflow: visible;
}
`;

const tabStyles = `
.exam-tabs-container {
  position: sticky;
  top: 0;
  z-index: 10;
  background: white;
}

.content-container {
  overflow: hidden;
}

.custom-tabs-wrapper {
  position: relative;
  width: 100%;
  overflow: visible;
}

.custom-tabs {
  --tab-highlight: #3b82f6;
  width: 100%;
}

.custom-tabs [data-slot="tablist"] {
  background-color: #f8fafc;
  padding: 0;
  width: 100%;
  display: flex;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.custom-tabs [data-slot="tablist"]::-webkit-scrollbar {
  display: none;
}

.mobile-scroll-helper {
  display: none;
  background: linear-gradient(to bottom, #f8fafc, transparent);
  width: 100%;
  text-align: center;
  padding: 8px;
  color: #6b7280;
  font-size: 0.875rem;
}

.custom-tabs [data-slot="tab"] {
  position: relative;
  font-weight: 500;
  padding: 16px 24px;
  margin: 0;
  transition: all 0.2s ease;
  flex: 1;
  min-width: auto;
  height: 100%;
}

@media (max-width: 768px) {
  .mobile-scroll-helper {
    display: block;
    animation: fadeInOut 2s ease-in-out infinite;
  }

  .custom-tabs [data-slot="tab"] {
    padding: 16px 20px;
    flex: 0 0 70%;
    max-width: 70%;
  }
  
  .custom-tabs [data-slot="tablist"] {
    overflow-x: auto;
    justify-content: flex-start;
  }
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

.swiper-container-wrapper {
  overflow: visible !important;
  padding: 1rem;
}

.swiper-container {
  overflow: visible !important;
}

.exam-card-container {
  background: white;
  border-radius: 16px;
  height: 100%;
  transition: all 0.2s ease;
  width: 100%;
}

.category-pills-container {
  overflow-x: auto;
  white-space: nowrap;
  padding: 0.5rem 0;
  margin-bottom: 1rem;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.category-pills-container::-webkit-scrollbar {
  display: none;
}

.category-pill {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  margin-right: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-pill:hover {
  transform: scale(1.05);
}

.category-pill-active {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.category-header {
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.custom-tabs [data-slot="tab"][data-selected="true"]::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--tab-highlight, #3b82f6);
}
`;

// Additional CSS for transitions, pagination, and navigation
const additionalStyles = `
.tab-content-transition {
  transition: opacity 0.2s ease-in-out;
}

.tab-content-transition.loading {
  opacity: 0.6;
}

.tab-content-transition.loaded {
  opacity: 1;
}

/* Add this to maintain content height during tab changes */
.tab-content-container {
  min-height: 500px; /* Increase this value to ensure it's taller than any tab content */
  position: relative;
}

/* Prevent layout shifts */
.tabs-wrapper {
  contain: layout;
  position: relative;
}

/* Fix for tab content positioning */
.tab-panel {
  position: relative !important;
  height: 100% !important;
  overflow: visible !important;
}

/* Ensure tab content doesn't cause page jumps */
.tab-content-inner {
  position: relative;
}

/* Fix for tab list */
.tabs-list {
  position: relative;
  z-index: 10;
}

/* Fix for inactive tabs */
[data-slot="tabpanel"][hidden] {
  display: block !important;
  visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  pointer-events: none;
}
`;

const paginationStyles = `
  .custom-pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
  }
  
  .inline-pagination {
    margin: 0;
    display: flex;
    gap: 0.25rem;
  }
  
  .custom-pagination-button {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s;
    background-color: #f3f4f6;
    color: #374151;
    border: none;
    cursor: pointer;
  }
  
  .inline-pagination .custom-pagination-button {
    min-width: 2rem;
    height: 2rem;
  }
  
  .custom-pagination-button:hover:not(:disabled):not(.active) {
    background-color: #e5e7eb;
  }
  
  .custom-pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .custom-pagination-button.active {
    background-color: #3b82f6;
    color: white;
  }
  
  .custom-pagination-button svg {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  .inline-pagination .custom-pagination-button svg {
    width: 1rem;
    height: 1rem;
  }
  
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
  }
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .pagination-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1.5rem;
    gap: 1rem;
  }
  
  .pagination-info {
    font-size: 0.875rem;
    color: #6b7280;
  }
`;

const navigationStyles = `
  .subject-navigation {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    padding: 0.5rem 0;
    border-top: 1px solid #f3f4f6;
  }
  
  .subject-navigation-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s;
    background-color: #f3f4f6;
    color: #374151;
  }
  
  .subject-navigation-button:hover:not(:disabled) {
    background-color: #e5e7eb;
  }
  
  .subject-navigation-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .subject-navigation-button svg {
    width: 1.25rem;
    height: 1.25rem;
  }
  
  /* Remove the tab-navigation styles since we're removing those buttons */
  .custom-pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1.5rem;
  }
  
  .pagination-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1.5rem;
    gap: 1rem;
  }
  
  .pagination-info {
    font-size: 0.875rem;
    color: #6b7280;
  }
`;

// Helper Components
const EmptyState = () => (
  <Card className="w-full py-12">
    <div className="flex flex-col items-center justify-center text-center p-4">
      <File02Icon className="w-12 h-12 text-gray-400 mb-4" />
      <h3 className="text-lg font-semibold text-gray-900">
        Kamu belum punya kartu ujian
      </h3>
      <p className="text-sm text-gray-500 mt-2 max-w-sm">
        Silakan bisa diambil dulu kartu ujiannya, dan itu baru akan tampil di halaman ini.
      </p>
    </div>
  </Card>
);

// Helper function to get category theme
const getCategoryTheme = (categoryName: string): CategoryTheme => {
  const themes: Record<string, CategoryTheme> = {
    LPDP: {
      icon: "🎓", 
      bgColor: "bg-gradient-to-r from-blue-400 to-indigo-500",
      textColor: "text-white"
    },
    CPNS: {
      icon: "🏛️", 
      bgColor: "bg-gradient-to-r from-teal-600 to-emerald-400",
      textColor: "text-white"
    },
    UTBK: {
      icon: "📚", 
      bgColorStyle: { background: "linear-gradient(to right, #15BBBB, #7fd8d8)" },
      bgColor: "", // Empty string as we'll use the style approach
      textColor: "text-white"
    },
    BUMN: {
      icon: "🏢", 
      bgColor: "bg-gradient-to-r from-purple-400 to-indigo-500",
      textColor: "text-white"
    },
    Uncategorized: {
      icon: "✨", 
      bgColor: "bg-gradient-to-r from-gray-400 to-gray-600",
      textColor: "text-white"
    }
  };

  return themes[categoryName] || themes.Uncategorized;
};

// Create a separate memoized component for exam content
const ExamContentSection = ({ 
  subject, 
  activeCategory,
  currentPage,
  contentLoading,
  categoryGroups,
  trialSessions,
  trialCompletion,
  isPremium,
  userId,
  ITEMS_PER_PAGE,
  handlePageChange,
  getPassingGrade,
  getTotalQuestions
}: ExamContentSectionProps) => {
  // Move these references out of the parent component
  const tabContentRef = useRef<HTMLDivElement>(null);
  
  // Memoize these calculations
  const totalExams = useMemo(() => 
    categoryGroups[activeCategory]?.subjects?.[subject]?.length || 0, 
    [categoryGroups, activeCategory, subject]
  );
  
  const needsPagination = useMemo(() => 
    Math.ceil(totalExams / ITEMS_PER_PAGE) > 1,
    [totalExams, ITEMS_PER_PAGE]
  );
  
  // Pass the real reference to this component
  return (
    <div 
      ref={tabContentRef}
      className={`tab-content-container tab-content-transition ${contentLoading ? 'loading' : 'loaded'}`}
      style={{ minHeight: contentLoading ? '500px' : 'auto' }} // Prevent layout shifts
    >
      {contentLoading ? (
        <div className="p-4 min-h-[300px] flex items-center justify-center">
          <Spinner size="md" color="primary" />
        </div>
      ) : (
        <div className="p-4 tab-content-inner">  
          {/* Header with inline pagination */}
          <div className="header-container">
            <div className="header-left">
              <h3 className="text-lg font-bold">{subject}</h3>  
              {getPassingGrade(subject) && (  
                <Chip  
                  variant="flat"  
                  size="sm"  
                  classNames={{  
                    base: "bg-orange-100",  
                    content: "text-orange-600 font-medium",  
                  }}  
                >  
                  Passing Grade: {getPassingGrade(subject)}  
                </Chip>  
              )}
            </div>
            
            {/* Inline pagination */}
            {needsPagination && (
              <div className="inline-pagination">
                {/* Previous button */}
                <button 
                  className="custom-pagination-button"
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  aria-label="Previous page"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                
                {/* Next button */}
                <button 
                  className="custom-pagination-button"
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === Math.ceil(totalExams / ITEMS_PER_PAGE)}
                  aria-label="Next page"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            )}
          </div>
      
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">  
            {categoryGroups[activeCategory]?.subjects?.[subject]?.slice(
              (currentPage - 1) * ITEMS_PER_PAGE, 
              currentPage * ITEMS_PER_PAGE
            ).map((exam, index) => (  
              <div key={`exam-${exam.id}`} className="exam-card-container p-2">  
                <PremiumExamWrapper  
                  index={(currentPage - 1) * ITEMS_PER_PAGE + index}  
                  subject={subject}  
                  isPremium={isPremium}  
                >  
                  <ExamCard  
                    key={`card-${exam.id}-${(currentPage - 1) * ITEMS_PER_PAGE + index}`}
                    index={(currentPage - 1) * ITEMS_PER_PAGE + index}  
                    examId={exam.id}  
                    subject={subject}  
                    totalQuestions={getTotalQuestions(subject)}  
                    passingGrade={getPassingGrade(subject)}  
                    className="w-full h-full"  
                    maxLives={isPremium ? 9999999 : 3}  
                    userId={userId}  
                    isPremium={isPremium}  
                    trialSession={trialSessions[exam.id]}  
                    trialCompletionData={trialCompletion}  
                  />  
                </PremiumExamWrapper>  
              </div>  
            ))}  
          </div>  
      
          {/* Bottom pagination (full) */}
          {needsPagination && (  
            <CustomPagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalExams / ITEMS_PER_PAGE)}
              onPageChange={handlePageChange}
              isDisabled={contentLoading}
            />
          )}  
        </div>
      )}
    </div>
  );
}

// Memoized CategoryPills component
const CategoryPills = React.memo<CategoryPillsProps>(({ 
  categoryGroups, 
  activeCategory, 
  handleCategoryChange,
  calculateCategoryTotalExams
}) => {
  return (
    <div className="category-pills-container">
      {Object.entries(categoryGroups).map(([categoryName, group]) => {
        const theme = getCategoryTheme(categoryName);
        const totalExamCount = calculateCategoryTotalExams(categoryName);
        
        return (
          <button
            key={categoryName}
            onClick={() => handleCategoryChange(categoryName)}
            className={`category-pill ${
              activeCategory === categoryName
                ? `${theme.bgColor} ${theme.textColor} category-pill-active`
                : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            }`}
            style={activeCategory === group.category_name && theme.bgColorStyle ? theme.bgColorStyle : {}}
          >
            <span className="mr-2">{theme.icon}</span>
            <span className="font-medium">{categoryName}</span>
            <span className="ml-2 bg-white bg-opacity-25 text-xs px-2 py-1 rounded-full">
              {totalExamCount}
            </span>
          </button>
        );
      })}
    </div>
  );
});

CategoryPills.displayName = 'CategoryPills';

// Memoized CategoryHeader component
const CategoryHeader = React.memo<CategoryHeaderProps>(({ activeCategory }) => {
  if (!activeCategory) return null;
  
  const theme = getCategoryTheme(activeCategory);
  
  return (
    <div className={`category-header ${theme.bgColor} ${theme.textColor}`} style={theme.bgColorStyle}>
      <div className="flex items-center mb-2">
        <span className="text-3xl mr-3">{theme.icon}</span>
        <h2 className="text-2xl font-bold">{activeCategory}</h2>
      </div>
      <p className="opacity-90">
        {activeCategory === "LPDP" && "Simulasi tes untuk beasiswa LPDP dalam berbagai paket pilihan."}
        {activeCategory === "CPNS" && "Persiapkan diri Kamu untuk tes CPNS dengan simulasi yang terakurat."}
        {activeCategory === "BUMN" && "Latihan soal untuk persiapan tes masuk BUMN terkemuka."}
        {activeCategory === "UTBK" && "Latihan SNBT UTBK terbaru sesuai kurikulum."}
        {activeCategory === "Uncategorized" && "Berbagai tes persiapan lainnya untuk menunjang persiapan Kamu."}
      </p>
    </div>
  );
});

CategoryHeader.displayName = 'CategoryHeader';

// Memoized CustomPagination
const CustomPagination = React.memo<CustomPaginationProps>(({ 
  currentPage, 
  totalPages, 
  onPageChange, 
  isDisabled 
}) => {
  // Calculate which page numbers to show
  const pageNumbers = useMemo(() => {
    const numbers: Array<number | string> = [];
    
    // Always show first page
    numbers.push(1);
    
    // Calculate range around current page
    let startPage = Math.max(2, currentPage - 1);
    let endPage = Math.min(totalPages - 1, currentPage + 1);
    
    // Add ellipsis after first page if needed
    if (startPage > 2) {
      numbers.push('ellipsis-start');
    }
    
    // Add pages in range
    for (let i = startPage; i <= endPage; i++) {
      numbers.push(i);
    }
    
    // Add ellipsis before last page if needed
    if (endPage < totalPages - 1) {
      numbers.push('ellipsis-end');
    }
    
    // Always show last page if there's more than one page
    if (totalPages > 1) {
      numbers.push(totalPages);
    }
    
    return numbers;
  }, [currentPage, totalPages]);
  
  // Calculate the range of items being displayed
  const itemsPerPage = ITEMS_PER_PAGE;
  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalPages * itemsPerPage);
  const totalItems = totalPages * itemsPerPage;
  
  return (
    <div className="pagination-wrapper">
      <div className="pagination-info">
        Showing {startItem}-{endItem} of {totalItems} items
      </div>
      
      <div className="custom-pagination">
        {/* Previous button */}
        <button 
          className="custom-pagination-button"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={isDisabled || currentPage === 1}
          aria-label="Previous page"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        {/* Page numbers */}
        {pageNumbers.map((page, index) => {
          if (page === 'ellipsis-start' || page === 'ellipsis-end') {
            return (
              <button 
                key={`ellipsis-${index}`}
                className="custom-pagination-button"
                disabled={true}
              >
                ...
              </button>
            );
          }
          
          return (
            <button 
              key={`page-${page}`}
              className={`custom-pagination-button ${currentPage === page ? 'active' : ''}`}
              onClick={() => onPageChange(page as number)}
              disabled={isDisabled}
              aria-label={`Page ${page}`}
              aria-current={currentPage === page ? 'page' : undefined}
            >
              {page}
            </button>
          );
        })}
        
        {/* Next button */}
        <button 
          className="custom-pagination-button"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={isDisabled || currentPage === totalPages}
          aria-label="Next page"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </div>
    </div>
  );
});

CustomPagination.displayName = 'CustomPagination';

// Main component with optimizations
const MyTrials: React.FC<Props> = ({ 
  examData, 
  examDataPagination, 
  isPremium, 
  userId,
  trialCompletionData,
  gradingInfoData
}) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const scrollPositionRef = useRef<number>(0);
  const isFirstMount = useRef<boolean>(true);
  const isMountedRef = useRef<boolean>(true);
  
  // Create stable references to the props data
  const trialCompletionRef = useRef<AvailableExam | null>(trialCompletionData);
  const gradingInfoRef = useRef<ExamGradingResponse | null>(gradingInfoData);
  
  // Store cache in a ref to avoid re-renders
  const gradingInfoCache = useRef<Record<string, {
    passingGrade: number | null;
    totalQuestions: number;
  }>>({});
  
  // Initialize cache from the provided data
  useEffect(() => {
    if (gradingInfoData?.question_counts) {
      gradingInfoData.question_counts.forEach(item => {
        gradingInfoCache.current[item.Subject] = {
          passingGrade: getSubjectInfo(item.Subject, gradingInfoData).passingGrade,
          totalQuestions: item.QuestionCount
        };
      });
    }
    
    // Update refs
    trialCompletionRef.current = trialCompletionData;
    gradingInfoRef.current = gradingInfoData;
  }, [gradingInfoData, trialCompletionData]);
  
  // State management (keeping necessary state only)
  const [loading, setLoading] = useState<boolean>(true);
  const [activeCategory, setActiveCategory] = useState<string>("");
  const [selectedSubject, setSelectedSubject] = useState<string>("");
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [trialSessions, setTrialSessions] = useState<Record<string, TrialSession | null>>({});
  const [contentLoading, setContentLoading] = useState<boolean>(false);
  const [canNavigatePrev, setCanNavigatePrev] = useState<boolean>(false);
  const [canNavigateNext, setCanNavigateNext] = useState<boolean>(true);

  // Set up mounted ref for cleanup
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Extract subject initialization logic for reuse
  const getInitialSubjectForCategory = useCallback((
    categoryName: string, 
    categoryGroups: Record<string, CategoryGroup>
  ): string => {
    let subjectToActivate = '';
    
    if (categoryGroups[categoryName]) {
      const subjects = Object.keys(categoryGroups[categoryName].subjects);
      if (subjects.length > 0) {
        try {
          const savedSubject = localStorage.getItem(`${LOCAL_STORAGE_SUBJECT_PREFIX}${categoryName}`);
          if (savedSubject && subjects.includes(savedSubject)) {
            subjectToActivate = savedSubject;
          } else {
            subjectToActivate = subjects[0];
            localStorage.setItem(`${LOCAL_STORAGE_SUBJECT_PREFIX}${categoryName}`, subjectToActivate);
          }
        } catch (error) {
          subjectToActivate = subjects[0];
        }
      }
    }
    
    return subjectToActivate;
  }, []);

  // Extract organized category data from examData - optimized with explicit dependencies
  const categoryGroups = useMemo<Record<string, CategoryGroup>>(() => {
    if (!examData?.data) return {};
    
    const categories: Record<string, CategoryGroup> = {};
    
    // Loop through all subjects and their exams
    Object.entries(examData.data).forEach(([subject, exams]) => {
      exams.forEach(exam => {
        // Get categories from the exam, or use "Uncategorized" if none found
        const examCategories = exam.categories && exam.categories.length > 0 
          ? exam.categories 
          : [{ id: "uncategorized", name: "Uncategorized" }];
        
        examCategories.forEach(category => {
          const categoryName = category.name;
          
          // Initialize category if it doesn't exist
          if (!categories[categoryName]) {
            categories[categoryName] = {
              category_name: categoryName,
              categories: [category],
              subjects: {},
              exam_count: 0,
              total_exam_count: 0
            };
          }
          
          // Make sure the subject array exists for this category
          if (!categories[categoryName].subjects[subject]) {
            categories[categoryName].subjects[subject] = [];
          }
          
          // Add the exam to its respective category and subject (avoid duplicates)
          const alreadyExists = categories[categoryName].subjects[subject].some(
            existingExam => existingExam.id === exam.id
          );
          
          if (!alreadyExists) {
            categories[categoryName].subjects[subject].push(exam);
            categories[categoryName].exam_count++;
          }
        });
      });
    });
    
    // Pre-calculate total exams for each category
    Object.keys(categories).forEach(categoryName => {
      const subjectCounts = examData.pagination?.subjectCounts || [];
      const categorySubjects = Object.keys(categories[categoryName].subjects);
      
      const matchingSubjectCounts = subjectCounts.filter(item => 
        categorySubjects.includes(item.Subject)
      );
      
      if (matchingSubjectCounts.length > 0) {
        categories[categoryName].total_exam_count = matchingSubjectCounts.reduce(
          (total, item) => total + item.Count, 0
        );
      } else {
        let totalExams = 0;
        Object.values(categories[categoryName].subjects).forEach(exams => {
          totalExams += exams.length;
        });
        categories[categoryName].total_exam_count = totalExams;
      }
    });
    
    return categories;
  }, [
    examData?.data, 
    examData?.pagination?.subjectCounts
  ]);

  // Simplified query string creator with minimal dependency
  const createQueryString = useCallback((updates: { page?: number; category_id?: string; subject?: string }) => {
    const newParams = new URLSearchParams(searchParams?.toString() || "");
    
    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        newParams.set(key, String(value));
      }
    });
    
    return newParams.toString();
  }, [searchParams]);

  // Calculate accurate category total exams with stable reference
  const calculateCategoryTotalExams = useCallback((category: string): number => {
    if (!categoryGroups[category]) return 0;
    // Use pre-calculated value from categoryGroups
    return categoryGroups[category].total_exam_count || 0;
  }, [categoryGroups]);

  // Optimized handleCategoryChange with better state management
  const handleCategoryChange = useCallback((categoryName: string): void => {
    if (categoryName === activeCategory) return;
    
    // Save scroll position BEFORE showing loading state
    scrollPositionRef.current = window.scrollY;
    
    // Show loading state
    setContentLoading(true);
    
    try {
      // Get the category ID
      const categoryObj = categoryGroups[categoryName]?.categories?.[0];
      const categoryId = categoryObj?.id || "uncategorized";
      
      // Store in localStorage for persistence
      try {
        localStorage.setItem(LOCAL_STORAGE_CATEGORY_KEY, categoryName);
      } catch (error) {
        console.warn("Failed to save category to localStorage:", error);
      }
      
      // Find the subject to activate for this category
      const subjectToActivate = getInitialSubjectForCategory(categoryName, categoryGroups);
      
      // Batch state updates
      setActiveCategory(categoryName);
      if (subjectToActivate) {
        setSelectedSubject(subjectToActivate);
      }
      setCurrentPage(1);
      
      // Update URL with new category, subject, and reset to page 1
      const queryString = createQueryString({ 
        category_id: categoryId,
        subject: subjectToActivate,
        page: 1
      });
      
      // Use replace instead of push to avoid history entries
      router.replace(`?${queryString}`, { scroll: false });
    } catch (error) {
      console.error("Error in handleCategoryChange:", error);
    }
    
    // Set loading back to false after a short delay
    setTimeout(() => {
      if (isMountedRef.current) {
        setContentLoading(false);
      }
    }, 150);
  }, [activeCategory, categoryGroups, getInitialSubjectForCategory, createQueryString, router]);

  // Optimized handlePageChange with fewer dependencies
  const handlePageChange = useCallback((page: number) => {
    if (!activeCategory || !selectedSubject || page === currentPage) return;
    
    // Save current scroll position BEFORE making any changes
    scrollPositionRef.current = window.scrollY;
    
    // Show content loading indicator
    setContentLoading(true);
    
    try {
      // Store the value in localStorage
      try {
        localStorage.setItem(`${LOCAL_STORAGE_PAGE_PREFIX}${activeCategory}_${selectedSubject}`, String(page));
      } catch (error) {
        console.warn("Failed to save page to localStorage:", error);
      }
      
      // Get category ID for the URL
      const categoryObj = categoryGroups[activeCategory]?.categories?.[0];
      const categoryId = categoryObj?.id || "uncategorized";
      
      // Update state first
      setCurrentPage(page);
      
      // Then update URL
      const queryString = createQueryString({ 
        page,
        category_id: categoryId,
        subject: selectedSubject
      });
      
      router.replace(`?${queryString}`, { scroll: false });
    } catch (error) {
      console.error("Error in handlePageChange:", error);
    }
    
    // Set content loading back to false after a short delay
    setTimeout(() => {
      if (isMountedRef.current) {
        setContentLoading(false);
      }
    }, 150);
  }, [activeCategory, selectedSubject, currentPage, categoryGroups, createQueryString, router]);

  // Optimized handleSubjectSelect with fewer dependencies
  const handleSubjectSelect = useCallback((subject: string): void => {
    if (subject === selectedSubject) return;
    
    // Save current scroll position BEFORE making any changes
    scrollPositionRef.current = window.scrollY;
    
    // Set content loading
    setContentLoading(true);
    
    try {
      // Reset trial sessions when changing subjects
      setTrialSessions({});
      
      // Store subject preference
      try {
        localStorage.setItem(`${LOCAL_STORAGE_SUBJECT_PREFIX}${activeCategory}`, subject);
      } catch (error) {
        console.warn("Failed to save subject to localStorage:", error);
      }
      
      // Get page to activate - default to 1
      let pageToActivate = 1;
      
      if (activeCategory) {
        try {
          const savedPage = localStorage.getItem(`${LOCAL_STORAGE_PAGE_PREFIX}${activeCategory}_${subject}`);
          if (savedPage) {
            const parsedPage = parseInt(savedPage);
            if (parsedPage > 0) {
              pageToActivate = parsedPage;
            } else {
              localStorage.setItem(`${LOCAL_STORAGE_PAGE_PREFIX}${activeCategory}_${subject}`, '1');
            }
          } else {
            localStorage.setItem(`${LOCAL_STORAGE_PAGE_PREFIX}${activeCategory}_${subject}`, '1');
          }
        } catch (error) {
          localStorage.setItem(`${LOCAL_STORAGE_PAGE_PREFIX}${activeCategory}_${subject}`, '1');
        }
        
        // Get category ID for URL
        const categoryObj = categoryGroups[activeCategory]?.categories?.[0];
        const categoryId = categoryObj?.id || "uncategorized";
        
        // Batch state updates
        setSelectedSubject(subject);
        setCurrentPage(pageToActivate);
        
        // Calculate total pages
        const estimatedTotalPages = 
          categoryGroups[activeCategory]?.subjects[subject]?.length
          ? Math.ceil(categoryGroups[activeCategory].subjects[subject].length / ITEMS_PER_PAGE)
          : 1;
        setTotalPages(estimatedTotalPages);
        
        // Update URL
        const queryString = createQueryString({ 
          category_id: categoryId,
          subject: subject,
          page: pageToActivate
        });
        
        router.replace(`?${queryString}`, { scroll: false });
      }
    } catch (error) {
      console.error("Error in handleSubjectSelect:", error);
    }
    
    // Set content loading back to false after a short delay
    setTimeout(() => {
      if (isMountedRef.current) {
        setContentLoading(false);
      }
    }, 150);
  }, [activeCategory, categoryGroups, createQueryString, router, selectedSubject]);

  // Optimized tab navigation
  const navigateTab = useCallback((direction: 'prev' | 'next') => {
    if (!activeCategory || !selectedSubject) return;
    
    // Save current scroll position BEFORE navigating
    scrollPositionRef.current = window.scrollY;
    
    const subjects = Object.keys(categoryGroups[activeCategory]?.subjects || {}).reverse();
    const currentIndex = subjects.indexOf(selectedSubject);
    
    if (direction === 'prev' && currentIndex > 0) {
      handleSubjectSelect(subjects[currentIndex - 1]);
    } else if (direction === 'next' && currentIndex < subjects.length - 1) {
      handleSubjectSelect(subjects[currentIndex + 1]);
    }
  }, [activeCategory, categoryGroups, handleSubjectSelect, selectedSubject]);

  // Optimized getTotalQuestions with cache - using ref to avoid re-renders
  const getTotalQuestions = useCallback((subject: string): number => {
    // Check cache first
    if (gradingInfoCache.current[subject]?.totalQuestions !== undefined) {
      return gradingInfoCache.current[subject].totalQuestions;
    }
    
    // If not in cache, compute and store
    if (gradingInfoRef.current) {
      const info = getSubjectInfo(subject, gradingInfoRef.current);
      
      // Update cache
      if (!gradingInfoCache.current[subject]) {
        gradingInfoCache.current[subject] = { 
          passingGrade: null, 
          totalQuestions: 0 
        };
      }
      gradingInfoCache.current[subject].totalQuestions = info.totalQuestions || 0;
      
      return info.totalQuestions || 0;
    }
    
    return 0;
  }, []); // No dependencies since we're using refs

  // Optimized getPassingGrade with cache - using ref to avoid re-renders
  const getPassingGrade = useCallback((subject: string): number | null => {
    // Check cache first
    if (gradingInfoCache.current[subject]?.passingGrade !== undefined) {
      return gradingInfoCache.current[subject].passingGrade;
    }
    
    // If not in cache, compute and store
    if (gradingInfoRef.current) {
      const info = getSubjectInfo(subject, gradingInfoRef.current);
      
      // Update cache
      if (!gradingInfoCache.current[subject]) {
        gradingInfoCache.current[subject] = { 
          passingGrade: null, 
          totalQuestions: 0 
        };
      }
      gradingInfoCache.current[subject].passingGrade = info.passingGrade;
      
      return info.passingGrade;
    }
    
    return null;
  }, []); // No dependencies since we're using refs

  // Function to extract active category data (memoized)
  const activeCategoryData = useMemo<ActiveCategoryData>(() => {
    if (!examDataPagination?.data || !activeCategory) {
      return {
        subjects: {},
        pagination: {
          currentPage: 1,
          totalPages: 1,
          perSubjectLimit: ITEMS_PER_PAGE,
          subjectCounts: [],
          totalExams: 0
        }
      };
    }
    
    // Initialize container for the active category data
    const activeData: ActiveCategoryData = {
      subjects: {},
      pagination: examDataPagination.pagination
    };
    
    // Loop through subjects in paginated data
    Object.entries(examDataPagination.data).forEach(([subject, exams]) => {
      // Only process exams if there are any
      if (exams && exams.length > 0) {
        // Check if any exam belongs to the active category
        const hasActiveCategory = exams.some(exam => 
          exam.categories && exam.categories.some(cat => cat.name === activeCategory)
        );
        
        // If this subject has exams in the active category, add it
        if (hasActiveCategory) {
          activeData.subjects[subject] = exams;
        }
      }
    });
    
    return activeData;
  }, [
    examDataPagination?.data, 
    examDataPagination?.pagination, 
    activeCategory
  ]);

  // Add useEffect to apply CSS styles (run once)
  useEffect(() => {
    const styleSheet = document.createElement("style");
    styleSheet.textContent = tabStyles + containerStyle + additionalStyles + navigationStyles + paginationStyles;
    document.head.appendChild(styleSheet);
    return () => {
      document.head.removeChild(styleSheet);
    };
  }, []);

  // Update navigation state whenever the selected subject changes
  useEffect(() => {
    if (!activeCategory || !selectedSubject) return;
    
    const subjects = Object.keys(categoryGroups[activeCategory]?.subjects || {}).reverse();
    const currentIndex = subjects.indexOf(selectedSubject);
    
    setCanNavigatePrev(currentIndex > 0);
    setCanNavigateNext(currentIndex < subjects.length - 1);
  }, [activeCategory, selectedSubject, categoryGroups]);

  // Calculate total pages from pagination data (optimized)
  useEffect(() => {
    if (examDataPagination?.pagination) {
      setTotalPages(examDataPagination.pagination.totalPages || 1);
      setLoading(false);
    }
  }, [examDataPagination?.pagination]);

  // Update total pages when activeCategory or selectedSubject changes
  useEffect(() => {
    if (activeCategory && selectedSubject && categoryGroups[activeCategory]?.subjects[selectedSubject]) {
      const exams = categoryGroups[activeCategory].subjects[selectedSubject];
      const newTotalPages = Math.ceil(exams.length / ITEMS_PER_PAGE);
      
      // Only update if needed to prevent unnecessary re-renders
      if (totalPages !== newTotalPages) {
        setTotalPages(newTotalPages);
      }
    }
  }, [activeCategory, selectedSubject, categoryGroups, ITEMS_PER_PAGE, totalPages]);

  // Fetch trial sessions when category or subject changes (optimized with cleanup)
  useEffect(() => {
    if (!selectedSubject || !activeCategory) {
      setTrialSessions({});
      return;
    }
    
    // Keep track if component is still mounted
    let isMounted = isMountedRef.current;
    
    const fetchTrialSessions = async () => {
      // Use the exams from the paginated data for the current view
      const currentExams = activeCategoryData.subjects?.[selectedSubject] || [];
      
      if (currentExams.length === 0) {
        if (isMounted) {
          setTrialSessions({});
        }
        return;
      }
      
      const sessionData: Record<string, TrialSession | null> = {};
      
      // Only fetch for exams that will be shown on the current page
      const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
      const endIndex = Math.min(startIndex + ITEMS_PER_PAGE, currentExams.length);
      const visibleExams = currentExams.slice(startIndex, endIndex);

      try {
        const fetchPromises = visibleExams.map(async (exam) => {
          try {
            const session = await fetchLastTrialSession(exam.id, userId, exam.subject);
            if (session && isMounted) {
              sessionData[exam.id] = {
                id: session.id || session.session_id,
                sessionId: session.session_id,
                userId: session.user_id,
                examId: session.exam_id,
                type: session.type,
                status: session.status,
                startTime: session.start_time,
                endTime: session.end_time,
                answers: session.answers,
                flaggedQuestion: session.flagged_questions,
                createdAt: session.created_at,
                modifiedAt: session.modified_at,
                subject: session.subject,
                remaining_duration: session.remaining_duration,
                exam_id: session.exam_id,
              };
            } else if (isMounted) {
              sessionData[exam.id] = null;
            }
          } catch (error) {
            if (isMounted) {
              sessionData[exam.id] = null;
            }
          }
        });

        await Promise.all(fetchPromises);
        
        if (isMounted) {
          setTrialSessions(prev => {
            // Check if anything changed to prevent unnecessary re-renders
            if (JSON.stringify(prev) === JSON.stringify(sessionData)) {
              return prev; // Return the previous state if nothing changed
            }
            return sessionData;
          });
        }
      } catch (error) {
        console.error("Error fetching trial sessions:", error);
        if (isMounted) {
          setTrialSessions({});
        }
      }
    };

    fetchTrialSessions();
    
    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [selectedSubject, activeCategory, userId, currentPage, activeCategoryData.subjects]);

  // Restore scroll position after page/tab changes
  useEffect(() => {
    // Only run if we have a saved scroll position and content has finished loading
    if (scrollPositionRef.current > 0 && !contentLoading) {
      const savedPosition = scrollPositionRef.current;
      
      // Create a longer sequence of restoration attempts to handle fast production environments
      const initialDelay = setTimeout(() => {
        // First attempt
        window.scrollTo({
          top: savedPosition,
          behavior: 'auto'
        });
        
        // Set up multiple follow-up attempts with increasing delays
        const attempts = [50, 100, 250, 500];
        
        attempts.forEach(delay => {
          setTimeout(() => {
            // Check if position still needs correction
            if (Math.abs(window.scrollY - savedPosition) > 5) {
              window.scrollTo({
                top: savedPosition,
                behavior: 'auto'
              });
            }
          }, delay);
        });
        
        // Final cleanup
        setTimeout(() => {
          scrollPositionRef.current = 0;
        }, 600);
        
      }, 100); // Initial delay increased
      
      return () => clearTimeout(initialDelay);
    }
  }, [selectedSubject, currentPage, contentLoading]);

  // Initialize from URL or localStorage on component mount (improved)
  useEffect(() => {
    if (!isFirstMount.current || Object.keys(categoryGroups).length === 0) return;
    
    isFirstMount.current = false;
    
    const urlCategoryId = searchParams?.get('category_id');
    const urlSubject = searchParams?.get('subject');
    const urlPage = parseInt(searchParams?.get('page') || '1');

    let categoryToActivate = '';
    let subjectToActivate = '';
    let pageToActivate = 1;
    let needsUrlUpdate = false;

    // First priority: Check URL for category_id
    if (urlCategoryId) {
      Object.entries(categoryGroups).forEach(([name, group]) => {
        if (group.categories?.some(cat => cat.id === urlCategoryId)) {
          categoryToActivate = name;
        }
      });
    }

    // Second priority: Check localStorage for saved category
    if (!categoryToActivate) {
      try {
        const savedCategory = localStorage.getItem(LOCAL_STORAGE_CATEGORY_KEY);
        if (savedCategory && categoryGroups[savedCategory]) {
          categoryToActivate = savedCategory;
          needsUrlUpdate = true;
        }
      } catch (error) {
        console.error("Error reading from localStorage:", error);
      }
    }

    // Third priority: Default to first category
    if (!categoryToActivate && Object.keys(categoryGroups).length > 0) {
      categoryToActivate = Object.keys(categoryGroups)[0];
      needsUrlUpdate = true;
    }

    if (categoryToActivate) {
      // Find subject to activate
      if (categoryGroups[categoryToActivate]) {
        const subjects = Object.keys(categoryGroups[categoryToActivate].subjects);
        if (subjects.length > 0) {
          // First check URL for subject
          if (urlSubject && subjects.includes(urlSubject)) {
            subjectToActivate = urlSubject;
          } else {
            // Then check localStorage
            try {
              const savedSubject = localStorage.getItem(`${LOCAL_STORAGE_SUBJECT_PREFIX}${categoryToActivate}`);
              if (savedSubject && subjects.includes(savedSubject)) {
                subjectToActivate = savedSubject;
                needsUrlUpdate = true;
              } else {
                subjectToActivate = subjects[0];
                needsUrlUpdate = true;
              }
            } catch (error) {
              subjectToActivate = subjects[0];
              needsUrlUpdate = true;
            }
          }

          // Get page
          if (urlPage > 0 && urlSubject === subjectToActivate) {
            pageToActivate = urlPage;
          } else {
            try {
              const savedPage = localStorage.getItem(`${LOCAL_STORAGE_PAGE_PREFIX}${categoryToActivate}_${subjectToActivate}`);
              if (savedPage) {
                const parsedPage = parseInt(savedPage);
                if (parsedPage > 0) {
                  pageToActivate = parsedPage;
                  needsUrlUpdate = true;
                }
              }
            } catch (error) {
              pageToActivate = 1;
              needsUrlUpdate = true;
            }
          }
        }
      }
    }

    // Batch all state updates together to avoid multiple renders
    if (categoryToActivate) {
      setActiveCategory(categoryToActivate);
      if (subjectToActivate) {
        setSelectedSubject(subjectToActivate);
      }
      setCurrentPage(pageToActivate);
      
      // Calculate total pages
      const estimatedTotalPages = 
        categoryGroups[categoryToActivate]?.subjects[subjectToActivate]?.length
        ? Math.ceil(categoryGroups[categoryToActivate].subjects[subjectToActivate].length / ITEMS_PER_PAGE)
        : 1;
      setTotalPages(estimatedTotalPages);

      // Update URL only if needed
      if (needsUrlUpdate) {
        const categoryObj = categoryGroups[categoryToActivate]?.categories?.[0];
        const categoryId = categoryObj?.id || "uncategorized";

        const queryString = createQueryString({ 
          category_id: categoryId,
          subject: subjectToActivate,
          page: pageToActivate
        });

        router.replace(`?${queryString}`, { scroll: false });
      }
    }

    setLoading(false);
  }, [categoryGroups, searchParams, createQueryString, router]);

  // Render ExamContent using the memoized component - dependencies optimized
  const renderExamContent = (subject: string) => {
    if (!activeCategory) return null;
    
    return (
      <ExamContentSection
        subject={subject}
        activeCategory={activeCategory}
        currentPage={currentPage}
        contentLoading={contentLoading}
        categoryGroups={categoryGroups}
        trialSessions={trialSessions}
        trialCompletion={trialCompletionRef.current}
        isPremium={isPremium}
        userId={userId}
        ITEMS_PER_PAGE={ITEMS_PER_PAGE}
        handlePageChange={handlePageChange}
        getPassingGrade={getPassingGrade}
        getTotalQuestions={getTotalQuestions}
      />
    );
  };

  // Show empty state when no data is available
  if (!examData?.data || Object.keys(examData.data).length === 0) {
    return (
      <div className="w-full max-w-[95rem] mx-auto px-4 lg:px-6 py-6">
        <div className="mb-6">
          <Breadcrumbs>
            <BreadcrumbItem href="/dashboard">Dashboard</BreadcrumbItem>
            <BreadcrumbItem>Latihan Dimiliki</BreadcrumbItem>
          </Breadcrumbs>
        </div>
        <EmptyState />
      </div>
    );
  }

  // Main return JSX
  return (
    <div className="w-full max-w-[95rem] mx-auto px-4 lg:px-6 py-6">
      <div className="mb-6">
        <Breadcrumbs>
          <BreadcrumbItem href="/dashboard">Dashboard</BreadcrumbItem>
          <BreadcrumbItem>Latihan Dimiliki</BreadcrumbItem>
        </Breadcrumbs>
      </div>

      {loading ? (
        <div className="w-full flex justify-center py-12">
          <Spinner size="lg" />
        </div>
      ) : (
        <div className="space-y-6">
          {/* Category Pills - using memoized component */}
          <CategoryPills 
            categoryGroups={categoryGroups}
            activeCategory={activeCategory}
            handleCategoryChange={handleCategoryChange}
            calculateCategoryTotalExams={calculateCategoryTotalExams}
          />
          
          {/* Category Header - using memoized component */}
          <CategoryHeader activeCategory={activeCategory} />
          
          {/* Subject Tabs */}
          <Card className="w-full content-container relative">
            <div className="exam-tabs-container">
              <div className="mobile-scroll-helper">
                ← Geser untuk melihat semua mata uji →
              </div>
              {activeCategory && categoryGroups[activeCategory] && (
                <div className="tabs-wrapper">
                  <Tabs
                    aria-label="Exam subjects"
                    selectedKey={selectedSubject}
                    onSelectionChange={(key) => handleSubjectSelect(key.toString())}
                    className="custom-tabs w-full"
                    color="primary"
                    variant="solid"
                    disableAnimation={true}
                    destroyInactiveTabPanel={false}
                    classNames={{
                      base: "tabs-base",
                      tabList: "tabs-list",
                      tab: "tab-item",
                      tabContent: "tab-content",
                      panel: "tab-panel",
                    }}
                  >
                    {Object.entries(categoryGroups[activeCategory]?.subjects || {}).reverse().map(([subject, exams]) => (
                      <Tab
                        key={subject}
                        title={
<div className="flex items-center gap-2 px-4">
                            <span>{subject}</span>
                            <Chip
                              size="sm"
                              variant="flat"
                              classNames={{
                                base: "min-w-[24px] h-6",
                                content: "px-2 text-sm",
                              }}
                            >
                              {exams.length}
                            </Chip>
                          </div>
                        }
                      >
                        {renderExamContent(subject)}
                      </Tab>
                    ))}
                  </Tabs>
                  
                  {/* Subject navigation buttons - moved below tabs */}
                  <div className="subject-navigation">
                    <button 
                      className="subject-navigation-button" 
                      onClick={() => navigateTab('prev')}
                      disabled={!canNavigatePrev}
                      aria-label="Previous subject"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <button 
                      className="subject-navigation-button" 
                      onClick={() => navigateTab('next')}
                      disabled={!canNavigateNext}
                      aria-label="Next subject"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

// Memoize the main component to prevent unnecessary renders
export default React.memo(MyTrials);