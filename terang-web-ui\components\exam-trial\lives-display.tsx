import React, { useEffect, useState } from 'react';
import { FavouriteIcon, Infinity02Icon } from 'hugeicons-react';
import { retrieveFromRedis } from './actions';

interface LivesDisplayProps {
  currentLives?: number;
  maxLives?: number;
  sessionId?: string;
  isPremium?: boolean;
}

const LivesDisplay: React.FC<LivesDisplayProps> = ({
  currentLives: initialLives = 3,
  maxLives = 3,
  sessionId,
  isPremium = false
}) => {
  const [lives, setLives] = useState(initialLives);

  useEffect(() => {
    if (!sessionId) return;

    // Initial fetch
    const fetchGamificationState = async () => {
      try {
        const gamificationData = await retrieveFromRedis(`${sessionId}_gamification`);
        if (gamificationData) {
          const parsedData = JSON.parse(gamificationData);
          setLives(parsedData.currentLives);
        }
      } catch (error) {
        console.error('Error fetching gamification state:', error);
      }
    };

    fetchGamificationState();

    // Set up polling for real-time updates
    const pollInterval = setInterval(fetchGamificationState, 1000);

    // Cleanup polling on unmount
    return () => {
      clearInterval(pollInterval);
    };
  }, [sessionId]);

  // Update when prop changes
  useEffect(() => {
    if (initialLives !== lives) {
      setLives(initialLives);
    }
  }, [initialLives]);

  // Premium display
  if (isPremium) {
    return (
      <div className="flex items-center justify-center gap-2 mb-4 mt-2">
        <span className="text-sm text-gray-600 mr-2">Nyawa tersedia:</span>
        <FavouriteIcon
          size={24}
          className="text-red-500 fill-current animate-pulse"
        />
        <Infinity02Icon
          size={24}
          className="text-blue-500"
        />
      </div>
    );
  }

  // Regular display with limited hearts
  const safeMaxLives = Math.min(maxLives, 5); // Limit maximum hearts displayed
  const heartIcons = [];
  
  // Create hearts array without using Array.fill
  for (let i = 0; i < safeMaxLives; i++) {
    heartIcons.push(
      <FavouriteIcon
        key={i}
        size={24}
        className={`transition-all duration-300 ${
          i < lives ? 'text-red-500 fill-current animate-bounce' : 'text-red-500 stroke-current'
        }`}
        style={{
          fill: i < lives ? 'currentColor' : 'none',
          stroke: i < lives ? 'none' : 'currentColor',
          strokeWidth: i < lives ? '0' : '1.5',
          animationDelay: `${i * 100}ms`,
          animationDuration: '1s',
        }}
      />
    );
  }

  return (
    <div className="flex items-center justify-center gap-2 mb-4 mt-2">
      <span className="text-sm text-gray-600 mr-2">Nyawa tersedia:</span>
      {heartIcons}
    </div>
  );
};

export default LivesDisplay;