"use server";

import { auth } from '@/auth';
import { createClient } from "redis";
import { cookies } from "next/headers";
import { headers } from "next/headers";

// Conditional logger for dev environment
const conditionalLogger = {
  log: async (...args: any[]) => {
    try {
      const headersList = await headers();
      const host = headersList.get('host');
      if (host === 'dev.terang.ai' || host === 'localhost') {
        // console.log(...args);
      }
    } catch (error) {
      // Silently fail if headers are not available
    }
  },
  error: async (...args: any[]) => {
    try {
      const headersList = await headers();
      const host = headersList.get('host');
      if (host === 'dev.terang.ai' || host === 'localhost') {
        console.error(...args);
      }
    } catch (error) {
      // Silently fail if headers are not available
    }
  }
};

// Sync wrapper that handles the async nature internally
const logger = {
  log: (...args: any[]) => {
    conditionalLogger.log(...args).catch(() => {});
  },
  error: (...args: any[]) => {
    conditionalLogger.error(...args).catch(() => {});
  }
};

async function isLocalEnvironment() {
  const headersList = await headers();
  const host = headersList.get("host") || "";
  return host.includes("localhost") || host.includes("127.0.0.1");
}

// Create Redis clients using correct type structure
const createWriteClient = () => {
  const redisUrl = process.env.REDIS_URL_WRITE;
  
  if (!redisUrl) {
    logger.error("REDIS_URL_WRITE environment variable is not defined");
    throw new Error("Redis write URL is not configured");
  }
  
  try {
    // Validate URL format
    new URL(redisUrl);
    
    // Use url at the top level, not in socket
    return createClient({
      url: redisUrl
    });
  } catch (error) {
    logger.error(`Invalid Redis write URL: ${error}`);
    throw new Error("Invalid Redis write URL format");
  }
};

const createReadClient = () => {
  const redisUrl = process.env.REDIS_URL_READ;
  
  if (!redisUrl) {
    logger.error("REDIS_URL_READ environment variable is not defined");
    throw new Error("Redis read URL is not configured");
  }
  
  try {
    // Validate URL format
    new URL(redisUrl);
    
    // Use url at the top level, not in socket
    return createClient({
      url: redisUrl
    });
  } catch (error) {
    logger.error(`Invalid Redis read URL: ${error}`);
    throw new Error("Invalid Redis read URL format");
  }
};

// Redis key prefix for user IDs
const USER_ID_PREFIX = "user_id:";
// Cache expiration time (6 hours in seconds)
const CACHE_EXPIRATION = 21600;

/**
 * Stores a value in Redis with expiration
 * @param key Redis key
 * @param value Value to store
 * @returns Boolean indicating success
 */
async function storeToRedis(key: string, value: any): Promise<boolean> {
  let redis = null;

  try {
    redis = createWriteClient();
    await redis.connect();
    await redis.set(key, JSON.stringify(value), {
      EX: CACHE_EXPIRATION,
    });
    logger.log(`Successfully stored value in Redis for key: ${key}`);
    return true;
  } catch (error) {
    logger.error("Failed to store data in Redis:", error);
    return false;
  } finally {
    if (redis && redis.isOpen) {
      await redis.disconnect();
    }
  }
}

/**
 * Retrieves a value from Redis
 * @param key Redis key
 * @returns Parsed value or null if not found
 */
async function retrieveFromRedis(key: string): Promise<any | null> {
  let redis = null;

  try {
    redis = createReadClient();
    await redis.connect();
    const value = await redis.get(key);
    
    if (value) {
      logger.log(`Cache HIT: Found value for key ${key}`);
      return JSON.parse(value);
    } else {
      logger.log(`Cache MISS: No value found for key ${key}`);
      return null;
    }
  } catch (error) {
    logger.error("Failed to retrieve data from Redis:", error);
    return null;
  } finally {
    if (redis && redis.isOpen) {
      await redis.disconnect();
    }
  }
}

/**
 * Deletes a key from Redis
 * @param key Redis key to delete
 */
async function deleteFromRedis(key: string): Promise<void> {
  let redis = null;

  try {
    redis = createWriteClient();
    await redis.connect();
    await redis.del(key);
    logger.log(`Successfully deleted key from Redis: ${key}`);
  } catch (error) {
    logger.error("Failed to delete key from Redis:", error);
  } finally {
    if (redis && redis.isOpen) {
      await redis.disconnect();
    }
  }
}

/**
 * Clears a specific user's ID from cache
 * @param email User's email address
 */
export async function clearUserIdCache(email: string): Promise<void> {
  if (!email) return;
  
  const cacheKey = `${USER_ID_PREFIX}${email}`;
  await deleteFromRedis(cacheKey);
}

/**
 * Gets the current user's ID, using Redis cache when available
 * @returns User ID string, false if not authenticated, or null on error
 */
export async function getUserId(): Promise<string | boolean | null> {
  try {
    const session = await auth();
    
    if (!session?.user?.email) {
      logger.log("No user session found");
      return false;
    }
    
    const userEmail = session.user.email;
    const cacheKey = `${USER_ID_PREFIX}${userEmail}`;
    
    // Try to get from Redis cache first
    let cachedUserId = null;
    try {
      cachedUserId = await retrieveFromRedis(cacheKey);
      
      if (cachedUserId) {
        logger.log(`Using cached user ID for ${userEmail}`);
        return cachedUserId;
      } else {
        logger.log(`No cached user ID found for ${userEmail}, fetching from API`);
      }
    } catch (redisError) {
      logger.error("Redis retrieval error, falling back to API:", redisError);
      // If Redis fails, we'll continue and fetch from the API
    }

    // Not in cache, fetch from API
    logger.log(`Fetching user ID from API for ${userEmail}`);
    const url = `${process.env.BACKEND_BASE_URL}/v1/users/emails/${userEmail}`;
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
    });

    if (!response.ok) {
      if (response.status === 400) {
        logger.log(`API response 400: User not found for ${userEmail}`);
        return false;
      }
      throw new Error(`Network response was not ok: ${response.status}`);
    }

    const data = await response.json();
    const userId = data.data.id;
    logger.log(`Retrieved user ID ${userId} from API for ${userEmail}`);
    
    // Store in Redis cache, but don't let it block the response
    try {
      const cacheSuccess = await storeToRedis(cacheKey, userId);
      if (cacheSuccess) {
        logger.log(`Successfully cached user ID ${userId} for ${userEmail}`);
      }
    } catch (redisError) {
      logger.error("Failed to cache userId in Redis:", redisError);
      // Continue even if caching fails
    }
    
    return userId;
  } catch (error) {
    logger.error(`Error in getUserId: ${error instanceof Error ? error.message : JSON.stringify(error)}`);
    // Return null on error
    return null;
  }
}

export async function getUserUsername(): Promise<string | boolean | null> {
  try {
    const session = await auth();

    if (!session?.user?.email) {
      logger.log("No user session found");
      return false;
    }
    
    const userEmail = session.user.email;
    
    // Try to get the user data from API
    const url = `${process.env.BACKEND_BASE_URL}/v1/users/emails/${userEmail}`;
    const response = await fetch(url, {
      headers: {
        "Content-Type": "application/json",
        "X-Api-Key": process.env.BACKEND_API_KEY as string,
      },
    });

    if (!response.ok) {
      if (response.status === 400) {
        logger.log(`API response 400: User not found for ${userEmail}`);
        return false;
      }
      
      const errorText = await response.text();
      throw new Error(
        `Network response was not ok: ${response.status} - ${response.statusText}. Details: ${errorText}`
      );
    }
    
    const data = await response.json();
    return data.data.username;
  } catch (error) {
    logger.error(`Error in getUserUsername: ${error instanceof Error ? error.message : JSON.stringify(error)}`);
    return null;
  }
}

/**
 * Sets a user session cookie directly in the server action
 * @param userEmail User's email address
 * @param userData User data to store in the cookie
 * @returns Object indicating success or failure
 */
export async function setUserSessionCookie(userEmail: string, userData: any) {
  "use server";
  
  try {
    if (!userEmail || !userData) {
      logger.error("Missing required fields for setting session cookie");
      return { success: false, message: 'Missing required fields' };
    }

    logger.log(`Setting session cookie for ${userEmail}`, {
      first_name: userData.first_name,
      last_name: userData.last_name,
      picture: userData.picture
    });
    
    const cookieStore = await cookies();
    const encodedEmail = Buffer.from(userEmail).toString('base64');
    const cookieName = `user_session_${encodedEmail}`;
    
    // Set the cookie with user data
    cookieStore.set(cookieName, JSON.stringify({
      timestamp: Date.now(),
      data: userData
    }), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 30 // 30 minutes cache
    });
    
    logger.log(`Successfully set session cookie: ${cookieName}`);
    return { success: true };
  } catch (error) {
    logger.error("Failed to set user session cookie:", error);
    return { success: false, error: String(error) };
  }
}

/**
 * Clears a user session cookie by setting it to invalid data with short expiration
 * @param email User's email address
 * @returns Object indicating success or failure
 */
export async function clearUserSessionCookie(email: string) {
  "use server";
  
  try {
    if (!email) {
      return { success: false, message: 'Email parameter is required' };
    }
    
    const encodedEmail = Buffer.from(email).toString('base64');
    const cookieName = `user_session_${encodedEmail}`;
    
    logger.log(`Clearing session cookie: ${cookieName} for ${email}`);
    
    // Check if cookie exists first (for logging purposes)
    const cookieStore = await cookies();
    const existingCookie = cookieStore.get(cookieName);
    
    if (existingCookie) {
      logger.log(`Found existing cookie to clear: ${cookieName}`);
    } else {
      logger.log(`No cookie found to clear: ${cookieName}`);
    }
    
    // Instead of deleting, set an invalid/empty cookie with short expiration
    const invalidData = {
      timestamp: 0, // Very old timestamp to ensure it's invalid
      data: {
        id: '',
        email: '',
        first_name: '',
        last_name: '',
        picture: '',
        role: ''
      },
      isLoggedOut: true
    };
    
    // Set the cookie with invalid data and short expiration
    cookieStore.set(cookieName, JSON.stringify(invalidData), {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 1 // Very short expiration (1 second)
    });
    
    logger.log(`Successfully cleared session cookie with invalid data: ${cookieName}`);
    return { success: true };
  } catch (error) {
    logger.error("Error clearing user session cookie:", error);
    return { success: false, error: String(error) };
  }
}

export async function clearAllAuthCookies() {
  "use server";
  
  try {
    const cookieStore = await cookies();
    
    // List of all auth-related cookies to delete (excluding logging_out)
    const cookiesToDelete = [
      // NextAuth/Auth.js cookies
      '__Secure-authjs.session-token',
      'authjs.session-token',
      
      // App-specific cookies (excluding logging_out)
      'account_exists',
      'session_id',
      'page_start_time'
    ];
    
    // Delete each cookie in the list
    for (const cookieName of cookiesToDelete) {
      cookieStore.delete({
        name: cookieName,
        path: '/',
      });
      logger.log(`Deleted auth cookie: ${cookieName}`);
    }
    
    // Find and overwrite any user_session cookies
    try {
      const allCookies = cookieStore.getAll();
      for (const cookie of allCookies) {
        if (cookie.name.startsWith('user_session_')) {
          logger.log(`Found user session cookie during clearAll: ${cookie.name}`);
          
          // Instead of deleting, set an invalid/empty cookie with short expiration
          const invalidData = {
            timestamp: 0,
            data: {
              id: '',
              email: '',
              first_name: '',
              last_name: '',
              picture: '',
              role: ''
            },
            isLoggedOut: true
          };
          
          // Set the cookie with the same name but invalid data
          cookieStore.set(cookie.name, JSON.stringify(invalidData), {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'lax',
            maxAge: 1 // Very short expiration
          });
          logger.log(`Overwrote user session cookie with invalid data: ${cookie.name}`);
        }
      }
    } catch (error) {
      logger.error("Error overwriting user session cookies during clearAll:", error);
      // Continue even if this fails
    }
    
    // Note: logging_out cookie is intentionally not deleted here
    
    return { success: true };
  } catch (error) {
    logger.error("Error clearing auth cookies:", error);
    return { success: false, error };
  }
}

// DEPRECATED: Use setUserSessionCookie directly instead
export async function setUserSessionCookieAction(
  userEmail: string, 
  userData: any
) {
  "use server";
  return await setUserSessionCookie(userEmail, userData);
}

// Function to get user session cookie
export async function getUserSessionCookieAction(userEmail: string) {
  "use server";
  
  try {
    const cookieStore = await cookies();
    const encodedEmail = Buffer.from(userEmail).toString('base64');
    const cookieName = `user_session_${encodedEmail}`;
    
    logger.log(`Looking for session cookie: ${cookieName}`);
    const cachedData = cookieStore.get(cookieName)?.value;
    
    if (cachedData) {
      logger.log(`Found cookie for ${userEmail}`);
      const parsed = JSON.parse(cachedData);
      
      // Check if this is a "logged out" cookie
      if (parsed.isLoggedOut === true) {
        logger.log(`Cookie marked as logged out: ${cookieName}`);
        return null;
      }
      
      // Check if data is valid
      if (!parsed.data || !parsed.data.email || parsed.data.email === '') {
        logger.log(`Invalid or empty data in cookie: ${cookieName}`);
        return null;
      }
      
      // Check if cache is still valid (within 30 minutes)
      const now = Date.now();
      const cacheAge = now - parsed.timestamp;
      const isValid = cacheAge < 30 * 60 * 1000; // 30 minutes in milliseconds
      
      logger.log(`Cache age: ${Math.round(cacheAge/1000)} seconds, Valid: ${isValid}`);
      
      if (isValid) {
        logger.log("Cache is still valid, returning cached data:", {
          first_name: parsed.data.first_name,
          last_name: parsed.data.last_name,
          picture: parsed.data.picture
        });
        return parsed.data;
      } else {
        logger.log("Cache expired, will fetch fresh data");
      }
    } else {
      logger.log(`No session cookie found for ${userEmail}`);
    }
    
    return null;
  } catch (error) {
    logger.error("Failed to get user session cookie:", error);
    return null;
  }
}

// Add this new function to set the logging_out flag
export async function setLoggingOutFlag() {
  "use server";
  
  try {
    const cookieStore = await cookies();
    const isLocal = await isLocalEnvironment();
    
    if (isLocal) {
      // For local development environment
      cookieStore.set({
        name: 'logging_out',
        value: 'true',
        path: '/',
        maxAge: 600, // 10 minutes
        // No domain for localhost
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      });
    } else {
      // For production environment
      cookieStore.set({
        name: 'logging_out',
        value: 'true',
        path: '/',
        domain: '.terang.ai', // Include leading dot for subdomains
        maxAge: 600, // 10 minutes
        secure: true,
        sameSite: 'lax'
      });
    }
    
    return { success: true };
  } catch (error) {
    logger.error("Error setting logging_out flag:", error);
    return { success: false, error };
  }
}

// Function to set the logging_out flag to false
export async function setLoggingOutFlagToFalse() {
  "use server";
  
  try {
    const cookieStore = await cookies();
    const isLocal = await isLocalEnvironment();
    
    if (isLocal) {
      // For local development environment
      cookieStore.set({
        name: 'logging_out',
        value: 'false', // Set to false instead of deleting
        path: '/',
        maxAge: 600, // 10 minutes
        // No domain for localhost
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax'
      });
    } else {
      // For production environment
      cookieStore.set({
        name: 'logging_out',
        value: 'false', // Set to false instead of deleting
        path: '/',
        domain: '.terang.ai', // Include leading dot for subdomains
        maxAge: 600, // 10 minutes
        secure: true,
        sameSite: 'lax'
      });
    }
    
    return { success: true };
  } catch (error) {
    logger.error("Error setting logging_out flag to false:", error);
    return { success: false, error };
  }
}

// Final function to clear the logging_out cookie - should be called last
export async function clearLoggingOutCookie() {
  "use server";
  
  try {
    const cookieStore = await cookies();
    const isLocal = await isLocalEnvironment();
    
    // Delete the logging_out cookie as the final step
    cookieStore.delete({
      name: 'logging_out',
      path: '/',
    });
    
    if (!isLocal) {
      // For production, try with various domain configurations
      cookieStore.delete({
        name: 'logging_out',
        path: '/',
        domain: '.terang.ai',
        secure: true
      });
    }
    
    return { success: true };
  } catch (error) {
    logger.error("Error clearing logging_out cookie:", error);
    return { success: false, error };
  }
}

/**
 * Clear all Redis cache related to a specific user
 * @param email User's email address
 * @returns Promise resolving to void
 */
export async function clearAllUserRedisCache(email: string): Promise<void> {
  "use server";
  
  if (!email) return;
  
  logger.log(`Clearing all Redis cache for user: ${email}`);
  
  try {
    // Clear user ID cache
    const userIdKey = `${USER_ID_PREFIX}${email}`;
    await deleteFromRedis(userIdKey);
    logger.log(`Cleared user ID cache for ${email}`);
    
    // Clear other potential user-related keys
    // We can add more patterns here if needed in the future
    
    // Add any additional cache clearing logic here
    
    // Get Redis client to scan for pattern matches
    let redis = null;
    try {
      redis = createReadClient();
      await redis.connect();
      
      // Use SCAN with pattern matching to find keys related to this user
      let cursor = 0;
      let matchedKeys: string[] = [];
      
      do {
        // @ts-ignore - Type definition issue with Redis client
        const scanResult = await redis.scan(cursor, {
          MATCH: `*${email}*`,
          COUNT: 100
        });
        
        cursor = scanResult.cursor;
        matchedKeys.push(...scanResult.keys);
      } while (cursor !== 0);
      
      logger.log(`Found ${matchedKeys.length} additional Redis keys for ${email}`);
      
      // Delete all matched keys
      if (matchedKeys.length > 0) {
        let deleteRedis = null;
        try {
          deleteRedis = createWriteClient();
          await deleteRedis.connect();
          
          for (const key of matchedKeys) {
            await deleteRedis.del(key);
            logger.log(`Deleted additional Redis key: ${key}`);
          }
        } catch (err) {
          logger.error("Error deleting additional Redis keys:", err);
        } finally {
          if (deleteRedis && deleteRedis.isOpen) {
            await deleteRedis.disconnect();
          }
        }
      }
    } catch (error) {
      logger.error("Error scanning Redis for user keys:", error);
    } finally {
      if (redis && redis.isOpen) {
        await redis.disconnect();
      }
    }
    
    logger.log(`Completed Redis cache clearing for ${email}`);
  } catch (error) {
    logger.error(`Error clearing Redis cache for ${email}:`, error);
  }
}