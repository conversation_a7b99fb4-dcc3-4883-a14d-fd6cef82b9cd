"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import Link from "next/link";
import { getInterviewHistoryItem } from "@/app/lib/actions/interview-history/actions";
import { 
  ArrowLeft, 
  Clock, 
  Calendar, 
  Check, 
  X, 
  AlertTriangle, 
  Info, 
  ArrowRight, 
  RefreshCw,
  Award,
  BarChart3,
  Target
} from "lucide-react";
import { getScoreColorClass, getScoreBgClass, getReadinessMessage } from "@/app/lib/mocks/interview-scores";

interface ScoreData {
  overallScore: number;
  maxScore: number;
  recommendation: string;
  readinessLevel: number;
  categoryScores: Array<{
    category: string;
    score: number;
    maxScore: number;
  }>;
}

interface InterviewHistoryItem {
  sessionId: string;
  interviewId: string;
  startTime: number;
  duration: string;
  status: 'active' | 'completed' | 'force_finished' | 'cancelled' | 'disconnected_timeout' | 'disconnected_grace';
  interviewName?: string;
  interviewSubname?: string;
  categoryName?: string;
  interviewType?: string;
  endedAt?: string;
  createdAt: string;
  updatedAt?: string;
  durationInSeconds: number;
  roomName: string;
  scoreData?: ScoreData;
}

export default function InterviewHistoryDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params?.sessionId as string;
  
  const [session, setSession] = useState<InterviewHistoryItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchSession() {
      if (!sessionId) {
        setError("Session ID is missing");
        setIsLoading(false);
        return;
      }
      
      try {
        setIsLoading(true);
        const sessionData = await getInterviewHistoryItem(sessionId);
        
        if (!sessionData) {
          setError("Session not found or you don't have permission to view it");
        } else {
          setSession(sessionData as InterviewHistoryItem);
        }
      } catch (err) {
        console.error("Failed to fetch interview session:", err);
        setError("Failed to load the interview session. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    }

    fetchSession();
  }, [sessionId]);

  // Format date for display
  const formatDate = (timestamp: number | string) => {
    const date = typeof timestamp === 'number' ? timestamp : new Date(timestamp).getTime();
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Format time for display
  const formatTime = (timestamp: number | string) => {
    const date = typeof timestamp === 'number' ? timestamp : new Date(timestamp).getTime();
    return new Date(date).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  // Format duration in a human-readable way
  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;

    let result = '';
    if (hours > 0) {
      result += `${hours} hour${hours !== 1 ? 's' : ''} `;
    }
    if (minutes > 0 || hours > 0) {
      result += `${minutes} minute${minutes !== 1 ? 's' : ''} `;
    }
    result += `${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''}`;
    
    return result;
  };

  // Get status badge style and icon
  const getStatusInfo = (status: string) => {
    switch (status) {
      case "completed":
        return {
          icon: <Check size={20} />,
          label: "Completed",
          bgColor: "bg-green-100",
          textColor: "text-green-800",
          description: "You successfully completed this interview session."
        };
      case "active":
        return {
          icon: <Clock size={20} />,
          label: "Active",
          bgColor: "bg-blue-100",
          textColor: "text-blue-800",
          description: "This interview session is currently active."
        };
      case "force_finished":
        return {
          icon: <AlertTriangle size={20} />,
          label: "Forced End",
          bgColor: "bg-yellow-100",
          textColor: "text-yellow-800",
          description: "This interview was ended due to time limit or system intervention."
        };
      case "cancelled":
        return {
          icon: <X size={20} />,
          label: "Cancelled",
          bgColor: "bg-gray-100",
          textColor: "text-gray-800",
          description: "This interview session was cancelled before completion."
        };
      case "disconnected_timeout":
        return {
          icon: <AlertTriangle size={20} />,
          label: "Disconnected",
          bgColor: "bg-red-100",
          textColor: "text-red-800",
          description: "This session ended due to a connection timeout."
        };
      case "disconnected_grace":
        return {
          icon: <RefreshCw size={20} />,
          label: "Reconnecting",
          bgColor: "bg-purple-100",
          textColor: "text-purple-800",
          description: "This session is in the reconnection grace period."
        };
      default:
        return {
          icon: <Info size={20} />,
          label: status,
          bgColor: "bg-gray-100",
          textColor: "text-gray-800",
          description: "Status information not available."
        };
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <h2 className="mt-4 text-lg font-semibold text-gray-900">Loading interview session details...</h2>
          </div>
        </div>
      </div>
    );
  }

  if (error || !session) {
    return (
      <div className="min-h-screen bg-gray-50 pt-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-red-50 p-6 rounded-lg inline-block">
              <AlertTriangle className="h-12 w-12 text-red-500 mb-3 mx-auto" />
              <h2 className="text-xl font-semibold text-red-800 mb-2">Session Not Found</h2>
              <p className="text-red-700 mb-6">{error || "The requested interview session could not be found."}</p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  onClick={() => router.back()}
                  className="px-4 py-2 bg-gray-100 text-gray-800 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Go Back
                </button>
                <Link
                  href="/interview-history"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  View All Sessions
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const statusInfo = getStatusInfo(session.status);
  const completedSession = session.status === 'completed' || session.status === 'force_finished';
  const hasScoreData = completedSession && session.scoreData;
  
  return (
    <div className="min-h-screen bg-gray-50 pt-10 pb-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6 flex items-center">
          <Link 
            href="/interview-history"
            className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <ArrowLeft size={18} />
            <span>Back to History</span>
          </Link>
        </div>
        
        <div className="bg-white rounded-xl shadow-sm overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {session.interviewName || "Interview Session"}
                </h1>
                {session.interviewSubname && (
                  <p className="text-gray-600 mt-1">{session.interviewSubname}</p>
                )}
              </div>
              
              <div className={`flex items-center gap-2 px-4 py-2 rounded-lg ${statusInfo.bgColor} ${statusInfo.textColor}`}>
                {statusInfo.icon}
                <span className="font-medium">{statusInfo.label}</span>
              </div>
            </div>
            
            <div className="mt-4 flex flex-wrap items-center gap-3">
              {session.categoryName && (
                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                  {session.categoryName}
                </span>
              )}
              {session.interviewType && (
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  session.interviewType === 'full' 
                    ? 'bg-indigo-100 text-indigo-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {session.interviewType === 'full' ? 'Full Interview' : 'Focused Session'}
                </span>
              )}
              <span className="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">
                Duration: {session.duration}
              </span>
            </div>
            
            {/* Score Summary (if available) */}
            {hasScoreData && (
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="flex items-center gap-3 mb-3">
                  <BarChart3 className="text-blue-600" size={20} />
                  <h2 className="text-lg font-semibold text-gray-900">Performance Summary</h2>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg text-center">
                    <div className={`inline-flex items-center justify-center ${getScoreBgClass(session.scoreData!.overallScore, session.scoreData!.maxScore)} rounded-full w-20 h-20 mb-2`}>
                      <span className={`text-2xl font-bold ${getScoreColorClass(session.scoreData!.overallScore, session.scoreData!.maxScore)}`}>
                        {session.scoreData!.overallScore.toFixed(1)}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">Overall Score</div>
                    <div className="text-xs text-gray-500">out of {session.scoreData!.maxScore}</div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg text-center">
                    <div className="flex items-center justify-center mb-2">
                      <Award size={28} className="text-blue-600" />
                    </div>
                    <div className="font-medium text-gray-800">{session.scoreData!.recommendation}</div>
                    <div className="text-xs text-gray-500 mt-1">Recommendation</div>
                  </div>
                  
                  <div className="bg-gray-50 p-4 rounded-lg text-center">
                    <div className="flex items-center justify-center mb-2">
                      <Target size={28} className="text-blue-600" />
                    </div>
                    <div className="font-medium text-gray-800">{session.scoreData!.readinessLevel}%</div>
                    <div className="text-xs text-gray-500 mt-1">Readiness Level</div>
                  </div>
                </div>
                
                <div className="mt-4 text-sm text-gray-700">
                  {getReadinessMessage(session.scoreData!.readinessLevel)}
                </div>
              </div>
            )}
          </div>
          
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Session Details</h2>
                <div className="space-y-3">
                  <div>
                    <div className="text-sm text-gray-500">Session ID</div>
                    <div className="font-mono text-sm bg-gray-50 p-2 rounded mt-1 overflow-x-auto">
                      {session.sessionId}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-gray-500">Interview ID</div>
                    <div className="font-mono text-sm bg-gray-50 p-2 rounded mt-1 overflow-x-auto">
                      {session.interviewId}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-gray-500">Start Date</div>
                    <div className="flex items-center gap-2 mt-1">
                      <Calendar className="text-gray-400" size={16} />
                      <span>{formatDate(session.startTime)}</span>
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm text-gray-500">Start Time</div>
                    <div className="flex items-center gap-2 mt-1">
                      <Clock className="text-gray-400" size={16} />
                      <span>{formatTime(session.startTime)}</span>
                    </div>
                  </div>
                  
                  {completedSession && session.endedAt && (
                    <div>
                      <div className="text-sm text-gray-500">End Time</div>
                      <div className="flex items-center gap-2 mt-1">
                        <Clock className="text-gray-400" size={16} />
                        <span>{formatTime(session.endedAt)}</span>
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <div className="text-sm text-gray-500">Duration</div>
                    <div className="flex items-center gap-2 mt-1">
                      <span>{session.duration} ({formatDuration(session.durationInSeconds)})</span>
                    </div>
                  </div>
                </div>
                
                {/* Category Scores Section (if available) */}
                {hasScoreData && (
                  <div className="mt-6 pt-4 border-t border-gray-200">
                    <h3 className="text-md font-semibold text-gray-900 mb-3">Category Scores</h3>
                    <div className="space-y-3">
                      {session.scoreData!.categoryScores.map((category, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">{category.category}</span>
                          <div className={`px-2 py-1 rounded ${getScoreBgClass(category.score, category.maxScore)}`}>
                            <span className={`font-medium ${getScoreColorClass(category.score, category.maxScore)}`}>
                              {category.score.toFixed(1)}
                            </span>
                            <span className="text-xs text-gray-500">/{category.maxScore}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Status Information</h2>
                <div className={`p-4 rounded-lg ${statusInfo.bgColor} bg-opacity-50`}>
                  <div className="flex items-center gap-3 mb-2">
                    {statusInfo.icon}
                    <span className={`font-semibold ${statusInfo.textColor}`}>{statusInfo.label}</span>
                  </div>
                  <p className="text-gray-700">{statusInfo.description}</p>
                </div>
                
                <div className="mt-6">
                  <h3 className="text-md font-semibold text-gray-900 mb-3">Actions</h3>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Link
                      href={`/ai-interview-result/${session.interviewId}/${session.sessionId}`}
                      className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                    >
                      View Interview Results
                      <ArrowRight size={16} />
                    </Link>
                    
                    {session.status === 'active' && (
                      <Link
                        href={`/ai-interview/${session.interviewId}/${session.sessionId}`}
                        className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                      >
                        Resume Interview
                        <ArrowRight size={16} />
                      </Link>
                    )}
                    
                    {session.status === 'disconnected_grace' && (
                      <Link
                        href={`/ai-interview/${session.interviewId}/${session.sessionId}`}
                        className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
                      >
                        Reconnect to Interview
                        <RefreshCw size={16} />
                      </Link>
                    )}
                  </div>
                </div>
                
                {/* Room Info */}
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <h3 className="text-md font-semibold text-gray-900 mb-3">Technical Information</h3>
                  <div className="bg-gray-50 p-3 rounded">
                    <div className="text-sm text-gray-500 mb-1">Room Name</div>
                    <div className="font-mono text-sm overflow-x-auto">{session.roomName}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}