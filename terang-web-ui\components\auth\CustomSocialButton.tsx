import { Button } from "@heroui/react";
import { ReactNode } from "react";

interface CustomSocialButtonProps {
  type?: "button" | "submit" | "reset";
  children: ReactNode;
  className?: string;
}

const CustomSocialButton = ({
  type = "button",
  children,
  className,
}: CustomSocialButtonProps) => {
  return (
    <Button
      className={`flex items-center px-4 py-2 bg-black text-white rounded-full hover:bg-gray-800 ${className}`} // More rounded corners
      size="lg"
      type={type}
    >
      {children}
    </Button>
  );
};

export default CustomSocialButton;
