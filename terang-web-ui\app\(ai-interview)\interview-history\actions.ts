'use server'

import { getUserId } from '@/app/lib/actions/account/actions'
import { auth } from '@/auth'

const API_URL = process.env.BACKEND_BASE_URL as string
const API_KEY = process.env.BACKEND_API_KEY as string

export interface InterviewSession {
  id: number
  sessionId: string
  userId: string
  userEmail: string
  userName: string
  interviewId: string
  interviewName: string
  interviewSubname: string
  category: string
  type: string
  status: string
  recordingUrl: string | null
  transcriptUrl: string | null
  duration: string
  duration_seconds: number
  startTime: string
  endTime: string | null
  createdAt: string
  updatedAt: string
  language: string
}

async function fetchWithHeaders(url: string, options: RequestInit = {}) {
  const response = await fetch(url, {
    headers: {
      'X-API-Key': API_KEY,
      ...(options.headers || {})
    },
    ...options,
    cache: 'no-store',
  })
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }
  
  return response.json()
}

export async function getUserSessions(): Promise<InterviewSession[]> {
  const userId = await getUserId()
  if (!userId) {
    throw new Error('Not authenticated')
  }

  return fetchWithHeaders(`${API_URL}/v0/ai-interview/users/${userId}/sessions`)
}

export async function getSession(sessionId: string): Promise<InterviewSession | null> {
  const userId = await getUserId()
  if (!userId) {
    throw new Error('Not authenticated')
  }
  
  return fetchWithHeaders(`${API_URL}/v0/ai-interview/sessions/${sessionId}`)
}

export async function createSession(session: Partial<InterviewSession> & { language?: string }): Promise<InterviewSession> {
  const authSession = await auth()
  const userId = await getUserId()
  if (!userId || !authSession?.user.id) {
    throw new Error('Not authenticated')
  }

  // Add user info from auth session
  const sessionWithUser = {
    ...session,
    userId: userId,
    userEmail: authSession.user.email || '',
    userName: authSession.user.name || ''
  }

  return fetchWithHeaders(`${API_URL}/v0/ai-interview/sessions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,  // Add API key explicitly
    },
    body: JSON.stringify(sessionWithUser),
  })
}

export async function updateSession(
  sessionId: string, 
  updates: {
    status?: string
    recordingUrl?: string
    transcriptUrl?: string
    endTime?: string
  }
): Promise<InterviewSession> {
  const userId = await getUserId()
  if (!userId) {
    throw new Error('Not authenticated')
  }

  return fetchWithHeaders(`${API_URL}/v0/ai-interview/sessions/${sessionId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': API_KEY,  // Add API key explicitly
    },
    body: JSON.stringify(updates),
  })
}
