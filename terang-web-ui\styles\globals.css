@tailwind base;
@tailwind components;
@tailwind utilities;


/* Prevent iOS zooming on form elements */
@supports (-webkit-touch-callout: none) {
    /* Target all basic form elements */
    input,
    select,
    textarea,
    button {
      font-size: 16px !important;
    }
    
    /* Target specific input types individually */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="search"],
    input[type="tel"],
    input[type="number"],
    input[type="url"],
    input[type="date"],
    input[type="datetime-local"],
    input[type="month"],
    input[type="time"],
    input[type="week"],
    input[type="color"] {
      font-size: 16px !important;
    }
    
    /* Target HeroUI specific components */
    .heroui-autocomplete-input,
    .heroui-autocomplete input,
    [role="combobox"] input {
      font-size: 16px !important;
    }
    
    /* Target for ProgramStudySelect component */
    .program-study-select-container input,
    .program-study-select-container select {
      font-size: 16px !important;
    }
  }
  
  /* Additional layer of protection for older iOS devices */
  @media screen and (-webkit-min-device-pixel-ratio:0) {
    select:focus,
    textarea:focus,
    input:focus {
      font-size: 16px !important;
    }
  }

  /* Global CSS */
.heroui-input {
    --input-bg: #fff !important;
    background-color: var(--input-bg) !important;
  }

  @keyframes fadeInSlideUp {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .animate-fadeInSlideUp {
    animation: fadeInSlideUp 800ms cubic-bezier(0.22, 1, 0.36, 1) forwards;
  }

  :root {
    --lk-va-bar-width: 72px;
    --lk-control-bar-height: unset;
  }
  
  .agent-visualizer > .lk-audio-bar {
    width: 72px;
  }
  
  @reference {
  .p-0 { padding: 0; }
  }

  .lk-agent-control-bar {
    @apply border-t-0 p-0 h-min mr-4 flex items-center;
  }
  
  .lk-disconnect-button {
    @apply h-[36px] hover:bg-[#6b221a] hover:text-[white] bg-[#31100c] border-[#6b221a] flex items-center justify-center;
  }