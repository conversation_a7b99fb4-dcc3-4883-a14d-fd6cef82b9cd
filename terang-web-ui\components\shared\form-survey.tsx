import React, { useState, useEffect, useRef, useCallback } from "react";
import {
  Card,
  CardBody,
  Button,
  Progress,
  RadioGroup,
  Radio,
  Chip,
  Divider,
  Tooltip,
  Avatar,
  Spinner
} from "@heroui/react";

import { EducationLevel } from "../types";

import {
  fetchProvinces,
  fetchCities,
  fetchDistricts,
  fetchVillages,
  getEducationLevels,
  fetchProgramStudies,
  fetchFormations,
  Formation,
} from "./actions";
import { ProgramStudySelect } from "./program-study-select";
import { BirthDateInput } from "./birth-date-input";
import { UserInterests } from "./interest";
import Link from "next/link";
import { color } from "framer-motion";
import { ProgramStudy } from "../types";
import { motion } from "framer-motion";
import { UniversitySearch } from "./university-search";
import DotLottieAnimation from "./dotlottie-animation";

interface DemographicsData {
  id: number;
  nama: string;
}

interface CountryCode {
  code: string;
  name: string;
}

const countryCodes: CountryCode[] = [
  { code: "+62", name: "Indonesia" },
  { code: "+60", name: "Malaysia" },
  { code: "+65", name: "Singapore" },
  // Add more country codes as needed
];

interface FormData {
  // ID fields
  provinceId: number;
  cityId: number;
  districtId: number;
  villageId: number;
  educationLevelId: number;
  programStudyId: number;
  
  // Name fields
  provinceName: string;
  cityName: string;
  districtName: string;
  villageName: string;
  educationLevelName: string;
  programStudyName: string;
  
  // Other fields
  birthDate: string | null;
  lastOccupation: string;
  interests: string[];
  mainPurpose: string;
  gender: string;
  phoneNumber: string;
  preferredStudyMethods: string[];
  weeklyStudyTime: string;
  primaryDevices: string[];
  learningStyle: string;
  studyBudget: number;
  targetJabatan: string;
  targetInstitution: string;
  targetScore: number;
  learningChallenges: string[];

  targetUniversity: string;
  targetMajor: string;
}

interface UserDemographicsFormProps {
  onFormDataChange: (formData: FormData) => void;
  initialData?: FormData | null;
  formErrors: { [key in keyof FormData]?: string };
  clearError: (field: keyof FormData) => void;
  onComplete?: () => void;
}

export const UserDemographicsForm: React.FC<UserDemographicsFormProps> = ({
  onFormDataChange,
  initialData,
  formErrors,
  clearError,
  onComplete
}) => {
  const formRef = useRef<HTMLDivElement>(null);
  const scrollRef = useRef<boolean>(false);
  const [provinces, setProvinces] = useState<DemographicsData[]>([]);
  const [cities, setCities] = useState<DemographicsData[]>([]);
  const [districts, setDistricts] = useState<DemographicsData[]>([]);
  const [villages, setVillages] = useState<DemographicsData[]>([]);
  const [educationLevels, setEducationLevels] = useState<EducationLevel[]>([]);
  const [isLoadingProgramStudies, setIsLoadingProgramStudies] = useState(false);
  const [formations, setFormations] = useState<Formation[]>([]);
  const [selectedFormation, setSelectedFormation] = useState<Formation | null>(null);
  const [isLoadingFormations, setIsLoadingFormations] = useState(false);
  const [uniqueJabatan, setUniqueJabatan] = useState<string[]>([]);
  const [filteredFormations, setFilteredFormations] = useState<Formation[]>([]);
  const [programStudies, setProgramStudies] = useState<ProgramStudy[]>([]);
  
  // Step management
  const [currentStep, setCurrentStep] = useState(0);
  const [isStepValid, setIsStepValid] = useState(false);

  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitResult, setSubmitResult] = useState<{ success: boolean; message: string } | null>(null);


  const [formData, setFormData] = useState<FormData>({
    // ID fields
    provinceId: 0,
    cityId: 0,
    districtId: 0,
    villageId: 0,
    educationLevelId: 0,
    programStudyId: 0,
    
    // Name fields
    provinceName: "",
    cityName: "",
    districtName: "",
    villageName: "",
    educationLevelName: "",
    programStudyName: "",
    
    // Other fields
    birthDate: null,
    lastOccupation: "",
    interests: [],
    mainPurpose: "",
    gender: "",
    phoneNumber: "",
    preferredStudyMethods: [],
    weeklyStudyTime: "",
    primaryDevices: [],
    learningStyle: "",
    studyBudget: 0,
    targetJabatan: "",
    targetInstitution: "",
    targetScore: 0,
    learningChallenges: [],
    targetUniversity: "",
    targetMajor: "",
    ...(initialData || {}),
  });

  const [selectedCountryCode, setSelectedCountryCode] = useState<string>("+62");

  const handleMainPurposeChange = (purpose: string): void => {
    // Update the main purpose
    setFormData((prev) => {
      // Get the recommended score for this purpose
      const recommendation = getTargetScoreRecommendation(purpose);
      
      const updatedData: FormData = { 
        ...prev, 
        mainPurpose: purpose,
        // Set the recommended target score if the user hasn't set one yet
        targetScore: prev.targetScore === 0 ? recommendation.score : prev.targetScore
      };
      
      // Clear fields that are not relevant to the selected purpose
      if (purpose === "persiapan_cpns") {
        updatedData.targetUniversity = "";
        updatedData.targetMajor = "";
      } else if (purpose === "persiapan_lpdp" || purpose === "belajar_utbk") {
        updatedData.targetJabatan = "";
        updatedData.targetInstitution = "";
      } else {
        // For other purposes, clear all target fields except score
        updatedData.targetUniversity = "";
        updatedData.targetMajor = "";
        updatedData.targetJabatan = "";
        updatedData.targetInstitution = "";
      }
      
      return updatedData;
    });
    
    // Clear any error for this field
    clearError("mainPurpose");
  };

  // Learning challenges options
  const learningChallengesOptions = [
    { id: "time", label: "Keterbatasan Waktu", icon: "⏰" },
    { id: "concentration", label: "Sulit Berkonsentrasi", icon: "🧠" },
    { id: "material", label: "Kesulitan Memahami Materi", icon: "📚" },
    { id: "memory", label: "Kesulitan Mengingat", icon: "🤔" },
    { id: "motivation", label: "Kurang Motivasi", icon: "🔥" },
    { id: "resources", label: "Keterbatasan Sumber Belajar", icon: "💻" },
    { id: "environment", label: "Lingkungan Belajar Tidak Kondusif", icon: "🏠" },
    { id: "consistency", label: "Kesulitan Konsisten", icon: "📅" },
  ];

  interface ScoreRecommendation {
    score: number;
    description: string;
  }
  

  const getTargetScoreRecommendation = (purpose: string): ScoreRecommendation => {
    switch (purpose) {
      case "persiapan_cpns":
        return {
          score: 350,
          description: "Untuk CPNS, nilai minimal kelulusan adalah 301 dari total maksimal 500 (TWK: 150, TIU: 175, TKP: 175). Kami merekomendasikan target 350 untuk meningkatkan peluang kelulusan."
        };
      case "persiapan_lpdp":
        return {
          score: 200,
          description: "Untuk beasiswa LPDP, skor TBS maksimal adalah 300. Skor terdiri dari: Tes Penalaran Verbal (23 soal), Tes Penalaran Kuantitatif (25 soal), dan Tes Pemecahan Masalah (12 soal). Skor minimal yang direkomendasikan adalah 200 untuk meningkatkan peluang kelulusan."
        };
      case "belajar_utbk":
        return {
          score: 700,
          description: "Untuk UTBK-SNBT, skor maksimal adalah 1000. Fokus pada tiga komponen utama: Tes Potensi Skolastik, Tes Literasi, dan Tes Penalaran Matematika. Skor 700+ biasanya diperlukan untuk program studi favorit di universitas terkemuka."
        };
      default:
        return {
          score: 300,
          description: "Tetapkan target nilai yang menantang namun realistis untuk dicapai dengan fokus pada subjek-subjek yang relevan dengan tujuan belajar Anda."
        };
    }
  };

  // Define the steps structure with enhanced UX
  const steps = [
    {
      title: "Tentang Kamu",
      description: "Mari kenali dirimu lebih baik",
      icon: "👤",
      fields: ["gender", "birthDate", "mainPurpose"],
      validate: () => !!formData.gender && !!formData.birthDate && !!formData.mainPurpose
    },
    {
      title: "Lokasi",
      description: "Dimana kamu tinggal saat ini?",
      icon: "📍",
      fields: ["provinceId", "cityId", "districtId", "villageId"],
      validate: () => !!formData.provinceId && !!formData.cityId && !!formData.districtId && !!formData.villageId
    },
    {
      title: "Pendidikan",
      description: "Bagaimana latar belakang pendidikanmu?",
      icon: "🎓",
      fields: ["educationLevelId", "programStudyId", "lastOccupation"],
      validate: () => !!formData.educationLevelId && !!formData.programStudyId && !!formData.lastOccupation
    },
    {
      title: "Target Karir",
      description: "Kemana kamu ingin melangkah selanjutnya?",
      icon: "🎯",
      fields: () => {
        const baseFields = ["targetScore"];
        
        switch(formData.mainPurpose) {
          case "persiapan_cpns":
            return [...baseFields, "targetJabatan"];
          case "persiapan_lpdp":
            return [...baseFields, "targetUniversity", "targetMajor"];
          case "belajar_utbk":
            return [...baseFields, "targetUniversity", "targetMajor"];
          default:
            return baseFields;
        }
      },
      validate: (): boolean => {
        console.log("Validating Target Karir with mainPurpose:", formData.mainPurpose);
        
        // First check if targetScore is valid
        if (!formData.targetScore) {
          return false;
        }
        
        // Then check purpose-specific fields
        switch(formData.mainPurpose) {
          case "persiapan_cpns":
            return true; // targetJabatan is optional
            
          case "persiapan_lpdp":
            return !!formData.targetUniversity && !!formData.targetMajor;
            
          case "belajar_utbk":
            return !!formData.targetUniversity && !!formData.targetMajor;
            
          default:
            return true;
        }
      }
    },
    {
      title: "Kebiasaan Belajar",
      description: "Bagaimana cara belajar terbaikmu?",
      icon: "📝",
      fields: ["preferredStudyMethods", "weeklyStudyTime", "primaryDevices", "learningStyle", "studyBudget", "learningChallenges"],
      validate: () => 
        formData.preferredStudyMethods.length > 0 && 
        !!formData.weeklyStudyTime && 
        formData.primaryDevices.length > 0 && 
        !!formData.learningStyle && 
        formData.studyBudget > 0
    },
    {
      title: "Minat dan Kontak",
      description: "Apa yang paling membuatmu tertarik?",
      icon: "🚀",
      fields: ["interests", "phoneNumber"],
      validate: () => formData.interests.length > 0
    }
  ];

  // Check if the current step is valid
  useEffect(() => {
    setIsStepValid(steps[currentStep].validate());
  }, [formData, currentStep]);

  useEffect(() => {
    if (initialData?.phoneNumber) {
      const countryCode = countryCodes.find((cc) =>
        initialData.phoneNumber.startsWith(cc.code),
      );

      if (countryCode) {
        setSelectedCountryCode(countryCode.code);
      }
    }
  }, [initialData]);

  useEffect(() => {
    if (formData.educationLevelId) {
      setIsLoadingProgramStudies(true);
      fetchProgramStudies(formData.educationLevelId)
        .then((studies) => {
          setProgramStudies(studies);
        })
        .catch((error) => {
          console.error("Error fetching program studies:", error);
        })
        .finally(() => {
          setIsLoadingProgramStudies(false);
        });
    }
  }, [formData.educationLevelId]);

  const handleInterestsChange = useCallback((interests: string[]) => {
    setFormData((prev) => {
      // Only update if the interests actually changed
      if (JSON.stringify(prev.interests) !== JSON.stringify(interests)) {
        clearError("interests");
        return { ...prev, interests };
      }
      return prev;
    });
  }, [clearError]);

  const handleMultiSelectChange = (field: keyof FormData, value: string[]) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    clearError(field);
  };

  const handleLocationSelect = (field: string, id: number, name: string) => {
    console.log(field, id, name)
    switch (field) {
      case 'province':
        setFormData(prev => ({
          ...prev,
          provinceId: id,
          provinceName: name ? name : provinces.find(p => p.id === id)?.nama || ''
        }));
        break;
      case 'city':
        setFormData(prev => ({
          ...prev,
          cityId: id,
          cityName: name ? name : cities.find(c => c.id === id)?.nama || ''
        }));
        break;
      case 'district':
        setFormData(prev => ({
          ...prev,
          districtId: id,
          districtName: name ? name : districts.find(d => d.id === id)?.nama || ''
        }));
        break;
      case 'village':
        setFormData(prev => ({
          ...prev,
          villageId: id,
          villageName: name ? name : villages.find(v => v.id === id)?.nama || ''
        }));
        break;
      case 'educationLevel':
        setFormData(prev => ({
          ...prev,
          educationLevelId: id,
          educationLevelName: name ? name : educationLevels.find(l => l.id === id)?.name || ''
        }));
        break;
    }
  };
  
  const handleProgramStudySelect = (id: number, name: string | null = null) => {
    // Log the incoming values to help with debugging
    console.log("Program Study Selection:", { id, name });
    
    if (id === 0) {
      setFormData(prev => ({
        ...prev,
        programStudyId: 0,
        programStudyName: ""
      }));
      return;
    }
    
    // Try to find the program study in our loaded list
    const programStudy = programStudies.find(ps => ps.kode_pend === id);
    
    // Determine the name to use:
    // 1. Use the explicitly provided name if it exists
    // 2. Use the name from the found program study if available
    // 3. Keep the existing name if we have nothing else
    const programStudyName = name ? name : 
                            (programStudy ? programStudy.nama_pend : 
                            (formData.programStudyName || ""));
    
    console.log("Setting program study name to:", programStudyName);
    
    setFormData(prev => ({
      ...prev,
      programStudyId: id,
      programStudyName: programStudyName
    }));
  };

  const handleChipsSelection = (field: keyof FormData, value: string) => {
    setFormData((prev) => {
      const current = [...(prev[field] as string[])];
      const index = current.indexOf(value);
      
      if (index > -1) {
        current.splice(index, 1);
      } else {
        current.push(value);
      }
      
      return { ...prev, [field]: current };
    });
    clearError(field);
  };

  // Convert the range selection to a representative integer value
  const handleBudgetChange = (selectedValue: string) => {
    let budgetValue: number;
    switch (selectedValue) {
      case "0-100000":
        budgetValue = 100000;
        break;
      case "100001-300000":
        budgetValue = 300000;
        break;
      case "300001-500000":
        budgetValue = 500000;
        break;
      case "500001-1000000":
        budgetValue = 1000000;
        break;
      case "1000001+":
        budgetValue = 1500000; // Representative value for "more than 1 million"
        break;
      default:
        budgetValue = 0;
    }
    handleInputChange("studyBudget", budgetValue);
  };

  useEffect(() => {
    const fetchEducationLevelsData = async () => {
      try {
        const levels = await getEducationLevels();
        console.log('Fetched Education Levels:', levels);
        setEducationLevels(levels);
  
        // If there's an initial education level ID, try to find and populate its name
        if (formData.educationLevelId) {
          const foundLevel = levels.find(l => l.id === formData.educationLevelId);
          if (foundLevel) {
            setFormData(prev => ({
              ...prev,
              educationLevelName: foundLevel.name
            }));
          }
        }
      } catch (error) {
        console.error('Error fetching education levels:', error);
      }
    };
  
    fetchEducationLevelsData();
  }, []);

  useEffect(() => {
    const fetchProvincesData = async () => {
      try {
        const fetchedProvinces = await fetchProvinces();
        console.log('Fetched Provinces:', fetchedProvinces);
        
        // Verify the structure of fetched provinces
        if (fetchedProvinces && fetchedProvinces.length > 0) {
          setProvinces(fetchedProvinces);
          
          // If there's an initial province ID, try to find and populate its name
          if (formData.provinceId) {
            const foundProvince = fetchedProvinces.find(p => p.id === formData.provinceId);
            console.log(foundProvince)
            if (foundProvince) {
              setFormData(prev => ({
                ...prev,
                provinceName: foundProvince.nama
              }));
            }
          }
        } else {
          console.warn('No provinces fetched');
        }
      } catch (error) {
        console.error('Error fetching provinces:', error);
      }
    };
  
    fetchProvincesData();
  }, []);

  useEffect(() => {
    if (formData.provinceId) {
      const fetchCitiesData = async () => {
        try {
          const fetchedCities = await fetchCities(formData.provinceId);
          console.log('Fetched Cities:', fetchedCities);
          setCities(fetchedCities);
  
          // If there's an initial city ID, try to find and populate its name
          if (formData.cityId) {
            const foundCity = fetchedCities.find(c => c.id === formData.cityId);
            if (foundCity) {
              setFormData(prev => ({
                ...prev,
                cityName: foundCity.nama
              }));
            }
          }
        } catch (error) {
          console.error('Error fetching cities:', error);
        }
      };
  
      fetchCitiesData();
    }
  }, [formData.provinceId]);

  useEffect(() => {
    if (formData.cityId) {
      const fetchDistrictsData = async () => {
        try {
          const fetchedDistricts = await fetchDistricts(formData.cityId);
          console.log('Fetched Districts:', fetchedDistricts);
          setDistricts(fetchedDistricts);
  
          // If there's an initial city ID, try to find and populate its name
          if (formData.cityId) {
            const foundDistrict = fetchedDistricts.find(c => c.id === formData.cityId);
            if (foundDistrict) {
              setFormData(prev => ({
                ...prev,
                districtName: foundDistrict.nama
              }));
            }
          }
        } catch (error) {
          console.error('Error fetching Districts:', error);
        }
      };
  
      fetchDistrictsData();
    }
  }, [formData.cityId]);

  useEffect(() => {
    if (formData.districtId) {
      const fetchVillagesData = async () => {
        try {
          const fetchedVillages = await fetchVillages(formData.districtId);
          console.log('Fetched Villages:', fetchedVillages);
          setVillages(fetchedVillages);
  
          // If there's an initial city ID, try to find and populate its name
          if (formData.districtId) {
            const foundVillage = fetchedVillages.find(c => c.id === formData.districtId);
            if (foundVillage) {
              setFormData(prev => ({
                ...prev,
                villageName: foundVillage.nama
              }));
            }
          }
        } catch (error) {
          console.error('Error fetching Villages:', error);
        }
      };
  
      fetchVillagesData();
    }
  }, [formData.districtId]);

  // Fetch formations when program study changes
  useEffect(() => {
    if (formData.programStudyId) {
      setIsLoadingFormations(true);
      fetchFormations(formData.programStudyId)
        .then((data) => {
          setFormations(data);
          const jabatanSet = new Set(data.map(f => f.jabatan_nm));
          setUniqueJabatan(Array.from(jabatanSet));
        })
        .catch((error) => {
          console.error("Error fetching formations:", error);
        })
        .finally(() => {
          setIsLoadingFormations(false);
        });
    }
  }, [formData.programStudyId]);

  // Filter formations when jabatan changes
  useEffect(() => {
    if (formData.targetJabatan) {
      const filtered = formations.filter(f => f.jabatan_nm === formData.targetJabatan);
      setFilteredFormations(filtered);
    } else {
      setFilteredFormations([]);
    }
  }, [formData.targetJabatan, formations]);

  // Update form data when formation is selected
  const handleFormationSelect = (formationId: string) => {
    const formation = formations.find(f => f.formasi_id === formationId);
    if (formation) {
      setSelectedFormation(formation);
      setFormData(prev => ({
        ...prev,
        targetJabatan: formation.jabatan_nm,
        targetInstitution: formation.ins_nm
      }));
    }
  };

  // Format salary range for display
  const formatSalary = (min: string, max: string) => {
    const formatter = new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    });
    return `${formatter.format(parseInt(min))} - ${formatter.format(parseInt(max))}`;
  };

  useEffect(() => {
    onFormDataChange(formData);
  }, [formData, onFormDataChange]);

  const handleInputChange = <K extends keyof FormData>(
    field: K,
    value: FormData[K] | string,
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    clearError(field); // Clear the error for this field
  };

  const handlePhoneNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let input = e.target.value.replace(/\D/g, "");

    if (input.startsWith("0")) {
      input = input.slice(1);
    }

    const maxLength = 12;

    input = input.slice(0, maxLength);

    handleInputChange("phoneNumber", `${selectedCountryCode}${input}`);
  };

  const handleDateChange = (dateString: string | null) => {
    console.log(dateString); // This will log: '1999-01-11' for January 11, 1999
    handleInputChange("birthDate", dateString);
  };

  // Convert budget value back to range for display
  const getBudgetRange = (value: number): string => {
    if (value <= 0) return "";
    if (value <= 100000) return "0-100000";
    if (value <= 300000) return "100001-300000";
    if (value <= 500000) return "300001-500000";
    if (value <= 1000000) return "500001-1000000";
    return "1000001+";
  };

  const handleFormComplete = async () => {
    if (!formData) {
      return;
    }
  
    try {
      setIsSubmitting(true);
      setSubmitResult(null);
      
      // Only call onComplete if it exists
      if (onComplete) {
        await onComplete();
        
        // If we get here, it means the parent component didn't navigate away
        // So we show a success animation
        setSubmitResult({
          success: true,
          message: "Data berhasil disimpan!"
        });
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      setSubmitResult({
        success: false,
        message: error instanceof Error ? error.message : "Terjadi kesalahan saat menyimpan data"
      });
    } finally {
      setIsSubmitting(false);
    }
  };


  // Add a new useEffect to handle scrolling
  useEffect(() => {
    if (scrollRef.current && formRef.current) {
      formRef.current.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'start' 
      });
      scrollRef.current = false;
    }
  }, [currentStep]);

  const nextStep = () => {
    // Directly check if the current step is valid instead of using isStepValid state
    const currentStepIsValid = steps[currentStep].validate();
    
    if (currentStepIsValid && currentStep < steps.length - 1) {
      // Use setTimeout to ensure DOM has updated
      setTimeout(() => {
        if (formRef.current) {
          formRef.current.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
          });
        }
      }, 100); // Small delay to allow rendering
      
      setCurrentStep(currentStep + 1);
    } else if (currentStepIsValid && currentStep === steps.length - 1) {
      // We're at the last step, so handle form completion
      handleFormComplete();
    }
  };
  
  const prevStep = () => {
    if (currentStep > 0) {
      // Use setTimeout to ensure DOM has updated
      setTimeout(() => {
        if (formRef.current) {
          formRef.current.scrollIntoView({ 
            behavior: 'smooth', 
            block: 'start' 
          });
        }
      }, 100); // Small delay to allow rendering
  
      setCurrentStep(currentStep - 1);
    }
  };

  const renderError = (field: keyof FormData) => {
    // Use the formErrors prop that's already available in the component scope
    if (!formErrors) {
      return null;
    }
    
    return (
      formErrors[field] ? (
        <span className="text-red-500 text-sm">{formErrors[field]}</span>
      ) : null
    );
  };

  // Render the current step's fields with enhanced UI
  const renderCurrentStep = () => {
    const currentStepData = steps[currentStep];
    const fieldsToRender = typeof currentStepData.fields === 'function' 
    ? currentStepData.fields() 
    : currentStepData.fields;
    
    return (
      <div className="space-y-6 py-4">
        <div className="text-center mb-8">
          <div className="flex justify-center mb-2">
            <div className="w-16 h-16 rounded-full bg-primary/20 flex items-center justify-center text-3xl">
              {currentStepData.icon}
            </div>
          </div>
          <h2 className="text-2xl font-bold">{currentStepData.title}</h2>
          <p className="text-gray-600">{currentStepData.description}</p>
        </div>

        <div className="space-y-6">
          {fieldsToRender.includes("gender") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Jenis Kelamin</h3>
              <div className="flex gap-4">
                <button 
                  className={`flex-1 flex flex-col items-center p-4 border rounded-xl cursor-pointer transition-all ${formData.gender === "laki-laki" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("gender", "laki-laki")}
                >
                  <div className="text-4xl mb-2">👨</div>
                  <div className="font-medium">Laki-laki</div>
                </button>
                <button 
                  className={`flex-1 flex flex-col items-center p-4 border rounded-xl cursor-pointer transition-all ${formData.gender === "perempuan" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("gender", "perempuan")}
                >
                  <div className="text-4xl mb-2">👩</div>
                  <div className="font-medium">Perempuan</div>
                </button>
              </div>
              {renderError("gender")}
            </div>
          )}

          {fieldsToRender.includes("birthDate") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Tahun Lahir</h3>
              <p className="text-sm text-gray-600 mb-4">
      Tahun kelahiran kamu membantu kami menyesuaikan pengalaman belajar yang tepat untuk kebutuhan kamu. Kami menjaga privasi data kamu dengan ketat.
    </p>
              <BirthDateInput required onChange={handleDateChange} />
              {renderError("birthDate")}
            </div>
          )}

          {fieldsToRender.includes("targetUniversity") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Universitas Tujuan</h3>
              <UniversitySearch 
                onUniversitySelect={(university) => {
                  handleInputChange("targetUniversity", university);
                }}
              />
              {renderError("targetUniversity")}
            </div>
          )}

          {fieldsToRender.includes("targetMajor") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Program Studi/Jurusan</h3>
              <input
                type="text"
                className="w-full p-3 border rounded-lg"
                placeholder="Masukkan program studi atau jurusan"
                value={formData.targetMajor}
                onChange={(e) => handleInputChange("targetMajor", e.target.value)}
              />
              {renderError("targetMajor")}
            </div>
          )}

          {/* Province field */}
          {fieldsToRender.includes("provinceId") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Provinsi</h3>
              <select 
                className="w-full p-3 border rounded-lg bg-white"
                value={formData.provinceId}
                onChange={(e) => {
                  const selectedId = parseInt(e.target.value);
                  console.log('Selected Province ID:', selectedId);
                  
                  // Find the province in the current list
                  const selectedProvince = provinces.find(p => p.id === selectedId);
                  
                  if (selectedProvince) {
                    console.log('Selected Province:', selectedProvince);
                    handleLocationSelect('province', selectedId, selectedProvince.nama);
                  } else {
                    console.warn('Province not found for ID:', selectedId);
                    handleLocationSelect('province', selectedId, '');
                  }
                }}
              >
                <option value="">Pilih Provinsi</option>
                {provinces.map((province) => (
                  <option key={province.id} value={province.id}>
                    {province.nama}
                  </option>
                ))}
              </select>
              {renderError("provinceId")}
            </div>
          )}

          {/* City field */}
          {fieldsToRender.includes("cityId") && formData.provinceId > 0 && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Kabupaten/Kota</h3>
              <select 
                className="w-full p-3 border rounded-lg bg-white"
                value={formData.cityId}
                onChange={(e) => {
                  const selectedId = parseInt(e.target.value);
                  const selectedCity = cities.find(c => c.id === selectedId);
                  const cityName = selectedCity ? selectedCity.nama : '';
                  console.log(cityName)
                  handleLocationSelect('city', selectedId, cityName);
                }}
              >
                <option value="">Pilih Kabupaten/Kota</option>
                {cities.map((city) => (
                  <option key={city.id} value={city.id}>
                    {city.nama}
                  </option>
                ))}
              </select>
              {renderError("cityId")}
            </div>
          )}

          {/* District field */}
          {fieldsToRender.includes("districtId") && formData.cityId > 0 && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Kecamatan</h3>
              <select 
                className="w-full p-3 border rounded-lg bg-white"
                value={formData.districtId}
                onChange={(e) => {
                  const selectedId = parseInt(e.target.value);
                  const selectedDistrict = districts.find(d => d.id === selectedId);
                  const districtName = selectedDistrict ? selectedDistrict.nama : '';
                  handleLocationSelect('district', selectedId, districtName);
                }}
              >
                <option value="">Pilih Kecamatan</option>
                {districts.map((district) => (
                  <option key={district.id} value={district.id}>
                    {district.nama}
                  </option>
                ))}
              </select>
              {renderError("districtId")}
            </div>
          )}

          {/* Village field */}
          {fieldsToRender.includes("villageId") && formData.districtId > 0 && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Kelurahan</h3>
              <select 
                className="w-full p-3 border rounded-lg bg-white"
                value={formData.villageId}
                onChange={(e) => {
                  const selectedId = parseInt(e.target.value);
                  const selectedVillage = villages.find(v => v.id === selectedId);
                  const villageName = selectedVillage ? selectedVillage.nama : '';
                  handleLocationSelect('village', selectedId, villageName);
                }}
              >
                <option value="">Pilih Kelurahan</option>
                {villages.map((village) => (
                  <option key={village.id} value={village.id}>
                    {village.nama}
                  </option>
                ))}
              </select>
              {renderError("villageId")}
            </div>
          )}

          {/* Education level field */}
          {fieldsToRender.includes("educationLevelId") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Jenjang Pendidikan</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {educationLevels.map((level) => (
                  <button
                    key={level.id}
                    className={`p-3 border rounded-lg cursor-pointer text-center transition-all ${
                      formData.educationLevelId === level.id
                        ? "border-primary bg-primary/10 shadow-sm"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                    onClick={() => handleLocationSelect('educationLevel', level.id, level.name)}
                  >
                    {level.name}
                  </button>
                ))}
              </div>
              {renderError("educationLevelId")}
            </div>
          )}

          {/* Program study field */}
          {fieldsToRender.includes("programStudyId") && formData.educationLevelId > 0 && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Program Studi</h3>
              <ProgramStudySelect
                required
                selectedEducationLevel={formData.educationLevelId}
                onSelectionChange={(value, name) => {
                  handleProgramStudySelect(value, name as string);
                }}
              />
              {renderError("programStudyId")}
            </div>
          )}

          {fieldsToRender.includes("lastOccupation") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Pekerjaan Terakhir</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <button 
                  className={`p-4 border rounded-xl flex flex-col items-center cursor-pointer transition-all ${formData.lastOccupation === "pelajar" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("lastOccupation", "pelajar")}
                >
                  <div className="text-3xl mb-2">🎒</div>
                  <div className="font-medium">Pelajar/Mahasiswa</div>
                </button>
                <button 
                  className={`p-4 border rounded-xl flex flex-col items-center cursor-pointer transition-all ${formData.lastOccupation === "profesional" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("lastOccupation", "profesional")}
                >
                  <div className="text-3xl mb-2">💼</div>
                  <div className="font-medium">Profesional</div>
                </button>
                <button 
                  className={`p-4 border rounded-xl flex flex-col items-center cursor-pointer transition-all ${formData.lastOccupation === "wirausaha" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("lastOccupation", "wirausaha")}
                >
                  <div className="text-3xl mb-2">🏪</div>
                  <div className="font-medium">Wirausaha</div>
                </button>
                <button 
                  className={`p-4 border rounded-xl flex flex-col items-center cursor-pointer transition-all ${formData.lastOccupation === "freelancer" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("lastOccupation", "freelancer")}
                >
                  <div className="text-3xl mb-2">🖥️</div>
                  <div className="font-medium">Freelancer</div>
                </button>
                <button 
                  className={`p-4 border rounded-xl flex flex-col items-center cursor-pointer transition-all ${formData.lastOccupation === "lainnya" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("lastOccupation", "lainnya")}
                >
                  <div className="text-3xl mb-2">📋</div>
                  <div className="font-medium">Lainnya</div>
                </button>
              </div>
              {renderError("lastOccupation")}
            </div>
          )}

          {fieldsToRender.includes("targetJabatan") && (
            <div className="space-y-4">
              <div className="bg-white p-6 rounded-xl shadow-sm border">
                <h3 className="text-lg font-semibold mb-4">Target Jabatan (Opsional)</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {uniqueJabatan.slice(0, 8).map((jabatan) => (
                    <button
                      key={jabatan}
                      className={`p-3 border rounded-lg cursor-pointer transition-all ${
                        formData.targetJabatan === jabatan
                          ? "border-primary bg-primary/10 shadow-sm"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                      onClick={() => handleInputChange("targetJabatan", jabatan)}
                    >
                      {jabatan}
                    </button>
                  ))}
                </div>
                {renderError("targetJabatan")}
              </div>

              {formData.targetJabatan && filteredFormations.length > 0 && (
                <div className="bg-white p-6 rounded-xl shadow-sm border">
                  <h3 className="text-lg font-semibold mb-4">Pilih Formasi yang Tersedia</h3>
                  <div className="space-y-3">
                    {filteredFormations.slice(0, 3).map((formation) => (
                      <Card 
                        key={formation.formasi_id}
                        isPressable
                        onPress={() => handleFormationSelect(formation.formasi_id)}
                        className={`border-2 ${
                          selectedFormation?.formasi_id === formation.formasi_id
                            ? "border-primary"
                            : "border-transparent"
                        }`}
                      >
                        <CardBody className="p-3">
                          <div className="space-y-1">
                            <div className="flex justify-between items-center">
                              <p className="font-medium">{formation.ins_nm}</p>
                              <Chip color="primary" variant="flat" size="sm">Kuota: {formation.jumlah_formasi}</Chip>
                            </div>
                            <p className="text-sm">{formation.lokasi_nm}</p>
                            <Divider className="my-2" />
                            <div className="flex justify-between text-sm">
                              <span>{formation.formasi_nm}</span>
                            </div>
                            <p className="text-sm text-default-500">
                              Kisaran Gaji: {formatSalary(formation.gaji_min, formation.gaji_max)}
                            </p>
                            <p className="text-sm text-default-500">
                              Jumlah Pelamar: <span className="font-semibold">{formation.jumlah_ms}</span>
                            </p>
                          </div>
                        </CardBody>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {fieldsToRender.includes("targetScore") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Target Nilai</h3>
              <p className="text-sm text-gray-600 mb-4">Nilai target yang ingin kamu capai di ujian</p>
              
              {/* Add recommendation box */}
              {formData.mainPurpose && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start">
                    <div className="text-blue-500 mr-2 text-xl">💡</div>
                    <div>
                      {/* Only show the recommended score value if user hasn't set a custom value */}
                      <p className="font-medium text-blue-700">
                        {formData.targetScore !== getTargetScoreRecommendation(formData.mainPurpose).score ? 
                          `Rekomendasi: ${getTargetScoreRecommendation(formData.mainPurpose).score}` : 
                          "Rekomendasi Nilai"}
                      </p>
                      <p className="text-sm text-blue-600">{getTargetScoreRecommendation(formData.mainPurpose).description}</p>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="space-y-4">
                <input
                  type="number"
                  min="0"
                  max="999"
                  step="1"
                  value={formData.targetScore === 0 ? "" : formData.targetScore}
                  onChange={(e) => {
                    const value = e.target.value === "" ? 0 : parseInt(e.target.value);
                    handleInputChange("targetScore", value);
                  }}
                  onWheel={(e) => (e.target as HTMLInputElement).blur()} // Prevent value change on mouse wheel
                  className="w-full p-3 border rounded-lg [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                  placeholder="Masukkan target nilai"
                />
                <div className="text-sm text-gray-500">
                  *Masukkan angka saja
                </div>
              </div>
            </div>
          )}

          {fieldsToRender.includes("preferredStudyMethods") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Metode Belajar yang Kamu Sukai</h3>
              <p className="text-sm text-gray-600 mb-4">Pilih satu atau lebih metode belajar yang paling efektif untukmu</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <button 
                  className={`p-4 border rounded-xl cursor-pointer transition flex items-start ${formData.preferredStudyMethods.includes("video") ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleChipsSelection("preferredStudyMethods", "video")}
                >
                  <div className="text-2xl mr-3 flex-shrink-0">🎬</div>
                  <div className="text-left">
                    <div className="font-medium">Video Pembelajaran</div>
                    <div className="text-xs text-gray-500">Belajar melalui video tutorial</div>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer transition flex items-start ${formData.preferredStudyMethods.includes("practice") ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleChipsSelection("preferredStudyMethods", "practice")}
                >
                  <div className="text-2xl mr-3 flex-shrink-0">✏️</div>
                  <div className="text-left">
                    <div className="font-medium">Latihan Soal</div>
                    <div className="text-xs text-gray-500">Belajar dengan mengerjakan soal-soal</div>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer transition flex items-start ${formData.preferredStudyMethods.includes("textbook") ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleChipsSelection("preferredStudyMethods", "textbook")}
                >
                  <div className="text-2xl mr-3 flex-shrink-0">📚</div>
                  <div className="text-left">
                    <div className="font-medium">Belajar dari Buku (atau PDF)</div>
                    <div className="text-xs text-gray-500">Membaca dan mempelajari materi tertulis</div>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer transition flex items-start ${formData.preferredStudyMethods.includes("discussion") ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleChipsSelection("preferredStudyMethods", "discussion")}
                >
                  <div className="text-2xl mr-3 flex-shrink-0">👥</div>
                  <div className="text-left">
                    <div className="font-medium">Diskusi Kelompok</div>
                    <div className="text-xs text-gray-500">Belajar melalui interaksi dengan orang lain</div>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer transition flex items-start ${formData.preferredStudyMethods.includes("interactive") ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleChipsSelection("preferredStudyMethods", "interactive")}
                >
                  <div className="text-2xl mr-3 flex-shrink-0">🎮</div>
                  <div className="text-left">
                    <div className="font-medium">Pembelajaran Interaktif</div>
                    <div className="text-xs text-gray-500">Belajar melalui quiz, games, dan simulasi</div>
                  </div>
                </button>
              </div>
              {renderError("preferredStudyMethods")}
            </div>
          )}

          {fieldsToRender.includes("weeklyStudyTime") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Waktu Belajar per Minggu</h3>
              <p className="text-sm text-gray-600 mb-4">Berapa jam yang kamu rencanakan untuk belajar setiap minggunya?</p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${formData.weeklyStudyTime === "0-5" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("weeklyStudyTime", "0-5")}
                >
                  <div className="text-2xl mb-1">⏱️</div>
                  <div className="font-medium">0-5 jam</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${formData.weeklyStudyTime === "6-10" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("weeklyStudyTime", "6-10")}
                >
                  <div className="text-2xl mb-1">⏱️⏱️</div>
                  <div className="font-medium">6-10 jam</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${formData.weeklyStudyTime === "11-15" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("weeklyStudyTime", "11-15")}
                >
                  <div className="text-2xl mb-1">⏱️⏱️⏱️</div>
                  <div className="font-medium">11-15 jam</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${formData.weeklyStudyTime === "16-20" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("weeklyStudyTime", "16-20")}
                >
                  <div className="text-2xl mb-1">⏱️⏱️⏱️⏱️</div>
                  <div className="font-medium">16-20 jam</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${formData.weeklyStudyTime === "20+" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("weeklyStudyTime", "20+")}
                >
                  <div className="text-2xl mb-1">⏱️⏱️⏱️⏱️⏱️</div>
                  <div className="font-medium">20+ jam</div>
                </button>
              </div>
              {renderError("weeklyStudyTime")}
            </div>
          )}

          {fieldsToRender.includes("primaryDevices") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Perangkat untuk Belajar</h3>
              <p className="text-sm text-gray-600 mb-4">Perangkat apa yang kamu gunakan untuk belajar online? (Pilih satu atau lebih)</p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${formData.primaryDevices.includes("smartphone") ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleChipsSelection("primaryDevices", "smartphone")}
                >
                  <div className="text-3xl mb-2">📱</div>
                  <div className="font-medium">Smartphone</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${formData.primaryDevices.includes("tablet") ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleChipsSelection("primaryDevices", "tablet")}
                >
                  <div className="text-3xl mb-2">📱</div>
                  <div className="font-medium">Tablet</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${formData.primaryDevices.includes("laptop") ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleChipsSelection("primaryDevices", "laptop")}
                >
                  <div className="text-3xl mb-2">💻</div>
                  <div className="font-medium">Laptop</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${formData.primaryDevices.includes("desktop") ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleChipsSelection("primaryDevices", "desktop")}
                >
                  <div className="text-3xl mb-2">🖥️</div>
                  <div className="font-medium">Komputer Desktop</div>
                </button>
              </div>
              {renderError("primaryDevices")}
            </div>
          )}

          {fieldsToRender.includes("learningStyle") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Gaya Belajar</h3>
              <p className="text-sm text-gray-600 mb-4">Bagaimana cara kamu paling efektif dalam menyerap informasi?</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button 
                  className={`p-4 border rounded-xl cursor-pointer transition flex items-center ${formData.learningStyle === "visual" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("learningStyle", "visual")}
                >
                  <div className="text-3xl mr-3">👁️</div>
                  <div className="text-left">
                    <div className="font-medium">Visual</div>
                    <div className="text-xs text-gray-500">Belajar melalui gambar, diagram, dan grafik</div>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer transition flex items-center ${formData.learningStyle === "auditory" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("learningStyle", "auditory")}
                >
                  <div className="text-3xl mr-3">👂</div>
                  <div className="text-left">
                    <div className="font-medium">Auditori</div>
                    <div className="text-xs text-gray-500">Belajar melalui pendengaran dan diskusi</div>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer transition flex items-center ${formData.learningStyle === "reading" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("learningStyle", "reading")}
                >
                  <div className="text-3xl mr-3">📝</div>
                  <div className="text-left">
                    <div className="font-medium">Membaca/Menulis</div>
                    <div className="text-xs text-gray-500">Belajar melalui teks dan menulis catatan</div>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer transition flex items-center ${formData.learningStyle === "kinesthetic" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleInputChange("learningStyle", "kinesthetic")}
                >
                  <div className="text-3xl mr-3">👐</div>
                  <div className="text-left">
                    <div className="font-medium">Kinestetik</div>
                    <div className="text-xs text-gray-500">Belajar melalui praktik dan pengalaman</div>
                  </div>
                </button>
              </div>
              {renderError("learningStyle")}
            </div>
          )}

          {fieldsToRender.includes("studyBudget") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Budget Belajar per Bulan</h3>
              <p className="text-sm text-gray-600 mb-4">Berapa budget yang kamu siapkan untuk belajar setiap bulan?</p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${getBudgetRange(formData.studyBudget) === "0-100000" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleBudgetChange("0-100000")}
                >
                  <div className="text-xl mb-1">💰</div>
                  <div className="font-medium">Rp 0 - Rp 100.000</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${getBudgetRange(formData.studyBudget) === "100001-300000" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleBudgetChange("100001-300000")}
                >
                  <div className="text-xl mb-1">💰💰</div>
                  <div className="font-medium">Rp 100.001 - Rp 300.000</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${getBudgetRange(formData.studyBudget) === "300001-500000" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleBudgetChange("300001-500000")}
                >
                  <div className="text-xl mb-1">💰💰💰</div>
                  <div className="font-medium">Rp 300.001 - Rp 500.000</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${getBudgetRange(formData.studyBudget) === "500001-1000000" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleBudgetChange("500001-1000000")}
                >
                  <div className="text-xl mb-1">💰💰💰💰</div>
                  <div className="font-medium">Rp 500.001 - Rp 1.000.000</div>
                </button>
                
                <button 
                  className={`p-4 border rounded-xl cursor-pointer text-center transition-all ${getBudgetRange(formData.studyBudget) === "1000001+" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                  onClick={() => handleBudgetChange("1000001+")}
                >
                  <div className="text-xl mb-1">💰💰💰💰💰</div>
                  <div className="font-medium">Lebih dari Rp 1.000.000</div>
                </button>
              </div>
              {renderError("studyBudget")}
            </div>
          )}

          {fieldsToRender.includes("learningChallenges") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Tantangan dalam Belajar</h3>
              <p className="text-sm text-gray-600 mb-4">Apa saja tantangan yang kamu hadapi saat belajar? (Pilih satu atau lebih)</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {learningChallengesOptions.map(challenge => (
                  <button 
                    key={challenge.id}
                    className={`p-3 border rounded-xl cursor-pointer transition flex items-center ${formData.learningChallenges.includes(challenge.id) ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                    onClick={() => handleChipsSelection("learningChallenges", challenge.id)}
                  >
                    <div className="text-2xl mr-3">{challenge.icon}</div>
                    <div className="font-medium">{challenge.label}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {fieldsToRender.includes("interests") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Minat dan Hobi</h3>
              <UserInterests required onInterestsChange={handleInterestsChange} />
              {formErrors.interests && (
                <span className="text-red-500 text-sm">{formErrors.interests}</span>
              )}
            </div>
          )}

          {fieldsToRender.includes("mainPurpose") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Tujuan Utama Menggunakan Platform Ini</h3>
              <p className="text-sm text-gray-600 mb-4">Apa tujuan utamamu menggunakan platform belajar kami?</p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button 
                className={`p-5 border rounded-xl cursor-pointer transition flex flex-col items-center ${formData.mainPurpose === "persiapan_cpns" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                onClick={() => handleMainPurposeChange("persiapan_cpns")}
              >
                <div className="text-4xl mb-3">🏛️</div>
                <div className="font-medium text-center">Persiapan CPNS</div>
                <div className="text-xs text-gray-500 text-center mt-1">Persiapan untuk ujian seleksi CPNS</div>
              </button>

              <button 
                className={`p-5 border rounded-xl cursor-pointer transition flex flex-col items-center ${formData.mainPurpose === "persiapan_lpdp" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                onClick={() => handleMainPurposeChange("persiapan_lpdp")}
              >
                <div className="text-4xl mb-3">🎓</div>
                <div className="font-medium text-center">Persiapan LPDP</div>
                <div className="text-xs text-gray-500 text-center mt-1">Persiapan untuk beasiswa LPDP</div>
              </button>

              <button 
                className={`p-5 border rounded-xl cursor-pointer transition flex flex-col items-center ${formData.mainPurpose === "belajar_utbk" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                onClick={() => handleMainPurposeChange("belajar_utbk")}
              >
                <div className="text-4xl mb-3">📝</div>
                <div className="font-medium text-center">Belajar UTBK</div>
                <div className="text-xs text-gray-500 text-center mt-1">Persiapan untuk Ujian Tulis Berbasis Komputer</div>
              </button>

              <button 
                className={`p-5 border rounded-xl cursor-pointer transition flex flex-col items-center ${formData.mainPurpose === "belajar" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                onClick={() => handleMainPurposeChange("belajar")}
              >
                <div className="text-4xl mb-3">📚</div>
                <div className="font-medium text-center">Belajar Materi Baru</div>
                <div className="text-xs text-gray-500 text-center mt-1">Mempelajari topik atau materi baru</div>
              </button>

              <button 
                className={`p-5 border rounded-xl cursor-pointer transition flex flex-col items-center ${formData.mainPurpose === "latihan" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                onClick={() => handleMainPurposeChange("latihan")}
              >
                <div className="text-4xl mb-3">✏️</div>
                <div className="font-medium text-center">Latihan Soal</div>
                <div className="text-xs text-gray-500 text-center mt-1">Berlatih mengerjakan berbagai soal</div>
              </button>

              <button 
                className={`p-5 border rounded-xl cursor-pointer transition flex flex-col items-center ${formData.mainPurpose === "konsultasi" ? "border-primary bg-primary/10" : "border-gray-200 hover:border-gray-300"}`}
                onClick={() => handleMainPurposeChange("konsultasi")}
              >
                <div className="text-4xl mb-3">💬</div>
                <div className="font-medium text-center">Konsultasi Karir/Pendidikan</div>
                <div className="text-xs text-gray-500 text-center mt-1">Mendapatkan bimbingan untuk karir/pendidikan</div>
              </button>
              </div>
              {renderError("mainPurpose")}
            </div>
          )}

          {fieldsToRender.includes("phoneNumber") && (
            <div className="bg-white p-6 rounded-xl shadow-sm border">
              <h3 className="text-lg font-semibold mb-4">Nomor Telepon (Opsional)</h3>
              <p className="text-sm text-gray-600 mb-4">Akan digunakan untuk notifikasi penting dan promo</p>
              
              <div className="flex space-x-2">
                <div className="w-1/3 md:w-1/4">
                  <select
                    className="w-full p-2 border rounded-lg"
                    value={selectedCountryCode}
                    onChange={(e) => setSelectedCountryCode(e.target.value)}
                  >
                    {countryCodes.map((country) => (
                      <option key={country.code} value={country.code}>
                        {country.code} {country.name}
                      </option>
                    ))}
                  </select>
                </div>
                <input
                  className="w-2/3 md:w-3/4 p-2 border rounded-lg"
                  placeholder="Masukkan nomor HP tanpa 0 di depan"
                  type="tel"
                  value={formData.phoneNumber.slice(selectedCountryCode.length)}
                  onChange={handlePhoneNumberChange}
                />
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="container mx-auto p-4 max-w-3xl bg-gradient-to-b from-white to-blue-50 rounded-lg shadow-md">
      <div className="py-4">
        <div className="flex flex-row items-center">
          <div className="w-1/2">
            <DotLottieAnimation
              src="/dotlotties/diagnostic.lottie"
              autoplay
              loop
              width="100%"
              height="100%"
            />
          </div>
          
          <div className="w-1/2">
            <h1 className="text-xl md:text-3xl font-bold">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
                Diagnostic Quiz
              </span>
            </h1>
          </div>
        </div>
        <p className="text-center text-gray-600 mb-8">
          Bantu kami mengenal kamu lebih baik untuk pengalaman belajar yang lebih personal dan biaya yang lebih terjangkau<br />
          (<Link href="https://terang.ai/privacy-policy">
            <span className="text-blue-500 hover:underline">
              Baca kebijakan privasi di Terang AI
            </span>
          </Link>)
        </p>
        
        {/* Steps progress indicator */}
        <div className="mb-8">
          <div ref={formRef} className="flex justify-between mb-2">
            {steps.map((step, index) => (
              <Tooltip key={index} content={step.title}>
                <div 
                  className={`flex flex-col items-center ${index <= currentStep ? 'text-primary' : 'text-gray-400'}`}
                >
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center mb-1 ${
                    index < currentStep 
                      ? 'bg-primary text-white' 
                      : index === currentStep 
                        ? 'border-2 border-primary bg-primary/10' 
                        : 'border border-gray-300'
                  }`}>
                    {index < currentStep ? '✓' : step.icon}
                  </div>
                  <div className="text-xs hidden md:block">{step.title}</div>
                </div>
              </Tooltip>
            ))}
          </div>
          
          <Progress 
            size="md" 
            value={(currentStep / (steps.length - 1)) * 100} 
            color="primary"
            className="mb-2"
          />
          <p className="text-sm text-right text-gray-500">
            Langkah {currentStep + 1} dari {steps.length}
          </p>
        </div>
        
        {renderCurrentStep()}
        
        <div className="flex justify-between mt-8">
          <Button
            variant="flat"
            color="default"
            onPress={prevStep}
            isDisabled={currentStep === 0 || isSubmitting}
            className="px-6"
          >
            ← Kembali
          </Button>
          
          <motion.div
            whileHover={!isSubmitting && !submitResult?.success ? { scale: 1.05 } : {}}
            animate={submitResult?.success ? {
              scale: [1, 1.2, 1.1],
              transition: { duration: 0.5 }
            } : {}}
          >
            <Button
              color={submitResult?.success ? "success" : "primary"}
              onPress={nextStep}
              isDisabled={!isStepValid || isSubmitting}
              className={`px-8 transition-all duration-300 ${
                submitResult?.success ? "bg-green-500" : ""
              }`}
            >
            {isSubmitting ? (
              <div className="flex items-center">
                <Spinner size="sm" color="white" className="mr-2" />
                <span className="text-white">Mengirim...</span>
              </div>
            ) : submitResult?.success ? (
              <motion.div 
                className="flex items-center"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              >
                <motion.span
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ 
                    type: "spring", 
                    stiffness: 260, 
                    damping: 20,
                    delay: 0.1 
                  }}
                  className="mr-2 text-lg text-white"
                >
                  ✓
                </motion.span>
                <motion.span
                  initial={{ x: 10, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ duration: 0.2, delay: 0.2 }}
                  className="text-white"
                >
                  Berhasil!
                </motion.span>
              </motion.div>
            ) : currentStep === steps.length - 1 ? (
              "Selesai ✓"
            ) : (
              "Lanjut →"
            )}
            </Button>
          </motion.div>
        </div>
      </div>
    </div>
  );
}