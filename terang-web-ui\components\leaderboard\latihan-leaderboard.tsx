import React, { useState, useEffect } from 'react';
import { 
  Table, 
  TableHeader, 
  TableBody, 
  TableColumn, 
  TableRow, 
  TableCell,
  Input,
  Card,
  CardHeader,
  CardBody,
  Pa<PERSON>ation,
  Spinner,
  Chip
} from "@heroui/react";

import { fetchLeaderboardData, fetchTargetAggregates, fetchExamConfig, fetchCategories } from './actions';
import { getUserUsername } from '@/app/lib/actions/account/actions';
import type { LeaderboardEntry, TargetAggregates, ExamType, CategoryOption } from './actions';

interface SubjectMetric {
  scoreKey: string;
  correctKey: string;
  totalKey: string;
}

interface LatihanFormProps {
  selectedExam: string;
  selectedCategory?: string;
}

// Define the structure of subjectScores to make TypeScript happy
interface SubjectScore {
  correct: number;
  total: number;
  score: number;
}

// Update LeaderboardEntry to include the properly typed subjectScores
interface EnhancedLeaderboardEntry extends Omit<LeaderboardEntry, 'subjectScores'> {
  subjectScores?: Record<string, SubjectScore>;
  isCurrentUser?: boolean;
  categoryIds?: string[];
  categoryNames?: string[];
}

// Category theme helper
const getCategoryTheme = (categoryName: string) => {
  const themes = {
    LPDP: {
      icon: "🎓", 
      color: "primary",
    },
    CPNS: {
      icon: "🏛️", 
      color: "success",
    },
    UTBK: {
      icon: "📚", 
      color: "warning",
    },
    SNBT: {
      icon: "📝", 
      color: "secondary",
    },
    Uncategorized: {
      icon: "✨", 
      color: "default",
    }
  };

  // Return the theme for the category or the uncategorized theme if not found
  return themes[categoryName as keyof typeof themes] || themes.Uncategorized;
};
const LatihanForm: React.FC<LatihanFormProps> = ({ selectedExam, selectedCategory }) => {
  const [filterText, setFilterText] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentUsername, setCurrentUsername] = useState<string | boolean | null>('');
  const [examConfig, setExamConfig] = useState<ExamType | null>(null);
  const [subjects, setSubjects] = useState<string[]>([]);
  const [examType, setExamType] = useState<string>(''); 
  const [leaderboards, setLeaderboards] = useState<Record<string, EnhancedLeaderboardEntry[]>>({});
  const [paginationState, setPaginationState] = useState<Record<string, number>>({});
  const [targetAggregates, setTargetAggregates] = useState<TargetAggregates | null>(null);
  const [categories, setCategories] = useState<CategoryOption[]>([]);
  const [activeCategory, setActiveCategory] = useState<string | null>(selectedCategory || null);
  
  const itemsPerPage = 7;

  // Fetch current username on mount
  useEffect(() => {
    const fetchUsername = async () => {
      try {
        const username = await getUserUsername();
        setCurrentUsername(username);
      } catch (error) {
        console.error('Error fetching username:', error);
      }
    };
    fetchUsername();
  }, []);

  // Fetch categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await fetchCategories();
        setCategories(categoriesData);
        
        // If not using a category from parent component, set default category
        if (!selectedCategory && categoriesData.length > 0) {
          setActiveCategory(categoriesData[0].value);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };
    
    loadCategories();
  }, [selectedCategory]);

  // Update active category when selectedCategory prop changes
  useEffect(() => {
    if (selectedCategory) {
      setActiveCategory(selectedCategory);
    }
  }, [selectedCategory]);
  // Fetch exam configuration
  useEffect(() => {
    const getExamConfig = async () => {
      if (!selectedExam) return;
      
      setLoading(true);
      try {
        // Call the fetchExamConfig function with the selected exam
        const examConfigData = await fetchExamConfig(selectedExam);
        
        if (!examConfigData) {
          throw new Error(`Failed to load exam configuration`);
        }
        
        // Save the exam type for future reference
        setExamType(examConfigData.id?.toUpperCase() || '');
        console.log(`Loaded exam configuration for exam type: ${examConfigData.id}`);
        
        // Initialize exam configuration based on subjects
        const subjectList = examConfigData.subjects.map(subject => subject.name);
        
        // Create initial pagination state
        const initialPagination: Record<string, number> = {};
        
        examConfigData.subjects.forEach(subject => {
          initialPagination[subject.name] = 1;
        });
        
        setSubjects(subjectList);
        setExamConfig(examConfigData);
        setPaginationState(initialPagination);
        
        // Reset any previous errors
        setError(null);
      } catch (error) {
        console.error('Error fetching exam configuration:', error);
        setError('Failed to load exam configuration. Please try again later or contact support if the problem persists.');
        setSubjects([]);
        setExamConfig(null);
      } finally {
        setLoading(false);
      }
    };
    
    getExamConfig();
  }, [selectedExam]);

  // Fetch target aggregates
  useEffect(() => {
    const getTargetAggregates = async () => {
      try {
        const aggregates = await fetchTargetAggregates();
        setTargetAggregates(aggregates);
      } catch (error) {
        console.error('Error fetching target aggregates:', error);
      }
    };
    
    getTargetAggregates();
  }, []);
  // Fetch leaderboards for all subjects with category filtering
// Fetch leaderboards with proper subject matching from the subjects array
// Fetch leaderboards and remove duplicate entries
useEffect(() => {
  const fetchAllLeaderboards = async () => {
    if (!selectedExam) return;
    
    setLoading(true);
    try {
      const examId = selectedExam;
      
      // Fetch leaderboard data for each subject
      const leaderboardPromises = subjects.map(subject => 
        fetchLeaderboardData(examId, subject, activeCategory || undefined)
      );
      
      const results = await Promise.all(leaderboardPromises);
      
      // Create a map to store unique subjects from the API responses
      const subjectToEntriesMap: Record<string, EnhancedLeaderboardEntry[]> = {};
      
      // Track unique users to prevent duplicates
      const processedUserIds: Record<string, Record<string, boolean>> = {};
      
      // Process all results
      results.forEach(result => {
        if (!result?.leaderboard || result.leaderboard.length === 0) return;
        
        // For each entry in the leaderboard
        result.leaderboard.forEach(entry => {
          // Skip entries that don't match the selected category
          if (activeCategory && (!entry.categoryIds || !entry.categoryIds.includes(activeCategory))) {
            return;
          }
          
          // Use the actual subject field from the API response
          if (entry.subject) {
            const subjectName = entry.subject;
            
            // Initialize tracking for this subject if needed
            if (!processedUserIds[subjectName]) {
              processedUserIds[subjectName] = {};
            }
            
            // Convert userId to string for safe indexing and check if we've processed this user
            const userIdKey = String(entry.userId || '');
            
            // Check if we've already processed this user for this subject
            if (processedUserIds[subjectName][userIdKey]) {
              console.log(`Skipping duplicate entry for user ${entry.username} in subject ${subjectName}`);
              return; // Skip duplicate user entries
            }
            
            // Mark this user as processed for this subject
            processedUserIds[subjectName][userIdKey] = true;
            
            // Initialize the array if needed
            if (!subjectToEntriesMap[subjectName]) {
              subjectToEntriesMap[subjectName] = [];
            }
            
            // Add the entry to its subject's array
            subjectToEntriesMap[subjectName].push(entry as EnhancedLeaderboardEntry);
          }
        });
      });
      
      console.log("Subjects with unique entries:", Object.keys(subjectToEntriesMap));
      
      // Now create the final leaderboard map with ranked entries
      const leaderboardMap: Record<string, EnhancedLeaderboardEntry[]> = {};
      
      Object.entries(subjectToEntriesMap).forEach(([subject, entries]) => {
        console.log(`Processing ${entries.length} unique entries for subject: ${subject}`);
        
        // Sort entries by score and time
        const sortedEntries = [...entries].sort((a, b) => {
          // Get total scores
          let scoreA = 0;
          let scoreB = 0;
          
          if (a.subjectScores) {
            Object.values(a.subjectScores).forEach(score => {
              scoreA += score.score || 0;
            });
          }
          
          if (b.subjectScores) {
            Object.values(b.subjectScores).forEach(score => {
              scoreB += score.score || 0;
            });
          }
          
          // Compare by score first
          if (scoreB !== scoreA) {
            return scoreB - scoreA;
          }
          
          // If scores are equal, compare by time
          return a.elapsedSeconds - b.elapsedSeconds;
        });
        
        // Add rank to each entry
        const rankedEntries = sortedEntries.map((entry, idx) => ({
          ...entry,
          rank: idx + 1
        }));
        
        // Add to the final map
        leaderboardMap[subject] = rankedEntries;
      });
      
      console.log("Final leaderboard subjects:", Object.keys(leaderboardMap));
      setLeaderboards(leaderboardMap);
      setError(null);
    } catch (err) {
      console.error('Error fetching leaderboards:', err);
      setError('Failed to load leaderboards');
    } finally {
      setLoading(false);
    }
  };

  if (selectedExam) {
    fetchAllLeaderboards();
  }
}, [selectedExam, subjects, activeCategory]);

  const filterData = (data: EnhancedLeaderboardEntry[]) =>
    data.filter((item) =>
      item.username.toLowerCase().includes(filterText.toLowerCase())
    );

  const getPaginatedData = (data: EnhancedLeaderboardEntry[], currentPage: number) => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return data.slice(startIndex, startIndex + itemsPerPage);
  };

  const setPageForSubject = (subject: string, page: number) => {
    setPaginationState(prev => ({
      ...prev,
      [subject]: page
    }));
  };

  const LeaderboardTable = ({ 
    data,
    title,
    subject,
    currentPage,
    setCurrentPage
  }: { 
    data: EnhancedLeaderboardEntry[],
    title: string,
    subject: string,
    currentPage: number,
    setCurrentPage: (page: number) => void
  }) => {   
    const filteredData = filterData(data);
    const paginatedData = getPaginatedData(filteredData, currentPage);
    const totalPages = Math.ceil(filteredData.length / itemsPerPage);

    // Find current user's entry
    const currentUserEntry = filteredData.find(entry => entry.username === currentUsername);
    const currentUserRank = currentUserEntry?.rank;
    const isCurrentUserInCurrentPage = paginatedData.some(entry => entry.username === currentUsername);

    // Create final data array including current user's entry if needed
    const finalData = [...paginatedData];
    if (currentUserEntry && !isCurrentUserInCurrentPage) {
      finalData.push({
        ...currentUserEntry,
        isCurrentUser: true
      });
    }
  
    interface Column {
      key: "rank" | "username" | "score" | "correct" | "categories";
      label: string;
    }
  
    const columns: Column[] = [
      {
        key: "rank",
        label: "RANK",
      },
      {
        key: "username",
        label: "USERNAME",
      },
      {
        key: "categories",
        label: "CATEGORIES",
      },
      {
        key: "score",
        label: "SCORE",
      },
      {
        key: "correct",
        label: "CORRECT",
      },
    ];
  
  // Update the renderCell function in the LeaderboardTable component of LatihanForm
  const renderCell = (item: EnhancedLeaderboardEntry, columnKey: Column["key"]) => {
    const value = (() => {
      switch (columnKey) {
        case "rank":
          return item.rank;
        case "username":
          return item.username;
        case "categories":
          if (!item.categoryNames || item.categoryNames.length === 0) return '-';
          return (
            <div className="flex flex-wrap gap-1">
              {item.categoryNames.slice(0, 2).map((cat, index) => {
                const theme = getCategoryTheme(cat);
                return (
                  <Chip key={index} color={theme.color as any} size="sm" variant="flat">
                    {theme.icon} {cat}
                  </Chip>
                );
              })}
              {item.categoryNames.length > 2 && (
                <Chip color="default" size="sm" variant="flat">
                  +{item.categoryNames.length - 2}
                </Chip>
              )}
            </div>
          );
        case "score":
          // Check if it's a UTBK exam by looking at the category names
          const isUtbk = item.categoryNames?.some(cat => cat.toUpperCase().includes('UTBK')) || false;
          
          if (isUtbk) {
            // UTBK calculation: average of (correct/total * 1000) for each subject
            let totalPoints = 0;
            let subjectCount = 0;
            
            if (item.subjectScores && Object.keys(item.subjectScores).length > 0) {
              Object.entries(item.subjectScores).forEach(([key, scoreData]) => {
                const correct = scoreData.correct || 0;
                const total = scoreData.total || 0;
                
                if (total > 0) {
                  // Calculate subject score using UTBK formula - Math.ceil((correct/total) * 1000)
                  const score = Math.ceil((correct / total) * 1000);
                  totalPoints += score;
                  subjectCount++;
                }
              });
              
              // Return average score, or 0 if no subjects
              return subjectCount > 0 ? Math.ceil(totalPoints / subjectCount) : 0;
            }
            return 0;
          } else {
            // CPNS/LPDP calculation: sum of (correct * 5) for all subjects except kepribadian
            let totalScore = 0;
            
            if (item.subjectScores && Object.keys(item.subjectScores).length > 0) {
              Object.entries(item.subjectScores).forEach(([key, scoreData]) => {
                // Skip kepribadian subject for score calculation
                if (key.toLowerCase() === 'kepribadian') {
                  return;
                }
                
                const correctValue = scoreData.correct || 0;
                if (typeof correctValue === 'number') {
                  totalScore += correctValue * 5;
                }
              });
            }
            
            return totalScore;
          }
        case "correct":
          // Similar logic for correct answers, but without multipliers
          // and respecting the UTBK vs CPNS/LPDP difference
          const isUtbkForCorrect = item.categoryNames?.some(cat => cat.toUpperCase().includes('UTBK')) || false;
          
          let totalCorrect = 0;
          
          if (item.subjectScores && Object.keys(item.subjectScores).length > 0) {
            Object.entries(item.subjectScores).forEach(([key, scoreData]) => {
              // Skip kepribadian subject for non-UTBK exams
              if (!isUtbkForCorrect && key.toLowerCase() === 'kepribadian') {
                return;
              }
              
              const correctValue = scoreData.correct || 0;
              if (typeof correctValue === 'number') {
                totalCorrect += correctValue;
              }
            });
          }
          
          return totalCorrect;
        default:
          return null;
      }
    })();

    return (
      <div>
        {value}
        {item.isCurrentUser && <span className="ml-2 text-sm text-gray-500">(You)</span>}
      </div>
    );
  };
  
    return (
      <Card className="w-full md:w-96">
        <CardHeader className="flex flex-col gap-2">
          <h4 className="text-lg font-bold text-center">{title}</h4>
          {currentUserRank && (
            <div className="text-sm text-center text-gray-500">
              Your Rank: #{currentUserRank}
            </div>
          )}
        </CardHeader>
        <CardBody>
          <Table
            aria-label={title}
            className="mb-4"
            removeWrapper
          >
            <TableHeader>
              {columns.map((column) => (
                <TableColumn key={column.key}>{column.label}</TableColumn>
              ))}
            </TableHeader>
            <TableBody
              items={finalData}
              emptyContent="No entries found"
            >
              {(item: EnhancedLeaderboardEntry) => (
                <TableRow 
                  key={`${item.rank}-${item.username}`}
                  className={item.username === currentUsername 
                    ? "bg-success-50/25 outline outline-success/50 outline-1 rounded-lg"
                    : ""}
                >
                  {(columnKey) => (
                    <TableCell>
                      {renderCell(item, columnKey as Column["key"])}
                    </TableCell>
                  )}
                </TableRow>
              )}
            </TableBody>
          </Table>
          {filteredData.length > 0 && (
            <div className="flex justify-center">
              <Pagination
                total={Math.max(1, totalPages)}
                page={currentPage}
                onChange={setCurrentPage}
                size="sm"
              />
            </div>
          )}
        </CardBody>
      </Card>
    );
  };
  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <Spinner label="Loading leaderboards..." />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex justify-center items-center min-h-[200px] text-danger">
        {error}
      </div>
    );
  }

  // No exam selected state
  if (!selectedExam) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        Please select an exam to view leaderboards
      </div>
    );
  }

  // Check if we have any leaderboard data to display
  const hasLeaderboardData = Object.keys(leaderboards).length > 0;

  return (
    <div className="p-6">
      <div className="mb-6 max-w-sm mx-auto">
        <Input
          type="text"
          placeholder="Filter by username..."
          value={filterText}
          onChange={(e) => setFilterText(e.target.value)}
          className="w-full"
          size="sm"
        />
      </div>

      {!hasLeaderboardData ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <p className="text-center">
            {activeCategory 
              ? `No leaderboard data found for this exam in the ${categories.find(c => c.value === activeCategory)?.label || ''} category.` 
              : 'No leaderboard data found for this exam.'}
          </p>
        </div>
      ) : (
        <div className="flex flex-wrap gap-6 justify-center">
          {subjects.map((subject) => {
            // Only render if there's data for this subject
            if (leaderboards[subject] && leaderboards[subject].length > 0) {
              return (
                <LeaderboardTable
                  key={subject}
                  title={`Si Paling ${subject}`}
                  subject={subject}
                  data={leaderboards[subject]}
                  currentPage={paginationState[subject] || 1}
                  setCurrentPage={(page) => setPageForSubject(subject, page)}
                />
              );
            }
            return null;
          })}
        </div>
      )}

      {hasLeaderboardData && targetAggregates && (
        <div className="mt-8">
          <h3 className="text-xl font-bold mb-4 text-center">Target Statistics</h3>
          <div className="flex flex-wrap gap-6 justify-center">
            {targetAggregates.positions && targetAggregates.positions.length > 0 && (
              <Card className="w-full md:w-96">
                <CardHeader>
                  <h4 className="text-lg font-bold">Popular Target Positions</h4>
                </CardHeader>
                <CardBody>
                  <Table aria-label="Target positions" removeWrapper>
                    <TableHeader>
                      <TableColumn>POSITION</TableColumn>
                      <TableColumn>COUNT</TableColumn>
                    </TableHeader>
                    <TableBody>
                      {targetAggregates.positions.slice(0, 5).map(position => (
                        <TableRow key={position.value}>
                          <TableCell>{position.label}</TableCell>
                          <TableCell>{position.count}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardBody>
              </Card>
            )}
            
            {targetAggregates.institutions && targetAggregates.institutions.length > 0 && (
              <Card className="w-full md:w-96">
                <CardHeader>
                  <h4 className="text-lg font-bold">Popular Target Institutions</h4>
                </CardHeader>
                <CardBody>
                  <Table aria-label="Target institutions" removeWrapper>
                    <TableHeader>
                      <TableColumn>INSTITUTION</TableColumn>
                      <TableColumn>COUNT</TableColumn>
                    </TableHeader>
                    <TableBody>
                      {targetAggregates.institutions.slice(0, 5).map(institution => (
                        <TableRow key={institution.value}>
                          <TableCell>{institution.label}</TableCell>
                          <TableCell>{institution.count}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardBody>
              </Card>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default LatihanForm;